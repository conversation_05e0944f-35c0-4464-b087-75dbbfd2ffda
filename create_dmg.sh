#!/bin/bash

# MediaPlayerManager DMG 製作腳本

echo "🎬 開始製作 MediaPlayerManager DMG 安裝檔..."

# 設定變數
APP_NAME="MediaPlayerManager"
DMG_NAME="MediaPlayerManager"
VERSION="1.0"
DMG_FILENAME="${DMG_NAME}_v${VERSION}.dmg"
TEMP_DMG="temp.dmg"

# 創建 DMG 構建目錄
BUILD_DIR="dmg_build"
mkdir -p "$BUILD_DIR"

echo "📁 創建 DMG 構建目錄: $BUILD_DIR"

# 檢查是否有 .app 檔案
if [ ! -d "build/Release/${APP_NAME}.app" ]; then
    echo "❌ 找不到 ${APP_NAME}.app"
    echo "請先在 Xcode 中構建 Release 版本："
    echo "1. 在 Xcode 中打開專案"
    echo "2. 選擇 Product → Archive"
    echo "3. Export 為 macOS App"
    echo "4. 將 .app 檔案複製到 build/Release/ 目錄"
    exit 1
fi

# 複製 App 到構建目錄
echo "📱 複製應用程式..."
cp -R "build/Release/${APP_NAME}.app" "$BUILD_DIR/"

# 創建 Applications 快捷方式
echo "🔗 創建 Applications 快捷方式..."
ln -sf /Applications "$BUILD_DIR/Applications"

# 創建 DMG 背景資料夾
mkdir -p "$BUILD_DIR/.background"

# 計算 DMG 大小（應用程式大小 + 50MB 緩衝）
APP_SIZE=$(du -sm "build/Release/${APP_NAME}.app" | cut -f1)
DMG_SIZE=$((APP_SIZE + 50))

echo "💾 創建 DMG 映像檔（大小: ${DMG_SIZE}MB）..."

# 創建空白 DMG
hdiutil create -srcfolder "$BUILD_DIR" -volname "$DMG_NAME" -fs HFS+ \
    -fsargs "-c c=64,a=16,e=16" -format UDRW -size ${DMG_SIZE}m "$TEMP_DMG"

# 掛載 DMG
echo "🔧 配置 DMG..."
DEVICE=$(hdiutil attach -readwrite -noverify -noautoopen "$TEMP_DMG" | \
    egrep '^/dev/' | sed 1q | awk '{print $1}')

# DMG 掛載點
MOUNT_DIR="/Volumes/$DMG_NAME"

# 等待掛載完成
sleep 2

# 設定 Finder 視窗屬性（AppleScript）
echo "🎨 設定 DMG 外觀..."
osascript << EOF
tell application "Finder"
    tell disk "$DMG_NAME"
        open
        set current view of container window to icon view
        set toolbar visible of container window to false
        set statusbar visible of container window to false
        set the bounds of container window to {400, 100, 900, 400}
        set theViewOptions to the icon view options of container window
        set arrangement of theViewOptions to not arranged
        set icon size of theViewOptions to 128
        set background picture of theViewOptions to file ".background:background.png"
        
        -- 設定圖標位置
        set position of item "${APP_NAME}.app" of container window to {150, 150}
        set position of item "Applications" of container window to {350, 150}
        
        close
        open
        update without registering applications
        delay 5
    end tell
end tell
EOF

# 設定檔案權限
chmod -Rf go-w "$MOUNT_DIR"

# 同步並卸載
sync
hdiutil detach "$DEVICE"

echo "📦 壓縮 DMG..."
# 轉換為最終壓縮格式
hdiutil convert "$TEMP_DMG" -format UDZO -imagekey zlib-level=9 -o "$DMG_FILENAME"

# 清理臨時檔案
rm -f "$TEMP_DMG"
rm -rf "$BUILD_DIR"

echo "✅ DMG 製作完成: $DMG_FILENAME"
echo "📁 檔案位置: $(pwd)/$DMG_FILENAME"

# 顯示檔案大小
DMG_SIZE_FINAL=$(du -h "$DMG_FILENAME" | cut -f1)
echo "📊 最終大小: $DMG_SIZE_FINAL"