# WebM 格式支援說明

## 問題描述

WebM 檔案可以加入播放列表，但無法播放。

## 原因分析

### WebM 格式限制

WebM 是 Google 開發的開放格式，使用：
- **視頻編碼**: VP8 或 VP9
- **音頻編碼**: Vorbis 或 Opus
- **容器格式**: Mat<PERSON><PERSON> (MKV 的變體)

### macOS AVPlayer 限制

Apple 的 AVPlayer **不原生支援** WebM 格式，因為：
1. AVPlayer 主要支援 Apple 認可的格式（MP4、MOV、M4V 等）
2. VP8/VP9 編碼不在 macOS 的原生編解碼器列表中
3. 需要第三方解碼器或轉換才能播放

## 實施的解決方案

### 1. 檔案載入時檢測 ✅
```swift
// 檢查是否為 WebM 格式
if url.pathExtension.lowercased() == "webm" {
    self.handleLoadError("WebM 格式需要額外的編解碼器支援。建議使用 MP4、MOV 或 MKV 格式。", completion: completion)
    return
}
```

### 2. 移除 WebM 從支援列表 ✅
```swift
// 從 supportedTypes 中移除 WebM
let videoExtensions = [
    "mp4", "m4v", "mov", "qt", "avi", "mkv", "flv", "wmv", "asf",
    // ... 其他格式
    // 注意：已移除 "webm" - macOS AVPlayer 不支援
]
```

### 3. 改進的錯誤處理 ✅
- 檢測檔案是否可播放
- 提供清晰的錯誤信息
- 建議使用支援的格式

## 支援的替代格式

### 推薦格式（最佳兼容性）
- **MP4** (.mp4) - H.264 視頻 + AAC 音頻
- **MOV** (.mov) - QuickTime 格式
- **M4V** (.m4v) - Apple 視頻格式

### 其他支援格式
- **MKV** (.mkv) - 需要 H.264 視頻編碼
- **AVI** (.avi) - 舊格式但廣泛支援
- **MPEG** (.mpg, .mpeg) - 標準視頻格式

## 如何轉換 WebM 檔案

### 使用 FFmpeg（命令行）
```bash
# 轉換 WebM 到 MP4
ffmpeg -i input.webm -c:v h264 -c:a aac output.mp4

# 快速轉換（保持質量）
ffmpeg -i input.webm -c:v libx264 -preset fast -crf 22 -c:a aac output.mp4
```

### 使用 HandBrake（圖形界面）
1. 下載 HandBrake: https://handbrake.fr/
2. 打開 WebM 檔案
3. 選擇 MP4 格式
4. 點擊「開始」轉換

### 線上轉換工具
- CloudConvert: https://cloudconvert.com/webm-to-mp4
- Convertio: https://convertio.co/webm-mp4/
- Online-Convert: https://www.online-convert.com/

## 未來可能的解決方案

### 1. 使用 VLCKit
整合 VLC 的媒體播放框架，支援更多格式包括 WebM。

### 2. 使用 FFmpeg 庫
直接整合 FFmpeg 進行即時轉碼。

### 3. 等待 Apple 支援
未來 macOS 版本可能會加入 VP9/WebM 支援。

## 總結

- ✅ WebM 檔案現在會顯示清晰的錯誤信息
- ✅ 不會再嘗試播放不支援的格式
- ✅ 提供了格式轉換的建議
- ✅ 保持了應用的穩定性

建議用戶將 WebM 檔案轉換為 MP4 格式以獲得最佳兼容性。

---

**更新時間**: 2025-01-08  
**影響**: WebM 檔案無法播放，但會顯示友好的錯誤提示  
**建議**: 使用 MP4、MOV 或 MKV 格式