import SwiftUI
import AVKit
import AVFoundation

// MARK: - 播放器診斷工具

struct PlayerDiagnosticsView: View {
    @ObservedObject var playerWindow: PlayerWindow
    @State private var diagnosticInfo: [String] = []
    @State private var isRunningDiagnostics = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("播放器診斷")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button("執行診斷") {
                    runDiagnostics()
                }
                .disabled(isRunningDiagnostics)
            }
            
            ScrollView {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(diagnosticInfo, id: \.self) { info in
                        Text(info)
                            .font(.system(.body, design: .monospaced))
                            .foregroundColor(getInfoColor(info))
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
            }
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
        .padding()
        .onAppear {
            runDiagnostics()
        }
    }
    
    private func getInfoColor(_ info: String) -> Color {
        if info.contains("❌") || info.contains("ERROR") {
            return .red
        } else if info.contains("⚠️") || info.contains("WARNING") {
            return .orange
        } else if info.contains("✅") || info.contains("SUCCESS") {
            return .green
        }
        return .primary
    }
    
    private func runDiagnostics() {
        isRunningDiagnostics = true
        diagnosticInfo.removeAll()
        
        addDiagnostic("=== 播放器診斷開始 ===")
        
        // 1. 檢查 PlayerWindow 狀態
        addDiagnostic("📱 PlayerWindow 狀態:")
        addDiagnostic("  - ID: \(playerWindow.id)")
        addDiagnostic("  - 標題: \(playerWindow.title)")
        addDiagnostic("  - 媒體 URL: \(playerWindow.mediaURL?.absoluteString ?? "無")")
        addDiagnostic("  - 播放清單數量: \(playerWindow.playlist.count)")
        
        // 2. 檢查 PlayerManager 狀態
        let playerManager = playerWindow.playerManager
        addDiagnostic("\n🎵 PlayerManager 狀態:")
        addDiagnostic("  - AVPlayer 存在: \(playerManager.player != nil ? "✅" : "❌")")
        addDiagnostic("  - 載入中: \(playerManager.isLoading ? "⏳" : "❌")")
        addDiagnostic("  - 播放中: \(playerManager.isPlaying ? "▶️" : "⏸️")")
        addDiagnostic("  - 音量: \(playerManager.volume)")
        addDiagnostic("  - 靜音: \(playerManager.isMuted ? "🔇" : "🔊")")
        addDiagnostic("  - 播放速度: \(playerManager.playbackRate)")
        addDiagnostic("  - 當前時間: \(formatTime(playerManager.currentTime))")
        addDiagnostic("  - 總時長: \(formatTime(playerManager.duration))")
        
        if let errorMessage = playerManager.errorMessage {
            addDiagnostic("  - 錯誤訊息: ❌ \(errorMessage)")
        }
        
        // 3. 檢查 AVPlayer 詳細狀態
        if let player = playerManager.player {
            addDiagnostic("\n🎬 AVPlayer 詳細狀態:")
            addDiagnostic("  - 播放速度: \(player.rate)")
            addDiagnostic("  - 音量: \(player.volume)")
            addDiagnostic("  - 靜音: \(player.isMuted)")
            addDiagnostic("  - 時間控制狀態: \(timeControlStatusString(player.timeControlStatus))")
            
            if let currentItem = player.currentItem {
                addDiagnostic("  - 播放項目存在: ✅")
                addDiagnostic("  - 項目狀態: \(playerItemStatusString(currentItem.status))")
                addDiagnostic("  - 可播放: \(currentItem.isPlaybackBufferFull ? "✅" : "❌")")
                addDiagnostic("  - 緩衝空間: \(currentItem.isPlaybackBufferEmpty ? "空" : "有資料")")
                
                if let asset = currentItem.asset as? AVURLAsset {
                    addDiagnostic("  - 檔案 URL: \(asset.url.absoluteString)")
                    addDiagnostic("  - 檔案存在: \(FileManager.default.fileExists(atPath: asset.url.path) ? "✅" : "❌")")
                }
                
                if let error = currentItem.error {
                    addDiagnostic("  - 播放項目錯誤: ❌ \(error.localizedDescription)")
                }
            } else {
                addDiagnostic("  - 播放項目: ❌ 無")
            }
        }
        
        // 4. 檢查媒體檔案
        if let mediaURL = playerWindow.mediaURL {
            addDiagnostic("\n📁 媒體檔案檢查:")
            addDiagnostic("  - 檔案路徑: \(mediaURL.path)")
            addDiagnostic("  - 檔案存在: \(FileManager.default.fileExists(atPath: mediaURL.path) ? "✅" : "❌")")
            addDiagnostic("  - 檔案擴展名: \(mediaURL.pathExtension)")
            addDiagnostic("  - 是視頻檔案: \(MediaInfoView.isVideoFile(url: mediaURL) ? "✅" : "❌")")
            
            // 檢查檔案大小
            do {
                let attributes = try FileManager.default.attributesOfItem(atPath: mediaURL.path)
                if let fileSize = attributes[.size] as? NSNumber {
                    addDiagnostic("  - 檔案大小: \(ByteCountFormatter.string(fromByteCount: fileSize.int64Value, countStyle: .file))")
                }
            } catch {
                addDiagnostic("  - 檔案大小: ❌ 無法讀取 (\(error.localizedDescription))")
            }
            
            // 檢查 AVAsset
            let asset = AVURLAsset(url: mediaURL)
            addDiagnostic("  - AVAsset 可讀取: \(asset.isReadable ? "✅" : "❌")")
            addDiagnostic("  - AVAsset 可播放: \(asset.isPlayable ? "✅" : "❌")")
            
            // 異步檢查媒體資訊
            Task {
                do {
                    let duration = try await asset.load(.duration)
                    let tracks = try await asset.load(.tracks)
                    
                    await MainActor.run {
                        addDiagnostic("  - 媒體時長: \(formatTime(duration.seconds))")
                        addDiagnostic("  - 音軌數量: \(tracks.filter { $0.mediaType == .audio }.count)")
                        addDiagnostic("  - 視軌數量: \(tracks.filter { $0.mediaType == .video }.count)")
                    }
                } catch {
                    await MainActor.run {
                        addDiagnostic("  - 媒體資訊: ❌ \(error.localizedDescription)")
                    }
                }
            }
        }
        
        // 5. 檢查系統權限
        addDiagnostic("\n🔒 系統權限:")
        
        // 檢查沙盒權限
        if let mediaURL = playerWindow.mediaURL {
            let canAccess = mediaURL.startAccessingSecurityScopedResource()
            addDiagnostic("  - 安全存取權限: \(canAccess ? "✅" : "❌")")
            if canAccess {
                mediaURL.stopAccessingSecurityScopedResource()
            }
        }
        
        addDiagnostic("\n=== 診斷完成 ===")
        isRunningDiagnostics = false
    }
    
    private func addDiagnostic(_ message: String) {
        diagnosticInfo.append(message)
    }
    
    private func formatTime(_ time: Double) -> String {
        if time.isNaN || time.isInfinite {
            return "00:00"
        }
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    private func timeControlStatusString(_ status: AVPlayer.TimeControlStatus) -> String {
        switch status {
        case .paused: return "暫停"
        case .playing: return "播放中"
        case .waitingToPlayAtSpecifiedRate: return "等待播放"
        @unknown default: return "未知"
        }
    }
    
    private func playerItemStatusString(_ status: AVPlayerItem.Status) -> String {
        switch status {
        case .unknown: return "未知"
        case .readyToPlay: return "準備播放"
        case .failed: return "失敗"
        @unknown default: return "未知狀態"
        }
    }
}

// MARK: - 增強的播放器視窗（包含診斷）
struct DiagnosticPlayerWindowView: View {
    @ObservedObject var playerWindow: PlayerWindow
    @State private var showDiagnostics = false
    
    var body: some View {
        ZStack {
            // 原本的播放器界面
            PlayerWindowView(playerWindow: playerWindow)
            
            // 診斷按鈕
            VStack {
                HStack {
                    Spacer()
                    Button("診斷") {
                        showDiagnostics.toggle()
                    }
                    .padding()
                }
                Spacer()
            }
        }
        .sheet(isPresented: $showDiagnostics) {
            PlayerDiagnosticsView(playerWindow: playerWindow)
                .frame(width: 600, height: 500)
        }
    }
}

// MARK: - 使用方法
/*
 在 MediaPlayerWindowController 中：
 
 將原本的：
 window.contentView = NSHostingView(rootView: PlayerWindowView(playerWindow: playerWindow))
 
 改為：
 window.contentView = NSHostingView(rootView: DiagnosticPlayerWindowView(playerWindow: playerWindow))
 
 或者直接使用 PlayerDiagnosticsView：
 PlayerDiagnosticsView(playerWindow: playerWindow)
*/