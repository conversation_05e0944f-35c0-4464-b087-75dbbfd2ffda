# 🔍 播放清單調試指南

## 🚨 當前問題
用戶反饋三個問題都沒有改善：
1. 混合格式播放清單自動切換失敗
2. 多個 MP4 檔案連續播放失敗  
3. WebM 載入時間仍然很長

## 🛠️ 已添加的調試功能

### 1. 播放清單點擊調試
```
🎵 UI點擊：準備播放索引 X, 檔案: filename.ext
🎵 播放清單點擊：選擇索引 X
🎵 當前索引：Y → 新索引：X
🎵 準備播放：filename.ext
📊 播放器當前狀態 - isLoading: true/false, isPlaying: true/false
```

### 2. PlayerManager 載入調試
```
🚀 快速載入: filename.ext
🔄 播放器狀態已重置
🎬 偵測到 WebM 格式，使用 WebView 播放器 (對於 WebM)
🔄 WebM: 已清理 AVPlayer，切換到 WebView 模式 (對於 WebM)
🔄 播放器已切換到: filename.ext
```

### 3. WindowManager 調試
```
🎥 PlayerWindow 開始載入媒體: filename.ext
📊 當前播放清單位置: X/Y
🎥 PlayerManager 載入結果: true/false for filename.ext
```

## 🧪 調試測試步驟

### 步驟 1: 建立簡單測試播放清單
1. **▶️ 重新運行應用** (Cmd+R)
2. **📝 添加兩個 MP4 檔案** 到播放清單
   - 檔案1: movie1.mp4
   - 檔案2: movie2.mp4
3. **🎬 播放第一個檔案** - 確認正常播放
4. **👆 手動點擊第二個檔案** - 觀察是否能正常切換

### 步驟 2: 檢查控制台日誌
當點擊第二個檔案時，應該看到：
```
🎵 UI點擊：準備播放索引 1, 檔案: movie2.mp4
🎵 播放清單點擊：選擇索引 1
🎵 當前索引：0 → 新索引：1
🎵 準備播放：movie2.mp4
📊 播放器當前狀態 - isLoading: false, isPlaying: true
🎥 PlayerWindow 開始載入媒體: movie2.mp4
📊 當前播放清單位置: 1/2
🚀 快速載入: movie2.mp4
🔄 播放器狀態已重置
🧹 清理所有觀察者
🧹 播放完成觀察者已清理
🔄 播放器已切換到: movie2.mp4
🎬 MP4 檔案特殊處理
🎥 PlayerManager 載入結果: true for movie2.mp4
```

### 步驟 3: 識別問題點
如果第二個檔案失敗，檢查日誌在哪一步停止：

#### A. UI 點擊沒有觸發
如果沒有看到 `🎵 UI點擊`，問題在於 UI 層面
- 播放清單按鈕可能沒有正確綁定
- SwiftUI 更新問題

#### B. PlayerManager 載入失敗  
如果看到 `🚀 快速載入` 但沒有後續日誌：
- 檔案路徑問題
- PlayerManager 內部錯誤

#### C. 觀察者或播放器設置失敗
如果載入成功但播放失敗：
- 觀察者設置問題
- MP4 特殊處理問題

### 步驟 4: WebM 測試
1. **📁 選擇一個 WebM 檔案**
2. **⏱️ 記錄載入時間** - 從點擊到開始播放
3. **🔍 檢查控制台** - 應該看到：
```
🎬 偵測到 WebM 格式，使用 WebView 播放器
🔄 WebM: 已清理 AVPlayer，切換到 WebView 模式
```

### 步驟 5: 混合格式測試
建立混合播放清單：
```
1. video.mov (AVPlayer)
2. movie.mp4 (AVPlayer with special handling)  
3. demo.webm (WebView)
```

手動點擊每個檔案，觀察切換是否正常。

## 🎯 關鍵修復點

### 1. WebM 狀態管理修復
```swift
// 檢查是否為 WebM 格式
if url.pathExtension.lowercased() == "webm" {
    // 清理舊播放器和觀察者 ⭐ 關鍵修復
    removeObservers()
    player?.pause()
    player = nil
    
    // 設置正確狀態
    isLoading = false
    isPlaying = false  // WebM 由 WebView 控制
    completion(true)
}
```

### 2. 詳細調試日誌
所有關鍵路徑都有詳細日誌，能夠精確定位問題所在。

## 📊 預期結果

### 如果修復成功
- 播放清單中任何檔案都能正常切換
- WebM 檔案不會影響後續檔案播放
- 控制台顯示完整的載入流程
- 沒有觀察者殘留或狀態衝突

### 如果問題仍存在
請截圖控制台日誌，特別注意：
1. 哪一步的日誌沒有出現
2. 是否有錯誤訊息
3. 播放器狀態變化

## 🔧 下一步行動

如果問題仍然存在，我們需要：
1. **檢查 UI 層面** - SwiftUI 狀態更新問題
2. **重構播放清單邏輯** - 可能需要完全不同的方法
3. **WebM 替代方案** - 考慮使用 AVPlayer 自定義資源載入器

---

**調試時間**: 2025-01-08  
**調試目標**: 播放清單第二個檔案播放失敗  
**調試策略**: 全面日誌追蹤 + WebM 狀態管理修復  
**狀態**: 🔍 準備深度調試