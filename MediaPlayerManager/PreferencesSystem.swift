import SwiftUI
import Foundation

// MARK: - 應用設定管理器
class AppSettings: ObservableObject {
    static let shared = AppSettings()
    
    // MARK: - 一般設定
    @Published var language: AppLanguage = .chinese {
        didSet { UserDefaults.standard.set(language.rawValue, forKey: "app_language") }
    }
    
    @Published var theme: AppTheme.ColorScheme = .dark {
        didSet { UserDefaults.standard.set(theme.rawValue, forKey: "app_theme") }
    }
    
    @Published var autoPlay: Bool = true {
        didSet { UserDefaults.standard.set(autoPlay, forKey: "auto_play") }
    }
    
    @Published var rememberWindowPositions: Bool = true {
        didSet { UserDefaults.standard.set(rememberWindowPositions, forKey: "remember_window_positions") }
    }
    
    // MARK: - 播放設定
    @Published var defaultVolume: Float = 0.8 {
        didSet { UserDefaults.standard.set(defaultVolume, forKey: "default_volume") }
    }
    
    @Published var defaultPlaybackSpeed: Float = 1.0 {
        didSet { UserDefaults.standard.set(defaultPlaybackSpeed, forKey: "default_playback_speed") }
    }
    
    @Published var skipIntroSeconds: Int = 0 {
        didSet { UserDefaults.standard.set(skipIntroSeconds, forKey: "skip_intro_seconds") }
    }
    
    @Published var skipOutroSeconds: Int = 0 {
        didSet { UserDefaults.standard.set(skipOutroSeconds, forKey: "skip_outro_seconds") }
    }
    
    @Published var crossfadeDuration: Double = 0.0 {
        didSet { UserDefaults.standard.set(crossfadeDuration, forKey: "crossfade_duration") }
    }
    
    // MARK: - 介面設定
    @Published var showProgressPreview: Bool = true {
        didSet { UserDefaults.standard.set(showProgressPreview, forKey: "show_progress_preview") }
    }
    
    @Published var controlsAutoHideDelay: Double = 3.0 {
        didSet { UserDefaults.standard.set(controlsAutoHideDelay, forKey: "controls_auto_hide_delay") }
    }
    
    @Published var showVolumeOSD: Bool = true {
        didSet { UserDefaults.standard.set(showVolumeOSD, forKey: "show_volume_osd") }
    }
    
    @Published var showSpeedOSD: Bool = true {
        didSet { UserDefaults.standard.set(showSpeedOSD, forKey: "show_speed_osd") }
    }
    
    // MARK: - 快捷鍵設定
    @Published var keyboardShortcuts: [ShortcutAction: KeyboardShortcut] = [:]
    
    // MARK: - 高級設定
    @Published var hardwareAcceleration: Bool = true {
        didSet { UserDefaults.standard.set(hardwareAcceleration, forKey: "hardware_acceleration") }
    }
    
    @Published var bufferSize: Int = 8 {
        didSet { UserDefaults.standard.set(bufferSize, forKey: "buffer_size") }
    }
    
    @Published var maxConcurrentStreams: Int = 4 {
        didSet { UserDefaults.standard.set(maxConcurrentStreams, forKey: "max_concurrent_streams") }
    }
    
    private init() {
        loadSettings()
        setupDefaultShortcuts()
    }
    
    private func loadSettings() {
        language = AppLanguage(rawValue: UserDefaults.standard.string(forKey: "app_language") ?? "zh") ?? .chinese
        theme = AppTheme.ColorScheme(rawValue: UserDefaults.standard.string(forKey: "app_theme") ?? "dark") ?? .dark
        autoPlay = UserDefaults.standard.object(forKey: "auto_play") as? Bool ?? true
        rememberWindowPositions = UserDefaults.standard.object(forKey: "remember_window_positions") as? Bool ?? true
        
        defaultVolume = UserDefaults.standard.object(forKey: "default_volume") as? Float ?? 0.8
        defaultPlaybackSpeed = UserDefaults.standard.object(forKey: "default_playback_speed") as? Float ?? 1.0
        skipIntroSeconds = UserDefaults.standard.object(forKey: "skip_intro_seconds") as? Int ?? 0
        skipOutroSeconds = UserDefaults.standard.object(forKey: "skip_outro_seconds") as? Int ?? 0
        crossfadeDuration = UserDefaults.standard.object(forKey: "crossfade_duration") as? Double ?? 0.0
        
        showProgressPreview = UserDefaults.standard.object(forKey: "show_progress_preview") as? Bool ?? true
        controlsAutoHideDelay = UserDefaults.standard.object(forKey: "controls_auto_hide_delay") as? Double ?? 3.0
        showVolumeOSD = UserDefaults.standard.object(forKey: "show_volume_osd") as? Bool ?? true
        showSpeedOSD = UserDefaults.standard.object(forKey: "show_speed_osd") as? Bool ?? true
        
        hardwareAcceleration = UserDefaults.standard.object(forKey: "hardware_acceleration") as? Bool ?? true
        bufferSize = UserDefaults.standard.object(forKey: "buffer_size") as? Int ?? 8
        maxConcurrentStreams = UserDefaults.standard.object(forKey: "max_concurrent_streams") as? Int ?? 4
    }
    
    func setupDefaultShortcuts() {
        keyboardShortcuts = [
            .playPause: KeyboardShortcut(.space),
            .fullscreen: KeyboardShortcut("f"),
            .mute: KeyboardShortcut("m"),
            .volumeUp: KeyboardShortcut(.upArrow),
            .volumeDown: KeyboardShortcut(.downArrow),
            .seekForward: KeyboardShortcut(.rightArrow),
            .seekBackward: KeyboardShortcut(.leftArrow),
            .nextTrack: KeyboardShortcut("n"),
            .previousTrack: KeyboardShortcut("p"),
            .openFile: KeyboardShortcut("o", modifiers: .command)
        ]
    }
}

// MARK: - 應用語言
enum AppLanguage: String, CaseIterable {
    case chinese = "zh"
    case english = "en"
    case japanese = "ja"
    
    var displayName: String {
        switch self {
        case .chinese: return "繁體中文"
        case .english: return "English"
        case .japanese: return "日本語"
        }
    }
}

// MARK: - 主題方案擴展
extension AppTheme {
    enum ColorScheme: String, CaseIterable {
        case dark = "dark"
        case light = "light"
        case auto = "auto"
        
        var displayName: String {
            switch self {
            case .dark: return "深色模式"
            case .light: return "淺色模式"
            case .auto: return "跟隨系統"
            }
        }
    }
}

// MARK: - 快捷鍵動作
enum ShortcutAction: String, CaseIterable {
    case playPause = "play_pause"
    case fullscreen = "fullscreen"
    case mute = "mute"
    case volumeUp = "volume_up"
    case volumeDown = "volume_down"
    case seekForward = "seek_forward"
    case seekBackward = "seek_backward"
    case nextTrack = "next_track"
    case previousTrack = "previous_track"
    case openFile = "open_file"
    case newWindow = "new_window"
    case closeWindow = "close_window"
    
    var displayName: String {
        switch self {
        case .playPause: return "播放/暫停"
        case .fullscreen: return "全螢幕切換"
        case .mute: return "靜音切換"
        case .volumeUp: return "音量增加"
        case .volumeDown: return "音量減少"
        case .seekForward: return "快進"
        case .seekBackward: return "快退"
        case .nextTrack: return "下一首"
        case .previousTrack: return "上一首"
        case .openFile: return "開啟檔案"
        case .newWindow: return "新視窗"
        case .closeWindow: return "關閉視窗"
        }
    }
}

// MARK: - 偏好設定視窗
struct EnhancedPreferencesView: View {
    @StateObject private var settings = AppSettings.shared
    @State private var selectedTab: PreferenceTab = .general
    @Environment(\.presentationMode) var presentationMode
    
    enum PreferenceTab: String, CaseIterable {
        case general = "一般"
        case playback = "播放"
        case interface = "介面"
        case shortcuts = "快捷鍵"
        case advanced = "高級"
        
        var icon: String {
            switch self {
            case .general: return "gearshape"
            case .playback: return "play.circle"
            case .interface: return "paintbrush"
            case .shortcuts: return "keyboard"
            case .advanced: return "wrench.and.screwdriver"
            }
        }
    }
    
    var body: some View {
        HSplitView {
            // 側邊欄
            PreferencesSidebarView(selectedTab: $selectedTab)
                .frame(minWidth: 200, maxWidth: 200)
            
            // 主內容區
            ScrollView {
                VStack(alignment: .leading, spacing: 0) {
                    // 標題
                    HStack {
                        Image(systemName: selectedTab.icon)
                            .font(.system(size: 24, weight: .medium))
                            .foregroundColor(AppTheme.primaryColor)
                        
                        Text(selectedTab.rawValue)
                            .font(.system(size: 28, weight: .bold))
                            .foregroundColor(AppTheme.primaryText)
                        
                        Spacer()
                        
                        Button("關閉") {
                            presentationMode.wrappedValue.dismiss()
                        }
                        .buttonStyle(ModernButtonStyle(color: .gray, size: .small))
                    }
                    .padding(.horizontal, 32)
                    .padding(.top, 32)
                    .padding(.bottom, 24)
                    
                    Divider()
                        .background(Color.white.opacity(0.1))
                        .padding(.horizontal, 32)
                    
                    // 設定內容
                    Group {
                        switch selectedTab {
                        case .general:
                            GeneralPreferencesView()
                        case .playback:
                            PlaybackPreferencesView()
                        case .interface:
                            InterfacePreferencesView()
                        case .shortcuts:
                            ShortcutsPreferencesView()
                        case .advanced:
                            AdvancedPreferencesView()
                        }
                    }
                    .padding(.horizontal, 32)
                    .padding(.vertical, 24)
                    
                    Spacer(minLength: 32)
                }
            }
            .frame(minWidth: 500)
        }
        .frame(width: 800, height: 600)
        .background(AppTheme.backgroundColor)
    }
}

// MARK: - 偏好設定側邊欄
struct PreferencesSidebarView: View {
    @Binding var selectedTab: EnhancedPreferencesView.PreferenceTab
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("偏好設定")
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(AppTheme.primaryText)
                .padding(.horizontal, 16)
                .padding(.top, 32)
                .padding(.bottom, 16)
            
            ForEach(EnhancedPreferencesView.PreferenceTab.allCases, id: \.self) { tab in
                PreferenceTabButton(
                    tab: tab,
                    isSelected: selectedTab == tab
                ) {
                    selectedTab = tab
                }
            }
            
            Spacer()
        }
        .background(AppTheme.surfaceColor)
    }
}

// MARK: - 偏好設定選項卡按鈕
struct PreferenceTabButton: View {
    let tab: EnhancedPreferencesView.PreferenceTab
    let isSelected: Bool
    let action: () -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: tab.icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(isSelected ? AppTheme.primaryColor : AppTheme.secondaryText)
                    .frame(width: 20)
                
                Text(tab.rawValue)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isSelected ? AppTheme.primaryText : AppTheme.secondaryText)
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? AppTheme.primaryColor.opacity(0.15) : (isHovered ? Color.white.opacity(0.05) : Color.clear))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? AppTheme.primaryColor.opacity(0.3) : Color.clear, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.horizontal, 8)
        .onHover { hovering in
            isHovered = hovering
        }
    }
}

// MARK: - 一般偏好設定
struct GeneralPreferencesView: View {
    @StateObject private var settings = AppSettings.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            PreferenceSection(title: "語言與地區") {
                PreferenceRow(title: "介面語言") {
                    Picker("", selection: $settings.language) {
                        ForEach(AppLanguage.allCases, id: \.self) { language in
                            Text(language.displayName).tag(language)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .frame(width: 150)
                }
            }
            
            PreferenceSection(title: "外觀") {
                PreferenceRow(title: "主題") {
                    Picker("", selection: $settings.theme) {
                        ForEach(AppTheme.ColorScheme.allCases, id: \.self) { theme in
                            Text(theme.displayName).tag(theme)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .frame(width: 150)
                }
            }
            
            PreferenceSection(title: "行為") {
                PreferenceRow(title: "自動播放") {
                    Toggle("", isOn: $settings.autoPlay)
                        .toggleStyle(SwitchToggleStyle())
                }
                
                PreferenceRow(title: "記住視窗位置") {
                    Toggle("", isOn: $settings.rememberWindowPositions)
                        .toggleStyle(SwitchToggleStyle())
                }
            }
        }
    }
}

// MARK: - 播放偏好設定
struct PlaybackPreferencesView: View {
    @StateObject private var settings = AppSettings.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            PreferenceSection(title: "音量設定") {
                PreferenceRow(title: "預設音量") {
                    HStack {
                        Slider(value: $settings.defaultVolume, in: 0...1)
                            .frame(width: 120)
                        
                        Text("\(Int(settings.defaultVolume * 100))%")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(AppTheme.secondaryText)
                            .frame(width: 40, alignment: .trailing)
                    }
                }
            }
            
            PreferenceSection(title: "播放速度") {
                PreferenceRow(title: "預設播放速度") {
                    Picker("", selection: $settings.defaultPlaybackSpeed) {
                        ForEach(PlayerManager.playbackSpeeds, id: \.self) { speed in
                            Text("\(String(format: speed == 1.0 ? "%.0fx" : "%.2fx", speed))").tag(speed)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .frame(width: 100)
                }
            }
            
            PreferenceSection(title: "跳過設定") {
                PreferenceRow(title: "跳過片頭") {
                    HStack {
                        Stepper("", value: $settings.skipIntroSeconds, in: 0...60, step: 5)
                            .frame(width: 80)
                        
                        Text("\(settings.skipIntroSeconds) 秒")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(AppTheme.secondaryText)
                    }
                }
                
                PreferenceRow(title: "跳過片尾") {
                    HStack {
                        Stepper("", value: $settings.skipOutroSeconds, in: 0...60, step: 5)
                            .frame(width: 80)
                        
                        Text("\(settings.skipOutroSeconds) 秒")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(AppTheme.secondaryText)
                    }
                }
            }
            
            PreferenceSection(title: "音訊效果") {
                PreferenceRow(title: "淡入淡出") {
                    HStack {
                        Slider(value: $settings.crossfadeDuration, in: 0...5)
                            .frame(width: 120)
                        
                        Text("\(String(format: "%.1f", settings.crossfadeDuration)) 秒")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(AppTheme.secondaryText)
                            .frame(width: 60, alignment: .trailing)
                    }
                }
            }
        }
    }
}

// MARK: - 介面偏好設定
struct InterfacePreferencesView: View {
    @StateObject private var settings = AppSettings.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            PreferenceSection(title: "控制項") {
                PreferenceRow(title: "顯示進度預覽") {
                    Toggle("", isOn: $settings.showProgressPreview)
                        .toggleStyle(SwitchToggleStyle())
                }
                
                PreferenceRow(title: "控制項自動隱藏") {
                    HStack {
                        Slider(value: $settings.controlsAutoHideDelay, in: 1...10)
                            .frame(width: 120)
                        
                        Text("\(String(format: "%.1f", settings.controlsAutoHideDelay)) 秒")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(AppTheme.secondaryText)
                            .frame(width: 60, alignment: .trailing)
                    }
                }
            }
            
            PreferenceSection(title: "螢幕顯示 (OSD)") {
                PreferenceRow(title: "顯示音量指示器") {
                    Toggle("", isOn: $settings.showVolumeOSD)
                        .toggleStyle(SwitchToggleStyle())
                }
                
                PreferenceRow(title: "顯示速度指示器") {
                    Toggle("", isOn: $settings.showSpeedOSD)
                        .toggleStyle(SwitchToggleStyle())
                }
            }
        }
    }
}

// MARK: - 快捷鍵偏好設定
struct ShortcutsPreferencesView: View {
    @StateObject private var settings = AppSettings.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("自訂快捷鍵")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(AppTheme.primaryText)
            
            VStack(spacing: 8) {
                ForEach(ShortcutAction.allCases, id: \.self) { action in
                    HStack {
                        Text(action.displayName)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppTheme.primaryText)
                            .frame(width: 120, alignment: .leading)
                        
                        Spacer()
                        
                        if let shortcut = settings.keyboardShortcuts[action] {
                            ShortcutDisplayView(shortcut: shortcut)
                        }
                        
                        Button("編輯") {
                            // TODO: 實現快捷鍵編輯
                        }
                        .buttonStyle(ModernButtonStyle(color: AppTheme.primaryColor, size: .small))
                    }
                    .padding(.vertical, 4)
                }
            }
            
            HStack {
                Spacer()
                
                Button("重設為預設值") {
                    settings.setupDefaultShortcuts()
                }
                .buttonStyle(ModernButtonStyle(color: .gray, size: .medium))
            }
            .padding(.top, 16)
        }
    }
}

// MARK: - 高級偏好設定
struct AdvancedPreferencesView: View {
    @StateObject private var settings = AppSettings.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            PreferenceSection(title: "效能") {
                PreferenceRow(title: "硬體加速") {
                    Toggle("", isOn: $settings.hardwareAcceleration)
                        .toggleStyle(SwitchToggleStyle())
                }
                
                PreferenceRow(title: "緩衝區大小") {
                    HStack {
                        Slider(value: Binding(
                            get: { Double(settings.bufferSize) },
                            set: { settings.bufferSize = Int($0) }
                        ), in: 1...32, step: 1)
                        .frame(width: 120)
                        
                        Text("\(settings.bufferSize) MB")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(AppTheme.secondaryText)
                            .frame(width: 50, alignment: .trailing)
                    }
                }
                
                PreferenceRow(title: "最大並發串流") {
                    HStack {
                        Stepper("", value: $settings.maxConcurrentStreams, in: 1...8)
                            .frame(width: 80)
                        
                        Text("\(settings.maxConcurrentStreams)")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(AppTheme.secondaryText)
                    }
                }
            }
            
            PreferenceSection(title: "偵錯") {
                PreferenceRow(title: "清除快取") {
                    Button("清除") {
                        // TODO: 實現清除快取
                    }
                    .buttonStyle(ModernButtonStyle(color: .orange, size: .small))
                }
                
                PreferenceRow(title: "匯出設定") {
                    Button("匯出") {
                        // TODO: 實現設定匯出
                    }
                    .buttonStyle(ModernButtonStyle(color: AppTheme.primaryColor, size: .small))
                }
                
                PreferenceRow(title: "重設所有設定") {
                    Button("重設") {
                        // TODO: 實現重設功能
                    }
                    .buttonStyle(ModernButtonStyle(color: .red, size: .small))
                }
            }
        }
    }
}

// MARK: - 偏好設定區塊
struct PreferenceSection<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(title)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(AppTheme.primaryText)
            
            VStack(alignment: .leading, spacing: 12) {
                content
            }
            .padding(.leading, 16)
        }
    }
}

// MARK: - 偏好設定行
struct PreferenceRow<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        HStack {
            Text(title)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(AppTheme.primaryText)
                .frame(width: 160, alignment: .leading)
            
            Spacer()
            
            content
        }
        .padding(.vertical, 2)
    }
}

// MARK: - 快捷鍵顯示視圖
struct ShortcutDisplayView: View {
    let shortcut: KeyboardShortcut
    
    var body: some View {
        HStack(spacing: 4) {
            if shortcut.modifiers.contains(.command) {
                KeyCapView(text: "⌘")
            }
            if shortcut.modifiers.contains(.option) {
                KeyCapView(text: "⌥")
            }
            if shortcut.modifiers.contains(.control) {
                KeyCapView(text: "⌃")
            }
            if shortcut.modifiers.contains(.shift) {
                KeyCapView(text: "⇧")
            }
            
            KeyCapView(text: keyDisplayText)
        }
    }
    
    private var keyDisplayText: String {
        switch shortcut.key {
        case .space: return "Space"
        case .upArrow: return "↑"
        case .downArrow: return "↓"
        case .leftArrow: return "←"
        case .rightArrow: return "→"
        case .escape: return "Esc"
        case .tab: return "Tab"
        case .return: return "↵"
        case .delete: return "⌫"
        default: return shortcut.key.character.uppercased()
        }
    }
}

// MARK: - 按鍵帽視圖
struct KeyCapView: View {
    let text: String
    
    var body: some View {
        Text(text)
            .font(.system(size: 12, weight: .medium))
            .foregroundColor(AppTheme.primaryText)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 4)
                    .fill(AppTheme.surfaceColor)
                    .overlay(
                        RoundedRectangle(cornerRadius: 4)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
    }
}