//
//  WebMPlayer.swift
//  MediaPlayerManager
//
//  WebM 播放器 - 使用 WKWebView 支援 WebM 格式
//

import SwiftUI
import WebKit

// MARK: - WebM Player View
struct WebMPlayerView: View {
    let url: URL
    @State private var isLoading = true
    @State private var errorMessage: String?
    
    var body: some View {
        VStack {
            if let errorMessage = errorMessage {
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 48))
                        .foregroundColor(.orange)
                    
                    Text("WebM 播放錯誤")
                        .font(.headline)
                        .foregroundColor(.white)
                    
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black)
            } else {
                ZStack {
                    WebMWebView(url: url, isLoading: $isLoading, errorMessage: $errorMessage)
                        .background(Color.black)
                    
                    if isLoading {
                        VStack(spacing: 16) {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.5)
                            
                            Text("載入 WebM 檔案...")
                                .font(.headline)
                                .foregroundColor(.white)
                        }
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.black.opacity(0.8))
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black)
    }
}

// MARK: - WebView Wrapper
struct WebMWebView: NSViewRepresentable {
    let url: URL
    @Binding var isLoading: Bool
    @Binding var errorMessage: String?
    
    func makeNSView(context: Context) -> WKWebView {
        let configuration = WKWebViewConfiguration()
        configuration.mediaTypesRequiringUserActionForPlayback = []
        
        let webView = WKWebView(frame: .zero, configuration: configuration)
        webView.navigationDelegate = context.coordinator
        
        // 簡化的 HTML 頁面來播放 WebM - 優化載入速度
        let html = """
        <!DOCTYPE html>
        <html>
        <head>
            <style>body{margin:0;background:#000;display:flex;height:100vh;}video{width:100%;height:100%;object-fit:contain;}</style>
        </head>
        <body>
            <video controls autoplay preload="metadata" style="background:#000;">
                <source src="file://\(url.path)" type="video/webm">
            </video>
            <script>
                const v=document.querySelector('video');
                v.onloadstart=()=>console.log('WebM開始載入');
                v.oncanplay=()=>console.log('WebM可以播放');
                v.onerror=()=>console.log('WebM載入錯誤');
            </script>
        </body>
        </html>
        """
        
        print("🎬 WebM 播放器初始化: \(url.lastPathComponent)")
        webView.loadHTMLString(html, baseURL: url.deletingLastPathComponent())
        return webView
    }
    
    func updateNSView(_ nsView: WKWebView, context: Context) {
        // 不需要更新
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate {
        let parent: WebMWebView
        
        init(_ parent: WebMWebView) {
            self.parent = parent
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            print("🎬 WebM 頁面載入完成")
            parent.isLoading = false
        }
        
        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            print("❌ WebM 導航失敗: \(error.localizedDescription)")
            parent.isLoading = false
            parent.errorMessage = "無法載入 WebM 檔案: \(error.localizedDescription)"
        }
        
        func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
            print("❌ WebM provisional 導航失敗: \(error.localizedDescription)")
            parent.isLoading = false
            parent.errorMessage = "無法載入 WebM 檔案: \(error.localizedDescription)"
        }
    }
}

// MARK: - WebM Detection Helper
extension URL {
    var isWebM: Bool {
        return self.pathExtension.lowercased() == "webm"
    }
}