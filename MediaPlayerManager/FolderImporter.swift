import SwiftUI
import UniformTypeIdentifiers
import AVFoundation

// MARK: - Folder Import Manager
class FolderImporter: ObservableObject {
    @Published var isImporting = false
    @Published var progress: Double = 0.0
    @Published var importedFiles: [URL] = []
    @Published var totalFiles: Int = 0
    @Published var currentFile: String = ""
    
    // Supported media file extensions
    private let supportedVideoExtensions = [
        "mp4", "m4v", "mov", "qt", "avi", "mkv", "webm", "flv", "wmv", "asf",
        "mts", "m2ts", "ts", "vob", "ogv", "ogg", "dv", "3gp", "3g2",
        "mxf", "r3d", "rm", "rmvb", "f4v", "swf", "divx", "xvid"
    ]
    
    private let supportedAudioExtensions = [
        "mp3", "m4a", "aac", "wav", "flac", "aiff", "au", "m4b", "m4p", "m4r",
        "alac", "ape", "wv", "tta", "dts", "ac3", "eac3",
        "ogg", "oga", "opus", "wma", "ra", "amr", "3ga", "caf", "sd2"
    ]
    
    /// Import all media files from a folder
    @MainActor
    func importFolder(_ folderURL: URL) async {
        isImporting = true
        progress = 0.0
        importedFiles = []
        totalFiles = 0
        currentFile = ""
        
        do {
            let allFiles = try await scanFolderRecursively(folderURL)
            let mediaFiles = filterMediaFiles(allFiles)
            
            totalFiles = mediaFiles.count
            
            for (index, file) in mediaFiles.enumerated() {
                currentFile = file.lastPathComponent
                progress = Double(index) / Double(totalFiles)
                
                // Validate file can be played
                if await validateMediaFile(file) {
                    importedFiles.append(file)
                }
                
                // Small delay to show progress
                try? await Task.sleep(nanoseconds: 10_000_000) // 0.01 seconds
            }
            
            progress = 1.0
            isImporting = false
            currentFile = "完成"
            
        } catch {
            isImporting = false
            print("資料夾匯入錯誤: \(error)")
        }
    }
    
    /// Scan folder recursively for all files
    private func scanFolderRecursively(_ folderURL: URL) async throws -> [URL] {
        return try await Task.detached {
            let fileManager = FileManager.default
            var allFiles: [URL] = []
            
            let resourceKeys: [URLResourceKey] = [.isDirectoryKey, .isRegularFileKey]
            
            if let enumerator = fileManager.enumerator(
                at: folderURL,
                includingPropertiesForKeys: resourceKeys,
                options: [.skipsHiddenFiles, .skipsPackageDescendants]
            ) {
                let urls = Array(enumerator)
                for case let fileURL as URL in urls {
                    let resourceValues = try fileURL.resourceValues(forKeys: Set(resourceKeys))
                    
                    if resourceValues.isRegularFile == true {
                        allFiles.append(fileURL)
                    }
                }
            }
            
            return allFiles
        }.value
    }
    
    /// Filter files to only include media files
    private func filterMediaFiles(_ files: [URL]) -> [URL] {
        let allSupportedExtensions = supportedVideoExtensions + supportedAudioExtensions
        
        return files.filter { url in
            let fileExtension = url.pathExtension.lowercased()
            return allSupportedExtensions.contains(fileExtension)
        }
    }
    
    /// Validate if a media file can be played
    private func validateMediaFile(_ url: URL) async -> Bool {
        return await withCheckedContinuation { continuation in
            let asset = AVURLAsset(url: url)
            
            Task {
                do {
                    let isPlayable = try await asset.load(.isPlayable)
                    let duration = try await asset.load(.duration)
                    
                    // Check if file is playable and has valid duration
                    let isValid = isPlayable && duration.seconds > 0
                    continuation.resume(returning: isValid)
                } catch {
                    continuation.resume(returning: false)
                }
            }
        }
    }
    
    /// Check if file is a media file
    func isMediaFile(_ url: URL) -> Bool {
        let allSupportedExtensions = supportedVideoExtensions + supportedAudioExtensions
        return allSupportedExtensions.contains(url.pathExtension.lowercased())
    }
    
    /// Check if file is a video file
    func isVideoFile(_ url: URL) -> Bool {
        return supportedVideoExtensions.contains(url.pathExtension.lowercased())
    }
    
    /// Create playlist from imported files
    func createPlaylistFromImportedFiles(name: String) -> Playlist? {
        guard !importedFiles.isEmpty else { return nil }
        
        let mediaItems = importedFiles.compactMap { url -> MediaItem? in
            var item = MediaItem(url: url)
            item.fileSize = getFileSize(url)
            return item
        }
        
        return Playlist(name: name, items: mediaItems)
    }
    
    /// Get file size in bytes
    private func getFileSize(_ url: URL) -> Int64 {
        do {
            let resourceValues = try url.resourceValues(forKeys: [.fileSizeKey])
            return Int64(resourceValues.fileSize ?? 0)
        } catch {
            return 0
        }
    }
}

// MARK: - Folder Import View
struct FolderImportView: View {
    @StateObject private var folderImporter = FolderImporter()
    @ObservedObject var playlistManager: PlaylistManager
    @State private var isShowingFolderPicker = false
    @State private var isShowingPlaylistCreation = false
    @State private var newPlaylistName = ""
    @Binding var isPresented: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            HStack {
                Text("資料夾匯入")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Spacer()
                
                Button("取消") {
                    isPresented = false
                }
            }
            .padding()
            
            // Import Status
            if folderImporter.isImporting {
                VStack(spacing: 15) {
                    ProgressView(value: folderImporter.progress)
                        .progressViewStyle(LinearProgressViewStyle())
                    
                    Text("正在掃描: \(folderImporter.currentFile)")
                        .foregroundColor(.secondary)
                        .font(.caption)
                    
                    Text("已找到 \(folderImporter.importedFiles.count) / \(folderImporter.totalFiles) 個媒體檔案")
                        .font(.caption)
                }
                .padding()
                .background(Color.secondary.opacity(0.1))
                .cornerRadius(10)
            }
            
            // Import Results
            if !folderImporter.importedFiles.isEmpty && !folderImporter.isImporting {
                VStack(alignment: .leading, spacing: 10) {
                    Text("找到 \(folderImporter.importedFiles.count) 個媒體檔案")
                        .font(.headline)
                    
                    ScrollView {
                        LazyVStack(spacing: 5) {
                            ForEach(folderImporter.importedFiles, id: \.absoluteString) { url in
                                HStack {
                                    let isVideo = folderImporter.isVideoFile(url)
                                    Image(systemName: isVideo ? "video" : "music.note")
                                        .foregroundColor(isVideo ? .blue : .green)
                                    
                                    Text(url.lastPathComponent)
                                        .font(.caption)
                                    
                                    Spacer()
                                }
                                .padding(.horizontal, 10)
                                .padding(.vertical, 2)
                            }
                        }
                    }
                    .frame(maxHeight: 200)
                    .background(Color.secondary.opacity(0.05))
                    .cornerRadius(8)
                }
            }
            
            Spacer()
            
            // Action Buttons
            HStack {
                Button("選擇資料夾") {
                    isShowingFolderPicker = true
                }
                .buttonStyle(.borderedProminent)
                .disabled(folderImporter.isImporting)
                
                if !folderImporter.importedFiles.isEmpty && !folderImporter.isImporting {
                    Button("建立播放清單") {
                        isShowingPlaylistCreation = true
                    }
                    .buttonStyle(.bordered)
                }
            }
        }
        .padding()
        .frame(width: 500, height: 400)
        .fileImporter(
            isPresented: $isShowingFolderPicker,
            allowedContentTypes: [.folder],
            allowsMultipleSelection: false
        ) { result in
            switch result {
            case .success(let urls):
                if let folderURL = urls.first {
                    Task {
                        await folderImporter.importFolder(folderURL)
                    }
                }
            case .failure(let error):
                print("資料夾選擇錯誤: \(error)")
            }
        }
        .alert("建立播放清單", isPresented: $isShowingPlaylistCreation) {
            TextField("播放清單名稱", text: $newPlaylistName)
            Button("建立") {
                createPlaylist()
            }
            Button("取消", role: .cancel) { }
        } message: {
            Text("為匯入的 \(folderImporter.importedFiles.count) 個檔案建立播放清單")
        }
    }
    
    private func isVideoFile(_ url: URL) -> Bool {
        let videoExtensions = ["mp4", "m4v", "mov", "avi", "mkv", "webm", "flv", "wmv"]
        return videoExtensions.contains(url.pathExtension.lowercased())
    }
    
    private func createPlaylist() {
        guard !newPlaylistName.isEmpty else { return }
        
        if let playlist = folderImporter.createPlaylistFromImportedFiles(name: newPlaylistName) {
            _ = playlistManager.createPlaylist(name: newPlaylistName, items: playlist.items)
            newPlaylistName = ""
            isPresented = false
        }
    }
}

// MARK: - Folder Drop Zone
struct FolderDropZone: View {
    @ObservedObject var playlistManager: PlaylistManager
    @State private var isShowingFolderImport = false
    @State private var isTargeted = false
    
    var body: some View {
        RoundedRectangle(cornerRadius: 12)
            .stroke(
                isTargeted ? Color.blue : Color.secondary.opacity(0.5),
                style: StrokeStyle(lineWidth: 2, dash: [5])
            )
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isTargeted ? Color.blue.opacity(0.1) : Color.secondary.opacity(0.05))
            )
            .overlay(
                VStack(spacing: 10) {
                    Image(systemName: "folder.badge.plus")
                        .font(.system(size: 40))
                        .foregroundColor(isTargeted ? .blue : .secondary)
                    
                    Text("拖放資料夾到此處")
                        .font(.headline)
                        .foregroundColor(isTargeted ? .blue : .secondary)
                    
                    Text("自動掃描所有多媒體檔案")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Button("或點擊選擇資料夾") {
                        isShowingFolderImport = true
                    }
                    .buttonStyle(.bordered)
                }
            )
            .frame(height: 120)
            .onDrop(of: [.fileURL], isTargeted: $isTargeted) { providers in
                handleFolderDrop(providers)
                return true
            }
            .sheet(isPresented: $isShowingFolderImport) {
                FolderImportView(
                    playlistManager: playlistManager,
                    isPresented: $isShowingFolderImport
                )
            }
    }
    
    private func handleFolderDrop(_ providers: [NSItemProvider]) {
        guard let provider = providers.first else { return }
        
        _ = provider.loadObject(ofClass: URL.self) { url, _ in
            guard let url = url else { return }
            
            // Check if it's a directory
            var isDirectory: ObjCBool = false
            if FileManager.default.fileExists(atPath: url.path, isDirectory: &isDirectory),
               isDirectory.boolValue {
                DispatchQueue.main.async {
                    isShowingFolderImport = true
                    // Auto-start import for dropped folder
                    Task {
                        let importer = FolderImporter()
                        await importer.importFolder(url)
                    }
                }
            }
        }
    }
}