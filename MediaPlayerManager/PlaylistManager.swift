import SwiftUI
import Foundation
import UniformTypeIdentifiers

// MARK: - Media File Helper
struct MediaFileHelper {
    static func getFileFormat(from url: URL) -> String {
        return url.pathExtension.uppercased()
    }
    
    static func isVideoFile(url: URL) -> Bool {
        let videoExtensions = [
            "mp4", "m4v", "mov", "qt", "avi", "mkv", "webm", "flv", "wmv", "asf",
            "mts", "m2ts", "ts", "vob", "ogv", "ogg", "dv", "3gp", "3g2",
            "mxf", "r3d", "rm", "rmvb", "f4v", "swf", "divx", "xvid"
        ]
        return videoExtensions.contains(url.pathExtension.lowercased())
    }
    
    static func isAudioFile(url: URL) -> <PERSON>ol {
        let audioExtensions = [
            "mp3", "m4a", "aac", "wav", "flac", "aiff", "au", "m4b", "m4p", "m4r",
            "alac", "ape", "wv", "tta", "dts", "ac3", "eac3",
            "oga", "opus", "wma", "ra", "amr", "3ga", "caf", "sd2"
        ]
        return audioExtensions.contains(url.pathExtension.lowercased())
    }
    
    static func getFileSize(url: URL) -> Int64 {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
            return attributes[.size] as? Int64 ?? 0
        } catch {
            return 0
        }
    }
}

// MARK: - 播放清單管理器
class PlaylistManager: ObservableObject {
    static let shared = PlaylistManager()
    
    @Published var playlists: [Playlist] = []
    @Published var currentPlaylist: Playlist?
    
    private let documentsURL: URL
    private let playlistsURL: URL
    
    private init() {
        documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        playlistsURL = documentsURL.appendingPathComponent("Playlists")
        
        createPlaylistsDirectoryIfNeeded()
        loadPlaylists()
    }
    
    // MARK: - 播放清單操作
    func createPlaylist(name: String, items: [MediaItem] = []) -> Playlist {
        let playlist = Playlist(name: name, items: items)
        playlists.append(playlist)
        savePlaylist(playlist)
        return playlist
    }
    
    func deletePlaylist(_ playlist: Playlist) {
        if let index = playlists.firstIndex(where: { $0.id == playlist.id }) {
            playlists.remove(at: index)
            deletePlaylistFile(playlist)
            
            if currentPlaylist?.id == playlist.id {
                currentPlaylist = nil
            }
        }
    }
    
    func renamePlaylist(_ playlist: Playlist, to newName: String) {
        if let index = playlists.firstIndex(where: { $0.id == playlist.id }) {
            let oldFilename = playlist.filename
            playlists[index].name = newName
            
            // 重新命名檔案
            let oldURL = playlistsURL.appendingPathComponent(oldFilename)
            let newURL = playlistsURL.appendingPathComponent(playlists[index].filename)
            
            try? FileManager.default.moveItem(at: oldURL, to: newURL)
            savePlaylist(playlists[index])
        }
    }
    
    func duplicatePlaylist(_ playlist: Playlist) -> Playlist {
        let duplicatedPlaylist = Playlist(
            name: "\(playlist.name) 副本",
            items: playlist.items
        )
        playlists.append(duplicatedPlaylist)
        savePlaylist(duplicatedPlaylist)
        return duplicatedPlaylist
    }
    
    // MARK: - 媒體項目操作
    func addItems(_ items: [MediaItem], to playlist: Playlist) {
        if let index = playlists.firstIndex(where: { $0.id == playlist.id }) {
            playlists[index].items.append(contentsOf: items)
            savePlaylist(playlists[index])
        }
    }
    
    func removeItem(at index: Int, from playlist: Playlist) {
        if let playlistIndex = playlists.firstIndex(where: { $0.id == playlist.id }),
           index < playlists[playlistIndex].items.count {
            playlists[playlistIndex].items.remove(at: index)
            savePlaylist(playlists[playlistIndex])
        }
    }
    
    func moveItem(from sourceIndex: Int, to destinationIndex: Int, in playlist: Playlist) {
        if let playlistIndex = playlists.firstIndex(where: { $0.id == playlist.id }) {
            let item = playlists[playlistIndex].items.remove(at: sourceIndex)
            playlists[playlistIndex].items.insert(item, at: destinationIndex)
            savePlaylist(playlists[playlistIndex])
        }
    }
    
    // MARK: - 智能播放清單
    func createSmartPlaylist(name: String, criteria: SmartPlaylistCriteria) -> SmartPlaylist {
        let smartPlaylist = SmartPlaylist(name: name, criteria: criteria)
        // TODO: 實現智能播放清單邏輯
        return smartPlaylist
    }
    
    // MARK: - 匯入/匯出
    func importPlaylist(from url: URL) throws -> Playlist {
        let data = try Data(contentsOf: url)
        let playlist = try JSONDecoder().decode(Playlist.self, from: data)
        playlists.append(playlist)
        savePlaylist(playlist)
        return playlist
    }
    
    func exportPlaylist(_ playlist: Playlist, to url: URL) throws {
        let data = try JSONEncoder().encode(playlist)
        try data.write(to: url)
    }
    
    // MARK: - 私有方法
    private func createPlaylistsDirectoryIfNeeded() {
        if !FileManager.default.fileExists(atPath: playlistsURL.path) {
            try? FileManager.default.createDirectory(at: playlistsURL, withIntermediateDirectories: true)
        }
    }
    
    private func loadPlaylists() {
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(at: playlistsURL, includingPropertiesForKeys: nil)
            let playlistFiles = fileURLs.filter { $0.pathExtension == "playlist" }
            
            for fileURL in playlistFiles {
                if let playlist = loadPlaylist(from: fileURL) {
                    playlists.append(playlist)
                }
            }
        } catch {
            print("載入播放清單失敗: \(error)")
        }
    }
    
    private func loadPlaylist(from url: URL) -> Playlist? {
        do {
            let data = try Data(contentsOf: url)
            return try JSONDecoder().decode(Playlist.self, from: data)
        } catch {
            print("載入播放清單失敗: \(error)")
            return nil
        }
    }
    
    private func savePlaylist(_ playlist: Playlist) {
        do {
            let data = try JSONEncoder().encode(playlist)
            let url = playlistsURL.appendingPathComponent(playlist.filename)
            try data.write(to: url)
        } catch {
            print("儲存播放清單失敗: \(error)")
        }
    }
    
    private func deletePlaylistFile(_ playlist: Playlist) {
        let url = playlistsURL.appendingPathComponent(playlist.filename)
        try? FileManager.default.removeItem(at: url)
    }
}

// MARK: - 播放清單模型
struct Playlist: Identifiable, Codable, Hashable {
    var id = UUID()
    var name: String
    var items: [MediaItem]
    var createdAt: Date
    var modifiedAt: Date
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: Playlist, rhs: Playlist) -> Bool {
        return lhs.id == rhs.id
    }
    
    init(name: String, items: [MediaItem] = []) {
        self.name = name
        self.items = items
        self.createdAt = Date()
        self.modifiedAt = Date()
    }
    
    var filename: String {
        return "\(name.replacingOccurrences(of: " ", with: "_")).playlist"
    }
    
    var duration: TimeInterval {
        return items.reduce(0) { $0 + $1.duration }
    }
    
    var itemCount: Int {
        return items.count
    }
}

// MARK: - 媒體項目模型
struct MediaItem: Identifiable, Codable, Hashable {
    var id = UUID()
    var url: URL
    var title: String
    var artist: String?
    var album: String?
    var duration: TimeInterval
    var fileSize: Int64
    var format: String
    var addedAt: Date
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: MediaItem, rhs: MediaItem) -> Bool {
        return lhs.id == rhs.id
    }
    
    init(url: URL) {
        self.url = url
        self.title = url.deletingPathExtension().lastPathComponent
        self.artist = nil
        self.album = nil
        self.duration = 0 // TODO: 從媒體檔案讀取實際時長
        self.fileSize = MediaFileHelper.getFileSize(url: url)
        self.format = MediaFileHelper.getFileFormat(from: url)
        self.addedAt = Date()
    }
    
    var isVideo: Bool {
        return MediaFileHelper.isVideoFile(url: url)
    }
}

// MARK: - 智能播放清單
struct SmartPlaylist: Identifiable, Codable {
    var id = UUID()
    var name: String
    var criteria: SmartPlaylistCriteria
    var createdAt: Date
    var modifiedAt: Date
    
    init(name: String, criteria: SmartPlaylistCriteria) {
        self.name = name
        self.criteria = criteria
        self.createdAt = Date()
        self.modifiedAt = Date()
    }
}

// MARK: - 智能播放清單條件
struct SmartPlaylistCriteria: Codable {
    var rules: [SmartPlaylistRule]
    var matchType: MatchType
    var sortBy: SortOption
    var sortOrder: SortOrder
    var limit: Int?
    
    enum MatchType: String, Codable, CaseIterable {
        case all = "所有條件"
        case any = "任一條件"
    }
    
    enum SortOption: String, Codable, CaseIterable {
        case name = "名稱"
        case dateAdded = "加入日期"
        case duration = "時長"
        case fileSize = "檔案大小"
        case format = "格式"
    }
    
    enum SortOrder: String, Codable, CaseIterable {
        case ascending = "升序"
        case descending = "降序"
    }
}

// MARK: - 智能播放清單規則
struct SmartPlaylistRule: Identifiable, Codable {
    var id = UUID()
    var field: Field
    var condition: Condition
    var value: String
    
    enum Field: String, Codable, CaseIterable {
        case title = "標題"
        case artist = "藝術家"
        case album = "專輯"
        case format = "格式"
        case duration = "時長"
        case fileSize = "檔案大小"
        case dateAdded = "加入日期"
    }
    
    enum Condition: String, Codable, CaseIterable {
        case contains = "包含"
        case notContains = "不包含"
        case equals = "等於"
        case notEquals = "不等於"
        case startsWith = "開始於"
        case endsWith = "結束於"
        case greaterThan = "大於"
        case lessThan = "小於"
    }
}

// MARK: - 播放清單視圖
struct EnhancedPlaylistsView: View {
    @StateObject private var playlistManager = PlaylistManager.shared
    @State private var isShowingCreatePlaylist = false
    @State private var isShowingImportPlaylist = false
    @State private var selectedPlaylist: Playlist?
    @State private var searchText = ""
    
    var filteredPlaylists: [Playlist] {
        if searchText.isEmpty {
            return playlistManager.playlists
        } else {
            return playlistManager.playlists.filter { playlist in
                playlist.name.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
    
    var body: some View {
        NavigationSplitView {
            // 播放清單列表
            VStack(spacing: 0) {
                // 頂部工具列
                HStack {
                    Text("播放清單")
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(AppTheme.primaryText)
                    
                    Spacer()
                    
                    HStack(spacing: 8) {
                        Button(action: { isShowingCreatePlaylist = true }) {
                            Image(systemName: "plus")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .buttonStyle(ToolbarButtonStyle())
                        
                        Button(action: { isShowingImportPlaylist = true }) {
                            Image(systemName: "square.and.arrow.down")
                                .font(.system(size: 16, weight: .medium))
                        }
                        .buttonStyle(ToolbarButtonStyle())
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                
                // 搜尋欄
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(AppTheme.tertiaryText)
                    
                    TextField("搜尋播放清單...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .foregroundColor(AppTheme.primaryText)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(AppTheme.surfaceColor)
                .cornerRadius(10)
                .padding(.horizontal, 16)
                .padding(.bottom, 16)
                
                Divider()
                    .background(Color.white.opacity(0.1))
                
                // 播放清單列表
                if filteredPlaylists.isEmpty {
                    EmptyPlaylistsView {
                        isShowingCreatePlaylist = true
                    }
                } else {
                    List(filteredPlaylists, selection: $selectedPlaylist) { playlist in
                        PlaylistRowView(playlist: playlist) {
                            selectedPlaylist = playlist
                        }
                        .listRowBackground(Color.clear)
                        .listRowSeparator(.hidden)
                    }
                    .listStyle(PlainListStyle())
                    .background(AppTheme.backgroundColor)
                }
                
                Spacer()
            }
            .frame(minWidth: 300)
            .background(AppTheme.backgroundColor)
            
        } detail: {
            // 播放清單詳細視圖
            if let playlist = selectedPlaylist {
                PlaylistDetailView(playlist: playlist)
            } else {
                EmptyPlaylistDetailView()
            }
        }
        .sheet(isPresented: $isShowingCreatePlaylist) {
            CreatePlaylistView()
        }
        .fileImporter(
            isPresented: $isShowingImportPlaylist,
            allowedContentTypes: [UTType(filenameExtension: "playlist")!],
            allowsMultipleSelection: false
        ) { result in
            switch result {
            case .success(let urls):
                if let url = urls.first {
                    do {
                        let playlist = try playlistManager.importPlaylist(from: url)
                        selectedPlaylist = playlist
                    } catch {
                        print("匯入播放清單失敗: \(error)")
                    }
                }
            case .failure(let error):
                print("選擇檔案失敗: \(error)")
            }
        }
    }
}

// MARK: - 工具列按鈕樣式
struct ToolbarButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .foregroundColor(AppTheme.secondaryText)
            .frame(width: 32, height: 32)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(configuration.isPressed ? Color.white.opacity(0.1) : Color.clear)
            )
            .scaleEffect(configuration.isPressed ? 0.9 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - 播放清單行視圖
struct PlaylistRowView: View {
    let playlist: Playlist
    let action: () -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                // 播放清單圖示
                ZStack {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(
                            LinearGradient(
                                colors: [AppTheme.primaryColor.opacity(0.3), AppTheme.secondaryColor.opacity(0.3)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: "music.note.list")
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(.white)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(playlist.name)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(AppTheme.primaryText)
                        .lineLimit(1)
                    
                    Text("\(playlist.itemCount) 項目 • \(formatDuration(playlist.duration))")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppTheme.secondaryText)
                }
                
                Spacer()
                
                if isHovered {
                    HStack(spacing: 8) {
                        Button(action: {
                            // 播放播放清單
                        }) {
                            Image(systemName: "play.circle.fill")
                                .font(.system(size: 20))
                                .foregroundColor(AppTheme.primaryColor)
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        Menu {
                            Button("重新命名") { /* TODO */ }
                            Button("複製") { /* TODO */ }
                            Button("匯出") { /* TODO */ }
                            Divider()
                            Button("刪除", role: .destructive) { /* TODO */ }
                        } label: {
                            Image(systemName: "ellipsis")
                                .font(.system(size: 16))
                                .foregroundColor(AppTheme.secondaryText)
                        }
                    }
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isHovered ? Color.white.opacity(0.05) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            isHovered = hovering
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = Int(duration) / 60 % 60
        
        if hours > 0 {
            return "\(hours) 小時 \(minutes) 分鐘"
        } else {
            return "\(minutes) 分鐘"
        }
    }
}

// MARK: - 空播放清單視圖
struct EmptyPlaylistsView: View {
    let action: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "music.note.list")
                .font(.system(size: 64, weight: .ultraLight))
                .foregroundColor(AppTheme.tertiaryText)
            
            VStack(spacing: 8) {
                Text("沒有播放清單")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(AppTheme.primaryText)
                
                Text("建立您的第一個播放清單來組織您的媒體")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppTheme.secondaryText)
                    .multilineTextAlignment(.center)
            }
            
            Button("建立播放清單") {
                action()
            }
            .buttonStyle(ModernButtonStyle(color: AppTheme.primaryColor, size: .medium))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.backgroundColor)
    }
}

// MARK: - 播放清單詳細視圖
struct PlaylistDetailView: View {
    let playlist: Playlist
    @State private var isShowingFileImporter = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 標題區域
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text(playlist.name)
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(AppTheme.primaryText)
                    
                    Text("\(playlist.itemCount) 項目 • 建立於 \(formatDate(playlist.createdAt))")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppTheme.secondaryText)
                }
                
                Spacer()
                
                HStack(spacing: 12) {
                    Button("播放全部") {
                        // TODO: 播放整個播放清單
                    }
                    .buttonStyle(ModernButtonStyle(color: AppTheme.primaryColor, size: .medium))
                    
                    Button("隨機播放") {
                        // TODO: 隨機播放
                    }
                    .buttonStyle(ModernButtonStyle(color: AppTheme.accentColor, size: .medium))
                    
                    Button("添加媒體") {
                        isShowingFileImporter = true
                    }
                    .buttonStyle(ModernButtonStyle(color: .gray, size: .medium))
                }
            }
            .padding(.horizontal, 32)
            .padding(.vertical, 24)
            
            Divider()
                .background(Color.white.opacity(0.1))
            
            // 項目列表
            if playlist.items.isEmpty {
                EmptyPlaylistDetailView {
                    isShowingFileImporter = true
                }
            } else {
                List {
                    ForEach(Array(playlist.items.enumerated()), id: \.element.id) { index, item in
                        PlaylistItemRowView(item: item, index: index)
                            .listRowBackground(Color.clear)
                            .listRowSeparator(.hidden)
                    }
                }
                .listStyle(PlainListStyle())
                .background(AppTheme.backgroundColor)
            }
        }
        .background(AppTheme.backgroundColor)
        .fileImporter(
            isPresented: $isShowingFileImporter,
            allowedContentTypes: PlayerManager.supportedTypes,
            allowsMultipleSelection: true
        ) { result in
            switch result {
            case .success(let urls):
                let mediaItems = urls.map { MediaItem(url: $0) }
                PlaylistManager.shared.addItems(mediaItems, to: playlist)
            case .failure(let error):
                print("選擇檔案失敗: \(error)")
            }
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
}

// MARK: - 空播放清單詳細視圖
struct EmptyPlaylistDetailView: View {
    let action: (() -> Void)?
    
    init(action: (() -> Void)? = nil) {
        self.action = action
    }
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "music.note")
                .font(.system(size: 64, weight: .ultraLight))
                .foregroundColor(AppTheme.tertiaryText)
            
            VStack(spacing: 8) {
                Text("播放清單是空的")
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(AppTheme.primaryText)
                
                Text("添加一些媒體檔案開始建立您的播放清單")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppTheme.secondaryText)
                    .multilineTextAlignment(.center)
            }
            
            if let action = action {
                Button("添加媒體") {
                    action()
                }
                .buttonStyle(ModernButtonStyle(color: AppTheme.primaryColor, size: .medium))
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.backgroundColor)
    }
}

// MARK: - 播放清單項目行視圖
struct PlaylistItemRowView: View {
    let item: MediaItem
    let index: Int
    
    @State private var isHovered = false
    
    var body: some View {
        HStack(spacing: 16) {
            // 序號
            Text("\(index + 1)")
                .font(.system(size: 14, weight: .medium, design: .monospaced))
                .foregroundColor(AppTheme.tertiaryText)
                .frame(width: 30, alignment: .trailing)
            
            // 媒體類型圖示
            Image(systemName: item.isVideo ? "film.fill" : "music.note")
                .font(.system(size: 16))
                .foregroundColor(AppTheme.primaryColor)
                .frame(width: 20)
            
            // 項目資訊
            VStack(alignment: .leading, spacing: 2) {
                Text(item.title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppTheme.primaryText)
                    .lineLimit(1)
                
                HStack(spacing: 8) {
                    Text(item.format)
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(AppTheme.tertiaryText)
                    
                    if item.duration > 0 {
                        Text("•")
                            .foregroundColor(AppTheme.tertiaryText)
                        
                        Text(formatDuration(item.duration))
                            .font(.system(size: 11, weight: .medium))
                            .foregroundColor(AppTheme.tertiaryText)
                    }
                }
            }
            
            Spacer()
            
            // 操作按鈕
            if isHovered {
                HStack(spacing: 8) {
                    Button(action: {
                        // TODO: 播放此項目
                    }) {
                        Image(systemName: "play.fill")
                            .font(.system(size: 12))
                            .foregroundColor(AppTheme.primaryColor)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Button(action: {
                        // TODO: 從播放清單移除
                    }) {
                        Image(systemName: "minus.circle")
                            .font(.system(size: 12))
                            .foregroundColor(.red)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(.horizontal, 32)
        .padding(.vertical, 8)
        .background(
            Rectangle()
                .fill(isHovered ? Color.white.opacity(0.03) : Color.clear)
        )
        .onHover { hovering in
            isHovered = hovering
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
}

// MARK: - 建立播放清單視圖
struct CreatePlaylistView: View {
    @State private var playlistName = ""
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        VStack(spacing: 24) {
            Text("建立新播放清單")
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(AppTheme.primaryText)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("播放清單名稱")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppTheme.secondaryText)
                
                TextField("輸入播放清單名稱", text: $playlistName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            HStack(spacing: 12) {
                Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                }
                .buttonStyle(ModernButtonStyle(color: .gray, size: .medium))
                
                Button("建立") {
                    if !playlistName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                        _ = PlaylistManager.shared.createPlaylist(name: playlistName)
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                .buttonStyle(ModernButtonStyle(color: AppTheme.primaryColor, size: .medium))
                .disabled(playlistName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }
        }
        .padding(32)
        .frame(width: 400)
        .background(AppTheme.backgroundColor)
    }
}