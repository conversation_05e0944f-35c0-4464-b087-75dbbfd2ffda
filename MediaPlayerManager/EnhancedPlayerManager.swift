import SwiftUI
import AVFoundation
import UniformTypeIdentifiers
import Combine

// MARK: - Enhanced Player Manager for Better File Support
class EnhancedPlayerManager: NSObject, ObservableObject {
    @Published var player: AVPlayer?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var currentTime: Double = 0
    @Published var duration: Double = 0
    @Published var isPlaying = false
    @Published var volume: Float = 1.0
    @Published var playbackRate: Float = 1.0
    
    // UI Control properties for compatibility with PlayerManager
    @Published var showControls = true
    @Published var isMuted = false
    
    // Static properties for compatibility with PlayerManager
    static let supportedTypes: [UTType] = [
        // 系統內建視頻類型
        .movie, .video, .mpeg4Movie, .mpeg, .mpeg2Video, .appleProtectedMPEG4Video,
        
        // 音頻類型
        .audio, .mp3, .wav, .aiff,
        
        // 常見視頻格式
        UTType(filenameExtension: "mp4") ?? .movie,
        UTType(filenameExtension: "m4v") ?? .movie,
        UTType(filenameExtension: "mov") ?? .movie,
        UTType(filenameExtension: "qt") ?? .movie,
        UTType(filenameExtension: "avi") ?? .movie,
        UTType(filenameExtension: "mkv") ?? .movie,
        UTType(filenameExtension: "webm") ?? .movie,
        UTType(filenameExtension: "flv") ?? .movie,
        UTType(filenameExtension: "wmv") ?? .movie,
        UTType(filenameExtension: "asf") ?? .movie,
        UTType(filenameExtension: "mts") ?? .movie,
        UTType(filenameExtension: "m2ts") ?? .movie,
        UTType(filenameExtension: "ts") ?? .movie,
        UTType(filenameExtension: "vob") ?? .movie,
        UTType(filenameExtension: "ogv") ?? .movie,
        UTType(filenameExtension: "ogg") ?? .movie,
        UTType(filenameExtension: "dv") ?? .movie,
        UTType(filenameExtension: "3gp") ?? .movie,
        UTType(filenameExtension: "3g2") ?? .movie,
        UTType(filenameExtension: "mxf") ?? .movie,
        UTType(filenameExtension: "r3d") ?? .movie,
        UTType(filenameExtension: "rm") ?? .movie,
        UTType(filenameExtension: "rmvb") ?? .movie,
        UTType(filenameExtension: "f4v") ?? .movie,
        UTType(filenameExtension: "swf") ?? .movie,
        UTType(filenameExtension: "divx") ?? .movie,
        UTType(filenameExtension: "xvid") ?? .movie,
        
        // 常見音頻格式
        UTType(filenameExtension: "m4a") ?? .audio,
        UTType(filenameExtension: "aac") ?? .audio,
        UTType(filenameExtension: "flac") ?? .audio,
        UTType(filenameExtension: "au") ?? .audio,
        UTType(filenameExtension: "m4b") ?? .audio,
        UTType(filenameExtension: "m4p") ?? .audio,
        UTType(filenameExtension: "m4r") ?? .audio,
        UTType(filenameExtension: "alac") ?? .audio,
        UTType(filenameExtension: "ape") ?? .audio,
        UTType(filenameExtension: "wv") ?? .audio,
        UTType(filenameExtension: "tta") ?? .audio,
        UTType(filenameExtension: "dts") ?? .audio,
        UTType(filenameExtension: "ac3") ?? .audio,
        UTType(filenameExtension: "eac3") ?? .audio,
        UTType(filenameExtension: "oga") ?? .audio,
        UTType(filenameExtension: "opus") ?? .audio,
        UTType(filenameExtension: "wma") ?? .audio,
        UTType(filenameExtension: "ra") ?? .audio,
        UTType(filenameExtension: "amr") ?? .audio,
        UTType(filenameExtension: "3ga") ?? .audio,
        UTType(filenameExtension: "caf") ?? .audio,
        UTType(filenameExtension: "sd2") ?? .audio
    ]
    
    static let playbackSpeeds: [Float] = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 2.0, 4.0, 8.0, 16.0]
    
    private var statusObserver: AnyCancellable?
    private var timeObserver: Any?
    private var completionObserver: NSObjectProtocol?
    
    var onPlaybackCompleted: (() -> Void)?
    
    override init() {
        super.init()
        setupVolumeBinding()
    }
    
    // MARK: - Enhanced Media Loading
    func loadMedia(from url: URL, completion: @escaping (Bool) -> Void) {
        print("🎥 載入媒體: \(url.lastPathComponent)")
        
        isLoading = true
        errorMessage = nil
        
        // Step 1: Basic file existence check
        guard FileManager.default.fileExists(atPath: url.path) else {
            handleLoadError("檔案不存在", completion: completion)
            return
        }
        
        // Step 2: Security scoped resource access
        let didStartAccessing = url.startAccessingSecurityScopedResource()
        
        // Step 3: 快速檔案檢查
        if let attributes = try? FileManager.default.attributesOfItem(atPath: url.path),
           let fileSize = attributes[.size] as? Int64, fileSize == 0 {
            if didStartAccessing { url.stopAccessingSecurityScopedResource() }
            handleLoadError("檔案為空", completion: completion)
            return
        }
        
        // Step 4: Create AVAsset with enhanced options
        let asset = createEnhancedAVAsset(from: url)
        
        // Step 5: Load media properties asynchronously
        Task { [weak self] in
            await self?.loadAssetProperties(asset: asset, url: url, didStartAccessing: didStartAccessing, completion: completion)
        }
    }
    
    // MARK: - Enhanced AVAsset Creation
    private func createEnhancedAVAsset(from url: URL) -> AVURLAsset {
        // 簡化配置以提高速度
        let options: [String: Any] = [
            AVURLAssetPreferPreciseDurationAndTimingKey: false  // 改為 false 提高速度
        ]
        
        return AVURLAsset(url: url, options: options)
    }
    
    // MARK: - Enhanced Asset Loading
    private func loadAssetProperties(asset: AVURLAsset, url: URL, didStartAccessing: Bool, completion: @escaping (Bool) -> Void) async {
        do {
            print("🔍 [Enhanced] 開始載入資產屬性...")
            
            // Load basic properties
            let isPlayable = try await asset.load(.isPlayable)
            let duration = try await asset.load(.duration)
            let tracks = try await asset.load(.tracks)
            
            print("🔍 [Enhanced] 基本屬性:")
            print("  - 可播放: \(isPlayable)")
            print("  - 時長: \(CMTimeGetSeconds(duration))秒")
            print("  - 軌道數: \(tracks.count)")
            
            // Load detailed track information
            for (index, track) in tracks.enumerated() {
                let mediaType = track.mediaType
                let isEnabled = try await track.load(.isEnabled)
                let isPlayable = try await track.load(.isPlayable)
                
                print("  - 軌道 \(index): \(mediaType.rawValue), 啟用: \(isEnabled), 可播放: \(isPlayable)")
            }
            
            await MainActor.run { [weak self] in
                if didStartAccessing {
                    url.stopAccessingSecurityScopedResource()
                    print("🔐 [Enhanced] 安全存取已停止")
                }
                
                if isPlayable && !tracks.isEmpty {
                    print("✅ [Enhanced] 媒體載入成功")
                    self?.setupEnhancedPlayer(with: asset, completion: completion)
                } else {
                    let reason = !isPlayable ? "格式不支援" : "沒有可播放軌道"
                    let errorMsg = "無法播放媒體檔案：\(reason)"
                    print("❌ [Enhanced] \(errorMsg)")
                    self?.handleLoadError(errorMsg, completion: completion)
                }
            }
            
        } catch {
            await MainActor.run { [weak self] in
                if didStartAccessing {
                    url.stopAccessingSecurityScopedResource()
                }
                
                let errorMsg = "載入媒體時發生錯誤：\(error.localizedDescription)"
                print("❌ [Enhanced] \(errorMsg)")
                print("❌ [Enhanced] 錯誤詳情: \(error)")
                self?.handleLoadError(errorMsg, completion: completion)
            }
        }
    }
    
    // MARK: - Enhanced Player Setup
    private func setupEnhancedPlayer(with asset: AVURLAsset, completion: @escaping (Bool) -> Void) {
        let playerItem = AVPlayerItem(asset: asset)
        
        // 優化的播放器配置
        playerItem.preferredForwardBufferDuration = 3.0  // 減少緩衝時間
        
        // Remove old observers
        removeObservers()
        
        // Setup or replace player
        if let existingPlayer = self.player {
            existingPlayer.replaceCurrentItem(with: playerItem)
        } else {
            self.player = AVPlayer(playerItem: playerItem)
            self.player?.automaticallyWaitsToMinimizeStalling = false
        }
        
        // Enhanced player configuration
        self.player?.actionAtItemEnd = .pause
        self.player?.volume = volume
        self.player?.rate = playbackRate
        
        // Monitor player item status
        monitorEnhancedPlayerItemStatus(playerItem)
        
        // Setup observers
        setupEnhancedObservers(for: playerItem)
        
        // 立即開始播放
        if let player = self.player {
            print("🎬 開始播放")
            player.play()
            self.isPlaying = true
        }
        
        completion(true)
    }
    
    // MARK: - Enhanced Player Item Monitoring
    private func monitorEnhancedPlayerItemStatus(_ playerItem: AVPlayerItem) {
        Task { [weak self] in
            var retryCount = 0
            let maxRetries = 10
            
            // Wait for player item to be ready with timeout
            while playerItem.status == .unknown && retryCount < maxRetries {
                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
                retryCount += 1
                print("⏳ [Enhanced] 等待播放器就緒... (\(retryCount)/\(maxRetries))")
            }
            
            await MainActor.run { [weak self] in
                self?.isLoading = false
                
                switch playerItem.status {
                case .readyToPlay:
                    print("✅ [Enhanced] 播放器已就緒")
                    self?.errorMessage = nil
                    
                case .failed:
                    if let error = playerItem.error {
                        let errorMsg = "播放器錯誤：\(error.localizedDescription)"
                        print("❌ [Enhanced] \(errorMsg)")
                        print("❌ [Enhanced] 錯誤代碼: \((error as NSError).code)")
                        print("❌ [Enhanced] 錯誤域: \((error as NSError).domain)")
                        self?.errorMessage = errorMsg
                    }
                    
                case .unknown:
                    let errorMsg = "播放器初始化超時"
                    print("⚠️ [Enhanced] \(errorMsg)")
                    self?.errorMessage = errorMsg
                    
                @unknown default:
                    print("⚠️ [Enhanced] 未知播放器狀態")
                }
            }
        }
    }
    
    // MARK: - Enhanced Observers Setup
    private func setupEnhancedObservers(for playerItem: AVPlayerItem) {
        guard let player = player else { return }
        
        print("👀 [Enhanced] 設置觀察者...")
        
        // Status observer
        statusObserver = player.publisher(for: \.timeControlStatus)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] status in
                switch status {
                case .playing:
                    self?.isPlaying = true
                    print("▶️ [Enhanced] 開始播放")
                case .paused:
                    self?.isPlaying = false
                    print("⏸️ [Enhanced] 暫停播放")
                case .waitingToPlayAtSpecifiedRate:
                    print("⏳ [Enhanced] 等待播放...")
                @unknown default:
                    print("❓ [Enhanced] 未知播放狀態")
                }
            }
        
        // Time observer
        let interval = CMTime(seconds: 0.5, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        timeObserver = player.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            self?.currentTime = CMTimeGetSeconds(time)
            
            if let duration = self?.player?.currentItem?.duration {
                let durationSeconds = CMTimeGetSeconds(duration)
                self?.duration = durationSeconds.isNaN ? 0 : durationSeconds
            }
        }
        
        // Completion observer
        completionObserver = NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: playerItem,
            queue: .main
        ) { [weak self] _ in
            print("🏁 [Enhanced] 播放完成")
            self?.onPlaybackCompleted?()
        }
        
        // Error observer
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemFailedToPlayToEndTime,
            object: playerItem,
            queue: .main
        ) { [weak self] notification in
            if let error = notification.userInfo?[AVPlayerItemFailedToPlayToEndTimeErrorKey] as? Error {
                let errorMsg = "播放中斷：\(error.localizedDescription)"
                print("❌ [Enhanced] \(errorMsg)")
                self?.errorMessage = errorMsg
            }
        }
        
        print("✅ [Enhanced] 觀察者設置完成")
    }
    
    // MARK: - Volume Binding
    private func setupVolumeBinding() {
        $volume
            .sink { [weak self] newVolume in
                self?.player?.volume = newVolume
            }
            .store(in: &cancellables)
    }
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Error Handling
    private func handleLoadError(_ message: String, completion: @escaping (Bool) -> Void) {
        print("❌ [Enhanced] 錯誤: \(message)")
        
        DispatchQueue.main.async { [weak self] in
            self?.isLoading = false
            self?.errorMessage = message
            completion(false)
        }
    }
    
    // MARK: - Cleanup
    private func removeObservers() {
        statusObserver?.cancel()
        
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
            self.timeObserver = nil
        }
        
        if let completionObserver = completionObserver {
            NotificationCenter.default.removeObserver(completionObserver)
            self.completionObserver = nil
        }
        
        NotificationCenter.default.removeObserver(self)
    }
    
    deinit {
        removeObservers()
        print("🗑️ [Enhanced] Enhanced Player Manager 已釋放")
    }
    
    // MARK: - Playback Controls
    func play() {
        player?.play()
    }
    
    func pause() {
        player?.pause()
    }
    
    func togglePlayPause() {
        if isPlaying {
            pause()
        } else {
            play()
        }
    }
    
    func seek(to time: Double) {
        let cmTime = CMTime(seconds: time, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        player?.seek(to: cmTime)
    }
    
    func setPlaybackRate(_ rate: Float) {
        playbackRate = rate
        player?.rate = rate
    }
    
    // MARK: - UI Control Methods (for compatibility with PlayerManager)
    
    func toggleMute() {
        isMuted.toggle()
        if isMuted {
            player?.volume = 0
        } else {
            player?.volume = volume
        }
    }
    
    func setVolume(_ newVolume: Float) {
        volume = newVolume
        if !isMuted {
            player?.volume = newVolume
        }
    }
    
    func userInteracted() {
        showControls = true
        // Could add logic to hide controls after delay if needed
    }
    
    func cleanup() {
        removeObservers()
        player?.pause()
        player = nil
    }
}

// MARK: - Enhanced Media File Validator
struct EnhancedMediaValidator {
    static func validateMediaFile(at url: URL) -> (isValid: Bool, issues: [String]) {
        var issues: [String] = []
        
        // Check file existence
        guard FileManager.default.fileExists(atPath: url.path) else {
            issues.append("檔案不存在")
            return (false, issues)
        }
        
        // Check file size
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
            let fileSize = attributes[.size] as? Int64 ?? 0
            
            if fileSize == 0 {
                issues.append("檔案為空")
            } else if fileSize < 1024 { // Less than 1KB
                issues.append("檔案太小，可能已損壞")
            }
        } catch {
            issues.append("無法讀取檔案屬性：\(error.localizedDescription)")
        }
        
        // Check file permissions
        guard FileManager.default.isReadableFile(atPath: url.path) else {
            issues.append("檔案無讀取權限")
            return (false, issues)
        }
        
        // Check file extension
        let supportedExtensions = [
            "mp4", "m4v", "mov", "qt", "avi", "mkv", "webm", "flv", "wmv",
            "mp3", "m4a", "aac", "wav", "flac", "aiff"
        ]
        
        let fileExtension = url.pathExtension.lowercased()
        if !supportedExtensions.contains(fileExtension) {
            issues.append("不支援的檔案格式：.\(fileExtension)")
        }
        
        // Check for problematic characters in path
        let problematicChars = CharacterSet(charactersIn: "\"<>|*?")
        if url.path.rangeOfCharacter(from: problematicChars) != nil {
            issues.append("檔案路徑包含特殊字符，可能影響播放")
        }
        
        return (issues.isEmpty, issues)
    }
}