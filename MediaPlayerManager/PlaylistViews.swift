//
//  PlaylistViews.swift
//  MediaPlayerManager
//
//  Refactored from ContentView.swift to reduce file size
//

import SwiftUI

// MARK: - Playlist View
struct PlaylistView: View {
    @ObservedObject var playerWindow: PlayerWindow
    @State private var isShowingFileImporter = false
    @State private var hoveredIndex: Int?
    @State private var draggedItem: URL?
    
    var body: some View {
        VStack(spacing: 0) {
            // 緊湊標題欄
            HStack {
                VStack(alignment: .leading, spacing: 1) {
                    Text("播放清單")
                        .font(.system(size: 16, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                    
                    Text("\(playerWindow.playlist.count) 項")
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
                
                HStack(spacing: 6) {
                    // 批量操作按鈕
                    if !playerWindow.playlist.isEmpty {
                        // 清除清單按鈕
                        Button(action: {
                            playerWindow.clearPlaylist()
                        }) {
                            Image(systemName: "trash.circle.fill")
                                .font(.system(size: 16))
                                .foregroundColor(.red.opacity(0.8))
                        }
                        .buttonStyle(PlainButtonStyle())
                        .help("清空播放清單")
                        
                        // 隨機播放按鈕
                        Button(action: {
                            shufflePlaylist()
                        }) {
                            Image(systemName: "shuffle.circle.fill")
                                .font(.system(size: 16))
                                .foregroundColor(.blue.opacity(0.8))
                        }
                        .buttonStyle(PlainButtonStyle())
                        .help("隨機排列")
                    }
                    
                    HStack(spacing: 4) {
                        // 測試按鈕 - 直接添加測試檔案
                        Button(action: {
                            print("🧪 測試：直接添加測試檔案")
                            // 創建一個測試檔案URL
                            if let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first {
                                let testURL = documentsPath.appendingPathComponent("test.mp4")
                                print("🧪 測試檔案路徑: \(testURL.path)")
                                playerWindow.addToPlaylist(url: testURL)
                            }
                        }) {
                            Image(systemName: "wrench.and.screwdriver")
                                .font(.system(size: 16))
                                .foregroundColor(.orange)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .help("測試添加功能")
                        
                        // 添加媒體按鈕
                        Button(action: {
                            print("🎵 播放清單：點擊添加媒體按鈕")
                            showFileImporterForPlaylist()
                        }) {
                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 16))
                                .foregroundColor(AppTheme.primaryColor)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .help("添加媒體檔案")
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                LinearGradient(
                    colors: [AppTheme.cardColor, AppTheme.surfaceColor],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            
            Divider()
                .background(Color.white.opacity(0.1))
            
            // 播放清單內容
            if playerWindow.playlist.isEmpty {
                // 簡化空狀態
                VStack(spacing: 16) {
                    Image(systemName: "music.note.list")
                        .font(.system(size: 24, weight: .light))
                        .foregroundColor(AppTheme.primaryColor.opacity(0.7))
                    
                    VStack(spacing: 4) {
                        Text("清單是空的")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white.opacity(0.8))
                        
                        Text("點擊 ＋ 添加檔案")
                            .font(.system(size: 11))
                            .foregroundColor(.white.opacity(0.5))
                    }
                    
                    Button(action: {
                        isShowingFileImporter = true
                    }) {
                        HStack(spacing: 8) {
                            Image(systemName: "plus.circle")
                                .font(.system(size: 14, weight: .medium))
                            
                            Text("添加媒體")
                                .font(.system(size: 14, weight: .medium))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(
                                    LinearGradient(
                                        colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    )
                                )
                                .shadow(color: AppTheme.primaryColor.opacity(0.3), radius: 8, x: 0, y: 4)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // 美化的播放清單
                ScrollView {
                    LazyVStack(spacing: 1) {
                        ForEach(Array(playerWindow.playlist.enumerated()), id: \.element.absoluteString) { index, url in
                            PlaylistItemView(
                                url: url,
                                index: index,
                                isCurrentItem: index == playerWindow.currentPlaylistIndex,
                                isHovered: hoveredIndex == index,
                                onPlay: {
                                    print("🎵 UI點擊：準備播放索引 \(index), 檔案: \(url.lastPathComponent)")
                                    playerWindow.playItemAt(index: index)
                                },
                                onRemove: {
                                    removeItem(at: index)
                                }
                            )
                            .onHover { isHovered in
                                hoveredIndex = isHovered ? index : nil
                            }
                        }
                    }
                    .padding(.vertical, 8)
                }
            }
        }
        .fileImporter(
            isPresented: $isShowingFileImporter,
            allowedContentTypes: PlayerManager.supportedTypes,
            allowsMultipleSelection: true
        ) { result in
            handleFileImportResult(result)
        }
    }
    
    // MARK: - Helper Methods
    private func showFileImporterForPlaylist() {
        isShowingFileImporter = true
    }
    
    private func handleFileImportResult(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            print("🎵 播放清單：成功選擇 \(urls.count) 個檔案")
            for url in urls {
                print("🎵 添加到播放清單: \(url.lastPathComponent)")
                playerWindow.addToPlaylist(url: url)
            }
        case .failure(let error):
            print("🎵 播放清單：檔案選擇失敗 - \(error.localizedDescription)")
        }
    }
    
    private func shufflePlaylist() {
        playerWindow.shufflePlaylist()
    }
    
    private func removeItem(at index: Int) {
        playerWindow.removeFromPlaylist(at: index)
    }
}

// MARK: - Playlist Item View
struct PlaylistItemView: View {
    let url: URL
    let index: Int
    let isCurrentItem: Bool
    let isHovered: Bool
    let onPlay: () -> Void
    let onRemove: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 播放狀態指示器
            ZStack {
                if isCurrentItem {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppTheme.primaryColor)
                } else {
                    Text("\(index + 1)")
                        .font(.system(size: 11, weight: .medium, design: .monospaced))
                        .foregroundColor(.white.opacity(0.6))
                }
            }
            .frame(width: 20, alignment: .center)
            
            // 檔案圖示
            Image(systemName: getFileIcon(for: url))
                .font(.system(size: 14))
                .foregroundColor(getFileColor(for: url))
                .frame(width: 16)
            
            // 檔案資訊
            VStack(alignment: .leading, spacing: 2) {
                Text(url.deletingPathExtension().lastPathComponent)
                    .font(.system(size: 13, weight: isCurrentItem ? .semibold : .medium))
                    .foregroundColor(isCurrentItem ? .white : .white.opacity(0.9))
                    .lineLimit(1)
                
                HStack(spacing: 6) {
                    Text(url.pathExtension.uppercased())
                        .font(.system(size: 10, weight: .medium, design: .monospaced))
                        .foregroundColor(.white.opacity(0.5))
                        .padding(.horizontal, 4)
                        .padding(.vertical, 1)
                        .background(Color.white.opacity(0.1))
                        .clipShape(Capsule())
                    
                    if let fileSize = getFileSize(url: url) {
                        Text(fileSize)
                            .font(.system(size: 10))
                            .foregroundColor(.white.opacity(0.4))
                    }
                }
            }
            
            Spacer()
            
            // 操作按鈕（懸停時顯示）
            if isHovered || isCurrentItem {
                HStack(spacing: 8) {
                    Button(action: onPlay) {
                        Image(systemName: "play.circle.fill")
                            .font(.system(size: 16))
                            .foregroundColor(AppTheme.primaryColor)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .help("播放")
                    
                    Button(action: onRemove) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 16))
                            .foregroundColor(.red.opacity(0.8))
                    }
                    .buttonStyle(PlainButtonStyle())
                    .help("移除")
                }
                .transition(.opacity.combined(with: .scale(scale: 0.9)))
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(
                    isCurrentItem ? 
                    AppTheme.primaryColor.opacity(0.2) : 
                    (isHovered ? Color.white.opacity(0.05) : Color.clear)
                )
                .animation(.easeInOut(duration: 0.2), value: isHovered)
        )
        .onTapGesture(count: 2) {
            onPlay()
        }
    }
    
    private func getFileIcon(for url: URL) -> String {
        let ext = url.pathExtension.lowercased()
        let videoExtensions = ["mp4", "mov", "avi", "mkv", "m4v", "wmv", "flv", "webm"]
        let audioExtensions = ["mp3", "m4a", "wav", "aac", "flac", "ogg"]
        
        if videoExtensions.contains(ext) {
            return "film"
        } else if audioExtensions.contains(ext) {
            return "music.note"
        } else {
            return "doc"
        }
    }
    
    private func getFileColor(for url: URL) -> Color {
        let ext = url.pathExtension.lowercased()
        let videoExtensions = ["mp4", "mov", "avi", "mkv", "m4v", "wmv", "flv", "webm"]
        let audioExtensions = ["mp3", "m4a", "wav", "aac", "flac", "ogg"]
        
        if videoExtensions.contains(ext) {
            return .blue.opacity(0.8)
        } else if audioExtensions.contains(ext) {
            return .green.opacity(0.8)
        } else {
            return .gray.opacity(0.8)
        }
    }
    
    private func getFileSize(url: URL) -> String? {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
            if let size = attributes[.size] as? Int64 {
                return ByteCountFormatter.string(fromByteCount: size, countStyle: .file)
            }
        } catch {
            // 無法獲取檔案大小
        }
        return nil
    }
}

// MARK: - Recent Files View
struct RecentFilesView: View {
    @ObservedObject var windowManager: WindowManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("最近播放")
                .font(.headline)
                .foregroundColor(.primary)
            
            if windowManager.recentFiles.isEmpty {
                Text("尚無最近播放記錄")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.vertical, 20)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(windowManager.recentFiles, id: \.absoluteString) { url in
                        RecentFileItemView(url: url) {
                            windowManager.createNewWindowWithMedia(url: url)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

// MARK: - Recent File Item View
struct RecentFileItemView: View {
    let url: URL
    let onPlay: () -> Void
    
    var body: some View {
        HStack {
            Image(systemName: getSystemImageName())
                .foregroundColor(.accentColor)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(url.lastPathComponent)
                    .lineLimit(1)
                    .font(.subheadline)
                
                Text(url.deletingLastPathComponent().path)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            Button("播放", action: onPlay)
                .buttonStyle(.borderedProminent)
                .controlSize(.small)
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(NSColor.controlColor))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    
    private func getSystemImageName() -> String {
        let ext = url.pathExtension.lowercased()
        let videoFormats = ["mp4", "mov", "avi", "mkv", "m4v", "wmv", "flv", "webm"]
        
        if videoFormats.contains(ext) {
            return "film"
        } else {
            return "music.note"
        }
    }
}

// MARK: - Extensions for PlayerWindow
extension PlayerWindow {
    var currentPlaylistIndex: Int {
        get { currentIndex }
    }
    
    func playItemAt(index: Int) {
        print("🎵 播放清單點擊：選擇索引 \(index)")
        guard index >= 0 && index < playlist.count else { 
            print("❌ 播放清單點擊：索引越界 \(index)/\(playlist.count)")
            return 
        }
        
        print("🎵 當前索引：\(currentIndex) → 新索引：\(index)")
        currentIndex = index
        let url = playlist[index]
        print("🎵 準備播放：\(url.lastPathComponent)")
        print("📊 播放器當前狀態 - isLoading: \(playerManager.isLoading), isPlaying: \(playerManager.isPlaying)")
        
        loadMedia(url: url)
    }
    
    func clearPlaylist() {
        playlist.removeAll()
        currentIndex = 0
    }
    
    func removeFromPlaylist(at index: Int) {
        guard index >= 0 && index < playlist.count else { return }
        playlist.remove(at: index)
        
        // 調整當前索引
        if currentIndex >= index && currentIndex > 0 {
            currentIndex -= 1
        }
        if playlist.isEmpty {
            currentIndex = 0
        }
    }
    
    func shufflePlaylist() {
        guard !playlist.isEmpty else { return }
        
        // 保存當前播放的檔案
        let currentURL = currentIndex < playlist.count ? playlist[currentIndex] : nil
        
        // 打亂清單
        playlist.shuffle()
        
        // 找到當前播放檔案的新位置
        if let currentURL = currentURL,
           let newIndex = playlist.firstIndex(of: currentURL) {
            currentIndex = newIndex
        } else {
            currentIndex = 0
        }
    }
}