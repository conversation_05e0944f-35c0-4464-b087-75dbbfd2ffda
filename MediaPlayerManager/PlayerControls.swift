//
//  PlayerControls.swift
//  MediaPlayerManager
//
//  Refactored from ContentView.swift to reduce file size
//

import SwiftUI

// MARK: - Player Controls
struct PlayerControls: View {
    @ObservedObject var playerManager: PlayerManager
    @ObservedObject var playerWindow: PlayerWindow
    @Binding var isShowingPlaylist: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            // 進度條
            HStack {
                Text(formatTime(playerManager.currentTime))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 60, alignment: .leading)
                
                Slider(
                    value: Binding(
                        get: { self.playerManager.currentTime },
                        set: { self.playerManager.seek(to: $0) }
                    ),
                    in: 0...(playerManager.duration > 0 ? playerManager.duration : 1)
                )
                .accentColor(.white)
                
                Text(formatTime(playerManager.duration))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 60, alignment: .trailing)
            }
            
            // 控制按鈕行 - 更小版本
            HStack(spacing: 12) {
                // 靜音按鈕
                Button(action: {
                    playerManager.toggleMute()
                }) {
                    Image(systemName: playerManager.isMuted ? "speaker.slash.fill" : "speaker.wave.2.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .frame(width: 30, height: 30)
                        .background(Color.black.opacity(0.4))
                        .clipShape(Circle())
                }
                
                // 上一個項目
                Button(action: {
                    playerWindow.playPrevious()
                }) {
                    Image(systemName: "backward.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .frame(width: 30, height: 30)
                        .background(Color.black.opacity(0.4))
                        .clipShape(Circle())
                }
                
                // 倒退10秒
                Button(action: {
                    let newTime = max(0, playerManager.currentTime - 10)
                    playerManager.seek(to: newTime)
                }) {
                    Image(systemName: "gobackward.10")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .frame(width: 30, height: 30)
                        .background(Color.black.opacity(0.4))
                        .clipShape(Circle())
                }
                
                // 播放/暫停 (保持稍大)
                Button(action: {
                    playerManager.togglePlayPause()
                }) {
                    Image(systemName: playerManager.isPlaying ? "pause.fill" : "play.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.white)
                        .frame(width: 38, height: 38)
                        .background(Color.blue)
                        .clipShape(Circle())
                        .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                }
                
                // 前進10秒
                Button(action: {
                    let newTime = min(playerManager.duration, playerManager.currentTime + 10)
                    playerManager.seek(to: newTime)
                }) {
                    Image(systemName: "goforward.10")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .frame(width: 30, height: 30)
                        .background(Color.black.opacity(0.4))
                        .clipShape(Circle())
                }
                
                // 下一個項目
                Button(action: {
                    playerWindow.playNext()
                }) {
                    Image(systemName: "forward.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.white)
                        .frame(width: 30, height: 30)
                        .background(Color.black.opacity(0.4))
                        .clipShape(Circle())
                }
                
                // 播放清單按鈕
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        isShowingPlaylist.toggle()
                    }
                }) {
                    Image(systemName: "list.bullet")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                        .frame(width: 30, height: 30)
                        .background(
                            isShowingPlaylist ? 
                            Color.blue.opacity(0.9) : 
                            Color.black.opacity(0.6)
                        )
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(isShowingPlaylist ? Color.blue : Color.white.opacity(0.3), lineWidth: 1)
                        )
                }
            }
            
            // 第二行控制項
            HStack(spacing: 16) {
                // 音量控制
                HStack(spacing: 8) {
                    Image(systemName: "speaker.fill")
                        .font(.system(size: 12))
                        .foregroundColor(.white.opacity(0.8))
                    
                    Slider(
                        value: Binding(
                            get: { Double(self.playerManager.volume) },
                            set: { self.playerManager.setVolume(Float($0)) }
                        ),
                        in: 0...1
                    )
                    .frame(width: 80)
                    .accentColor(.white)
                }
                
                Spacer()
                
                // 播放速度選擇器
                Menu {
                    ForEach(PlayerManager.playbackSpeeds, id: \.self) { speed in
                        Button(action: {
                            playerManager.setPlaybackRate(speed)
                        }) {
                            HStack {
                                Text("\(speed == 1.0 ? "正常" : String(format: "%.2fx", speed))")
                                if playerManager.playbackRate == speed {
                                    Image(systemName: "checkmark")
                                }
                            }
                        }
                    }
                } label: {
                    Text(playerManager.playbackRate == 1.0 ? "正常" : String(format: "%.2fx", playerManager.playbackRate))
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.black.opacity(0.4))
                        .clipShape(Capsule())
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
    }
    
    private func formatTime(_ time: Double) -> String {
        guard !time.isNaN && !time.isInfinite else { return "00:00" }
        
        let totalSeconds = Int(time)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60
        
        if hours > 0 {
            return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
}

// MARK: - Media Info View
struct MediaInfoView: View {
    @ObservedObject var playerManager: PlayerManager
    let mediaURL: URL?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            if let url = mediaURL {
                Text("正在播放")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(url.lastPathComponent)
                    .font(.headline)
                    .lineLimit(1)
                
                HStack {
                    if playerManager.duration > 0 {
                        Text("時長: \(formatDuration(playerManager.duration))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Text(url.pathExtension.uppercased())
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.secondary.opacity(0.2))
                        .clipShape(Capsule())
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    
    private func formatDuration(_ duration: Double) -> String {
        let totalSeconds = Int(duration)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60
        
        if hours > 0 {
            return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
}

// MARK: - Stylized Button
struct StylizedButton: View {
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14))
                    .foregroundColor(.white.opacity(0.7))
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .clipShape(Capsule())
            .shadow(color: .blue.opacity(0.3), radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}