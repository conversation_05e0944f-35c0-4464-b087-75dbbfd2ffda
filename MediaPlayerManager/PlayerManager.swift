//
//  PlayerManager.swift
//  MediaPlayerManager
//
//  Refactored from ContentView.swift to reduce file size
//

import SwiftUI
import AVKit
import Combine
import UniformTypeIdentifiers

// MARK: - URL Extensions for debugging
extension URL {
    var fileSizeString: String {
        do {
            let resourceValues = try self.resourceValues(forKeys: [.fileSizeKey])
            if let fileSize = resourceValues.fileSize {
                return ByteCountFormatter.string(fromByteCount: Int64(fileSize), countStyle: .file)
            }
        } catch {
            return "無法讀取"
        }
        return "未知"
    }
}

// MARK: - Player Manager (基於 AVQueuePlayer 的專業解決方案)
class PlayerManager: NSObject, ObservableObject {
    // 🚀 使用 AVQueuePlayer 解決多檔案播放問題 (行業標準)
    @Published var queuePlayer: AVQueuePlayer?
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var currentTime: Double = 0
    @Published var duration: Double = 0
    @Published var isPlaying = false
    @Published var volume: Float = 0.8
    @Published var isMuted = false
    @Published var playbackRate: Float = 1.0
    @Published var showControls = true
    @Published var lastInteractionTime = Date()

    // 佇列模式（使用 AVQueuePlayer 自動前進）
    @Published var isQueueMode: Bool = true

    // 兼容性屬性 (保持現有程式碼工作)
    var player: AVPlayer? {
        get { return queuePlayer }
        set {
            if let queuePlayer = newValue as? AVQueuePlayer {
                self.queuePlayer = queuePlayer
            }
        }
    }

    private var timeObserver: Any?
    private var statusObserver: AnyCancellable?
    private var cancellables = Set<AnyCancellable>()
    private var controlsTimer: Timer?
    private var completionObserver: Any?
    private var playerItemStatusObserver: NSKeyValueObservation?
    private var currentItemObserver: NSKeyValueObservation?



    // 播放完成回調
    // Security-scoped URLs we started accessing; release on cleanup
    private var accessedURLs = Set<URL>()

    private func makePlayerItem(for url: URL) -> AVPlayerItem {
        var started = false
        if url.isFileURL {
            started = url.startAccessingSecurityScopedResource()
            if started { accessedURLs.insert(url) }
        }
        let asset = AVURLAsset(url: url, options: [
            AVURLAssetPreferPreciseDurationAndTimingKey: false
        ])
        let item = AVPlayerItem(asset: asset)
        item.preferredForwardBufferDuration = 1.0
        return item
    }

    var onPlaybackCompleted: (() -> Void)?

    // 快取和預載入
    private var preloadedAssets: [URL: AVURLAsset] = [:]
    private var isPreloading = false

    // 重試機制 - 增強版
    private var retryCount = 0
    private var maxRetries = 3  // 增加到 3 次
    private var currentLoadingURL: URL?
    private var retryDelay: TimeInterval = 1.0  // 可調整的重試延遲

    // 預熱緩存 - 追蹤已預熱的檔案類型
    private static var preWarmedFormats: Set<String> = []
    private var isAVFoundationWarmedUp = false

    // 全局 AVAsset 緩存 - 避免重複預處理
    private static var processedAssets: [URL: AVURLAsset] = [:]
    private static var processingAssets: Set<URL> = []

    // 媒體格式支援 - 擴展版本
    static let supportedTypes: [UTType] = {
        var types: [UTType] = [
            // 系統內建視頻類型
            .movie, .video, .mpeg4Movie, .mpeg, .mpeg2Video, .appleProtectedMPEG4Video,

            // 音頻類型
            .audio, .mp3, .wav, .aiff, .audiovisualContent
        ]

        // 常見視頻格式
        let videoExtensions = [
            "mp4", "m4v", "mov", "qt", "avi", "mkv", "webm", "flv", "wmv", "asf",
            "mts", "m2ts", "ts", "vob", "ogv", "ogg", "dv", "3gp", "3g2",
            "mxf", "r3d", "rm", "rmvb", "f4v", "divx", "xvid"
        ]

        for ext in videoExtensions {
            if let type = UTType(filenameExtension: ext) {
                types.append(type)
            }
        }

        // 音頻格式
        let audioExtensions = [
            "m4a", "aac", "flac", "aiff", "au", "m4b", "m4p", "m4r",
            "alac", "ape", "wv", "tta", "dts", "ac3", "eac3",
            "oga", "opus", "wma", "ra", "amr", "3ga", "caf", "sd2"
        ]

        for ext in audioExtensions {
            if let type = UTType(filenameExtension: ext) {
                types.append(type)
            }
        }

        return types
    }()

    // 播放速度選項，最高支援 16 倍速
    static let playbackSpeeds: [Float] = [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 2.0, 4.0, 8.0, 16.0]

    override init() {
        super.init()
        setupVolumeBinding()
        startControlsTimer()

        // 只在程式啟動時進行一次基本預熱
        if !isAVFoundationWarmedUp {
            preWarmAVFoundation()
            isAVFoundationWarmedUp = true
        }
    }

    // MARK: - AVFoundation 預熱
    private func preWarmAVFoundation() {
        print("🌡️ 開始預熱 AVFoundation...")

        // 在背景執行緒預熱 AVFoundation 子系統
        DispatchQueue.global(qos: .utility).async {
            // 創建一個虛擬的 AVPlayer 來初始化底層系統
            let dummyURL = URL(fileURLWithPath: "/System/Library/Sounds/Ping.aiff")
            if FileManager.default.fileExists(atPath: dummyURL.path) {
                let dummyAsset = AVURLAsset(url: dummyURL)
                let dummyItem = AVPlayerItem(asset: dummyAsset)
                let dummyPlayer = AVPlayer(playerItem: dummyItem)

                // 短暫創建播放器來觸發內部初始化
                DispatchQueue.main.async {
                    dummyPlayer.volume = 0  // 靜音
                    dummyPlayer.play()

                    // 立即停止並清理
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        dummyPlayer.pause()
                        dummyPlayer.replaceCurrentItem(with: nil)
                        print("✅ AVFoundation 預熱完成")
                    }
                }
            }
        }
    }

    // MARK: - 格式預熱
    private func preWarmFormat(_ format: String) {
        print("🔥 預熱 \(format.uppercased()) 編碼器...")

        // 針對不同格式進行特殊預熱
        switch format.lowercased() {
        case "mp4", "m4v":
            preWarmH264Decoder()
        case "mov", "qt":
            preWarmQuickTimeDecoder()
        case "mkv":
            preWarmMatroskaDecoder()
        case "avi":
            preWarmAVIDecoder()
        default:
            preWarmGenericDecoder(format: format)
        }
    }

    private func preWarmH264Decoder() {
        print("🎬 預熱 H.264 解码器...")
        // 創建微小的 H.264 測試資源來觸發解碼器初始化
        DispatchQueue.global(qos: .utility).async {
            let testAsset = AVURLAsset(url: URL(fileURLWithPath: "/System/Library/Sounds/Ping.aiff"))
            let _ = AVPlayerItem(asset: testAsset)
        }
    }

    private func preWarmQuickTimeDecoder() {
        print("🎬 預熱 QuickTime 解碼器...")
        DispatchQueue.global(qos: .utility).async {
            let testAsset = AVURLAsset(url: URL(fileURLWithPath: "/System/Library/Sounds/Ping.aiff"))
            let _ = AVPlayerItem(asset: testAsset)
        }
    }

    private func preWarmMatroskaDecoder() {
        print("🎬 預熱 Matroska 解碼器...")
        DispatchQueue.global(qos: .utility).async {
            // MKV 需要特殊處理
            let testAsset = AVURLAsset(url: URL(fileURLWithPath: "/System/Library/Sounds/Ping.aiff"))
            let _ = AVPlayerItem(asset: testAsset)
        }
    }

    private func preWarmAVIDecoder() {
        print("🎬 預熱 AVI 解碼器...")
        DispatchQueue.global(qos: .utility).async {
            let testAsset = AVURLAsset(url: URL(fileURLWithPath: "/System/Library/Sounds/Ping.aiff"))
            let _ = AVPlayerItem(asset: testAsset)
        }
    }

    private func preWarmGenericDecoder(format: String) {
        print("🎬 預熱 \(format.uppercased()) 解碼器...")
        DispatchQueue.global(qos: .utility).async {
            let testAsset = AVURLAsset(url: URL(fileURLWithPath: "/System/Library/Sounds/Ping.aiff"))
            let _ = AVPlayerItem(asset: testAsset)
        }
    }

    // MARK: - Media Loading
    func loadMedia(from url: URL, completion: @escaping (Bool) -> Void) {
        // 檢查是否是新檔案，如果是就重置重試計數
        if currentLoadingURL != url {
            retryCount = 0
            currentLoadingURL = url
            print("🎵 AVQueuePlayer 載入媒體: \(url.lastPathComponent)")
            print("📊 使用行業標準解決方案 (如 KMPlayer/OmniPlayer)")
        } else {
            retryCount += 1
            print("🔄 重試載入 (\(retryCount)/\(maxRetries)): \(url.lastPathComponent)")
        }

        // 若為 WebM，交由 WebView 播放器處理（不使用 AVQueuePlayer）
        if url.pathExtension.lowercased() == "webm" {
            print("🎬 偵測到 WebM 格式，使用 WebView 播放器")
            isLoading = false
            isPlaying = false
            completion(true)
            return
        }

        // 使用 AVQueuePlayer 專業方案
        loadMediaWithQueuePlayer(from: url, completion: completion)
        return
    }

    // MARK: - 完全清理所有元件 (模擬應用重啟)
    private func performCompleteCleanup() {
        print("🧹 階段 1: 完全清理所有元件 (模擬重新啟動)")

        // 強制停止播放器並清理狀態
        if let currentPlayer = player {
            currentPlayer.pause()
            currentPlayer.rate = 0

            // 完全斷開所有連接
            currentPlayer.replaceCurrentItem(with: nil)
            currentPlayer.actionAtItemEnd = AVPlayer.ActionAtItemEnd.pause

            print("🧹 已完全斷開播放器連接")
        }

        // 移除所有觀察者和通知
        removeObservers()
        NotificationCenter.default.removeObserver(self)
        print("🧹 已移除所有觀察者和通知")

        // 徹底釋放播放器實例
        player = nil
        print("🧹 播放器實例已完全銷毀")

        // 系統級清理 (模擬重啟效果)
        performSystemLevelReset()
    }

    // MARK: - 輕量級系統重置 (快速隔離模式)
    private func performSystemLevelReset() {
        print("🔄 執行輕量級系統重置...")

        // 只清理必要的狀態，保持快速載入
        PlayerManager.processedAssets.removeAll()
        PlayerManager.processingAssets.removeAll()

        // 清理實例狀態但保持快取以提高速度
        preloadedAssets.removeAll()
        timeObserver = nil

        // 移除耗時的操作：
        // - URLCache.shared.removeAllCachedResponses() // 太慢，跳過
        // - Thread.sleep() // 不等待，直接繼續
        // - autoreleasepool // 讓系統自動處理

        print("✅ 輕量級重置完成 - 快速隔離模式")
    }

    // MARK: - 重置所有狀態到初始值
    private func resetAllStates() {
        print("🔄 階段 2: 重置所有狀態")

        // 播放狀態重置
        isPlaying = false
        isLoading = true
        errorMessage = nil

        // 時間狀態重置
        currentTime = 0
        duration = 0

        // UI 狀態重置
        showControls = true
        lastInteractionTime = Date()

        // 播放設定重置到預設值
        playbackRate = 1.0
        // volume 和 isMuted 保持用戶設定

        print("✅ 所有狀態已重置到初始值")
    }

    // MARK: - 清理所有快取和資源
    private func clearAllCachesAndResources() {
        print("🧹 階段 3: 清理所有快取和資源")

        // 清理預載入資源
        preloadedAssets.removeAll()
        isPreloading = false

        // 清理靜態快取 (讓每個檔案都像第一次載入)
        PlayerManager.processedAssets.removeAll()
        PlayerManager.processingAssets.removeAll()

        // 重置預熱狀態 (讓每個檔案都重新預熱)
        // PlayerManager.preWarmedFormats.removeAll() // 這個保留，避免重複預熱格式

        print("✅ 所有快取和資源已清理")
    }

    // MARK: - 輕量級預熱
    private func performLightweightWarmup(for url: URL) {
        let fileExtension = url.pathExtension.lowercased()

        // 檢查是否已經預熱過這種格式
        if !PlayerManager.preWarmedFormats.contains(fileExtension) {
            print("🔥 輕量級預熱格式: \(fileExtension.uppercased())")

            // 快速格式預熱 - 背景執行，不阻塞主執行緒
            DispatchQueue.global(qos: .utility).async {
                // 只創建 AVURLAsset，不創建播放器
                let quickAsset = AVURLAsset(url: url, options: [
                    AVURLAssetPreferPreciseDurationAndTimingKey: false
                ])

                // 觸發基本屬性載入（非阻塞）
                Task {
                    do {
                        _ = try await quickAsset.load(.duration)
                    } catch {
                        // 忽略載入錯誤，這只是預熱
                    }
                }

                // 標記為已預熱
                DispatchQueue.main.async {
                    PlayerManager.preWarmedFormats.insert(fileExtension)
                    print("✅ 格式 \(fileExtension.uppercased()) 預熱完成")
                }
            }
        }
    }

    // MARK: - 深度預處理 (保留但不使用)
    private func deepPreprocessMedia(url: URL, completion: @escaping (Bool) -> Void) {
        print("🔍 開始深度預處理: \(url.lastPathComponent)")

        // 檢查是否已經預處理過
        if PlayerManager.processedAssets[url] != nil {
            print("💾 使用已預處理的資源")
            DispatchQueue.main.async {
                completion(true)
            }
            return
        }

        // 檢查是否正在處理中
        if PlayerManager.processingAssets.contains(url) {
            print("⏳ 檔案正在預處理中，等待完成...")
            // 等待處理完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                self?.deepPreprocessMedia(url: url, completion: completion)
            }
            return
        }

        // 標記為處理中
        PlayerManager.processingAssets.insert(url)

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            // 創建 AVURLAsset 並強制載入所有屬性
            let asset = AVURLAsset(url: url, options: [
                AVURLAssetPreferPreciseDurationAndTimingKey: true,
                AVURLAssetHTTPCookiesKey: [],
                // 強制預載入
                "AVURLAssetReferenceRestrictionsKey": 0
            ])

            // 強制載入關鍵屬性
            let requiredKeys = [
                "duration",
                "tracks",
                "playable",
                "hasProtectedContent",
                "metadata",
                "availableMediaCharacteristicsWithMediaSelectionOptions"
            ]

            print("📊 預載入媒體屬性...")
            Task { @MainActor in
                var allKeysLoaded = true
                var hasErrors = false

                // 使用新的 async/await API 載入屬性
                for key in requiredKeys {
                    do {
                        switch key {
                        case "duration":
                            _ = try await asset.load(.duration)
                        case "tracks":
                            _ = try await asset.load(.tracks)
                        case "playable":
                            _ = try await asset.load(.isPlayable)
                        case "hasProtectedContent":
                            _ = try await asset.load(.hasProtectedContent)
                        case "metadata":
                            _ = try await asset.load(.metadata)
                        default:
                            // 對於其他屬性，跳過不支援的屬性
                            print("⚠️ 跳過不支援的屬性: \(key)")
                            continue
                        }
                        print("✅ \(key): 載入成功")
                    } catch {
                        print("❌ \(key): 載入失敗 - \(error.localizedDescription)")
                        hasErrors = true
                        allKeysLoaded = false
                    }
                }

                if allKeysLoaded && !hasErrors {
                    print("🎯 預處理成功，建立測試播放器...")
                    // 創建測試播放器來觸發解碼器初始化
                    self?.createTestPlayer(with: asset) { testSuccess in
                        // 處理完成，從處理中集合移除
                        PlayerManager.processingAssets.remove(url)

                        if testSuccess {
                            // 成功時緩存 asset
                            PlayerManager.processedAssets[url] = asset
                            print("💾 預處理成功並已緩存")
                        }

                        completion(testSuccess)
                    }
                } else {
                    print("❌ 預處理失敗")
                    // 處理完成，從處理中集合移除
                    PlayerManager.processingAssets.remove(url)
                    completion(false)
                }
            }
        }
    }

    private func createTestPlayer(with asset: AVURLAsset, completion: @escaping (Bool) -> Void) {
        print("🧪 創建測試播放器進行深度初始化...")

        let testItem = AVPlayerItem(asset: asset)
        let testPlayer = AVPlayer(playerItem: testItem)

        // 觀察測試播放器狀態
        var hasCompleted = false
        let statusObserver = testItem.observe(\.status, options: [.new]) { item, _ in
            guard !hasCompleted else { return }

            switch item.status {
            case .readyToPlay:
                hasCompleted = true
                print("✅ 測試播放器準備完成")

                // 嘗試短暫播放來觸發解碼器
                testPlayer.volume = 0
                testPlayer.play()

                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    testPlayer.pause()
                    testPlayer.replaceCurrentItem(with: nil)
                    completion(true)
                }

            case .failed:
                hasCompleted = true
                print("❌ 測試播放器失敗: \(item.error?.localizedDescription ?? "未知錯誤")")
                completion(false)

            default:
                break
            }
        }

        // 5秒超時
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            guard !hasCompleted else { return }
            hasCompleted = true
            statusObserver.invalidate()

            print("⏰ 測試播放器超時")
            testPlayer.pause()
            testPlayer.replaceCurrentItem(with: nil)
            completion(false)
        }
    }

    // 使用預處理後的媒體建立播放器
    private func setupPlayerWithPreprocessedMedia(url: URL, completion: @escaping (Bool) -> Void) {
        print("🎬 使用預處理媒體建立播放器...")

        // 使用完全重新初始化的播放器建立方式
        createFreshPlayer(with: url, completion: completion)
    }

    // MARK: - 全新建立播放器 (完全隔離，模擬首次啟動)
    private func createFreshPlayer(with url: URL, completion: @escaping (Bool) -> Void) {
        print("🆕 階段 4: 全新建立播放器 (完全隔離模式)")
        print("📊 目標檔案: \(url.lastPathComponent)")
        print("📊 檔案大小: \(url.fileSizeString)")
        print("📊 檔案路徑: \(url.path)")

        // 檢查檔案是否存在和可讀取
        let fileManager = FileManager.default
        guard fileManager.fileExists(atPath: url.path) else {
            print("❌ 檔案不存在: \(url.path)")
            completion(false)
            return
        }

        // 創建快速隔離的 AVURLAsset (平衡速度與隔離)
        let fastIsolatedOptions: [String: Any] = [
            AVURLAssetPreferPreciseDurationAndTimingKey: false,  // 改為 false，加快載入
            // 移除可能導致慢載入的選項
            // AVURLAssetHTTPCookiesKey: [],  // 跳過，減少初始化時間
            // "AVURLAssetReferenceRestrictionsKey": 0,  // 跳過強制重新載入
            AVURLAssetAllowsCellularAccessKey: false
        ]

        print("🔧 創建快速隔離 AVURLAsset...")
        let isolatedAsset = AVURLAsset(url: url, options: fastIsolatedOptions)
        print("✅ 快速隔離 AVURLAsset 創建完成")

        // 創建完全隔離的 PlayerItem
        print("🔧 創建隔離 PlayerItem...")
        let isolatedPlayerItem = AVPlayerItem(asset: isolatedAsset)
        print("✅ 隔離 PlayerItem 創建完成，狀態: \(isolatedPlayerItem.status.rawValue)")

        // 為快速載入配置 PlayerItem 參數
        isolatedPlayerItem.preferredForwardBufferDuration = 1.0  // 減少緩衝，加快開始播放
        isolatedPlayerItem.canUseNetworkResourcesForLiveStreamingWhilePaused = false
        isolatedPlayerItem.preferredPeakBitRate = 0  // 不限制位元率

        // 創建完全隔離的播放器實例
        print("🔧 創建隔離 AVPlayer...")
        let isolatedPlayer = AVPlayer(playerItem: isolatedPlayerItem)
        print("✅ AVPlayer 創建完成")

        // 為隔離播放器設定所有屬性 (完全獨立配置)
        isolatedPlayer.automaticallyWaitsToMinimizeStalling = true  // 確保準備好才播放
        isolatedPlayer.preventsDisplaySleepDuringVideoPlayback = true
        isolatedPlayer.actionAtItemEnd = AVPlayer.ActionAtItemEnd.pause  // 明確設定結束行為

        // 重新應用用戶設定 (不依賴先前狀態)
        isolatedPlayer.volume = volume
        isolatedPlayer.isMuted = isMuted
        isolatedPlayer.rate = 0  // 從完全停止狀態開始

        // 指派隔離播放器
        self.player = isolatedPlayer
        print("✅ 隔離播放器已建立並指派 - 完全獨立環境")

        // 為隔離播放器設定全新觀察者
        setupObservers(for: isolatedPlayer)

        // 等待隔離播放器完全準備
        waitForPlayerReady(isolatedPlayer, completion: completion)
    }

    // MARK: - 等待播放器準備完成 (增強調試版)
    private func waitForPlayerReady(_ player: AVPlayer, completion: @escaping (Bool) -> Void) {
        guard let playerItem = player.currentItem else {
            print("❌ PlayerItem 不存在")
            completion(false)
            return
        }

        let asset = playerItem.asset
        print("⏳ 等待播放器完全準備...")

        // 檢查是否為 AVURLAsset 以獲取 URL 資訊
        if let urlAsset = asset as? AVURLAsset {
            print("📊 檔案資訊: \(urlAsset.url.lastPathComponent)")
            print("📊 檔案大小: \(urlAsset.url.fileSizeString)")
            print("📊 PlayerItem 初始狀態: \(playerItem.status.rawValue)")

            // 檢查檔案是否可訪問
            if !FileManager.default.fileExists(atPath: urlAsset.url.path) {
                print("❌ 檔案不存在: \(urlAsset.url.path)")
                completion(false)
                return
            }
        } else {
            print("📊 非 URL 資源")
            print("📊 PlayerItem 初始狀態: \(playerItem.status.rawValue)")
        }

        // 檢查是否已經準備好
        if playerItem.status == .readyToPlay {
            print("✅ 播放器已準備好，立即播放")
            startPlayback(player, completion: completion)
            return
        }

        // 觀察播放器狀態
        var hasCompleted = false
        let statusObserver = playerItem.observe(\.status, options: [.new]) { [weak self] item, _ in
            guard !hasCompleted else { return }

            print("🔍 PlayerItem 狀態變化: \(item.status.rawValue)")

            switch item.status {
            case .readyToPlay:
                hasCompleted = true
                print("✅ 播放器準備完成")
                print("📊 時長: \(item.duration.isValid ? CMTimeGetSeconds(item.duration) : -1) 秒")
                print("📊 tracks: \(item.tracks.count)")
                self?.startPlayback(player, completion: completion)

            case .failed:
                hasCompleted = true
                let error = item.error
                print("❌ 播放器準備失敗")
                print("📊 錯誤描述: \(error?.localizedDescription ?? "未知錯誤")")
                if let nsError = error as NSError? {
                    print("📊 錯誤碼: \(nsError.code)")
                    print("📊 錯誤域: \(nsError.domain)")
                    print("📊 用戶信息: \(nsError.userInfo)")
                }
                completion(false)

            default:
                print("⏳ 播放器準備中... 狀態: \(item.status.rawValue)")
            }
        }

        // 7秒超時機制 (減少等待時間，更快重試)
        DispatchQueue.main.asyncAfter(deadline: .now() + 7.0) { [weak self] in
            guard !hasCompleted else { return }
            hasCompleted = true
            statusObserver.invalidate()

            print("⏰ 播放器準備超時 (10秒)")
            print("📊 最終狀態: \(playerItem.status.rawValue)")
            print("📊 是否有錯誤: \(playerItem.error != nil)")
            print("📊 播放器控制狀態: \(player.timeControlStatus.rawValue)")

            // 超時時觸發失敗，讓重試機制介入
            DispatchQueue.main.async {
                self?.handlePlaybackFailure()
            }
            completion(false)
        }
    }

    // MARK: - 開始播放
    private func startPlayback(_ player: AVPlayer, completion: @escaping (Bool) -> Void) {
        print("🎬 開始播放")

        isLoading = false

        // 給播放器一點時間完全初始化
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
            player.play()
            self?.isPlaying = true

            print("✅ 播放已開始")
            completion(true)
        }
    }


    // MARK: - 增強版播放失敗處理
    private func handlePlaybackFailure() {
        guard let url = currentLoadingURL else {
            print("❌ 沒有當前載入的 URL")
            return
        }

        if retryCount < maxRetries {
            retryDelay = min(retryDelay * 1.5, 3.0)  // 遞增延遲，最多3秒
            print("🔄 播放失敗，準備重試 (\(retryCount + 1)/\(maxRetries))，延遲 \(retryDelay) 秒")
            print("📝 檔案: \(url.lastPathComponent)")

            // 強制進行系統級清理
            performSystemLevelCleanup()

            // 遞增延遲重試
            DispatchQueue.main.asyncAfter(deadline: .now() + retryDelay) { [weak self] in
                print("🚀 開始第 \(self?.retryCount ?? 0 + 1) 次重試")
                self?.loadMedia(from: url) { success in
                    if success {
                        print("✅ 重試第 \(self?.retryCount ?? 0) 次成功！")
                    } else {
                        print("❌ 重試第 \(self?.retryCount ?? 0) 次失敗")
                    }
                }
            }
        } else {
            print("❌ 已達最大重試次數(\(maxRetries))，放棄載入")
            print("📝 檔案: \(url.lastPathComponent)")
            errorMessage = "播放失敗，已重試 \(maxRetries) 次"
            isLoading = false

            // 重置重試參數供下次使用
            retryCount = 0
            retryDelay = 1.0
            currentLoadingURL = nil
        }
    }

    // MARK: - 系統級清理
    private func performSystemLevelCleanup() {
        print("🧹 執行系統級深度清理...")

        // 1. 強制釋放所有 AVFoundation 資源
        player?.pause()
        player?.rate = 0
        player?.replaceCurrentItem(with: nil)
        player = nil

        // 2. 清理所有觀察者和通知
        removeObservers()
        NotificationCenter.default.removeObserver(self)

        // 3. 清理所有快取 (包括系統快取)
        URLCache.shared.removeAllCachedResponses()
        preloadedAssets.removeAll()
        PlayerManager.processedAssets.removeAll()
        PlayerManager.processingAssets.removeAll()

        // 4. 重置預熱狀態 (強制重新預熱)
        PlayerManager.preWarmedFormats.removeAll()

        // 5. 強制垃圾回收
        autoreleasepool {
            // 創建臨時對象來觸發記憶體清理
        }

        // 6. 給系統時間進行清理
        Thread.sleep(forTimeInterval: 0.1)

        print("✅ 系統級深度清理完成")
    }

    // MARK: - 預載入功能
    func preloadNextMedia(_ nextURL: URL) {
        guard !isPreloading && preloadedAssets[nextURL] == nil else { return }

        isPreloading = true
        DispatchQueue.global(qos: .utility).async { [weak self] in
            let asset = AVURLAsset(url: nextURL, options: [
                AVURLAssetPreferPreciseDurationAndTimingKey: false,
                AVURLAssetHTTPCookiesKey: []
            ])

            DispatchQueue.main.async {
                self?.preloadedAssets[nextURL] = asset
                self?.isPreloading = false
                print("📥 預載入完成: \(nextURL.lastPathComponent)")
            }
        }
    }

    private func preloadNextMediaInBackground() {
        // 這個方法會被 WindowManager 調用來傳入下一個 URL
        // 在這裡只是預留接口
    }

    // MARK: - Playback Controls
    func togglePlayPause() {
        guard let player = player else {
            print("播放器未初始化")
            return
        }

        if isPlaying {
            print("暫停播放")
            player.pause()
            // 立即更新狀態，不依賴觀察者
            isPlaying = false
        } else {
            print("開始播放")
            player.play()
            player.rate = playbackRate
            // 立即更新狀態，不依賴觀察者
            isPlaying = true
        }

        // 強制更新播放狀態，確保UI同步
        print("🎮 手動切換播放狀態: \(isPlaying)")

        userInteracted()
    }

    func seek(to time: Double) {
        guard let player = player else { return }
        let cmTime = CMTime(seconds: time, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        player.seek(to: cmTime, toleranceBefore: CMTime.zero, toleranceAfter: CMTime.zero)

        userInteracted()
    }

    func setVolume(_ newVolume: Float) {
        volume = max(0.0, min(1.0, newVolume))
        player?.volume = volume

        userInteracted()
    }

    func toggleMute() {
        isMuted.toggle()
        player?.isMuted = isMuted

        userInteracted()
    }

    func setPlaybackRate(_ rate: Float) {
        playbackRate = rate
        player?.rate = rate

        userInteracted()
    }

    // MARK: - Private Methods
    private func handleLoadError(_ message: String, completion: @escaping (Bool) -> Void) {
        DispatchQueue.main.async { [weak self] in
            self?.isLoading = false
            self?.errorMessage = message
            print("媒體載入錯誤: \(message)")
            completion(false)
        }
    }

    private func setupPlayerWithAsset(_ asset: AVURLAsset, completion: @escaping (Bool) -> Void) {
        // 快速創建 PlayerItem
        let playerItem = AVPlayerItem(asset: asset)

        // 優化播放器設置
        playerItem.preferredForwardBufferDuration = 1.0  // 只緩衝 1 秒

        let newPlayer = AVPlayer(playerItem: playerItem)

        // 優化播放器設置
        newPlayer.automaticallyWaitsToMinimizeStalling = false  // 不等待緩衝

        // 設置音量
        newPlayer.volume = volume
        newPlayer.isMuted = isMuted

        // 清理舊的觀察者
        removeObservers()

        // 設置新播放器
        self.player = newPlayer

        // 添加觀察者
        setupObservers(for: newPlayer)

        isLoading = false

        // 立即開始播放
        newPlayer.play()
        isPlaying = true

        completion(true)
    }

    private func setupObservers(for player: AVPlayer) {
        // 時間觀察者
        let timeScale = CMTimeScale(NSEC_PER_SEC)
        let time = CMTime(seconds: 0.1, preferredTimescale: timeScale)

        timeObserver = player.addPeriodicTimeObserver(forInterval: time, queue: .main) { [weak self] time in
            self?.currentTime = CMTimeGetSeconds(time)
        }

        // 狀態觀察者
        statusObserver = player.publisher(for: \.timeControlStatus)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] status in
                let wasPlaying = self?.isPlaying ?? false
                let nowPlaying = (status == .playing)
                self?.isPlaying = nowPlaying

                print("🎮 播放狀態變化: \(wasPlaying) → \(nowPlaying) (status: \(status.rawValue))")

                // 移除自動重新播放邏輯，讓用戶完全控制播放狀態
                // 這樣暫停按鈕才會正常工作
            }

        // 播放完成觀察者 - 確保綁定到正確的 playerItem
        if let playerItem = player.currentItem {
            print("🔚 設置播放完成觀察者給: \(playerItem)")
            completionObserver = NotificationCenter.default.addObserver(
                forName: .AVPlayerItemDidPlayToEndTime,
                object: playerItem,
                queue: .main
            ) { [weak self] notification in
                print("🔚 播放完成通知觸發")
                self?.onPlaybackCompleted?()
            }

            // 播放失敗觀察者 - 自動重試機制
            NotificationCenter.default.addObserver(
                forName: .AVPlayerItemFailedToPlayToEndTime,
                object: playerItem,
                queue: .main
            ) { [weak self] notification in
                print("❌ 播放失敗通知觸發")
                self?.handlePlaybackFailure()
            }

            // 時長觀察者
            playerItem.publisher(for: \.duration)
                .receive(on: DispatchQueue.main)
                .sink { [weak self] duration in
                    if duration.isValid && !duration.isIndefinite {
                        self?.duration = CMTimeGetSeconds(duration)
                    }
                }
                .store(in: &cancellables)

            // PlayerItem 狀態觀察者 - 檢測載入失敗
            playerItem.publisher(for: \.status)
                .receive(on: DispatchQueue.main)
                .sink { [weak self] status in
                    if status == .failed {
                        print("❌ PlayerItem 狀態失敗")
                        self?.handlePlaybackFailure()
                    }
                }
                .store(in: &cancellables)
        }
    }

    private func removeObservers() {
        print("🧹 清理所有觀察者")

        // 先暫停播放器，避免在清理過程中觸發觀察者
        queuePlayer?.pause()

        if let timeObserver = timeObserver {
            queuePlayer?.removeTimeObserver(timeObserver)
            self.timeObserver = nil
            print("🧹 AVQueuePlayer 時間觀察者已清理")
        }

        statusObserver?.cancel()
        statusObserver = nil
        print("🧹 狀態觀察者已清理")

        if let completionObserver = completionObserver {
            NotificationCenter.default.removeObserver(completionObserver)
            self.completionObserver = nil
            print("🧹 播放完成觀察者已清理")
        }

        // 新增：KVO 觀察者清理
        playerItemStatusObserver?.invalidate()
        playerItemStatusObserver = nil
        currentItemObserver?.invalidate()
        currentItemObserver = nil
        print("🧹 KVO 觀察者已清理")

        cancellables.removeAll()
        print("🧹 所有 Cancellables 已清理")

        // 重置播放狀態
        isPlaying = false
        currentTime = 0
        duration = 0
    }

    private func setupVolumeBinding() {
        // 音量變化時更新播放器
        $volume
            .sink { [weak self] (newVolume: Float) in
                self?.player?.volume = newVolume
            }
            .store(in: &cancellables)

        $isMuted
            .sink { [weak self] (muted: Bool) in
                self?.player?.isMuted = muted
            }
            .store(in: &cancellables)
    }

    private func startControlsTimer() {
        controlsTimer = Timer.scheduledTimer(withTimeInterval: 8.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }

            let timeSinceLastInteraction = Date().timeIntervalSince(self.lastInteractionTime)
            if timeSinceLastInteraction >= 8.0 && self.showControls {
                withAnimation(.easeOut(duration: 0.3)) {
                    self.showControls = false
                }
            }
        }
    }

    private func userInteracted() {
        lastInteractionTime = Date()
        if !showControls {
            withAnimation(.easeIn(duration: 0.2)) {
                showControls = true
            }
        }
    }

    // MARK: - 完全清理方法 (外部調用)
    func cleanup() {
        print("🗑️ PlayerManager 執行完全清理...")

        // 停止播放並清理播放器
        player?.pause()
        player?.rate = 0
        player?.replaceCurrentItem(with: nil)

        // 移除所有觀察者
        removeObservers()
        NotificationCenter.default.removeObserver(self)

        // 清理控制器和定時器
        controlsTimer?.invalidate()
        controlsTimer = nil

        // 清理所有狀態和快取
        preloadedAssets.removeAll()
        timeObserver = nil

        // 清理靜態狀態
        PlayerManager.processedAssets.removeAll()
        PlayerManager.processingAssets.removeAll()
        PlayerManager.preWarmedFormats.removeAll()

        // 釋放播放器
        player = nil

        // 釋放 security-scoped access
        for url in accessedURLs { url.stopAccessingSecurityScopedResource() }
        accessedURLs.removeAll()

        print("✅ PlayerManager 完全清理完成")
    }

    deinit {
        print("🗑️ PlayerManager 正在釋放...")
        cleanup()
    }

    // MARK: - AVQueuePlayer 專業解決方案 (參考 KMPlayer/OmniPlayer)

    private func loadMediaWithQueuePlayer(from url: URL, completion: @escaping (Bool) -> Void) {
        print("🚀 創建 AVQueuePlayer (專業多媒體播放器方案)")

        DispatchQueue.main.async {
            self.isLoading = true
            self.errorMessage = nil
        }

        // 清理舊的播放器
        cleanupQueuePlayer()

        // 創建 PlayerItem
        print("🔧 創建 PlayerItem for \(url.lastPathComponent)")
        let playerItem = makePlayerItem(for: url)

        // 設定 PlayerItem 屬性 (快速載入配置)
        playerItem.preferredForwardBufferDuration = 1.0
        playerItem.canUseNetworkResourcesForLiveStreamingWhilePaused = false

        // 創建 AVQueuePlayer (為多檔案播放設計)
        print("🔧 創建 AVQueuePlayer")
        let newQueuePlayer = AVQueuePlayer(items: [playerItem])

        // 設定播放器屬性
        newQueuePlayer.volume = volume
        newQueuePlayer.isMuted = isMuted
        newQueuePlayer.rate = 0
        newQueuePlayer.automaticallyWaitsToMinimizeStalling = false // 不等待緩衝，立即播放
        newQueuePlayer.preventsDisplaySleepDuringVideoPlayback = true
        newQueuePlayer.actionAtItemEnd = .advance

        self.queuePlayer = newQueuePlayer
        print("✅ AVQueuePlayer 創建完成")

        // 設定觀察者
        setupQueuePlayerObservers(for: newQueuePlayer, playerItem: playerItem)

        // 等待準備完成
        waitForQueuePlayerReady(newQueuePlayer, playerItem: playerItem, completion: completion)
    }

    private func cleanupQueuePlayer() {
        print("🧹 清理舊的 AVQueuePlayer")

        if let existingPlayer = queuePlayer {
            existingPlayer.pause()
            existingPlayer.removeAllItems()
        }

        removeObservers()
        NotificationCenter.default.removeObserver(self)
        timeObserver = nil
        controlsTimer?.invalidate()
        controlsTimer = nil

        queuePlayer = nil

        print("✅ AVQueuePlayer 清理完成")
    }

    private func setupQueuePlayerObservers(for queuePlayer: AVQueuePlayer, playerItem: AVPlayerItem) {
        print("🔧 設定 AVQueuePlayer 觀察者")

        // 設定時間觀察者
        let interval = CMTime(seconds: 0.1, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        timeObserver = queuePlayer.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            guard let self = self else { return }
            let seconds = CMTimeGetSeconds(time)
            if !seconds.isNaN && !seconds.isInfinite {
                self.currentTime = seconds
            }
        }

        // 播放結束通知（對任何 AVPlayerItem）
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePlayerItemDidReachEnd),
            name: .AVPlayerItemDidPlayToEndTime,
            object: nil
        )

        // 觀察 currentItem 變化：advance 時確保繼續播放並通知上層同步索引/佇列
        currentItemObserver?.invalidate()
        currentItemObserver = queuePlayer.observe(\.currentItem, options: [.old, .new]) { [weak self] _, change in
            guard let self = self else { return }
            if change.oldValue != nil { // 排除第一次從 nil -> item 的情況
                print("🔁 AVQueuePlayer currentItem 已切換")
                // 保險觸發播放
                if queuePlayer.timeControlStatus != .playing {
                    queuePlayer.playImmediately(atRate: self.playbackRate)
                }
                // 通知上層（會同步索引並安排下一首入佇列）
                self.onPlaybackCompleted?()
            }
        }

        print("✅ AVQueuePlayer 觀察者設定完成")
    }

    private func waitForQueuePlayerReady(_ queuePlayer: AVQueuePlayer, playerItem: AVPlayerItem, completion: @escaping (Bool) -> Void) {
        print("⏳ 等待 AVQueuePlayer 準備完成...")

        var hasCompleted = false

        playerItemStatusObserver?.invalidate()
        playerItemStatusObserver = playerItem.observe(\.status, options: [.new]) { [weak self] item, _ in
            guard !hasCompleted else { return }

            print("🔍 PlayerItem 狀態: \(item.status.rawValue)")

            switch item.status {
            case .readyToPlay:
                hasCompleted = true
                print("✅ AVQueuePlayer 準備完成，可以播放")
                self?.playerItemStatusObserver?.invalidate()

                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }
                    self.isLoading = false
                    self.duration = CMTimeGetSeconds(item.duration)
                    // 自動開始播放，確保連播不會卡住
                    queuePlayer.playImmediately(atRate: self.playbackRate)
                    self.isPlaying = true
                    // 保險機制：若仍未開始播放，稍後再嘗試一次
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.4) { [weak self] in
                        guard let self = self, let qp = self.queuePlayer else { return }
                        if qp.timeControlStatus != .playing {
                            print("⚠️ 未自動開始播放，重試 playImmediately")
                            qp.playImmediately(atRate: self.playbackRate)
                        }
                    }
                    completion(true)
                }

            case .failed:
                hasCompleted = true
                print("❌ AVQueuePlayer 準備失敗")

                if let error = item.error {
                    print("❌ 錯誤詳情: \(error.localizedDescription)")
                }

                DispatchQueue.main.async {
                    self?.isLoading = false
                    self?.errorMessage = "播放失敗"
                    completion(false)
                }

            default:
                print("⏳ AVQueuePlayer 準備中...")
            }
        }

        // 5秒超時
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            guard !hasCompleted else { return }
            hasCompleted = true
            self.playerItemStatusObserver?.invalidate()

            print("⏰ AVQueuePlayer 準備超時")

            // 保險：若 queue 中還有下一個 item，但未開始播放，強制 playImmediately
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
                guard let self = self, let qp = self.queuePlayer else { return }
                if !qp.items().isEmpty && qp.timeControlStatus != .playing {
                    print("⚠️ 超時後保險 playImmediately")
                    qp.playImmediately(atRate: self.playbackRate)
                }
            }


            DispatchQueue.main.async {
                self.isLoading = false
                self.errorMessage = "載入超時"
                completion(false)
            }
        }
    }

    // MARK: - 播放完成處理
    @objc private func handlePlayerItemDidReachEnd() {
        print("🎵 AVQueuePlayer 播放完成")

        // 佇列模式：自動前進時保險觸發播放（不在此通知上層，避免與 currentItem KVO 重複）
        if isQueueMode {
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                if let qp = self.queuePlayer, qp.timeControlStatus != .playing {
                    print("⚠️ 完成後保險 playImmediately")
                    qp.playImmediately(atRate: self.playbackRate)
                }
                self.isPlaying = true
                self.currentTime = 0
            }
        } else {
            DispatchQueue.main.async {
                self.isPlaying = false
                self.currentTime = 0
            }
            // 非佇列模式才交給上層切下一首
            onPlaybackCompleted?()
        }
    }

    // MARK: - Queue management for continuous playback
    func enqueueNext(url: URL) {
        guard isQueueMode else { return }
        guard let qp = queuePlayer else { return }
        // 若佇列中已經有下一個項目，避免重複加入
        if qp.items().count > 1 {
            print("ℹ️ 佇列已有下一首，略過插入")
            return
        }
        let nextItem = makePlayerItem(for: url)
        nextItem.preferredForwardBufferDuration = 1.0
        if let last = qp.items().last {
            qp.insert(nextItem, after: last)
        } else {
            qp.insert(nextItem, after: nil)
        }
        print("📥 已加入佇列: \(url.lastPathComponent). 目前佇列數量: \(qp.items().count)")
    }

    func clearQueue(keeping current: Bool = true) {
        guard let qp = queuePlayer else { return }
        let items = qp.items()
        if current, let first = items.first { // 保留當前 item，其餘清掉
            for item in items.dropFirst() { qp.remove(item) }
            print("🧹 佇列清理，保留當前: \(first)")
        } else {
            qp.removeAllItems()
            print("🧹 佇列全部清空")
        }
    }
}

