//
//  WindowManager.swift
//  MediaPlayerManager
//
//  Refactored from ContentView.swift to reduce file size
//

import SwiftUI
import AppKit
import UniformTypeIdentifiers
import AVKit

// MARK: - Window Controller
class MediaPlayerWindowController: NSWindowController {
    var playerWindow: PlayerWindow
    
    init(playerWindow: PlayerWindow) {
        self.playerWindow = playerWindow
        
        let window = NSWindow(
            contentRect: NSRect(x: 100, y: 100, width: 1200, height: 800),
            styleMask: [.titled, .closable, .resizable, .miniaturizable],
            backing: .buffered,
            defer: false
        )
        
        super.init(window: window)
        
        setupWindow()
        setupContentView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupWindow() {
        guard let window = window else { return }
        
        window.title = "MediaPro 播放器"
        window.titlebarAppearsTransparent = true
        window.isMovableByWindowBackground = true
        window.delegate = self
        window.center()
        
        // 設置最小尺寸
        window.minSize = NSSize(width: 800, height: 600)
        
        // 設置視窗復原標識符
        window.identifier = NSUserInterfaceItemIdentifier("PlayerWindow-\(playerWindow.id.uuidString)")
        window.isRestorable = true
    }
    
    private func setupContentView() {
        guard let window = window,
              let windowManager = playerWindow.windowManager else { return }
        
        let contentView = NSHostingView(rootView: PlayerWindowView(playerWindow: playerWindow, windowManager: windowManager))
        window.contentView = contentView
    }
    
    override func windowDidLoad() {
        super.windowDidLoad()
        
        // 額外配置
        if let window = window {
            window.acceptsMouseMovedEvents = true
        }
    }
}

// MARK: - Window Controller Extensions
extension MediaPlayerWindowController: NSWindowDelegate {
    func windowWillClose(_ notification: Notification) {
        WindowManager.shared.windowWillClose(self)
    }
}

extension MediaPlayerWindowController: NSWindowRestoration {
    static func restoreWindow(withIdentifier identifier: NSUserInterfaceItemIdentifier, state: NSCoder, completionHandler: @escaping (NSWindow?, Error?) -> Void) {
        // 復原視窗邏輯
        completionHandler(nil, nil)
    }
}

// MARK: - Player Window Model
class PlayerWindow: Identifiable, ObservableObject {
    let id = UUID()
    @Published var playerManager = PlayerManager()  // 切換回原始 PlayerManager
    @Published var playlist: [URL] = []
    @Published var currentIndex = 0
    
    weak var windowManager: WindowManager?
    
    init(windowManager: WindowManager? = nil) {
        self.windowManager = windowManager
        setupPlayerManager()
    }
    
    private func setupPlayerManager() {
        // 播放完成時：在佇列模式下由 AVQueuePlayer 自動前進；
        // 若非佇列模式，才手動呼叫下一首
        playerManager.onPlaybackCompleted = { [weak self] in
            guard let self = self else { return }
            // 完成後先同步 UI 索引
            self.syncIndexAfterAdvance()
            // 佇列模式：持續保持「下一首」已排入佇列
            if self.playerManager.isQueueMode {
                self.enqueueNextAfterCurrent()
            } else {
                // 非佇列模式：手動切到下一首
                DispatchQueue.main.async { self.playNext() }
            }
        }
        
        // 移除靜態變數，改為依賴 WindowManager
        print("🎬 PlayerWindow \(id) 初始化完成")
    }
    
    // MARK: - 完全重建 PlayerManager (模擬應用重啟)
    private func recreatePlayerManager() {
        print("🔄 完全重建 PlayerManager 實例 (模擬應用重啟)")
        
        // 保存當前的用戶設定
        let currentVolume = playerManager.volume
        let currentMuted = playerManager.isMuted
        let currentPlaybackRate = playerManager.playbackRate
        
        // 完全銷毀舊的 PlayerManager
        playerManager.cleanup()  // 如果有清理方法的話
        
        // 創建全新的 PlayerManager 實例
        playerManager = PlayerManager()
        
        // 重新設置回調和配置
        setupPlayerManager()
        
        // 恢復用戶設定
        playerManager.volume = currentVolume
        playerManager.isMuted = currentMuted
        playerManager.playbackRate = currentPlaybackRate
        
        print("✅ PlayerManager 完全重建完成 - 全新實例")
    }
    
    func loadMedia(url: URL) {
        print("🎥 PlayerWindow 開始載入媒體: \(url.lastPathComponent)")
        print("📊 當前播放清單位置: \(currentIndex)/\(playlist.count)")
        
        // 佇列模式：重建 PlayerManager 但改為用 AVQueuePlayer 管理佇列
        recreatePlayerManager()
        playerManager.isLoading = true
        playerManager.errorMessage = nil

        playerManager.loadMedia(from: url, completion: { [weak self] (success: Bool) in
            guard let self = self else { return }
            print("🎥 PlayerManager 載入結果: \(success) for \(url.lastPathComponent)")
            if success {
                self.windowManager?.addToRecentFiles(url)
                // 將下一首加入佇列，讓 AVQueuePlayer 自動前進
                if let nextURL = self.getNextMediaURL() {
                    self.playerManager.enqueueNext(url: nextURL)
                }
                print("✅ 媒體載入成功，下一首已加入佇列（若存在）")
            } else {
                print("❌ 媒體載入失敗: \(url.lastPathComponent)")
            }
        })
    }
    
    // 預載入下一個媒體
    private func preloadNextMedia() {
        if let nextURL = getNextMediaURL() {
            print("🚀 開始預載入下一個媒體: \(nextURL.lastPathComponent)")
            playerManager.preloadNextMedia(nextURL)
        }
    }
    
    // 獲取下一個媒體 URL
    private func getNextMediaURL() -> URL? {
        let nextIndex = currentIndex + 1
        guard nextIndex < playlist.count else { return nil }
        return playlist[nextIndex]
    }

    // 與 AVQueuePlayer advance 同步 currentIndex（保守增一）
    private func syncIndexAfterAdvance() {
        guard !playlist.isEmpty else { return }
        let old = currentIndex
        currentIndex = (currentIndex + 1) % playlist.count
        print("🔁 同步索引: \(old) → \(currentIndex)")
    }

    // 在佇列模式下，保持下一首已排入佇列
    private func enqueueNextAfterCurrent() {
        guard let nextURL = getNextMediaURL() else { return }
        print("📥 保持佇列：加入下一首 \(nextURL.lastPathComponent)")
        playerManager.enqueueNext(url: nextURL)
    }
    
    func addToPlaylist(url: URL) {
        if !playlist.contains(url) {
            playlist.append(url)
            
            // 如果這是第一個檔案，立即預載入
            if playlist.count == 1 {
                print("🚀 立即預載入首個媒體: \(url.lastPathComponent)")
                playerManager.preloadNextMedia(url)
            }
            
            // 預熱檔案格式 - 確保每種格式都被預熱
            let fileExtension = url.pathExtension.lowercased()
            print("📝 播放清單新增檔案: \(url.lastPathComponent) (.\(fileExtension))")
        }
    }
    
    func playNext() {
        guard !playlist.isEmpty else {
            print("📝 播放清單為空，無法播放下一首")
            return
        }

        let oldIndex = currentIndex
        currentIndex = (currentIndex + 1) % playlist.count
        let nextURL = playlist[currentIndex]

        print("▶️ 自動播放下一首: \(oldIndex) → \(currentIndex), 檔案: \(nextURL.lastPathComponent)")
        // 佇列模式：不要重建播放器，僅保險把下一首再插佇列（避免空佇列）
        if playerManager.isQueueMode {
            playerManager.enqueueNext(url: nextURL)
        } else {
            loadMedia(url: nextURL)
        }
    }
    
    func playPrevious() {
        guard !playlist.isEmpty else { return }
        
        currentIndex = currentIndex > 0 ? currentIndex - 1 : playlist.count - 1
        let previousURL = playlist[currentIndex]
        loadMedia(url: previousURL)
    }
}

// MARK: - Window Manager
class WindowManager: ObservableObject {
    static let shared = WindowManager()
    
    internal init() {}
    
    private var windowControllers: [UUID: MediaPlayerWindowController] = [:]
    var windows: [PlayerWindow] {
        return windowControllers.values.map { $0.playerWindow }
    }
    @Published private(set) var windowCount: Int = 0
    @Published private(set) var recentFiles: [URL] = []
    
    func createNewWindow() -> PlayerWindow {
        let playerWindow = PlayerWindow(windowManager: self)
        let controller = MediaPlayerWindowController(playerWindow: playerWindow)
        
        windowControllers[playerWindow.id] = controller
        windowCount = windowControllers.count
        
        controller.showWindow(nil)
        return playerWindow
    }
    
    func createNewWindowWithMedia(url: URL) {
        let playerWindow = createNewWindow()
        playerWindow.addToPlaylist(url: url)
        playerWindow.loadMedia(url: url)
        addToRecentFiles(url)
    }
    
    func windowWillClose(_ controller: MediaPlayerWindowController) {
        let id = controller.playerWindow.id
        // 只有當視窗還在管理器中時才處理
        if windowControllers[id] != nil {
            windowControllers.removeValue(forKey: id)
            windowCount = windowControllers.count
        }
    }
    
    func addToRecentFiles(_ url: URL) {
        // 避免重複
        if let index = recentFiles.firstIndex(where: { $0.absoluteString == url.absoluteString }) {
            recentFiles.remove(at: index)
        }
        
        // 添加到開頭
        recentFiles.insert(url, at: 0)
        
        // 保持最近記錄在10個以內
        if recentFiles.count > 10 {
            recentFiles.removeLast()
        }
    }
    
    func closeWindowWithId(_ id: UUID) {
        if let controller = windowControllers[id] {
            // 先從集合中移除，避免 windowWillClose 重複處理
            windowControllers.removeValue(forKey: id)
            windowCount = windowControllers.count
            
            // 然後關閉視窗
            controller.close()
        }
    }
    
    func showFileImporter() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = true
        panel.canChooseDirectories = false
        panel.canChooseFiles = true
        panel.allowedContentTypes = PlayerManager.supportedTypes
        
        if panel.runModal() == .OK {
            for url in panel.urls {
                createNewWindowWithMedia(url: url)
            }
        }
    }

    func togglePlayPause() {
        // 控制當前活躍視窗的播放狀態
        guard let keyWindow = NSApplication.shared.keyWindow,
              let controller = windowControllers.values.first(where: { $0.window === keyWindow }) else {
            return
        }
        
        controller.playerWindow.playerManager.togglePlayPause()
    }

    func toggleMute() {
        // 控制當前活躍視窗的靜音狀態
        guard let keyWindow = NSApplication.shared.keyWindow,
              let controller = windowControllers.values.first(where: { $0.window === keyWindow }) else {
            return
        }
        
        controller.playerWindow.playerManager.toggleMute()
    }

    func toggleFullscreen() {
        // 控制當前活躍視窗的全螢幕狀態
        guard let keyWindow = NSApplication.shared.keyWindow else { return }
        keyWindow.toggleFullScreen(nil)
    }
    
    func clearRecentFiles() {
        recentFiles.removeAll()
    }
    
    func getController(for playerWindow: PlayerWindow) -> MediaPlayerWindowController? {
        return windowControllers[playerWindow.id]
    }
}