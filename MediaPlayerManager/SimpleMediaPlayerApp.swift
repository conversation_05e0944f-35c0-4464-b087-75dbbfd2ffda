import SwiftUI
import AVKit
import UniformTypeIdentifiers

// 備用的簡單播放器實現
struct SimpleMediaPlayerApp: App {
    var body: some Scene {
        WindowGroup {
            SimpleMediaPlayerView()
                .frame(minWidth: 800, minHeight: 600)
        }
    }
}

struct SimpleMediaPlayerView: View {
    @State private var player: AVPlayer?
    @State private var isShowingFilePicker = false
    
    var body: some View {
        VStack {
            if let player = player {
                VideoPlayer(player: player)
                    .onAppear {
                        player.play()
                    }
            } else {
                VStack(spacing: 20) {
                    Image(systemName: "play.circle")
                        .font(.system(size: 80))
                        .foregroundColor(.blue)
                    
                    Text("MediaPro 播放器")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("選擇媒體檔案開始播放")
                        .foregroundColor(.secondary)
                    
                    Button("選擇檔案") {
                        isShowingFilePicker = true
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
        }
        .fileImporter(
            isPresented: $isShowingFilePicker,
            allowedContentTypes: PlayerManager.supportedTypes,
            allowsMultipleSelection: false
        ) { result in
            switch result {
            case .success(let urls):
                if let url = urls.first {
                    player = AVPlayer(url: url)
                }
            case .failure(let error):
                print("選擇檔案失敗: \(error)")
            }
        }
        .onDrop(of: [.fileURL], isTargeted: nil) { providers in
            return handleDroppedFiles(providers)
        }
    }
    
    private func handleDroppedFiles(_ providers: [NSItemProvider]) -> Bool {
        for provider in providers {
            provider.loadItem(forTypeIdentifier: "public.file-url", options: nil) { (item, error) in
                if let data = item as? Data,
                   let url = URL(dataRepresentation: data, relativeTo: nil) {
                    DispatchQueue.main.async {
                        player = AVPlayer(url: url)
                    }
                }
            }
        }
        return true
    }
}