//
//  WebMPlayer.swift
//  MediaPlayerManager
//
//  WebM 播放器 - 使用 WKWebView 支援 WebM 格式
//

import SwiftUI
import WebKit

// MARK: - WebM Player View
struct WebMPlayerView: View {
    let url: URL
    @State private var isLoading = true
    @State private var errorMessage: String?
    
    var body: some View {
        VStack {
            if let errorMessage = errorMessage {
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 48))
                        .foregroundColor(.orange)
                    
                    Text("WebM 播放錯誤")
                        .font(.headline)
                    
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black)
            } else {
                ZStack {
                    WebMWebView(url: url, isLoading: $isLoading, errorMessage: $errorMessage)
                        .background(Color.black)
                    
                    if isLoading {
                        ProgressView("載入 WebM 檔案...")
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.black.opacity(0.8))
                            .cornerRadius(10)
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black)
    }
}

// MARK: - WebView Wrapper
struct WebMWebView: NSViewRepresentable {
    let url: URL
    @Binding var isLoading: Bool
    @Binding var errorMessage: String?
    
    func makeNSView(context: Context) -> WKWebView {
        let configuration = WKWebViewConfiguration()
        configuration.mediaTypesRequiringUserActionForPlayback = []
        
        let webView = WKWebView(frame: .zero, configuration: configuration)
        webView.navigationDelegate = context.coordinator
        
        // 創建 HTML 頁面來播放 WebM
        let html = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    background: black;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    overflow: hidden;
                }
                video {
                    max-width: 100%;
                    max-height: 100%;
                    object-fit: contain;
                }
            </style>
        </head>
        <body>
            <video controls autoplay>
                <source src="file://\(url.path)" type="video/webm">
                無法播放 WebM 檔案。
            </video>
        </body>
        </html>
        """
        
        webView.loadHTMLString(html, baseURL: url.deletingLastPathComponent())
        return webView
    }
    
    func updateNSView(_ nsView: WKWebView, context: Context) {
        // 不需要更新
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate {
        let parent: WebMWebView
        
        init(_ parent: WebMWebView) {
            self.parent = parent
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            parent.isLoading = false
        }
        
        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            parent.isLoading = false
            parent.errorMessage = "無法載入 WebM 檔案: \(error.localizedDescription)"
        }
        
        func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
            parent.isLoading = false
            parent.errorMessage = "無法載入 WebM 檔案: \(error.localizedDescription)"
        }
    }
}

// MARK: - WebM Detection Helper
extension URL {
    var isWebM: Bool {
        return self.pathExtension.lowercased() == "webm"
    }
}