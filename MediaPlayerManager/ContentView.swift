//
//  ContentView.swift
//  MediaPlayerManager
//
//  Refactored - Main content view with core functionality
//

import SwiftUI
import AVKit
import UniformTypeIdentifiers
import Combine
import AppKit

// MARK: - View Extensions
extension View {
    @ViewBuilder
    func `if`<Transform: View>(_ condition: Bool, transform: (Self) -> Transform) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
}

// MARK: - Main Media Player Manager View
struct MediaPlayerManagerView: View {
    @StateObject private var windowManager = WindowManager.shared
    @StateObject private var playlistManager = PlaylistManager.shared
    @State private var isShowingFileImporter = false
    @State private var isShowingFolderImporter = false
    @State private var isShowingRecentFiles = false
    @State private var selectedFolder: URL?
    @State private var folderMediaFiles: [URL] = []
    
    // 使用 NSOpenPanel 開啟檔案
    private func showFilePickerNative() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = true
        panel.canChooseDirectories = false
        panel.canChooseFiles = true
        panel.allowedContentTypes = PlayerManager.supportedTypes
        panel.title = "選擇媒體檔案"
        panel.message = "選擇一個或多個媒體檔案來播放"
        
        panel.begin { response in
            if response == .OK {
                print("📁 NSOpenPanel 成功選擇 \(panel.urls.count) 個檔案")
                for url in panel.urls {
                    print("📄 處理檔案: \(url.lastPathComponent)")
                    
                    // 快速檢查檔案存在性
                    guard FileManager.default.fileExists(atPath: url.path) else {
                        print("❌ 檔案不存在: \(url.lastPathComponent)")
                        continue
                    }
                    
                    // 創建視窗並載入媒體
                    windowManager.createNewWindowWithMedia(url: url)
                }
            } else {
                print("❌ 使用者取消檔案選擇")
            }
        }
    }
    
    // 使用 NSOpenPanel 開啟資料夾
    private func showFolderPickerNative() {
        let panel = NSOpenPanel()
        panel.allowsMultipleSelection = false
        panel.canChooseDirectories = true
        panel.canChooseFiles = false
        panel.title = "選擇資料夾"
        panel.message = "選擇包含媒體檔案的資料夾"
        
        panel.begin { response in
            if response == .OK, let folderURL = panel.url {
                print("📁 NSOpenPanel 選擇資料夾: \(folderURL.path)")
                selectedFolder = folderURL
                scanFolderForMediaFiles(folderURL)
            } else {
                print("❌ 使用者取消資料夾選擇")
            }
        }
    }
    
    // 媒體檔案過濾器
    private func isMediaFile(_ url: URL) -> Bool {
        let videoExtensions = [
            // 常見視頻格式
            "mp4", "m4v", "mov", "qt", "avi", "mkv", "webm", "flv", "wmv", "asf",
            // 高清視頻格式
            "mts", "m2ts", "ts", "vob", "ogv", "ogg", "dv", "3gp", "3g2",
            // 專業視頻格式
            "mxf", "r3d", "rm", "rmvb", "f4v", "swf", "divx", "xvid"
        ]
        
        let audioExtensions = [
            // 常見音頻格式
            "mp3", "m4a", "aac", "wav", "flac", "aiff", "au", "m4b", "m4p", "m4r",
            // 無損音頻格式
            "alac", "ape", "wv", "tta", "dts", "ac3", "eac3",
            // 其他音頻格式
            "ogg", "oga", "opus", "wma", "ra", "amr", "3ga", "caf", "sd2"
        ]
        
        let allExtensions = videoExtensions + audioExtensions
        return allExtensions.contains(url.pathExtension.lowercased())
    }
    
    var body: some View {
        ZStack {
            // 主背景
            AppTheme.backgroundGradient
                .ignoresSafeArea()
            
            VStack(spacing: 0) {
                // 頂部控制欄
                TopControlBar(
                    windowManager: windowManager,
                    isShowingFileImporter: $isShowingFileImporter,
                    isShowingFolderImporter: $isShowingFolderImporter,
                    isShowingRecentFiles: $isShowingRecentFiles,
                    onFilePickerRequested: showFilePickerNative,
                    onFolderPickerRequested: showFolderPickerNative
                )
                
                // 主內容區域
                if windowManager.windows.isEmpty {
                    // 歡迎頁面
                    WelcomeView(
                        playerWindow: createDefaultPlayerWindow(),
                        onFileImportRequested: {
                            print("🔘 WelcomeView 請求開啟檔案選擇器")
                            showFilePickerNative()
                        }
                    )
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    // 視窗管理區域
                    WindowsOverviewView(windowManager: windowManager)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
            }
            
            // 彈出視窗覆蓋層
            if isShowingRecentFiles {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                    .onTapGesture {
                        isShowingRecentFiles = false
                    }
                
                RecentFilesListView(
                    windowManager: windowManager,
                    isShowingRecentFiles: $isShowingRecentFiles
                )
            }
        }
        .onAppear {
            print("📂 支持的檔案類型數量: \(PlayerManager.supportedTypes.count)")
            print("📂 支持的檔案類型: \(PlayerManager.supportedTypes.map { $0.preferredFilenameExtension ?? $0.identifier })")
        }
    }
    
    private func createDefaultPlayerWindow() -> PlayerWindow {
        return PlayerWindow(windowManager: windowManager)
    }
    
    private func scanFolderForMediaFiles(_ folderURL: URL) {
        do {
            let fileManager = FileManager.default
            let contents = try fileManager.contentsOfDirectory(
                at: folderURL,
                includingPropertiesForKeys: [.isRegularFileKey],
                options: [.skipsHiddenFiles]
            )
            
            folderMediaFiles = contents.filter { isMediaFile($0) }
            
            // 自動為每個媒體檔案創建視窗
            for mediaFile in folderMediaFiles.prefix(5) { // 限制前5個檔案避免創建太多視窗
                windowManager.createNewWindowWithMedia(url: mediaFile)
            }
        } catch {
            print("掃描資料夾失敗: \(error)")
        }
    }
}

// MARK: - Top Control Bar
struct TopControlBar: View {
    @ObservedObject var windowManager: WindowManager
    @Binding var isShowingFileImporter: Bool
    @Binding var isShowingFolderImporter: Bool  
    @Binding var isShowingRecentFiles: Bool
    var onFilePickerRequested: (() -> Void)?
    var onFolderPickerRequested: (() -> Void)?
    
    var body: some View {
        HStack(spacing: 16) {
            // Logo 和標題
            HStack(spacing: 12) {
                Image(systemName: "play.circle.fill")
                    .font(.system(size: 24))
                    .foregroundColor(AppTheme.primaryColor)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("MediaPro")
                        .font(.system(size: 18, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                    
                    Text("\(windowManager.windowCount) 個視窗")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            
            Spacer()
            
            // 控制按鈕
            HStack(spacing: 12) {
                Button(action: { isShowingRecentFiles.toggle() }) {
                    Label("最近播放", systemImage: "clock")
                        .font(.system(size: 14))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.white.opacity(0.1))
                        .clipShape(Capsule())
                }
                .buttonStyle(PlainButtonStyle())
                
                Button(action: { 
                    print("🔘 匯入資料夾按鈕被點擊")
                    onFolderPickerRequested?()
                }) {
                    Label("匯入資料夾", systemImage: "folder")
                        .font(.system(size: 14))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.white.opacity(0.1))
                        .clipShape(Capsule())
                }
                .buttonStyle(PlainButtonStyle())
                
                Button(action: { 
                    print("🔘 開啟檔案按鈕被點擊")
                    onFilePickerRequested?()
                }) {
                    Label("開啟檔案", systemImage: "plus.circle")
                        .font(.system(size: 14))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(AppTheme.primaryColor)
                        .clipShape(Capsule())
                }
                .buttonStyle(PlainButtonStyle())
                
                Button(action: { _ = windowManager.createNewWindow() }) {
                    Label("新視窗", systemImage: "plus.square")
                        .font(.system(size: 14))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.white.opacity(0.1))
                        .clipShape(Capsule())
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(Color.black.opacity(0.3))
    }
}

// MARK: - Windows Overview View  
struct WindowsOverviewView: View {
    @ObservedObject var windowManager: WindowManager
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: 300, maximum: 400), spacing: 16)
            ], spacing: 16) {
                ForEach(windowManager.windows) { playerWindow in
                    WindowPreviewCard(playerWindow: playerWindow, windowManager: windowManager)
                }
            }
            .padding()
        }
    }
}

// MARK: - Window Preview Card
struct WindowPreviewCard: View {
    @ObservedObject var playerWindow: PlayerWindow
    @ObservedObject var windowManager: WindowManager
    
    var body: some View {
        VStack(spacing: 12) {
            // 預覽區域
            ZStack {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.black.opacity(0.8))
                    .aspectRatio(16/9, contentMode: .fit)
                
                if playerWindow.playerManager.player != nil {
                    // 使用 VideoPlayer 的簡化版本作為預覽
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black)
                        .overlay(
                            Image(systemName: "play.circle.fill")
                                .font(.system(size: 48))
                                .foregroundColor(.white.opacity(0.6))
                        )
                        .aspectRatio(16/9, contentMode: .fit)
                } else {
                    Image(systemName: "play.rectangle")
                        .font(.system(size: 48))
                        .foregroundColor(.white.opacity(0.3))
                }
            }
            
            // 資訊區域
            VStack(alignment: .leading, spacing: 8) {
                if let currentURL = playerWindow.playlist.first {
                    Text(currentURL.lastPathComponent)
                        .font(.headline)
                        .foregroundColor(.white)
                        .lineLimit(2)
                } else {
                    Text("空白視窗")
                        .font(.headline)
                        .foregroundColor(.white.opacity(0.7))
                }
                
                HStack {
                    Text("播放清單: \(playerWindow.playlist.count) 項")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                    
                    Spacer()
                    
                    if playerWindow.playerManager.isPlaying {
                        HStack(spacing: 4) {
                            Image(systemName: "play.fill")
                                .font(.caption)
                                .foregroundColor(.green)
                            Text("播放中")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                    }
                }
                
                // 操作按鈕
                HStack(spacing: 8) {
                    Button("顯示") {
                        // 聚焦到對應視窗
                        if let controller = windowManager.getController(for: playerWindow) {
                            controller.showWindow(nil)
                            controller.window?.makeKeyAndOrderFront(nil)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.small)
                    
                    Button("關閉") {
                        windowManager.closeWindowWithId(playerWindow.id)
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                    
                    Spacer()
                }
            }
            .padding(.horizontal, 12)
            .padding(.bottom, 12)
        }
        .background(AppTheme.cardColor)
        .clipShape(RoundedRectangle(cornerRadius: 16))
        .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
    }
}

// MARK: - ContentView (for compatibility)
struct ContentView: View {
    var body: some View {
        MediaPlayerManagerView()
    }
}


#Preview {
    ContentView()
}