import SwiftUI
import AVKit
import UniformTypeIdentifiers
import Combine
import AppKit

// MARK: - 增強版播放器視窗
struct EnhancedPlayerWindowView: View {
    @ObservedObject var playerWindow: PlayerWindow
    @StateObject private var playerState = EnhancedPlayerState()
    @State private var isShowingFileImporter = false
    @State private var isShowingPlaylist = false
    @State private var isShowingSettings = false
    @State private var controlsAutoHideTimer: Timer?
    @FocusState private var isFocused: Bool
    
    var body: some View {
        ZStack {
            // 主播放區域
            GeometryReader { geometry in
                ZStack {
                    // 背景
                    AppTheme.backgroundColor
                        .ignoresSafeArea()
                    
                    // 視頻/音頻播放區域
                    PlayerContentView(
                        playerWindow: playerWindow,
                        playerState: playerState,
                        geometry: geometry
                    )
                    
                    // 頂部工具列
                    VStack {
                        if playerState.showControls {
                            TopToolbarView(
                                playerWindow: playerWindow,
                                playerState: playerState,
                                isShowingFileImporter: $isShowingFileImporter,
                                isShowingPlaylist: $isShowingPlaylist,
                                isShowingSettings: $isShowingSettings
                            )
                            .transition(.move(edge: .top).combined(with: .opacity))
                        }
                        
                        Spacer()
                    }
                    
                    // 底部控制項
                    VStack {
                        Spacer()
                        
                        if playerState.showControls {
                            EnhancedPlayerControls(
                                playerWindow: playerWindow,
                                playerManager: playerWindow.playerManager,
                                playerState: playerState
                            )
                            .transition(.move(edge: .bottom).combined(with: .opacity))
                        }
                    }
                    
                    // 播放清單側邊欄
                    if isShowingPlaylist {
                        HStack {
                            Spacer()
                            
                            EnhancedPlaylistView(playerWindow: playerWindow)
                                .frame(width: 320)
                                .transition(.move(edge: .trailing))
                        }
                    }
                    
                    // 載入指示器
                    if playerWindow.playerManager.isLoading {
                        LoadingView()
                    }
                    
                    // 音量指示器
                    if playerState.showVolumeIndicator {
                        VolumeIndicatorView(volume: playerWindow.playerManager.volume)
                            .transition(.opacity)
                    }
                    
                    // 播放速度指示器
                    if playerState.showSpeedIndicator {
                        SpeedIndicatorView(speed: playerWindow.playerManager.playbackRate)
                            .transition(.opacity)
                    }
                }
            }
        }
        .focused($isFocused)
        .onAppear {
            isFocused = true
            playerState.startControlsTimer()
        }
        .onDisappear {
            playerState.cleanup()
        }
        .onTapGesture {
            playerState.toggleControls()
        }
        .onKeyPress { press in
            handleKeyPress(press)
        }
        .fileImporter(
            isPresented: $isShowingFileImporter,
            allowedContentTypes: PlayerManager.supportedTypes,
            allowsMultipleSelection: true
        ) { result in
            handleFileImport(result)
        }
        .animation(.easeInOut(duration: 0.3), value: playerState.showControls)
        .animation(.easeInOut(duration: 0.3), value: isShowingPlaylist)
    }
    
    // MARK: - 鍵盤處理
    private func handleKeyPress(_ press: KeyPress) -> KeyPress.Result {
        guard isFocused else { return .ignored }
        
        playerState.userInteracted()
        
        switch press.key {
        case .space:
            playerWindow.playerManager.togglePlayPause()
            return .handled
            
        case .leftArrow:
            let newTime = max(0, playerWindow.playerManager.currentTime - 10)
            playerWindow.playerManager.seek(to: newTime)
            return .handled
            
        case .rightArrow:
            let newTime = min(playerWindow.playerManager.duration, playerWindow.playerManager.currentTime + 10)
            playerWindow.playerManager.seek(to: newTime)
            return .handled
            
        case .upArrow:
            let newVolume = min(1.0, playerWindow.playerManager.volume + 0.05)
            playerWindow.playerManager.volume = newVolume
            playerState.showVolumeIndicator(for: 1.0)
            return .handled
            
        case .downArrow:
            let newVolume = max(0.0, playerWindow.playerManager.volume - 0.05)
            playerWindow.playerManager.volume = newVolume
            playerState.showVolumeIndicator(for: 1.0)
            return .handled
            
        default:
            let character = press.key.character
            switch character {
            case "f":
                playerWindow.toggleFullScreen()
                return .handled
            case "m":
                playerWindow.playerManager.toggleMute()
                return .handled
            case "p":
                isShowingPlaylist.toggle()
                return .handled
            case "s":
                isShowingSettings.toggle()
                return .handled
            case "o":
                isShowingFileImporter = true
                return .handled
            default:
                return .ignored
            }
        }
    }
    
    // MARK: - 檔案匯入處理
    private func handleFileImport(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            for url in urls {
                playerWindow.addToPlaylist(url: url)
                WindowManager.shared.addToRecentFiles(url)
            }
            
            if let firstURL = urls.first, playerWindow.mediaURL == nil {
                playerWindow.loadMedia(url: firstURL)
            }
            
        case .failure(let error):
            print("選擇檔案失敗: \(error.localizedDescription)")
        }
    }
}

// MARK: - 增強版播放器狀態管理
class EnhancedPlayerState: ObservableObject {
    @Published var showControls = true
    @Published var showVolumeIndicator = false
    @Published var showSpeedIndicator = false
    @Published var lastInteractionTime = Date()
    
    private var controlsTimer: Timer?
    private var volumeTimer: Timer?
    private var speedTimer: Timer?
    
    func userInteracted() {
        lastInteractionTime = Date()
        showControls = true
        startControlsTimer()
    }
    
    func toggleControls() {
        showControls.toggle()
        if showControls {
            userInteracted()
        }
    }
    
    func showVolumeIndicator(for duration: TimeInterval) {
        showVolumeIndicator = true
        volumeTimer?.invalidate()
        volumeTimer = Timer.scheduledTimer(withTimeInterval: duration, repeats: false) { _ in
            DispatchQueue.main.async {
                self.showVolumeIndicator = false
            }
        }
    }
    
    func showSpeedIndicator(for duration: TimeInterval) {
        showSpeedIndicator = true
        speedTimer?.invalidate()
        speedTimer = Timer.scheduledTimer(withTimeInterval: duration, repeats: false) { _ in
            DispatchQueue.main.async {
                self.showSpeedIndicator = false
            }
        }
    }
    
    func startControlsTimer() {
        controlsTimer?.invalidate()
        controlsTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
            DispatchQueue.main.async {
                if Date().timeIntervalSince(self.lastInteractionTime) > 3.0 {
                    withAnimation {
                        self.showControls = false
                    }
                }
            }
        }
    }
    
    func cleanup() {
        controlsTimer?.invalidate()
        volumeTimer?.invalidate()
        speedTimer?.invalidate()
    }
}

// MARK: - 播放器內容視圖
struct PlayerContentView: View {
    @ObservedObject var playerWindow: PlayerWindow
    @ObservedObject var playerState: EnhancedPlayerState
    let geometry: GeometryProxy
    
    var body: some View {
        ZStack {
            if let player = playerWindow.playerManager.player, let url = playerWindow.mediaURL {
                if MediaInfoView.isVideoFile(url: url) {
                    // 視頻播放器
                    VideoPlayer(player: player)
                        .ignoresSafeArea()
                        .onAppear {
                            player.play()
                        }
                } else {
                    // 音頻可視化界面
                    AudioVisualizationView(url: url, player: player)
                }
                
                // 媒體資訊覆疊
                if playerState.showControls {
                    VStack {
                        HStack {
                            MediaInfoOverlay(url: url)
                            Spacer()
                        }
                        Spacer()
                    }
                    .padding()
                    .transition(.opacity)
                }
                
            } else {
                // 空狀態
                EmptyPlayerView {
                    // 觸發檔案選擇
                }
            }
        }
        .onTapGesture {
            playerState.toggleControls()
        }
    }
}

// MARK: - 頂部工具列
struct TopToolbarView: View {
    @ObservedObject var playerWindow: PlayerWindow
    @ObservedObject var playerState: EnhancedPlayerState
    @Binding var isShowingFileImporter: Bool
    @Binding var isShowingPlaylist: Bool
    @Binding var isShowingSettings: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            // 左側按鈕組
            HStack(spacing: 8) {
                ToolbarButton(icon: "folder", tooltip: "開啟檔案 (O)") {
                    isShowingFileImporter = true
                    playerState.userInteracted()
                }
                
                ToolbarButton(icon: "list.bullet", tooltip: "播放清單 (P)") {
                    isShowingPlaylist.toggle()
                    playerState.userInteracted()
                }
                
                ToolbarButton(icon: "gearshape", tooltip: "設定 (S)") {
                    isShowingSettings.toggle()
                    playerState.userInteracted()
                }
            }
            
            Spacer()
            
            // 視窗標題
            if let url = playerWindow.mediaURL {
                Text(url.lastPathComponent)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppTheme.primaryText)
                    .lineLimit(1)
            }
            
            Spacer()
            
            // 右側按鈕組
            HStack(spacing: 8) {
                ToolbarButton(icon: "plus.rectangle.on.rectangle", tooltip: "新視窗") {
                    WindowManager.shared.createNewWindow()
                    playerState.userInteracted()
                }
                
                ToolbarButton(
                    icon: playerWindow.isFullScreen ? "arrow.down.right.and.arrow.up.left" : "arrow.up.left.and.arrow.down.right",
                    tooltip: playerWindow.isFullScreen ? "退出全螢幕 (F)" : "全螢幕 (F)"
                ) {
                    playerWindow.toggleFullScreen()
                    playerState.userInteracted()
                }
                
                ToolbarButton(icon: "xmark", tooltip: "關閉", color: .red) {
                    WindowManager.shared.closeWindowWithId(playerWindow.id)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            Rectangle()
                .fill(.ultraThinMaterial)
                .ignoresSafeArea()
        )
    }
}

// MARK: - 工具列按鈕
struct ToolbarButton: View {
    let icon: String
    let tooltip: String
    let color: Color
    let action: () -> Void
    
    @State private var isHovered = false
    
    init(icon: String, tooltip: String, color: Color = AppTheme.primaryColor, action: @escaping () -> Void) {
        self.icon = icon
        self.tooltip = tooltip
        self.color = color
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(isHovered ? .white : AppTheme.secondaryText)
                .frame(width: 32, height: 32)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(isHovered ? color : Color.clear)
                )
                .scaleEffect(isHovered ? 1.1 : 1.0)
                .animation(.easeInOut(duration: 0.2), value: isHovered)
        }
        .buttonStyle(PlainButtonStyle())
        .help(tooltip)
        .onHover { hovering in
            isHovered = hovering
        }
    }
}

// MARK: - 增強版播放控制項
struct EnhancedPlayerControls: View {
    @ObservedObject var playerWindow: PlayerWindow
    @ObservedObject var playerManager: PlayerManager
    @ObservedObject var playerState: EnhancedPlayerState
    
    var body: some View {
        VStack(spacing: 16) {
            // 進度條區域
            ProgressBarView(
                playerManager: playerManager,
                playerState: playerState
            )
            
            // 主控制項
            HStack(spacing: 24) {
                // 左側控制項
                HStack(spacing: 16) {
                    VolumeControlView(
                        playerManager: playerManager,
                        playerState: playerState
                    )
                }
                
                Spacer()
                
                // 中央播放控制項
                PlaybackControlsView(
                    playerWindow: playerWindow,
                    playerManager: playerManager,
                    playerState: playerState
                )
                
                Spacer()
                
                // 右側控制項
                HStack(spacing: 16) {
                    PlaybackSpeedMenu(
                        playerManager: playerManager,
                        playerState: playerState
                    )
                    
                    QualitySettingsMenu()
                }
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .shadow(color: .black.opacity(0.3), radius: 20, x: 0, y: 10)
        )
        .padding(.horizontal, 20)
        .padding(.bottom, 20)
    }
}

// MARK: - 進度條視圖
struct ProgressBarView: View {
    @ObservedObject var playerManager: PlayerManager
    @ObservedObject var playerState: EnhancedPlayerState
    
    @State private var isDragging = false
    @State private var dragValue: Double = 0
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Text(formatTime(isDragging ? dragValue : playerManager.currentTime))
                    .font(.system(size: 12, weight: .medium, design: .monospaced))
                    .foregroundColor(AppTheme.secondaryText)
                    .frame(width: 60, alignment: .leading)
                
                // 自定義進度條
                GeometryReader { geometry in
                    ZStack(alignment: .leading) {
                        // 背景軌道
                        RoundedRectangle(cornerRadius: 2)
                            .fill(Color.white.opacity(0.3))
                            .frame(height: 4)
                        
                        // 進度軌道
                        RoundedRectangle(cornerRadius: 2)
                            .fill(
                                LinearGradient(
                                    colors: [AppTheme.primaryColor, AppTheme.accentColor],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(
                                width: geometry.size.width * CGFloat((isDragging ? dragValue : playerManager.currentTime) / max(playerManager.duration, 1)),
                                height: 4
                            )
                        
                        // 拖拽指示器
                        Circle()
                            .fill(Color.white)
                            .frame(width: isDragging ? 16 : 12, height: isDragging ? 16 : 12)
                            .offset(x: geometry.size.width * CGFloat((isDragging ? dragValue : playerManager.currentTime) / max(playerManager.duration, 1)) - (isDragging ? 8 : 6))
                            .animation(.easeInOut(duration: 0.2), value: isDragging)
                    }
                }
                .frame(height: 20)
                .gesture(
                    DragGesture()
                        .onChanged { value in
                            isDragging = true
                            let progress = min(max(value.location.x / value.startLocation.x, 0), 1)
                            dragValue = progress * playerManager.duration
                            playerState.userInteracted()
                        }
                        .onEnded { value in
                            isDragging = false
                            playerManager.seek(to: dragValue)
                        }
                )
                
                Text(formatTime(playerManager.duration))
                    .font(.system(size: 12, weight: .medium, design: .monospaced))
                    .foregroundColor(AppTheme.secondaryText)
                    .frame(width: 60, alignment: .trailing)
            }
        }
    }
    
    private func formatTime(_ timeInSeconds: Double) -> String {
        let hours = Int(timeInSeconds) / 3600
        let minutes = Int(timeInSeconds) / 60 % 60
        let seconds = Int(timeInSeconds) % 60
        
        if hours > 0 {
            return String(format: "%d:%02d:%02d", hours, minutes, seconds)
        } else {
            return String(format: "%02d:%02d", minutes, seconds)
        }
    }
}

// MARK: - 音量控制視圖
struct VolumeControlView: View {
    @ObservedObject var playerManager: PlayerManager
    @ObservedObject var playerState: EnhancedPlayerState
    
    var body: some View {
        HStack(spacing: 12) {
            Button(action: {
                playerManager.toggleMute()
                playerState.userInteracted()
            }) {
                Image(systemName: playerManager.isMuted ? "speaker.slash.fill" : volumeIcon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(width: 24, height: 24)
            }
            .buttonStyle(PlainButtonStyle())
            
            Slider(
                value: Binding(
                    get: { playerManager.isMuted ? 0 : playerManager.volume },
                    set: { value in
                        playerManager.volume = value
                        if playerManager.isMuted && value > 0 {
                            playerManager.isMuted = false
                        }
                        playerState.showVolumeIndicator(for: 1.0)
                        playerState.userInteracted()
                    }
                ),
                in: 0...1
            )
            .frame(width: 80)
            .accentColor(AppTheme.primaryColor)
        }
    }
    
    private var volumeIcon: String {
        if playerManager.volume < 0.33 {
            return "speaker.wave.1.fill"
        } else if playerManager.volume < 0.66 {
            return "speaker.wave.2.fill"
        } else {
            return "speaker.wave.3.fill"
        }
    }
}

// MARK: - 播放控制視圖
struct PlaybackControlsView: View {
    @ObservedObject var playerWindow: PlayerWindow
    @ObservedObject var playerManager: PlayerManager
    @ObservedObject var playerState: EnhancedPlayerState
    
    var body: some View {
        HStack(spacing: 20) {
            // 上一首
            ControlButton(icon: "backward.fill", size: 20) {
                playerWindow.playPrevious()
                playerState.userInteracted()
            }
            
            // 快退
            ControlButton(icon: "gobackward.10", size: 20) {
                let newTime = max(0, playerManager.currentTime - 10)
                playerManager.seek(to: newTime)
                playerState.userInteracted()
            }
            
            // 播放/暫停
            ControlButton(
                icon: playerManager.isPlaying ? "pause.fill" : "play.fill",
                size: 24,
                isMain: true
            ) {
                playerManager.togglePlayPause()
                playerState.userInteracted()
            }
            
            // 快進
            ControlButton(icon: "goforward.10", size: 20) {
                let newTime = min(playerManager.duration, playerManager.currentTime + 10)
                playerManager.seek(to: newTime)
                playerState.userInteracted()
            }
            
            // 下一首
            ControlButton(icon: "forward.fill", size: 20) {
                playerWindow.playNext()
                playerState.userInteracted()
            }
        }
    }
}

// MARK: - 控制按鈕
struct ControlButton: View {
    let icon: String
    let size: CGFloat
    let isMain: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    
    init(icon: String, size: CGFloat, isMain: Bool = false, action: @escaping () -> Void) {
        self.icon = icon
        self.size = size
        self.isMain = isMain
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: size, weight: .medium))
                .foregroundColor(.white)
                .frame(width: isMain ? 56 : 44, height: isMain ? 56 : 44)
                .background(
                    Circle()
                        .fill(
                            isMain ?
                            LinearGradient(
                                colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ) :
                            LinearGradient(
                                colors: [Color.white.opacity(0.2), Color.white.opacity(0.1)],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .shadow(color: isMain ? AppTheme.glowColor : .clear, radius: isMain ? 10 : 0)
                )
                .scaleEffect(isPressed ? 0.9 : 1.0)
                .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - 播放速度選單
struct PlaybackSpeedMenu: View {
    @ObservedObject var playerManager: PlayerManager
    @ObservedObject var playerState: EnhancedPlayerState
    
    var body: some View {
        Menu {
            ForEach(PlayerManager.playbackSpeeds, id: \.self) { speed in
                Button(action: {
                    playerManager.playbackRate = speed
                    playerState.showSpeedIndicator(for: 1.5)
                    playerState.userInteracted()
                }) {
                    HStack {
                        Text("\(String(format: speed == 1.0 ? "%.0fx" : "%.2fx", speed))")
                        if playerManager.playbackRate == speed {
                            Image(systemName: "checkmark")
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 4) {
                Image(systemName: "speedometer")
                    .font(.system(size: 14, weight: .medium))
                Text("\(String(format: playerManager.playbackRate == 1.0 ? "%.0fx" : "%.1fx", playerManager.playbackRate))")
                    .font(.system(size: 12, weight: .medium))
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.white.opacity(0.2))
            )
        }
    }
}

// MARK: - 品質設定選單 (佔位符)
struct QualitySettingsMenu: View {
    var body: some View {
        Menu {
            Button("自動") { }
            Button("1080p") { }
            Button("720p") { }
            Button("480p") { }
        } label: {
            HStack(spacing: 4) {
                Image(systemName: "tv")
                    .font(.system(size: 14, weight: .medium))
                Text("品質")
                    .font(.system(size: 12, weight: .medium))
            }
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.white.opacity(0.2))
            )
        }
    }
}

// MARK: - 音頻可視化視圖
struct AudioVisualizationView: View {
    let url: URL
    let player: AVPlayer
    
    var body: some View {
        ZStack {
            // 背景漸變
            LinearGradient(
                colors: [
                    AppTheme.primaryColor.opacity(0.3),
                    AppTheme.secondaryColor.opacity(0.3),
                    AppTheme.accentColor.opacity(0.2)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 32) {
                // 音樂圖示
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color.white.opacity(0.1), Color.white.opacity(0.05)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 200, height: 200)
                    
                    Image(systemName: "music.note")
                        .font(.system(size: 80, weight: .light))
                        .foregroundColor(.white.opacity(0.8))
                }
                
                // 檔案資訊
                VStack(spacing: 12) {
                    Text(url.deletingPathExtension().lastPathComponent)
                        .font(.system(size: 28, weight: .semibold))
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                    
                    Text(MediaInfoView.getFileFormat(from: url))
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.7))
                }
            }
        }
    }
}

// MARK: - 媒體資訊覆疊
struct MediaInfoOverlay: View {
    let url: URL
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(url.lastPathComponent)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
                .lineLimit(1)
            
            Text(MediaInfoView.getFileFormat(from: url))
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.white.opacity(0.7))
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(.ultraThinMaterial)
        )
    }
}

// MARK: - 空狀態視圖
struct EmptyPlayerView: View {
    let action: () -> Void
    
    var body: some View {
        VStack(spacing: 32) {
            Image(systemName: "play.circle")
                .font(.system(size: 80, weight: .ultraLight))
                .foregroundColor(.white.opacity(0.4))
            
            VStack(spacing: 16) {
                Text("選擇媒體開始播放")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                
                Text("拖放檔案到此處，或點擊下方按鈕選擇檔案")
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(.white.opacity(0.6))
                    .multilineTextAlignment(.center)
            }
            
            Button(action: action) {
                HStack(spacing: 12) {
                    Image(systemName: "folder")
                        .font(.system(size: 16, weight: .medium))
                    
                    Text("選擇檔案")
                        .font(.system(size: 16, weight: .semibold))
                }
            }
            .buttonStyle(ModernButtonStyle(color: AppTheme.primaryColor, size: .large))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.backgroundColor)
    }
}

// MARK: - 載入視圖
struct LoadingView: View {
    @State private var rotationAngle: Double = 0
    
    var body: some View {
        VStack(spacing: 24) {
            ZStack {
                Circle()
                    .stroke(Color.white.opacity(0.2), lineWidth: 4)
                    .frame(width: 60, height: 60)
                
                Circle()
                    .trim(from: 0, to: 0.7)
                    .stroke(
                        LinearGradient(
                            colors: [AppTheme.primaryColor, AppTheme.accentColor],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        style: StrokeStyle(lineWidth: 4, lineCap: .round)
                    )
                    .frame(width: 60, height: 60)
                    .rotationEffect(.degrees(rotationAngle))
                    .animation(.linear(duration: 1).repeatForever(autoreverses: false), value: rotationAngle)
            }
            
            Text("載入媒體中...")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(.white.opacity(0.8))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppTheme.backgroundColor.opacity(0.8))
        .onAppear {
            rotationAngle = 360
        }
    }
}

// MARK: - 音量指示器
struct VolumeIndicatorView: View {
    let volume: Float
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: volumeIcon)
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.white)
            
            Text("\(Int(volume * 100))%")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
        }
        .frame(width: 100, height: 100)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topTrailing)
        .padding(.top, 120)
        .padding(.trailing, 80)
    }
    
    private var volumeIcon: String {
        if volume == 0 {
            return "speaker.slash.fill"
        } else if volume < 0.33 {
            return "speaker.wave.1.fill"
        } else if volume < 0.66 {
            return "speaker.wave.2.fill"
        } else {
            return "speaker.wave.3.fill"
        }
    }
}

// MARK: - 播放速度指示器
struct SpeedIndicatorView: View {
    let speed: Float
    
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "speedometer")
                .font(.system(size: 24, weight: .medium))
                .foregroundColor(.white)
            
            Text("\(String(format: speed == 1.0 ? "%.0fx" : "%.1fx", speed))")
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.white)
        }
        .frame(width: 100, height: 100)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
        )
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topTrailing)
        .padding(.top, 240)
        .padding(.trailing, 80)
    }
}

// MARK: - 增強版播放清單
struct EnhancedPlaylistView: View {
    @ObservedObject var playerWindow: PlayerWindow
    @State private var isShowingFileImporter = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 標題列
            HStack {
                Text("播放清單")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.white)
                
                Spacer()
                
                Button(action: {
                    isShowingFileImporter = true
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 18))
                        .foregroundColor(AppTheme.primaryColor)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(AppTheme.surfaceColor)
            
            Divider()
                .background(Color.white.opacity(0.1))
            
            // 播放清單內容
            if playerWindow.playlist.isEmpty {
                VStack(spacing: 20) {
                    Image(systemName: "music.note.list")
                        .font(.system(size: 40))
                        .foregroundColor(.white.opacity(0.4))
                    
                    Text("播放清單是空的")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                    
                    Button("添加媒體") {
                        isShowingFileImporter = true
                    }
                    .buttonStyle(ModernButtonStyle(color: AppTheme.primaryColor, size: .medium))
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(AppTheme.cardColor.opacity(0.3))
            } else {
                ScrollView {
                    LazyVStack(spacing: 0) {
                        ForEach(Array(playerWindow.playlist.enumerated()), id: \.element.absoluteString) { index, url in
                            PlaylistItemView(
                                url: url,
                                index: index,
                                isCurrentlyPlaying: index == playerWindow.currentPlaylistIndex,
                                onPlay: {
                                    playerWindow.loadMedia(url: url)
                                },
                                onRemove: {
                                    playerWindow.removeFromPlaylist(at: index)
                                }
                            )
                        }
                    }
                }
                .background(AppTheme.cardColor.opacity(0.3))
            }
        }
        .background(AppTheme.backgroundColor)
        .fileImporter(
            isPresented: $isShowingFileImporter,
            allowedContentTypes: PlayerManager.supportedTypes,
            allowsMultipleSelection: true
        ) { result in
            switch result {
            case .success(let urls):
                for url in urls {
                    playerWindow.addToPlaylist(url: url)
                    WindowManager.shared.addToRecentFiles(url)
                }
                
                if let firstURL = urls.first, playerWindow.mediaURL == nil {
                    playerWindow.loadMedia(url: firstURL)
                }
                
            case .failure(let error):
                print("選擇檔案失敗: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - 播放清單項目
struct PlaylistItemView: View {
    let url: URL
    let index: Int
    let isCurrentlyPlaying: Bool
    let onPlay: () -> Void
    let onRemove: () -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        HStack(spacing: 12) {
            // 播放狀態指示器
            ZStack {
                if isCurrentlyPlaying {
                    Image(systemName: "play.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(AppTheme.primaryColor)
                } else {
                    Text("\(index + 1)")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white.opacity(0.6))
                        .frame(width: 20, height: 20)
                }
            }
            
            // 媒體類型圖示
            Image(systemName: MediaInfoView.isVideoFile(url: url) ? "film.fill" : "music.note")
                .font(.system(size: 16))
                .foregroundColor(isCurrentlyPlaying ? AppTheme.primaryColor : .white.opacity(0.7))
            
            // 檔案資訊
            VStack(alignment: .leading, spacing: 2) {
                Text(url.deletingPathExtension().lastPathComponent)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isCurrentlyPlaying ? .white : .white.opacity(0.9))
                    .lineLimit(1)
                
                Text(MediaInfoView.getFileFormat(from: url))
                    .font(.system(size: 11, weight: .regular))
                    .foregroundColor(.white.opacity(0.5))
            }
            
            Spacer()
            
            // 操作按鈕
            if isHovered {
                HStack(spacing: 8) {
                    Button(action: onPlay) {
                        Image(systemName: "play.fill")
                            .font(.system(size: 12))
                            .foregroundColor(AppTheme.primaryColor)
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Button(action: onRemove) {
                        Image(systemName: "trash")
                            .font(.system(size: 12))
                            .foregroundColor(.red)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 12)
        .background(
            Rectangle()
                .fill(isCurrentlyPlaying ? AppTheme.primaryColor.opacity(0.1) : (isHovered ? Color.white.opacity(0.05) : Color.clear))
        )
        .onHover { hovering in
            isHovered = hovering
        }
    }
}