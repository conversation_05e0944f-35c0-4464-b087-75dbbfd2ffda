import SwiftUI

// MARK: - 主題系統
struct AppTheme {
    // 主色調
    static let primaryColor = Color(red: 0.0, green: 0.478, blue: 1.0) // Apple Blue
    static let secondaryColor = Color(red: 0.345, green: 0.337, blue: 0.839) // Indigo
    static let accentColor = Color(red: 1.0, green: 0.584, blue: 0.0) // Orange
    
    // 背景色
    static let backgroundColor = Color(red: 0.027, green: 0.027, blue: 0.058)
    static let surfaceColor = Color(red: 0.051, green: 0.051, blue: 0.098)
    static let cardColor = Color(red: 0.078, green: 0.078, blue: 0.133)
    
    // 文字色
    static let primaryText = Color.white
    static let secondaryText = Color.white.opacity(0.7)
    static let tertiaryText = Color.white.opacity(0.5)
    
    // 陰影
    static let shadowColor = Color.black.opacity(0.3)
    static let glowColor = Color.blue.opacity(0.3)
    
    // 漸層背景
    static let backgroundGradient = LinearGradient(
        colors: [backgroundColor, surfaceColor],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
}

// MARK: - 現代化按鈕樣式
struct ModernButtonStyle: ButtonStyle {
    let color: Color
    let size: ButtonSize
    
    enum ButtonSize {
        case small, medium, large
        
        var padding: EdgeInsets {
            switch self {
            case .small: return EdgeInsets(top: 8, leading: 12, bottom: 8, trailing: 12)
            case .medium: return EdgeInsets(top: 12, leading: 16, bottom: 12, trailing: 16)
            case .large: return EdgeInsets(top: 16, leading: 24, bottom: 16, trailing: 24)
            }
        }
        
        var fontSize: CGFloat {
            switch self {
            case .small: return 12
            case .medium: return 14
            case .large: return 16
            }
        }
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: size.fontSize, weight: .semibold))
            .foregroundColor(.white)
            .padding(size.padding)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(
                        LinearGradient(
                            colors: [color, color.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .shadow(
                        color: configuration.isPressed ? .clear : color.opacity(0.3),
                        radius: configuration.isPressed ? 0 : 8,
                        x: 0,
                        y: configuration.isPressed ? 0 : 4
                    )
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}