import SwiftUI
import AVKit
import UniformTypeIdentifiers
import Combine

// MARK: - 主題系統
struct AppTheme {
    // 主色調
    static let primaryColor = Color(red: 0.0, green: 0.478, blue: 1.0) // Apple Blue
    static let secondaryColor = Color(red: 0.345, green: 0.337, blue: 0.839) // Indigo
    static let accentColor = Color(red: 1.0, green: 0.584, blue: 0.0) // Orange
    
    // 背景色
    static let backgroundColor = Color(red: 0.027, green: 0.027, blue: 0.058)
    static let surfaceColor = Color(red: 0.051, green: 0.051, blue: 0.098)
    static let cardColor = Color(red: 0.078, green: 0.078, blue: 0.133)
    
    // 文字色
    static let primaryText = Color.white
    static let secondaryText = Color.white.opacity(0.7)
    static let tertiaryText = Color.white.opacity(0.5)
    
    // 陰影
    static let shadowColor = Color.black.opacity(0.3)
    static let glowColor = Color.blue.opacity(0.3)
}

// MARK: - 現代化按鈕樣式
struct ModernButtonStyle: ButtonStyle {
    let color: Color
    let size: ButtonSize
    
    enum ButtonSize {
        case small, medium, large
        
        var padding: EdgeInsets {
            switch self {
            case .small: return EdgeInsets(top: 8, leading: 12, bottom: 8, trailing: 12)
            case .medium: return EdgeInsets(top: 12, leading: 16, bottom: 12, trailing: 16)
            case .large: return EdgeInsets(top: 16, leading: 24, bottom: 16, trailing: 24)
            }
        }
        
        var fontSize: CGFloat {
            switch self {
            case .small: return 12
            case .medium: return 14
            case .large: return 16
            }
        }
    }
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.system(size: size.fontSize, weight: .semibold))
            .foregroundColor(.white)
            .padding(size.padding)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [color, color.opacity(0.8)],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .shadow(color: color.opacity(0.4), radius: configuration.isPressed ? 5 : 10, x: 0, y: configuration.isPressed ? 2 : 5)
            )
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - 玻璃效果背景
struct GlassBackground: View {
    var body: some View {
        RoundedRectangle(cornerRadius: 20)
            .fill(.ultraThinMaterial)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .stroke(Color.white.opacity(0.1), lineWidth: 1)
            )
            .shadow(color: AppTheme.shadowColor, radius: 20, x: 0, y: 10)
    }
}

// MARK: - 改進的媒體播放器管理視窗
struct ImprovedMediaPlayerManagerView: View {
    @StateObject private var windowManager = WindowManager.shared
    @StateObject private var mediaLibrary = MediaLibrary()
    @State private var isShowingFileImporter = false
    @State private var isShowingPreferences = false
    @State private var selectedTab: SidebarTab = .quickActions
    @State private var searchText = ""
    
    enum SidebarTab: String, CaseIterable {
        case quickActions = "快速操作"
        case library = "媒體庫"
        case recent = "最近播放"
        case playlists = "播放清單"
        case settings = "設定"
        
        var icon: String {
            switch self {
            case .quickActions: return "bolt.fill"
            case .library: return "folder.fill"
            case .recent: return "clock.fill"
            case .playlists: return "music.note.list"
            case .settings: return "gearshape.fill"
            }
        }
    }
    
    var body: some View {
        NavigationSplitView {
            // 側邊欄
            VStack(spacing: 0) {
                // 標題區域
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("MediaPro")
                            .font(.system(size: 24, weight: .bold, design: .rounded))
                            .foregroundColor(AppTheme.primaryText)
                        
                        Text("專業媒體播放器")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(AppTheme.secondaryText)
                    }
                    
                    Spacer()
                    
                    Button(action: { isShowingPreferences = true }) {
                        Image(systemName: "gearshape.fill")
                            .font(.system(size: 16))
                            .foregroundColor(AppTheme.secondaryText)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                
                Divider()
                    .background(Color.white.opacity(0.1))
                
                // 搜尋欄
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(AppTheme.tertiaryText)
                    
                    TextField("搜尋媒體...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .foregroundColor(AppTheme.primaryText)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(AppTheme.surfaceColor)
                .cornerRadius(10)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                
                // 側邊欄選項
                VStack(spacing: 4) {
                    ForEach(SidebarTab.allCases, id: \.self) { tab in
                        SidebarTabButton(
                            tab: tab,
                            isSelected: selectedTab == tab,
                            action: { selectedTab = tab }
                        )
                    }
                }
                .padding(.horizontal, 12)
                
                Spacer()
                
                // 底部狀態
                VStack(alignment: .leading, spacing: 8) {
                    Text("活動視窗")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(AppTheme.secondaryText)
                    
                    HStack {
                        Circle()
                            .fill(windowManager.windows.count > 0 ? Color.green : Color.gray)
                            .frame(width: 8, height: 8)
                        
                        Text("\(windowManager.windows.count) 個視窗")
                            .font(.system(size: 14))
                            .foregroundColor(AppTheme.primaryText)
                        
                        Spacer()
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
            }
            .frame(minWidth: 280)
            .background(AppTheme.backgroundColor)
        } detail: {
            // 主內容區域
            ZStack {
                AppTheme.backgroundColor
                    .ignoresSafeArea()
                
                switch selectedTab {
                case .quickActions:
                    QuickActionsView()
                case .library:
                    MediaLibraryView(mediaLibrary: mediaLibrary)
                case .recent:
                    RecentMediaView()
                case .playlists:
                    PlaylistsView()
                case .settings:
                    SettingsView()
                }
            }
        }
        .navigationSplitViewStyle(.balanced)
        .background(AppTheme.backgroundColor)
        .sheet(isPresented: $isShowingPreferences) {
            PreferencesView()
        }
        .onAppear {
            mediaLibrary.scanForMedia()
        }
    }
}

// MARK: - 側邊欄按鈕
struct SidebarTabButton: View {
    let tab: ImprovedMediaPlayerManagerView.SidebarTab
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: tab.icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(isSelected ? AppTheme.primaryColor : AppTheme.secondaryText)
                    .frame(width: 20)
                
                Text(tab.rawValue)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(isSelected ? AppTheme.primaryText : AppTheme.secondaryText)
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? AppTheme.primaryColor.opacity(0.15) : Color.clear)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? AppTheme.primaryColor.opacity(0.3) : Color.clear, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 快速操作視圖
struct QuickActionsView: View {
    @StateObject private var windowManager = WindowManager.shared
    @State private var isShowingFileImporter = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 32) {
                // 歡迎區域
                VStack(spacing: 20) {
                    Image(systemName: "play.rectangle.on.rectangle.fill")
                        .font(.system(size: 80))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .shadow(color: AppTheme.glowColor, radius: 20, x: 0, y: 0)
                    
                    VStack(spacing: 8) {
                        Text("歡迎使用 MediaPro")
                            .font(.system(size: 32, weight: .bold, design: .rounded))
                            .foregroundColor(AppTheme.primaryText)
                        
                        Text("專業級多視窗媒體播放器")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(AppTheme.secondaryText)
                    }
                }
                .padding(.top, 40)
                
                // 快速操作按鈕
                LazyVGrid(columns: [
                    GridItem(.flexible(), spacing: 16),
                    GridItem(.flexible(), spacing: 16)
                ], spacing: 16) {
                    QuickActionCard(
                        icon: "folder.badge.plus",
                        title: "開啟媒體檔案",
                        description: "選擇本機媒體檔案",
                        color: AppTheme.primaryColor
                    ) {
                        isShowingFileImporter = true
                    }
                    
                    QuickActionCard(
                        icon: "plus.rectangle.on.rectangle",
                        title: "新建播放視窗",
                        description: "建立空白播放器",
                        color: AppTheme.accentColor
                    ) {
                        _ = windowManager.createNewWindow()
                    }
                    
                    QuickActionCard(
                        icon: "folder.fill.badge.gearshape",
                        title: "掃描資料夾",
                        description: "批量匯入媒體",
                        color: AppTheme.secondaryColor
                    ) {
                        // TODO: 實現資料夾掃描
                    }
                    
                    QuickActionCard(
                        icon: "link.badge.plus",
                        title: "串流網址",
                        description: "播放網路串流",
                        color: Color.purple
                    ) {
                        // TODO: 實現串流播放
                    }
                }
                .padding(.horizontal, 32)
                
                // 最近文件快速存取
                if !windowManager.recentFiles.isEmpty {
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Text("最近播放")
                                .font(.system(size: 20, weight: .semibold))
                                .foregroundColor(AppTheme.primaryText)
                            
                            Spacer()
                            
                            Button("查看全部") {
                                // TODO: 切換到最近播放頁面
                            }
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppTheme.primaryColor)
                        }
                        
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 16) {
                                ForEach(windowManager.recentFiles.prefix(6), id: \.absoluteString) { url in
                                    RecentMediaCard(url: url) {
                                        windowManager.createNewWindowWithMedia(url: url)
                                    }
                                }
                            }
                            .padding(.horizontal, 32)
                        }
                    }
                    .padding(.horizontal, 32)
                }
                
                Spacer(minLength: 40)
            }
        }
        .fileImporter(
            isPresented: $isShowingFileImporter,
            allowedContentTypes: PlayerManager.supportedTypes,
            allowsMultipleSelection: true
        ) { result in
            switch result {
            case .success(let urls):
                for url in urls {
                    windowManager.createNewWindowWithMedia(url: url)
                }
            case .failure(let error):
                print("選擇檔案失敗: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - 快速操作卡片
struct QuickActionCard: View {
    let icon: String
    let title: String
    let description: String
    let color: Color
    let action: () -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.system(size: 32, weight: .medium))
                    .foregroundColor(color)
                    .frame(width: 60, height: 60)
                    .background(
                        Circle()
                            .fill(color.opacity(0.15))
                    )
                
                VStack(spacing: 4) {
                    Text(title)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(AppTheme.primaryText)
                        .multilineTextAlignment(.center)
                    
                    Text(description)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppTheme.secondaryText)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 160)
            .background(GlassBackground())
            .scaleEffect(isHovered ? 1.02 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isHovered)
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            isHovered = hovering
        }
    }
}

// MARK: - 最近媒體卡片
struct RecentMediaCard: View {
    let url: URL
    let action: () -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                // 媒體類型圖示
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                colors: [AppTheme.primaryColor.opacity(0.3), AppTheme.secondaryColor.opacity(0.3)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(height: 80)
                    
                    Image(systemName: MediaInfoView.isVideoFile(url: url) ? "film.fill" : "music.note")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundColor(.white)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(url.lastPathComponent)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppTheme.primaryText)
                        .lineLimit(2)
                    
                    Text(MediaInfoView.getFileFormat(from: url))
                        .font(.system(size: 11, weight: .medium))
                        .foregroundColor(AppTheme.tertiaryText)
                }
            }
            .frame(width: 140)
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(AppTheme.cardColor.opacity(0.6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
            .scaleEffect(isHovered ? 1.05 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isHovered)
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            isHovered = hovering
        }
    }
}

// MARK: - 媒體庫視圖 (佔位符)
struct MediaLibraryView: View {
    @ObservedObject var mediaLibrary: MediaLibrary
    
    var body: some View {
        VStack {
            Text("媒體庫")
                .font(.largeTitle)
                .foregroundColor(AppTheme.primaryText)
            
            Text("功能開發中...")
                .foregroundColor(AppTheme.secondaryText)
        }
    }
}

// MARK: - 最近媒體視圖 (佔位符)
struct RecentMediaView: View {
    var body: some View {
        VStack {
            Text("最近播放")
                .font(.largeTitle)
                .foregroundColor(AppTheme.primaryText)
            
            Text("功能開發中...")
                .foregroundColor(AppTheme.secondaryText)
        }
    }
}

// MARK: - 播放清單視圖 (佔位符)
struct PlaylistsView: View {
    var body: some View {
        VStack {
            Text("播放清單")
                .font(.largeTitle)
                .foregroundColor(AppTheme.primaryText)
            
            Text("功能開發中...")
                .foregroundColor(AppTheme.secondaryText)
        }
    }
}

// MARK: - 設定視圖 (佔位符)
struct SettingsView: View {
    var body: some View {
        VStack {
            Text("設定")
                .font(.largeTitle)
                .foregroundColor(AppTheme.primaryText)
            
            Text("功能開發中...")
                .foregroundColor(AppTheme.secondaryText)
        }
    }
}

// MARK: - 偏好設定視圖 (佔位符)
struct PreferencesView: View {
    var body: some View {
        VStack {
            Text("偏好設定")
                .font(.largeTitle)
                .foregroundColor(AppTheme.primaryText)
            
            Text("功能開發中...")
                .foregroundColor(AppTheme.secondaryText)
        }
        .frame(width: 500, height: 400)
    }
}

// MARK: - 媒體庫管理器 (佔位符)
class MediaLibrary: ObservableObject {
    @Published var mediaItems: [URL] = []
    
    func scanForMedia() {
        // TODO: 實現媒體掃描功能
    }
}