//
//  MediaPlayerManagerApp.swift
//  MediaPlayerManager
//
//  Created by Kasa's Macbook Pro on 2025/4/11.
//

import SwiftUI

@main
struct MediaPlayerManagerApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    
    var body: some Scene {
        WindowGroup {
            MediaPlayerManagerView()
                .frame(minWidth: 1200, minHeight: 800)
        }
        .windowStyle(HiddenTitleBarWindowStyle())
        .commands {
            CommandGroup(replacing: .newItem) {
                But<PERSON>("開啟媒體檔案") {
                    WindowManager.shared.showFileImporter()
                }
                .keyboardShortcut("O", modifiers: .command)
                
                But<PERSON>("新增播放視窗") {
                    _ = WindowManager.shared.createNewWindow()
                }
                .keyboardShortcut("N", modifiers: .command)
            }
            
            CommandMenu("播放控制") {
                But<PERSON>("播放/暫停") {
                    WindowManager.shared.togglePlayPause()
                }
                .keyboardShortcut(.space, modifiers: [])
                
                But<PERSON>("靜音") {
                    WindowManager.shared.toggleMute()
                }
                .keyboardShortcut("M", modifiers: .command)
                
                <PERSON><PERSON>("全螢幕") {
                    WindowManager.shared.toggleFullscreen()
                }
                .keyboardShortcut("F", modifiers: .command)
            }
        }
    }
    class AppDelegate: NSObject, NSApplicationDelegate {
        func applicationDockMenu(_ sender: NSApplication) -> NSMenu? {
            let menu = NSMenu()
            menu.addItem(NSMenuItem(title: "開啟媒體檔案", action: #selector(openFile), keyEquivalent: ""))
            menu.addItem(NSMenuItem(title: "新增播放視窗", action: #selector(newWindow), keyEquivalent: ""))
            
            // 添加最近播放項目
            if !WindowManager.shared.recentFiles.isEmpty {
                menu.addItem(NSMenuItem.separator())
                let recentMenu = NSMenu()
                
                for url in WindowManager.shared.recentFiles.prefix(5) {
                    let item = NSMenuItem(title: url.lastPathComponent, action: #selector(openRecentFile(_:)), keyEquivalent: "")
                    item.representedObject = url
                    recentMenu.addItem(item)
                }
                
                let recentItem = NSMenuItem(title: "最近播放", action: nil, keyEquivalent: "")
                recentItem.submenu = recentMenu
                menu.addItem(recentItem)
            }
            
            return menu
        }
        
        @objc func openFile() {
            WindowManager.shared.showFileImporter()
        }
        
        @objc func newWindow() {
            _ = WindowManager.shared.createNewWindow()
        }
        
        @objc func openRecentFile(_ sender: NSMenuItem) {
            if let url = sender.representedObject as? URL {
                WindowManager.shared.createNewWindowWithMedia(url: url)
            }
        }
        
        // 處理文件開啟請求
        func application(_ application: NSApplication, open urls: [URL]) {
            for url in urls {
                WindowManager.shared.createNewWindowWithMedia(url: url)
            }
        }
    }
}
