//
//  MediaPlayerViews.swift
//  MediaPlayerManager
//
//  Refactored from ContentView.swift to reduce file size
//

import SwiftUI
import AVKit
import UniformTypeIdentifiers

// MARK: - Recent Files List View
struct RecentFilesListView: View {
    @ObservedObject var windowManager: WindowManager
    @Binding var isShowingRecentFiles: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("最近播放")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Button("清空") {
                    windowManager.clearRecentFiles()
                }
                .buttonStyle(.borderless)
            }
            
            if windowManager.recentFiles.isEmpty {
                Text("尚無最近播放記錄")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.vertical, 20)
                    .frame(maxWidth: .infinity)
            } else {
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(windowManager.recentFiles, id: \.absoluteString) { url in
                            RecentFileRowView(url: url) {
                                windowManager.createNewWindowWithMedia(url: url)
                                isShowingRecentFiles = false
                            }
                        }
                    }
                }
                .frame(maxHeight: 300)
            }
            
            HStack {
                Spacer()
                Button("關閉") {
                    isShowingRecentFiles = false
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .padding()
        .frame(width: 400)
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(radius: 10)
    }
}

// MARK: - Recent File Row View
struct RecentFileRowView: View {
    let url: URL
    let onPlay: () -> Void
    
    @State private var isHovered = false
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: getFileIcon())
                .foregroundColor(getFileColor())
                .font(.system(size: 16))
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(url.lastPathComponent)
                    .font(.subheadline)
                    .lineLimit(1)
                
                Text(url.deletingLastPathComponent().path)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            if isHovered {
                Button(action: onPlay) {
                    Image(systemName: "play.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.accentColor)
                }
                .buttonStyle(.plain)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isHovered ? Color.accentColor.opacity(0.1) : Color.clear)
        )
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .onTapGesture {
            onPlay()
        }
    }
    
    private func getFileIcon() -> String {
        let ext = url.pathExtension.lowercased()
        let videoFormats = ["mp4", "mov", "avi", "mkv", "m4v", "wmv", "flv"]
        let audioFormats = ["mp3", "m4a", "wav", "aac", "flac", "ogg"]
        
        if videoFormats.contains(ext) {
            return "film"
        } else if audioFormats.contains(ext) {
            return "music.note"
        } else {
            return "doc"
        }
    }
    
    private func getFileColor() -> Color {
        let ext = url.pathExtension.lowercased()
        let videoFormats = ["mp4", "mov", "avi", "mkv", "m4v", "wmv", "flv"]
        let audioFormats = ["mp3", "m4a", "wav", "aac", "flac", "ogg"]
        
        if videoFormats.contains(ext) {
            return .blue
        } else if audioFormats.contains(ext) {
            return .green
        } else {
            return .gray
        }
    }
}

// MARK: - Multi Window Player View
struct MultiWindowPlayerView: View {
    @ObservedObject var playerWindow: PlayerWindow
    @State private var isShowingPlaylist = false
    @State private var currentMediaURL: URL?
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景漸層
                AppTheme.backgroundGradient
                    .ignoresSafeArea()
                
                MainPlayerContent(playerWindow: playerWindow, isShowingPlaylist: $isShowingPlaylist)
                
                PlaylistSidebar(playerWindow: playerWindow, isShowingPlaylist: $isShowingPlaylist)
            }
        }
        .onDrop(of: [.fileURL], isTargeted: nil) { providers in
            return handleDroppedFiles(providers)
        }
    }
    
    private func handleDroppedFiles(_ providers: [NSItemProvider]) -> Bool {
        for provider in providers {
            provider.loadItem(forTypeIdentifier: "public.file-url", options: nil) { (item, error) in
                if let data = item as? Data,
                   let url = URL(dataRepresentation: data, relativeTo: nil) {
                    DispatchQueue.main.async {
                        playerWindow.addToPlaylist(url: url)
                        playerWindow.loadMedia(url: url)
                        currentMediaURL = url
                    }
                }
            }
        }
        return true
    }
}

// MARK: - Main Player Content
struct MainPlayerContent: View {
    @ObservedObject var playerWindow: PlayerWindow
    @Binding var isShowingPlaylist: Bool
    
    var body: some View {
        // 檢查是否需要使用 WebM 播放器
        if let currentURL = getCurrentPlayingURL(), currentURL.isWebM {
            // 使用 WebM 播放器
            ZStack {
                WebMPlayerView(url: currentURL)
                
                // 固定的播放清單按鈕 - 總是可見
                VStack {
                    HStack {
                        Spacer()
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                isShowingPlaylist.toggle()
                            }
                        }) {
                            Image(systemName: "list.bullet")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(.white)
                                .frame(width: 44, height: 44)
                                .background(
                                    Circle()
                                        .fill(isShowingPlaylist ? Color.blue : Color.black.opacity(0.7))
                                        .shadow(color: .black.opacity(0.3), radius: 4)
                                )
                        }
                        .padding(.trailing, 16)
                    }
                    .padding(.top, 16)
                    Spacer()
                }
            }
        } else if let player = playerWindow.playerManager.player {
            ZStack {
                VStack(spacing: 0) {
                    PlayerVideoView(playerWindow: playerWindow, player: player)
                    
                    if playerWindow.playerManager.showControls {
                        PlayerControls(
                            playerManager: playerWindow.playerManager,
                            playerWindow: playerWindow,
                            isShowingPlaylist: $isShowingPlaylist
                        )
                        .background(
                            LinearGradient(
                                colors: [Color.black.opacity(0.8), Color.black.opacity(0.6)],
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                    }
                }
                
                // 固定的播放清單按鈕 - 總是可見
                VStack {
                    HStack {
                        Spacer()
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                isShowingPlaylist.toggle()
                            }
                        }) {
                            Image(systemName: "list.bullet")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(.white)
                                .frame(width: 44, height: 44)
                                .background(
                                    Circle()
                                        .fill(isShowingPlaylist ? Color.blue : Color.black.opacity(0.7))
                                        .shadow(color: .black.opacity(0.3), radius: 4)
                                )
                        }
                        .padding(.trailing, 16)
                    }
                    .padding(.top, 16)
                    Spacer()
                }
            }
        } else {
            // 播放器未初始化時顯示的內容
            VStack(spacing: 16) {
                if playerWindow.playerManager.isLoading {
                    ProgressView("載入媒體中...")
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .foregroundColor(.white)
                } else if let error = playerWindow.playerManager.errorMessage {
                    VStack(spacing: 12) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.system(size: 48))
                            .foregroundColor(.red)
                        Text("播放錯誤")
                            .font(.headline)
                            .foregroundColor(.white)
                        Text(error)
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                            .multilineTextAlignment(.center)
                    }
                } else {
                    VStack(spacing: 12) {
                        Image(systemName: "play.circle")
                            .font(.system(size: 48))
                            .foregroundColor(.white.opacity(0.6))
                        Text("等待載入媒體")
                            .font(.headline)
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.black)
        }
    }
    
    // 獲取當前播放的 URL
    private func getCurrentPlayingURL() -> URL? {
        guard !playerWindow.playlist.isEmpty,
              playerWindow.currentIndex < playerWindow.playlist.count else {
            return nil
        }
        return playerWindow.playlist[playerWindow.currentIndex]
    }
}

// MARK: - Player Video View
struct PlayerVideoView: View {
    @ObservedObject var playerWindow: PlayerWindow
    let player: AVPlayer
    
    var body: some View {
        VideoPlayer(player: player)
            .onTapGesture {
                withAnimation(.easeInOut(duration: 0.3)) {
                    playerWindow.playerManager.showControls.toggle()
                }
            }
            .onAppear {
                // 如果播放器未在播放，立即開始
                if player.timeControlStatus != .playing {
                    player.play()
                }
            }
            .overlay(LoadingOverlay(playerManager: playerWindow.playerManager))
            .overlay(ErrorOverlay(playerManager: playerWindow.playerManager))
    }
}

// MARK: - Loading Overlay
struct LoadingOverlay: View {
    @ObservedObject var playerManager: PlayerManager
    
    var body: some View {
        Group {
            if playerManager.isLoading {
                VStack(spacing: 16) {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)
                    
                    Text("載入中...")
                        .font(.headline)
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black.opacity(0.7))
            }
        }
    }
}

// MARK: - Error Overlay
struct ErrorOverlay: View {
    @ObservedObject var playerManager: PlayerManager
    
    var body: some View {
        Group {
            if let errorMessage = playerManager.errorMessage {
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 48))
                        .foregroundColor(.orange)
                    
                    Text("播放錯誤")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                    
                    Text(errorMessage)
                        .font(.body)
                        .foregroundColor(.white.opacity(0.8))
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black.opacity(0.8))
            }
        }
    }
}

// MARK: - Playlist Sidebar
struct PlaylistSidebar: View {
    @ObservedObject var playerWindow: PlayerWindow
    @Binding var isShowingPlaylist: Bool
    
    var body: some View {
        if isShowingPlaylist {
            HStack {
                Spacer()
                
                PlaylistView(playerWindow: playerWindow)
                    .frame(width: 320)
                    .background(AppTheme.surfaceColor)
                    .transition(.move(edge: .trailing))
            }
            .background(Color.black.opacity(0.3))
            .onTapGesture {
                isShowingPlaylist = false
            }
        }
    }
}

// MARK: - Welcome View
struct WelcomeView: View {
    @ObservedObject var playerWindow: PlayerWindow
    var onFileImportRequested: (() -> Void)?
    
    var body: some View {
        VStack(spacing: 32) {
            // 歡迎標題區域
            VStack(spacing: 16) {
                Image(systemName: "play.circle")
                    .font(.system(size: 80, weight: .ultraLight))
                    .foregroundColor(AppTheme.primaryColor)
                    .symbolEffect(.pulse.wholeSymbol, options: .repeat(.continuous))
                
                VStack(spacing: 8) {
                    Text("MediaPro 播放器")
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.white)
                    
                    Text("專業的媒體播放體驗")
                        .font(.title3)
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            
            // 功能介紹
            VStack(spacing: 12) {
                FeatureRow(icon: "film", title: "支援多種格式", description: "MP4, MOV, AVI, MKV 等")
                FeatureRow(icon: "music.note", title: "高品質音頻", description: "MP3, FLAC, WAV 等無損格式")
                FeatureRow(icon: "list.bullet", title: "智能播放清單", description: "拖放添加，一鍵管理")
            }
            .padding(.horizontal, 40)
            
            // 操作按鈕
            VStack(spacing: 16) {
                StylizedButton(title: "選擇媒體檔案") {
                    onFileImportRequested?()
                }
                
                HStack(spacing: 12) {
                    Text("或直接拖放檔案到此處")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.6))
                    
                    Image(systemName: "arrow.down")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.6))
                        .symbolEffect(.bounce.down, options: .repeat(.continuous))
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

// MARK: - Feature Row
struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(AppTheme.primaryColor)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.7))
            }
            
            Spacer()
        }
    }
}

// MARK: - Player Window View
struct PlayerWindowView: View {
    @ObservedObject var playerWindow: PlayerWindow
    @ObservedObject var windowManager: WindowManager
    
    var body: some View {
        MultiWindowPlayerView(playerWindow: playerWindow)
            .frame(minWidth: 800, minHeight: 600)
    }
}

