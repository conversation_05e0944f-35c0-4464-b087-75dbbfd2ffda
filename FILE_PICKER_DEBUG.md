# 文件選擇器問題診斷報告

## 問題狀況

用戶反饋："除了開啟檔案跟那個多媒体外 其他都有作用" - 表示文件選擇器按鈕無法正常工作

## 診斷步驟已執行

### 1. 移除衝突的 fileImporter
- **問題發現**: WelcomeView 和 ContentView 都有自己的 fileImporter，造成衝突
- **解決方案**: 移除 WelcomeView 的 fileImporter，統一由 ContentView 處理
- **狀態**: ✅ 已完成

### 2. 統一文件導入邏輯
- **改進**: 將所有文件導入請求都路由到 ContentView 的單一 fileImporter
- **機制**: WelcomeView 透過回調函數請求開啟文件選擇器
- **狀態**: ✅ 已完成

### 3. 加強調試輸出
- **目的**: 追蹤文件選擇器的狀態變化和觸發過程
- **內容**: 
  - 按鈕點擊事件
  - 狀態變化監控 (isShowingFileImporter)
  - 支持檔案類型輸出
  - 文件處理過程追蹤
- **狀態**: ✅ 已完成

### 4. 優化文件處理邏輯
- **改進**: 針對歡迎頁面的特殊處理邏輯
- **機制**: 第一個檔案添加到預設視窗，其餘檔案創建新視窗
- **狀態**: ✅ 已完成

## 可能的根本原因

### 1. SwiftUI fileImporter 的已知問題
- **問題**: macOS 上的 fileImporter 有時不會顯示
- **原因**: SwiftUI 的 fileImporter 依賴於 NSOpenPanel，可能受到窗口層級影響
- **參考**: 這是 SwiftUI 在 macOS 上的已知限制

### 2. 窗口管理衝突
- **問題**: 複雜的視窗管理可能影響文件對話框的顯示
- **原因**: 當有多個視窗控制器時，文件對話框可能不知道應該附加到哪個窗口

### 3. 狀態管理問題
- **問題**: `@State` 變數的更新可能沒有觸發 fileImporter
- **原因**: 在某些情況下，SwiftUI 的狀態更新不會觸發 modifier 的重新評估

## 進一步的診斷建議

### 測試方案 1: 簡化測試
```swift
// 創建一個最簡單的測試視圖
struct SimpleFilePickerTest: View {
    @State private var showingPicker = false
    
    var body: some View {
        VStack {
            Button("測試文件選擇器") {
                showingPicker = true
            }
        }
        .fileImporter(
            isPresented: $showingPicker,
            allowedContentTypes: [.movie, .audio]
        ) { result in
            print("文件選擇結果: \(result)")
        }
    }
}
```

### 測試方案 2: 使用 NSOpenPanel
如果 SwiftUI 的 fileImporter 持續有問題，可以考慮直接使用 AppKit：

```swift
func showNativeFilePicker() {
    let panel = NSOpenPanel()
    panel.allowsMultipleSelection = true
    panel.canChooseDirectories = false
    panel.canChooseFiles = true
    panel.allowedContentTypes = PlayerManager.supportedTypes
    
    panel.begin { response in
        if response == .OK {
            for url in panel.urls {
                // 處理選擇的檔案
                windowManager.createNewWindowWithMedia(url: url)
            }
        }
    }
}
```

## 目前狀態

- ✅ 移除了 fileImporter 衝突
- ✅ 統一了文件導入邏輯  
- ✅ 加強了調試輸出
- ✅ 優化了文件處理邏輯
- ⏳ 等待用戶測試反饋

## 預期結果

如果問題仍然存在，調試輸出將顯示：
1. 按鈕點擊是否被正確檢測
2. `isShowingFileImporter` 狀態是否正確變化
3. 支持的檔案類型是否正確載入
4. 文件選擇器回調是否被觸發

這些信息將幫助確定問題是在狀態管理、fileImporter 顯示，還是其他地方。

---

**修復時間**: 2025-01-08  
**修復內容**: 移除 fileImporter 衝突，統一文件導入邏輯  
**狀態**: 等待用戶測試反饋