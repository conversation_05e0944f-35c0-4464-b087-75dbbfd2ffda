{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildAndRun"}, "configuredTargets": [{"guid": "8950a7aecba567997afe1207b2c8f77232b8bf1e3ee86cfdca867037f53e29df"}], "containerPath": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "NO"}}}}, "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": false, "useLegacyBuildLocations": false, "useParallelTargets": true}