{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Desktop/Swift/MediaPlayerManager/build": {"is-mutated": true}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug": {"is-mutated": true}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app": {"is-mutated": true}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager": {"is-mutated": true}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>": {"is-command-timestamp": true}, "<TRIGGER: CreateUniversalBinary /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager normal arm64\\ x86_64>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>": {"is-command-timestamp": true}, "<TRIGGER: Validate /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/_CodeSignature", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/var/folders/tc/bl6tsw1n1jq9l790jb55d_gr0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<target-MediaPlayerManager-****************************************************************--begin-scanning>", "<target-MediaPlayerManager-****************************************************************--end>", "<target-MediaPlayerManager-****************************************************************--linker-inputs-ready>", "<target-MediaPlayerManager-****************************************************************--modules-ready>", "<workspace-Debug--stale-file-removal>"], "outputs": ["<all>"]}, "<target-MediaPlayerManager-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/_CodeSignature", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/EnhancedPlayerView.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/ImprovedContentView.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/IntegrationGuide.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/TestCompile.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/WebMPlayer.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/Assets.car", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_signature", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Info.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/PkgInfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent.der", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_lto.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_dependency_info.dat", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_lto.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_dependency_info.dat", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-generated-files.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-own-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-project-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyMetadataFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-OutputFileMap.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.LinkFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftConstValuesFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_const_extract_protocols.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-OutputFileMap.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.LinkFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftConstValuesFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_const_extract_protocols.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/empty-MediaPlayerManager.plist"], "roots": ["/tmp/MediaPlayerManager.dst", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build"], "outputs": ["<target-MediaPlayerManager-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>"]}, "<workspace-Debug--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager-8950a7aecba567997afe1207b2c8f772-VFS/all-product-headers.yaml"], "outputs": ["<workspace-Debug--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /var/folders/tc/bl6tsw1n1jq9l790jb55d_gr0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /var/folders/tc/bl6tsw1n1jq9l790jb55d_gr0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "inputs": [], "outputs": ["/var/folders/tc/bl6tsw1n1jq9l790jb55d_gr0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<ClangStatCache /var/folders/tc/bl6tsw1n1jq9l790jb55d_gr0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-o", "/var/folders/tc/bl6tsw1n1jq9l790jb55d_gr0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager.xcodeproj", "signature": "10e0dcb2197fc979779e2e9ce2506bdd"}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/Swift/MediaPlayerManager/build": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/Swift/MediaPlayerManager/build", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager-8950a7aecba567997afe1207b2c8f772-VFS/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyMetadataFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftConstValuesFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftConstValuesFileList"], "outputs": ["<target-MediaPlayerManager-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-ChangePermissions>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-StripSymbols>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-GenerateStubAPI>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ProductPostprocessingTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-CodeSign>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-Validate>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<LSRegisterURL /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "<Touch /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-CopyAside>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--GeneratedFilesTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ProductStructureTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent.der", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/Entitlements.plist"], "outputs": ["<target-MediaPlayerManager-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-generated-files.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-own-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-project-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.hmap"], "outputs": ["<target-MediaPlayerManager-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Info.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/PkgInfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/empty-MediaPlayerManager.plist"], "outputs": ["<target-MediaPlayerManager-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--RealityAssetsTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MediaPlayerManager-****************************************************************--ModuleMapTaskProducer>", "<target-MediaPlayerManager-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-MediaPlayerManager-****************************************************************--InfoPlistTaskProducer>", "<target-MediaPlayerManager-****************************************************************--SanitizerTaskProducer>", "<target-MediaPlayerManager-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-MediaPlayerManager-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-MediaPlayerManager-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-MediaPlayerManager-****************************************************************--TestTargetTaskProducer>", "<target-MediaPlayerManager-****************************************************************--TestHostTaskProducer>", "<target-MediaPlayerManager-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-MediaPlayerManager-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-MediaPlayerManager-****************************************************************--DocumentationTaskProducer>", "<target-MediaPlayerManager-****************************************************************--CustomTaskProducer>", "<target-MediaPlayerManager-****************************************************************--StubBinaryTaskProducer>", "<target-MediaPlayerManager-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--start>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents>", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS>", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--HeadermapTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ProductPostprocessingTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-MediaPlayerManager-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist_unthinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/EnhancedPlayerView.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/ImprovedContentView.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/IntegrationGuide.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/TestCompile.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/WebMPlayer.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/Assets.car", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_signature", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftmodule", "<Linked Binary /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_lto.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_dependency_info.dat", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_lto.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_dependency_info.dat", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-OutputFileMap.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.LinkFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_const_extract_protocols.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-OutputFileMap.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.LinkFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_const_extract_protocols.json"], "outputs": ["<target-MediaPlayerManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "outputs": ["<target-MediaPlayerManager-****************************************************************--generated-headers>"]}, "P0:::Gate target-MediaPlayerManager-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/MediaPlayerManager-Swift.h"], "outputs": ["<target-MediaPlayerManager-****************************************************************--swift-generated-headers>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:CodeSign /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/AppTheme.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/CompileFixes.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/ContentView.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/EnhancedPlayerManager.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/EnhancedPlayerView.swift.backup/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/FolderImporter.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/ImprovedContentView.swift.backup/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/IntegrationGuide.swift.backup/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerManagerApp.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerViews.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerControls.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerManager.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistManager.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistViews.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PreferencesSystem.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/SimpleMediaPlayerApp.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/TestCompile.swift.backup/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WebMPlayer.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WebMPlayer.swift.backup/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WindowManager.swift/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Info.plist/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent/", "<target-MediaPlayerManager-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--entry>", "<TRIGGER: CreateUniversalBinary /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager normal arm64\\ x86_64>", "<TRIGGER: MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/_CodeSignature", "<CodeSign /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets/", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist_thinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets", "--compile", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist_thinned", "--app-icon", "AppIcon", "--include-all-app-icons", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "15.4", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Desktop/Swift/MediaPlayerManager", "control-enabled": false, "deps": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_thinned"], "deps-style": "dependency-info", "signature": "ef82237a0ad4336933ed9d0d79ba12be"}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets/", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist_unthinned"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets", "--compile", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist_unthinned", "--app-icon", "AppIcon", "--include-all-app-icons", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "15.4", "--platform", "macosx"], "env": {}, "working-directory": "/Users/<USER>/Desktop/Swift/MediaPlayerManager", "control-enabled": false, "deps": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_unthinned"], "deps-style": "dependency-info", "signature": "0f6be81da52d7395fadecb8c29f05bcd"}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"], "deps": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/EnhancedPlayerView.swift.backup /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/EnhancedPlayerView.swift.backup": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/EnhancedPlayerView.swift.backup /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/EnhancedPlayerView.swift.backup", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/EnhancedPlayerView.swift.backup/", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/EnhancedPlayerView.swift.backup"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/ImprovedContentView.swift.backup /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/ImprovedContentView.swift.backup": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/ImprovedContentView.swift.backup /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/ImprovedContentView.swift.backup", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/ImprovedContentView.swift.backup/", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/ImprovedContentView.swift.backup"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/IntegrationGuide.swift.backup /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/IntegrationGuide.swift.backup": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/IntegrationGuide.swift.backup /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/IntegrationGuide.swift.backup", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/IntegrationGuide.swift.backup/", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/IntegrationGuide.swift.backup"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/TestCompile.swift.backup /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/TestCompile.swift.backup": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/TestCompile.swift.backup /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/TestCompile.swift.backup", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/TestCompile.swift.backup/", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/TestCompile.swift.backup"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/WebMPlayer.swift.backup /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WebMPlayer.swift.backup": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/WebMPlayer.swift.backup /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WebMPlayer.swift.backup", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WebMPlayer.swift.backup/", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/WebMPlayer.swift.backup"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WebMPlayer.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/SimpleMediaPlayerApp.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerControls.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/CompileFixes.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistViews.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerManagerApp.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PreferencesSystem.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/AppTheme.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/EnhancedPlayerManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/ContentView.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WindowManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/FolderImporter.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerViews.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyMetadataFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_dependency_info.dat", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftConstValuesFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_dependency_info.dat", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftConstValuesFileList", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MediaPlayerManager-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "MediaPlayerManager", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "--xcode-version", "16F6", "--platform-family", "macOS", "--deployment-target", "15.4", "--bundle-identifier", "com.example.MediaPlayerManager", "--output", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources", "--target-triple", "arm64-apple-macos15.4", "--target-triple", "x86_64-apple-macos15.4", "--binary-file", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager", "--dependency-file", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_dependency_info.dat", "--dependency-file", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--stringsdata-file", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftFileList", "--source-file-list", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftFileList", "--metadata-file-list", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftConstValuesFileList", "--swift-const-vals-list", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/Desktop/Swift/MediaPlayerManager", "signature": "b893d3581b216368bf6c2c69d463fc84"}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:Gate target-MediaPlayerManager-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/MediaPlayerManager.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--begin-compiling>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:Gate target-MediaPlayerManager-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/MediaPlayerManager.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--begin-linking>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:Gate target-MediaPlayerManager-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/MediaPlayerManager.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--begin-scanning>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:Gate target-MediaPlayerManager-****************************************************************--end": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--entry>", "<CodeSign /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/EnhancedPlayerView.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/ImprovedContentView.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/IntegrationGuide.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/TestCompile.swift.backup", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/WebMPlayer.swift.backup", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/Assets.car", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_signature", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents>", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS>", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources>", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Info.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/PkgInfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "<LSRegisterURL /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "<Validate /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "<ValidateDevelopmentAssets-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftmodule", "<Linked Binary /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_lto.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_dependency_info.dat", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_lto.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_dependency_info.dat", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-generated-files.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-own-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-project-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyMetadataFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-OutputFileMap.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.LinkFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftConstValuesFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_const_extract_protocols.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-OutputFileMap.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.LinkFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftConstValuesFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_const_extract_protocols.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/empty-MediaPlayerManager.plist", "<target-MediaPlayerManager-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-MediaPlayerManager-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-MediaPlayerManager-****************************************************************--Barrier-ChangePermissions>", "<target-MediaPlayerManager-****************************************************************--Barrier-CodeSign>", "<target-MediaPlayerManager-****************************************************************--Barrier-CopyAside>", "<target-MediaPlayerManager-****************************************************************--Barrier-GenerateStubAPI>", "<target-MediaPlayerManager-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-MediaPlayerManager-****************************************************************--Barrier-RegisterProduct>", "<target-MediaPlayerManager-****************************************************************--Barrier-StripSymbols>", "<target-MediaPlayerManager-****************************************************************--Barrier-Validate>", "<target-MediaPlayerManager-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-MediaPlayerManager-****************************************************************--CustomTaskProducer>", "<target-MediaPlayerManager-****************************************************************--DocumentationTaskProducer>", "<target-MediaPlayerManager-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-MediaPlayerManager-****************************************************************--GeneratedFilesTaskProducer>", "<target-MediaPlayerManager-****************************************************************--HeadermapTaskProducer>", "<target-MediaPlayerManager-****************************************************************--InfoPlistTaskProducer>", "<target-MediaPlayerManager-****************************************************************--ModuleMapTaskProducer>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--ProductPostprocessingTaskProducer>", "<target-MediaPlayerManager-****************************************************************--ProductStructureTaskProducer>", "<target-MediaPlayerManager-****************************************************************--RealityAssetsTaskProducer>", "<target-MediaPlayerManager-****************************************************************--SanitizerTaskProducer>", "<target-MediaPlayerManager-****************************************************************--StubBinaryTaskProducer>", "<target-MediaPlayerManager-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-MediaPlayerManager-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-MediaPlayerManager-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-MediaPlayerManager-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-MediaPlayerManager-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-MediaPlayerManager-****************************************************************--TestHostTaskProducer>", "<target-MediaPlayerManager-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-MediaPlayerManager-****************************************************************--TestTargetTaskProducer>", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-MediaPlayerManager-****************************************************************--generated-headers>", "<target-MediaPlayerManager-****************************************************************--swift-generated-headers>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--end>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:Gate target-MediaPlayerManager-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/MediaPlayerManager.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--entry>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:Gate target-MediaPlayerManager-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/MediaPlayerManager.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug>", "<CreateBuildDirectory-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:Gate target-MediaPlayerManager-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<Linked Binary /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_lto.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_dependency_info.dat", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_lto.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_dependency_info.dat", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.LinkFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.LinkFileList"], "outputs": ["<target-MediaPlayerManager-****************************************************************--linker-inputs-ready>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:Gate target-MediaPlayerManager-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/MediaPlayerManager-Swift.h"], "outputs": ["<target-MediaPlayerManager-****************************************************************--modules-ready>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:Gate target-MediaPlayerManager-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_thinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist_thinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies_unthinned", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist_unthinned", "<CopySwiftStdlib /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/Metadata.appintents>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/Assets.car", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_signature", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent.der", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftmodule", "<Linked Binary /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_lto.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_dependency_info.dat", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_lto.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_dependency_info.dat", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftdoc", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/Entitlements.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyMetadataFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyStaticMetadataFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-OutputFileMap.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.LinkFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftConstValuesFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_const_extract_protocols.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-OutputFileMap.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.LinkFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftConstValuesFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_const_extract_protocols.json", "<target-MediaPlayerManager-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--unsigned-product-ready>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:Gate target-MediaPlayerManager-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-MediaPlayerManager-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-MediaPlayerManager-****************************************************************--will-sign>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets/", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets", "--compile", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--include-all-app-icons", "--accent-color", "AccentColor", "--enable-on-demand-resources", "NO", "--development-region", "en", "--target-device", "mac", "--minimum-deployment-target", "15.4", "--platform", "macosx", "--bundle-identifier", "com.example.MediaPlayerManager", "--generate-swift-asset-symbols", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Desktop/Swift/MediaPlayerManager", "control-enabled": false, "signature": "851f735de8784652021cff09e28ee8f3"}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:LinkAssetCatalog /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Assets.xcassets/", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_signature", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources/Assets.car"], "deps": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_dependencies"}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "inputs": ["<target-MediaPlayerManager-****************************************************************--start>", "<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "<TRIGGER: MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents", "inputs": ["<target-MediaPlayerManager-****************************************************************--start>", "<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS", "inputs": ["<target-MediaPlayerManager-****************************************************************--start>", "<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources", "inputs": ["<target-MediaPlayerManager-****************************************************************--start>", "<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Resources>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/thinned>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned", "inputs": ["<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_output/unthinned>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Info.plist /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/empty-MediaPlayerManager.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Info.plist /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/empty-MediaPlayerManager.plist", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/empty-MediaPlayerManager.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/assetcatalog_generated_info.plist", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Info.plist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/PkgInfo"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:ProcessProductPackaging /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerManager.entitlements /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging /Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerManager.entitlements /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerManager.entitlements", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/Entitlements.plist", "<target-MediaPlayerManager-****************************************************************--ProductStructureTaskProducer>", "<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent.der", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent", "<target-MediaPlayerManager-****************************************************************--ProductStructureTaskProducer>", "<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent", "-o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.app.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Desktop/Swift/MediaPlayerManager", "signature": "f5b1010a3bbf23a15ba5e2719677c0fe"}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "<target-MediaPlayerManager-****************************************************************--Barrier-CodeSign>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:RegisterWithLaunchServices /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app": {"tool": "lsregisterurl", "description": "RegisterWithLaunchServices /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "inputs": ["<target-MediaPlayerManager-****************************************************************--Barrier-Validate>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--entry>", "<TRIGGER: Validate /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"], "outputs": ["<LSRegisterURL /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:SwiftDriver Compilation MediaPlayerManager normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation MediaPlayerManager normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WebMPlayer.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/SimpleMediaPlayerApp.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerControls.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/CompileFixes.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistViews.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerManagerApp.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PreferencesSystem.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/AppTheme.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/EnhancedPlayerManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/ContentView.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WindowManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/FolderImporter.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerViews.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-OutputFileMap.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_const_extract_protocols.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-generated-files.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-own-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-project-headers.hmap", "<ClangStatCache /var/folders/tc/bl6tsw1n1jq9l790jb55d_gr0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-MediaPlayerManager-****************************************************************--generated-headers>", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:SwiftDriver Compilation MediaPlayerManager normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation MediaPlayerManager normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WebMPlayer.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/SimpleMediaPlayerApp.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerControls.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/CompileFixes.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistViews.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerManagerApp.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PreferencesSystem.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/AppTheme.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/EnhancedPlayerManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/ContentView.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WindowManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/FolderImporter.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerViews.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-OutputFileMap.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_const_extract_protocols.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-generated-files.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-own-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-project-headers.hmap", "<ClangStatCache /var/folders/tc/bl6tsw1n1jq9l790jb55d_gr0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-MediaPlayerManager-****************************************************************--generated-headers>", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.swiftconstvalues", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:Touch /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "<target-MediaPlayerManager-****************************************************************--Barrier-Validate>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/Swift/MediaPlayerManager", "signature": "cc84737708251f240079a75cf74905ef"}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:Validate /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/Info.plist", "<target-MediaPlayerManager-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-MediaPlayerManager-****************************************************************--will-sign>", "<target-MediaPlayerManager-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"], "outputs": ["<Validate /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>", "<TRIGGER: Validate /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app>"]}, "P0:target-MediaPlayerManager-****************************************************************-:Debug:ValidateDevelopmentAssets /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build": {"tool": "validate-development-assets", "description": "ValidateDevelopmentAssets /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/Preview Content", "<target-MediaPlayerManager-****************************************************************--entry>"], "outputs": ["<ValidateDevelopmentAssets-/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build>"], "allow-missing-inputs": true}, "P2:::WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager-8950a7aecba567997afe1207b2c8f772-VFS/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager-8950a7aecba567997afe1207b2c8f772-VFS/all-product-headers.yaml", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager-8950a7aecba567997afe1207b2c8f772-VFS/all-product-headers.yaml"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftsourceinfo", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftsourceinfo/", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftsourceinfo", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftsourceinfo/", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.abi.json", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.abi.json/", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.abi.json"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftdoc", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftdoc/", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftdoc"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftmodule", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftmodule/", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/arm64-apple-macos.swiftmodule"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.abi.json /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.abi.json /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.abi.json", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.abi.json/", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.abi.json"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftdoc /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftdoc /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftdoc", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftdoc/", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftdoc"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftmodule /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftmodule /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftmodule", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftmodule/", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.swiftmodule/x86_64-apple-macos.swiftmodule"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:CreateUniversalBinary /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager normal arm64 x86_64": {"tool": "shell", "description": "CreateUniversalBinary /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager normal arm64 x86_64", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/Binary/MediaPlayerManager", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager", "<Linked Binary /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager>", "<TRIGGER: CreateUniversalBinary /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager normal arm64\\ x86_64>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo", "-create", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/Binary/MediaPlayerManager", "-output", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug/MediaPlayerManager.app/Contents/MacOS/MediaPlayerManager"], "env": {}, "working-directory": "/Users/<USER>/Desktop/Swift/MediaPlayerManager", "signature": "ca0df965fc3ee73abc790d4a9cf0b98f"}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:Ld /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/Binary/MediaPlayerManager normal arm64": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/Binary/MediaPlayerManager normal arm64", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.LinkFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug", "<target-MediaPlayerManager-****************************************************************--generated-headers>", "<target-MediaPlayerManager-****************************************************************--swift-generated-headers>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_lto.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos15.4", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-O0", "-L/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug", "-L/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug", "-F/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug", "-F/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug", "-filelist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftmodule", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/Binary/MediaPlayerManager"], "env": {}, "working-directory": "/Users/<USER>/Desktop/Swift/MediaPlayerManager", "deps": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_dependency_info.dat"], "deps-style": "dependency-info", "signature": "1eee81da3873c862632345e1dae37985"}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:Ld /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/Binary/MediaPlayerManager normal x86_64": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/Binary/MediaPlayerManager normal x86_64", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.LinkFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug", "<target-MediaPlayerManager-****************************************************************--generated-headers>", "<target-MediaPlayerManager-****************************************************************--swift-generated-headers>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/Binary/MediaPlayerManager", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_lto.o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "x86_64-apple-macos15.4", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-O0", "-L/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug", "-L/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug", "-F/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/EagerLinkingTBDs/Debug", "-F/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/Debug", "-filelist", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.LinkFileList", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftmodule", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-o", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/Binary/MediaPlayerManager"], "env": {}, "working-directory": "/Users/<USER>/Desktop/Swift/MediaPlayerManager", "deps": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_dependency_info.dat"], "deps-style": "dependency-info", "signature": "bc26ab6ab9a9fb4ab2570a620fb23de5"}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:SwiftDriver Compilation Requirements MediaPlayerManager normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements MediaPlayerManager normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WebMPlayer.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/SimpleMediaPlayerApp.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerControls.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/CompileFixes.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistViews.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerManagerApp.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PreferencesSystem.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/AppTheme.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/EnhancedPlayerManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/ContentView.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WindowManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/FolderImporter.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerViews.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-OutputFileMap.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_const_extract_protocols.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-generated-files.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-own-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-project-headers.hmap", "<ClangStatCache /var/folders/tc/bl6tsw1n1jq9l790jb55d_gr0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.swiftdoc"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:SwiftDriver Compilation Requirements MediaPlayerManager normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements MediaPlayerManager normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WebMPlayer.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/SimpleMediaPlayerApp.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerControls.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/CompileFixes.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistViews.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerManagerApp.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PreferencesSystem.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/AppTheme.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/EnhancedPlayerManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/ContentView.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WindowManager.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/FolderImporter.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerViews.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftFileList", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-OutputFileMap.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_const_extract_protocols.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-generated-files.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-own-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-target-headers.hmap", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-project-headers.hmap", "<ClangStatCache /var/folders/tc/bl6tsw1n1jq9l790jb55d_gr0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-MediaPlayerManager-****************************************************************--copy-headers-completion>", "<target-MediaPlayerManager-****************************************************************--ModuleVerifierTaskProducer>", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftmodule", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftsourceinfo", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.abi.json", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.swiftdoc"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/MediaPlayerManager-Swift.h /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-Swift.h /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/MediaPlayerManager-Swift.h /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-Swift.h /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-Swift.h", "inputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-Swift.h", "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-Swift.h", "<target-MediaPlayerManager-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/MediaPlayerManager-Swift.h"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/Entitlements.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/Entitlements.plist", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/Entitlements.plist"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-non-framework-target-headers.hmap", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-non-framework-target-headers.hmap"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-target-headers.hmap", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-all-target-headers.hmap"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-generated-files.hmap", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-generated-files.hmap"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-own-target-headers.hmap", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-own-target-headers.hmap"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-project-headers.hmap", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager-project-headers.hmap"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyMetadataFileList", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyMetadataFileList"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyStaticMetadataFileList", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.DependencyStaticMetadataFileList"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.hmap", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/MediaPlayerManager.hmap"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-OutputFileMap.json", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-OutputFileMap.json"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.LinkFileList", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.LinkFileList"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftConstValuesFileList", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftConstValuesFileList"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftFileList", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager.SwiftFileList"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_const_extract_protocols.json", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager_const_extract_protocols.json"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-OutputFileMap.json", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager-OutputFileMap.json"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.LinkFileList", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.LinkFileList"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftConstValuesFileList", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftConstValuesFileList"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftFileList", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager.SwiftFileList"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_const_extract_protocols.json", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManager_const_extract_protocols.json"]}, "P2:target-MediaPlayerManager-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/empty-MediaPlayerManager.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/empty-MediaPlayerManager.plist", "inputs": ["<target-MediaPlayerManager-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/empty-MediaPlayerManager.plist"]}}}