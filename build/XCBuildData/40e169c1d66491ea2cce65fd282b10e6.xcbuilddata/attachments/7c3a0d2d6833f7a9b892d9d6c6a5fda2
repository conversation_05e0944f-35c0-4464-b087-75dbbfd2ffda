/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.o
