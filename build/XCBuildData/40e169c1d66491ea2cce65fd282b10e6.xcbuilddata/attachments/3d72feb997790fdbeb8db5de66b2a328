{"": {"diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-master.dia", "emit-module-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManager-master.swiftdeps"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/AppTheme.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/AppTheme~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/CompileFixes.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/CompileFixes~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/ContentView.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/ContentView~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/EnhancedPlayerManager.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/EnhancedPlayerManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/FolderImporter.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/FolderImporter~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerManagerApp.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerManagerApp~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/MediaPlayerViews.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/MediaPlayerViews~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerControls.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerControls~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlayerManager.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlayerManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistManager.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PlaylistViews.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PlaylistViews~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/PreferencesSystem.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/PreferencesSystem~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/SimpleMediaPlayerApp.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/SimpleMediaPlayerApp~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WebMPlayer.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WebMPlayer~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/MediaPlayerManager/WindowManager.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/WindowManager~partial.swiftmodule"}, "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}}