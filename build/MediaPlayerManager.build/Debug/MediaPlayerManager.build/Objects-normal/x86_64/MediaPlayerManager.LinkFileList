/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WebMPlayer.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/SimpleMediaPlayerApp.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerControls.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/CompileFixes.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistViews.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerManagerApp.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PreferencesSystem.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/AppTheme.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/EnhancedPlayerManager.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlayerManager.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/ContentView.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/WindowManager.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/FolderImporter.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/MediaPlayerViews.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/PlaylistManager.o
/Users/<USER>/Desktop/Swift/MediaPlayerManager/build/MediaPlayerManager.build/Debug/MediaPlayerManager.build/Objects-normal/x86_64/GeneratedAssetSymbols.o
