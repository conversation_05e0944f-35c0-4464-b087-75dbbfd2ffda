#!/usr/bin/env python3
"""
MediaPlayerManager 應用程式圖標生成器
使用 Python PIL 創建專業的 macOS 應用程式圖標
"""

from PIL import Image, ImageDraw, ImageFont
import os
import math

def create_icon_sizes():
    """定義 macOS 應用程式所需的所有圖標尺寸"""
    return [
        (16, "icon_16x16.png"),
        (32, "<EMAIL>"),
        (32, "icon_32x32.png"),
        (64, "<EMAIL>"),
        (128, "icon_128x128.png"),
        (256, "<EMAIL>"),
        (256, "icon_256x256.png"),
        (512, "<EMAIL>"),
        (512, "icon_512x512.png"),
        (1024, "<EMAIL>")
    ]

def create_media_icon(size):
    """創建媒體播放器風格的圖標"""
    # 創建透明背景
    image = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(image)
    
    # 計算邊距
    margin = size * 0.1
    inner_size = size - 2 * margin
    
    # 背景圓形漸變效果
    center = size // 2
    
    # 繪製主背景圓
    circle_rect = [margin, margin, size - margin, size - margin]
    
    # 深藍色到藍色的漸變效果（手動實現）
    for i in range(int(inner_size // 2)):
        alpha = 1.0 - (i / (inner_size // 2)) * 0.3
        color_val = int(50 + i * 0.3)  # 從深藍到藍
        color = (color_val, color_val + 20, min(255, color_val + 100), int(255 * alpha))
        
        circle_margin = margin + i
        if circle_margin < size // 2:
            draw.ellipse([circle_margin, circle_margin, 
                         size - circle_margin, size - circle_margin], 
                        fill=color)
    
    # 繪製播放按鈕
    play_size = inner_size * 0.4
    play_center_x = center + play_size * 0.1  # 稍微右移
    play_center_y = center
    
    # 計算三角形播放按鈕的點
    triangle_height = play_size * 0.6
    triangle_width = play_size * 0.5
    
    triangle_points = [
        (play_center_x - triangle_width // 2, play_center_y - triangle_height // 2),
        (play_center_x - triangle_width // 2, play_center_y + triangle_height // 2),
        (play_center_x + triangle_width // 2, play_center_y)
    ]
    
    # 繪製白色播放三角形
    draw.polygon(triangle_points, fill=(255, 255, 255, 240))
    
    # 添加圓形邊框
    border_width = max(2, size // 64)
    draw.ellipse([margin, margin, size - margin, size - margin], 
                outline=(255, 255, 255, 180), width=border_width)
    
    # 添加小的裝飾元素（音波）
    if size >= 64:
        wave_x = center + inner_size * 0.25
        wave_y = center
        wave_size = size // 16
        
        for i in range(3):
            wave_radius = wave_size + i * wave_size // 2
            # 繪製部分圓弧表示音波
            bbox = [wave_x - wave_radius, wave_y - wave_radius,
                   wave_x + wave_radius, wave_y + wave_radius]
            
            # 使用線條模擬音波
            for angle in range(-30, 31, 10):
                rad = math.radians(angle)
                x1 = wave_x + (wave_radius - 2) * math.cos(rad)
                y1 = wave_y + (wave_radius - 2) * math.sin(rad)
                x2 = wave_x + wave_radius * math.cos(rad)
                y2 = wave_y + wave_radius * math.sin(rad)
                
                alpha = max(50, 150 - i * 30)
                draw.line([(x1, y1), (x2, y2)], 
                         fill=(255, 255, 255, alpha), width=max(1, size // 128))
    
    return image

def create_iconset():
    """創建完整的 .iconset 資料夾"""
    iconset_dir = "MediaPlayerManager.iconset"
    
    # 創建目錄
    if not os.path.exists(iconset_dir):
        os.makedirs(iconset_dir)
    
    print("🎨 正在生成應用程式圖標...")
    
    # 生成所有尺寸的圖標
    sizes = create_icon_sizes()
    
    for size, filename in sizes:
        print(f"  📐 生成 {size}x{size} - {filename}")
        icon = create_media_icon(size)
        
        # 保存到 iconset 目錄
        icon_path = os.path.join(iconset_dir, filename)
        icon.save(icon_path, "PNG")
    
    print(f"✅ 圖標檔案已生成在: {iconset_dir}/")
    
    # 生成 .icns 檔案的指令
    print("\n🔧 生成 .icns 檔案:")
    print(f"iconutil -c icns {iconset_dir}")
    
    return iconset_dir

def main():
    print("🎬 MediaPlayerManager 圖標製作工具")
    print("=" * 50)
    
    try:
        iconset_path = create_iconset()
        
        print("\n📋 下一步:")
        print("1. 執行以下指令生成 .icns 檔案:")
        print(f"   iconutil -c icns {iconset_path}")
        print("\n2. 在 Xcode 中設定圖標:")
        print("   - 打開專案設定")
        print("   - 在 App Icon 欄位設定生成的 .icns 檔案")
        print("\n3. 重新構建應用程式")
        
    except ImportError:
        print("❌ 需要安裝 Pillow 函式庫:")
        print("pip3 install Pillow")
    except Exception as e:
        print(f"❌ 錯誤: {e}")

if __name__ == "__main__":
    main()