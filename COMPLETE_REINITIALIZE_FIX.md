# 完全重新初始化修復報告

## 問題根源分析

根據您的觀察："每次階段設定重新載入" + macOS AVFoundation 元件狀態累積問題

**根本原因**：
- AVFoundation 元件在多次使用後會累積內部狀態
- 舊的 PlayerItem/AVPlayer 可能會保留編碼器快取
- macOS 系統層級的資源管理問題
- 每個檔案需要"完全乾淨"的播放環境

## 完全重新初始化解決方案

### 🔄 四階段完全重建流程

#### 階段 1: 完全清理所有元件
```swift
private func performCompleteCleanup() {
    // 強制停止播放器
    player?.pause()
    player?.rate = 0
    
    // 移除所有觀察者
    removeObservers()
    
    // 清理播放器項目
    if player?.currentItem != nil {
        player?.replaceCurrentItem(with: nil)
    }
    
    // 徹底釋放播放器
    player = nil
}
```

#### 階段 2: 重置所有狀態到初始值
```swift
private func resetAllStates() {
    // 播放狀態完全重置
    isPlaying = false
    isLoading = true
    errorMessage = nil
    currentTime = 0
    duration = 0
    
    // UI 狀態重置
    showControls = true
    lastInteractionTime = Date()
    playbackRate = 1.0
}
```

#### 階段 3: 清理所有快取和資源
```swift
private func clearAllCachesAndResources() {
    // 清理預載入資源
    preloadedAssets.removeAll()
    isPreloading = false
    
    // 清理靜態快取 (讓每個檔案都像第一次載入)
    PlayerManager.processedAssets.removeAll()
    PlayerManager.processingAssets.removeAll()
}
```

#### 階段 4: 全新建立播放器
```swift
private func createFreshPlayer(with url: URL, completion: @escaping (Bool) -> Void) {
    // 每次都創建全新的 AVURLAsset (不使用任何快取)
    let freshOptions: [String: Any] = [
        AVURLAssetPreferPreciseDurationAndTimingKey: true,
        AVURLAssetHTTPCookiesKey: [],
        "AVURLAssetReferenceRestrictionsKey": 0  // 強制重新載入
    ]
    
    let freshAsset = AVURLAsset(url: url, options: freshOptions)
    let freshPlayerItem = AVPlayerItem(asset: freshAsset)
    let freshPlayer = AVPlayer(playerItem: freshPlayerItem)
    
    // 重新設定所有屬性 (不依賴預設值)
    freshPlayer.automaticallyWaitsToMinimizeStalling = true
    freshPlayer.actionAtItemEnd = .pause
    
    self.player = freshPlayer
}
```

## 核心特點

### 🆕 每次都像"第一次使用"
- 完全不依賴任何先前狀態或快取
- 每個檔案都經歷完整的初始化程序
- 徹底避免 macOS AVFoundation 狀態累積

### ⏳ 完全準備再播放
```swift
private func waitForPlayerReady(_ player: AVPlayer, completion: @escaping (Bool) -> Void) {
    // 等待 PlayerItem.status == .readyToPlay
    // 5秒超時保護機制
    // 確保播放器完全準備好
}
```

### 🛡️ 增強穩定性
- 更長緩衝時間 (2.0秒)
- `automaticallyWaitsToMinimizeStalling = true`
- 完整的錯誤處理和超時保護

## 預期效果

### 解決問題
1. **第二個檔案播放成功**：每次都是"全新"的播放器
2. **消除狀態累積**：完全清理避免 macOS 元件問題
3. **統一播放體驗**：每個檔案都有相同的初始化流程

### 技術優勢
- **隔離性**：每次載入完全獨立
- **可靠性**：等待完全準備再播放
- **一致性**：統一的初始化流程
- **可預測性**：移除所有狀態依賴

## 工作原理

```
載入任何檔案
    ↓
🧹 完全清理舊元件
    ↓  
🔄 重置所有狀態
    ↓
🧹 清理所有快取
    ↓
🆕 創建全新播放器
    ↓
⏳ 等待完全準備
    ↓
✅ 開始播放
```

## 兼容性修復

同時修復了所有編譯警告：
- 使用新的 `async/await` API 替代已棄用的方法
- 修復未使用變數警告
- 更新到 macOS 13.0+ 的最新 API

這個方案確保每個檔案載入都像程式第一次啟動時一樣乾淨和穩定！