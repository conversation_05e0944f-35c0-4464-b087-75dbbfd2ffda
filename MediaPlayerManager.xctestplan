{"configurations": [{"id": "5984FB9F-D36E-4D55-B38E-AB41B1C0FDBE", "name": "Test Scheme Action", "options": {}}], "defaultOptions": {"targetForVariableExpansion": {"containerPath": "container:MediaPlayerManager.xcodeproj", "identifier": "1EC9E0802DA8D3090053B25D", "name": "MediaPlayerManager"}}, "testTargets": [{"parallelizable": true, "target": {"containerPath": "container:MediaPlayerManager.xcodeproj", "identifier": "1EC9E08E2DA8D30A0053B25D", "name": "MediaPlayerManagerTests"}}, {"parallelizable": true, "target": {"containerPath": "container:MediaPlayerManager.xcodeproj", "identifier": "1EC9E0982DA8D30A0053B25D", "name": "MediaPlayerManagerUITests"}}], "version": 1}