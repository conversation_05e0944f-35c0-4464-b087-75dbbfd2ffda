import SwiftUI
import AppKit

// MARK: - 視窗管理修復方案

/*
 問題診斷：
 1. WindowManager 使用單例模式但有多個實例被創建
 2. 視窗關閉時有循環調用導致無限創建和釋放
 3. 關閉按鈕無法正常工作
 
 解決方案：
 1. 確保只使用 WindowManager.shared 單例
 2. 改進視窗關閉邏輯，避免循環調用
 3. 添加更好的視窗控制機制
*/

// MARK: - 改進的視窗控制器
class ImprovedMediaPlayerWindowController: NSWindowController {
    let playerWindow: PlayerWindow
    private var isClosing = false // 防止重複關閉
    
    init(playerWindow: PlayerWindow) {
        self.playerWindow = playerWindow
        
        // 創建視窗
        let window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 1200, height: 700),
            styleMask: [.titled, .closable, .miniaturizable, .resizable, .fullSizeContentView],
            backing: .buffered,
            defer: false
        )
        
        // 設置視窗屬性
        window.titlebarAppearsTransparent = true
        window.isMovableByWindowBackground = true
        window.backgroundColor = NSColor.black
        window.minSize = NSSize(width: 800, height: 600)
        
        // 設置內容視圖
        let contentView = PlayerWindowView(playerWindow: playerWindow)
        window.contentView = NSHostingView(rootView: contentView)
        
        super.init(window: window)
        
        // 設置代理
        window.delegate = self
        
        // 設置標題
        window.title = playerWindow.title
        
        // 置中顯示
        window.center()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        print("ImprovedMediaPlayerWindowController 被釋放: \(playerWindow.title)")
    }
    
    // 安全關閉視窗
    func safeClose() {
        guard !isClosing else { return }
        isClosing = true
        close()
    }
}

// MARK: - 視窗代理
extension ImprovedMediaPlayerWindowController: NSWindowDelegate {
    func windowWillClose(_ notification: Notification) {
        guard !isClosing else { return }
        isClosing = true
        
        // 清理資源
        playerWindow.cleanup()
        
        // 通知管理器（避免循環調用）
        WindowManager.shared.handleWindowClosed(self)
    }
    
    func windowShouldClose(_ sender: NSWindow) -> Bool {
        // 可以在這裡添加確認對話框
        return true
    }
}

// MARK: - 改進的視窗管理器擴展
extension WindowManager {
    // 處理視窗關閉（避免循環調用）
    func handleWindowClosed(_ controller: MediaPlayerWindowController) {
        let id = controller.playerWindow.id
        
        // 只處理一次
        guard windowControllers[id] != nil else { return }
        
        // 移除引用
        windowControllers.removeValue(forKey: id)
        windowCount = windowControllers.count
        
        // 更新 UI
        objectWillChange.send()
    }
    
    // 安全關閉視窗
    func safeCloseWindow(withId id: UUID) {
        guard let controller = windowControllers[id] else { return }
        
        // 先移除引用，避免循環
        windowControllers.removeValue(forKey: id)
        windowCount = windowControllers.count
        
        // 然後關閉視窗
        DispatchQueue.main.async {
            controller.close()
        }
        
        // 更新 UI
        objectWillChange.send()
    }
    
    // 關閉所有視窗
    func closeAllWindows() {
        let ids = windowControllers.keys.map { $0 }
        for id in ids {
            safeCloseWindow(withId: id)
        }
    }
}

// MARK: - 改進的關閉按鈕
struct ImprovedCloseButton: View {
    let playerWindow: PlayerWindow
    @State private var isHovered = false
    
    var body: some View {
        Button(action: {
            // 使用安全關閉方法
            WindowManager.shared.safeCloseWindow(withId: playerWindow.id)
        }) {
            Image(systemName: "xmark.circle.fill")
                .font(.system(size: 20))
                .foregroundColor(isHovered ? .red : .gray)
                .background(
                    Circle()
                        .fill(Color.black.opacity(0.5))
                        .frame(width: 30, height: 30)
                )
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            isHovered = hovering
        }
        .help("關閉視窗")
    }
}

// MARK: - 使用說明
/*
 整合步驟：
 
 1. 在 ContentView.swift 中：
    - 將所有 WindowManager() 改為 WindowManager.shared
    - 將 closeWindowWithId 調用改為 safeCloseWindow
 
 2. 在視窗視圖中：
    - 使用 ImprovedCloseButton 替代原有的關閉按鈕
 
 3. 在 WindowManager 中：
    - 使用 handleWindowClosed 替代 windowWillClose
    - 使用 safeCloseWindow 替代 closeWindowWithId
 
 這樣可以避免記憶體洩漏和無限循環創建視窗的問題。
*/