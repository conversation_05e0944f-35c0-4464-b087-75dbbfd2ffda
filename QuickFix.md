# 快速修復視窗管理問題

## 問題描述
- 大量 "MediaPlayerWindowController 被釋放" 的訊息
- 視窗無法正常關閉
- 記憶體洩漏問題

## 快速修復步驟

### 1. 修復 WindowManager 單例問題
在 ContentView.swift 中找到以下兩處並修改：

```swift
// 第 22 行附近
// 原本：@StateObject private var windowManager = WindowManager()
// 改為：
@StateObject private var windowManager = WindowManager.shared

// 第 1434 行附近  
// 原本：@StateObject private var windowManager = WindowManager()
// 改為：
@StateObject private var windowManager = WindowManager.shared
```

### 2. 修復視窗關閉循環問題
在 ContentView.swift 中找到 `closeWindowWithId` 函數（約第 890 行）：

```swift
func closeWindowWithId(_ id: UUID) {
    if let controller = windowControllers[id] {
        // 先從集合中移除，避免 windowWillClose 重複處理
        windowControllers.removeValue(forKey: id)
        windowCount = windowControllers.count
        
        // 然後關閉視窗
        controller.close()
    }
}
```

### 3. 移除自動創建新視窗的邏輯
找到所有 "如果關閉所有視窗，則創建一個新視窗" 的程式碼並刪除或註解掉：

```swift
// 刪除或註解這些程式碼：
if windowControllers.isEmpty {
    DispatchQueue.main.async {
        _ = self.createNewWindow()
    }
}
```

### 4. 改進關閉按鈕（可選）
如果關閉按鈕還是有問題，可以在播放器視窗的關閉按鈕處添加延遲：

```swift
Button(action: {
    DispatchQueue.main.async {
        WindowManager.shared.closeWindowWithId(playerWindow.id)
    }
}) {
    // 關閉按鈕的 UI
}
```

## 驗證修復

完成修復後，重新編譯並執行程式：
1. 開啟一個視窗
2. 播放媒體
3. 點擊關閉按鈕
4. 確認視窗正常關閉，沒有重複的釋放訊息

## 注意事項
- 確保整個專案中只使用 `WindowManager.shared`
- 避免在 `windowWillClose` 和 `closeWindowWithId` 中產生循環調用
- 如果還有問題，可以參考 WindowManagementFix.swift 中的完整解決方案