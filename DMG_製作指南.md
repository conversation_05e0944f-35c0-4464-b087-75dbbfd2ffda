# 📦 MediaPlayerManager DMG 製作完整指南

## 🎨 已完成項目

✅ **應用程式圖標已生成**：
- `MediaPlayerManager.icns` (373KB) - macOS 應用程式圖標
- `MediaPlayerManager.iconset/` - 所有尺寸的 PNG 圖標檔案

✅ **DMG 製作腳本準備完成**：
- `create_dmg.sh` - 自動化 DMG 製作腳本

## 🛠️ 完整製作流程

### 步驟 1: 在 Xcode 中設定圖標並構建

1. **設定應用程式圖標**：
   ```
   打開 MediaPlayerManager.xcodeproj
   → 選擇專案設定
   → General 標籤
   → App Icon 欄位選擇 "MediaPlayerManager.icns"
   ```

2. **構建 Release 版本**：
   ```
   方案 A (GUI - 推薦):
   - Product → Archive
   - 等待 Archive 完成
   - Export as macOS App
   - 選擇 "Developer ID" 或 "Export"
   - 儲存到 build/Release/ 目錄

   方案 B (命令行 - 若系統問題解決後):
   - 執行 ./create_dmg.sh 中的指令
   ```

### 步驟 2: 製作 DMG（假設有 .app 檔案）

**自動製作**：
```bash
# 確保 .app 檔案在正確位置
ls build/Release/MediaPlayerManager.app

# 執行 DMG 製作腳本
./create_dmg.sh
```

**手動製作**：
```bash
# 1. 創建構建目錄
mkdir dmg_build
cp -R build/Release/MediaPlayerManager.app dmg_build/
ln -sf /Applications dmg_build/Applications

# 2. 創建 DMG
hdiutil create -srcfolder dmg_build -volname "MediaPlayerManager" \
    -fs HFS+ -format UDZO -imagekey zlib-level=9 \
    MediaPlayerManager_v1.0.dmg
```

## 🎯 目前狀況

**✅ 已完成**：
- 專業的應用程式圖標設計和生成
- DMG 製作腳本準備
- 完整的自動化流程

**⚠️ 待解決**：
- Xcode C++ 標準庫編譯問題
- 需要在 Xcode GUI 中手動構建

## 🔧 編譯問題解決方案

### 方案 1: Xcode 設定調整

在 Xcode 中：
```
Build Settings 搜尋 "C++ Language"
→ C++ Language Dialect 設為 "C++17"
→ C++ Standard Library 設為 "libc++"
→ 清理並重新構建
```

### 方案 2: 系統環境修復

```bash
# 清理 Xcode 緩存
rm -rf ~/Library/Developer/Xcode/DerivedData

# 重設 Xcode 命令行工具
sudo xcode-select --reset
sudo xcode-select --install

# 重啟 Xcode
```

### 方案 3: 使用較舊版本 Xcode

如果問題持續，考慮使用 Xcode 15.3 或更早版本。

## 📋 DMG 內容規劃

**DMG 包含內容**：
```
MediaPlayerManager_v1.0.dmg
├── MediaPlayerManager.app (你的應用程式)
├── Applications (快捷方式到 /Applications)
└── README.txt (使用說明)
```

**DMG 特色**：
- 美觀的圖標布局
- 拖放安裝界面
- 壓縮優化 (預計 ~50MB)
- 專業的視覺外觀

## 🚀 發布準備

**發布檢查清單**：
- [ ] 應用程式功能完整測試
- [ ] 圖標顯示正確
- [ ] DMG 安裝流程順暢
- [ ] 在不同 macOS 版本測試
- [ ] 程式碼簽名（可選）

**版本資訊**：
- 應用程式名稱：MediaPlayerManager
- 版本：1.0
- 支援系統：macOS 15.4+
- 架構：Universal (Apple Silicon + Intel)

## 💡 下一步行動

1. **立即可做**：
   - 在 Xcode 中設定新圖標
   - 嘗試 GUI 構建

2. **構建成功後**：
   - 執行 `./create_dmg.sh`
   - 測試 DMG 安裝

3. **最終檢查**：
   - 在乾淨的 Mac 上測試安裝
   - 確認所有功能正常運作

## 🎉 預期結果

**最終產品**：
- `MediaPlayerManager_v1.0.dmg` (約 50MB)
- 專業級 macOS 安裝程式
- 拖放安裝體驗
- 美觀的應用程式圖標

你的 MediaPlayerManager 已經準備好成為一個完整的 macOS 應用程式發布包！