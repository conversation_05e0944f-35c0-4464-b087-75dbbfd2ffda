stages:
    - release
    - build

variables:
    VLC_VERSION: "3.0"
    VLC_DEBIAN_IMAGE: registry.videolan.org/vlc-debian-unstable:20200529132440
    VLC_PATH: /Users/<USER>/sandbox/bin

############
## Builds ##
############

.continous-job-base:
    tags:
        - macos-m1
    stage: build
    only:
        - merge_requests
    interruptible: true

.continous-build-base:
    script:
        - ./buildMobileVLCKit.sh -${BUILD_PARAM}

continous-MobileVLCKit:
    extends:
        - .continous-job-base
        - .continous-build-base
    variables:
        BUILD_PARAM: "vf3"
        JOB_NAME: "MobileVLCKit"

continous-TVVLCKit:
    extends:
        - .continous-job-base
        - .continous-build-base
    variables:
        BUILD_PARAM: "vft"
        JOB_NAME: "TVVLCKit"

continous-VLCKit:
    extends:
        - .continous-job-base
        - .continous-build-base
    variables:
        BUILD_PARAM: "vx"
        JOB_NAME: "VLCKit"

############################################################
## Development artifacts builds                           ##
## These are running every time a merge request is merged ##
## or a commit gets pushed to the default branch          ##
############################################################

.dev-artifacts-job-base:
    tags:
        - macos-m1
    stage: build
    rules:
        - if: '$CI_PIPELINE_SOURCE == "push" && $CI_PROJECT_PATH == "videolan/VLCKit"'
        - if: '$CI_COMMIT_TAG'
          when: never

    artifacts:
        paths:
            - ./*.tar.xz

.dev-artifacts-build-base:
    script:
        - ./buildMobileVLCKit.sh -${BUILD_PARAM}
        - ./Packaging/create-distributable-package.sh -${PACKAGING_PARAM}
        - mv ${JOB_NAME}-*.tar.xz ${JOB_NAME}-${VLC_VERSION}-`date +%Y%m%d-%H%M`.tar.xz

dev-artifacts-MobileVLCKit:
    extends:
        - .dev-artifacts-job-base
        - .dev-artifacts-build-base
    variables:
        BUILD_PARAM: "vf3"
        PACKAGING_PARAM: "vm"
        JOB_NAME: "MobileVLCKit"

dev-artifacts-TVVLCKit:
    extends:
        - .dev-artifacts-job-base
        - .dev-artifacts-build-base
    variables:
        BUILD_PARAM: "vft"
        PACKAGING_PARAM: "vt"
        JOB_NAME: "TVVLCKit"

dev-artifacts-VLCKit:
    extends:
        - .dev-artifacts-job-base
        - .dev-artifacts-build-base
    variables:
        BUILD_PARAM: "vx"
        PACKAGING_PARAM: "v"
        JOB_NAME: "VLCKit"

#########
## doc ##
#########

pages:
    tags:
        - docker
        - amd64
    stage: build
    rules:
        - if: '$CI_PIPELINE_SOURCE == "push" && $CI_PROJECT_PATH == "videolan/VLCKit"'
    image:
        name: $VLC_DEBIAN_IMAGE
    script:
        - doxygen doxygen.conf
        - mkdir public
        - cp -r doc/html/* public/
    artifacts:
        paths:
            - public

####################
## Release builds ##
####################

.release-job-base:
    tags:
        - macos-m1
    stage: release
    only:
        - tags
    artifacts:
        paths:
            - ./*.tar.xz

.release-build-base:
    script:
        - cd Packaging
        - ./buildAndDeployFrameworks.sh -${BUILD_PARAM}

MobileVLCKit:
    extends:
        - .release-job-base
        - .release-build-base
    variables:
        BUILD_PARAM: "m"

TVVLCKit:
    extends:
        - .release-job-base
        - .release-build-base
    variables:
        BUILD_PARAM: "t"

VLCKit:
    extends:
        - .release-job-base
        - .release-build-base
    variables:
        BUILD_PARAM: "x"
