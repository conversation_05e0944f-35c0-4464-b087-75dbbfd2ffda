// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		3C411D08297969390057ACA1 /* VLCAudioEqualizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C411D07297969390057ACA1 /* VLCAudioEqualizer.m */; };
		3C411D09297969390057ACA1 /* VLCAudioEqualizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 3C411D07297969390057ACA1 /* VLCAudioEqualizer.m */; };
		3C411D0B2979694C0057ACA1 /* VLCAudioEqualizer.h in Headers */ = {isa = PBXBuildFile; fileRef = 3C411D0A2979694C0057ACA1 /* VLCAudioEqualizer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3C411D0C2979694C0057ACA1 /* VLCAudioEqualizer.h in Headers */ = {isa = PBXBuildFile; fileRef = 3C411D0A2979694C0057ACA1 /* VLCAudioEqualizer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C121E0127BA566500769422 /* VLCFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C121E0027BA565A00769422 /* VLCFilter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C121E0227BA566D00769422 /* VLCFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C121E0027BA565A00769422 /* VLCFilter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C121E0427BA584200769422 /* VLCAdjustFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C121E0327BA584200769422 /* VLCAdjustFilter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C121E0527BA584200769422 /* VLCAdjustFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C121E0327BA584200769422 /* VLCAdjustFilter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C121E0727BA59C500769422 /* VLCFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C121E0627BA59C500769422 /* VLCFilter.m */; };
		6C121E0827BA59C500769422 /* VLCFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C121E0627BA59C500769422 /* VLCFilter.m */; };
		6C121E0A27BA60BE00769422 /* VLCAdjustFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C121E0927BA60BE00769422 /* VLCAdjustFilter.m */; };
		6C121E0B27BA60BE00769422 /* VLCAdjustFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C121E0927BA60BE00769422 /* VLCAdjustFilter.m */; };
		6C13FFBB29BF8D4300C4C68E /* VLCEventsHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C13FFBA29BF8D4300C4C68E /* VLCEventsHandler.h */; settings = {ATTRIBUTES = (Private, ); }; };
		6C13FFBC29BF8D4300C4C68E /* VLCEventsHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C13FFBA29BF8D4300C4C68E /* VLCEventsHandler.h */; settings = {ATTRIBUTES = (Private, ); }; };
		6C13FFBE29BF8D5300C4C68E /* VLCEventsHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C13FFBD29BF8D5300C4C68E /* VLCEventsHandler.m */; };
		6C13FFBF29BF8D5300C4C68E /* VLCEventsHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C13FFBD29BF8D5300C4C68E /* VLCEventsHandler.m */; };
		6C57B71827C4D024002E5A3A /* VLCMediaPlayer+Internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C57B71727C4CFF1002E5A3A /* VLCMediaPlayer+Internal.h */; settings = {ATTRIBUTES = (Private, ); }; };
		6C57B71927C4D024002E5A3A /* VLCMediaPlayer+Internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C57B71727C4CFF1002E5A3A /* VLCMediaPlayer+Internal.h */; settings = {ATTRIBUTES = (Private, ); }; };
		6C63354A28169D9A00AF8CCF /* VLCLogging.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C63354928169D9A00AF8CCF /* VLCLogging.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C63354B28169D9A00AF8CCF /* VLCLogging.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C63354928169D9A00AF8CCF /* VLCLogging.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C63354E28169EDE00AF8CCF /* VLCConsoleLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C63354C28169EDE00AF8CCF /* VLCConsoleLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C63354F28169EDE00AF8CCF /* VLCConsoleLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C63354C28169EDE00AF8CCF /* VLCConsoleLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C63355028169EDE00AF8CCF /* VLCConsoleLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C63354D28169EDE00AF8CCF /* VLCConsoleLogger.m */; };
		6C63355128169EDE00AF8CCF /* VLCConsoleLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C63354D28169EDE00AF8CCF /* VLCConsoleLogger.m */; };
		6C6335542816A09600AF8CCF /* VLCFileLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C6335522816A09600AF8CCF /* VLCFileLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C6335552816A09600AF8CCF /* VLCFileLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C6335522816A09600AF8CCF /* VLCFileLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C6335562816A09600AF8CCF /* VLCFileLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C6335532816A09600AF8CCF /* VLCFileLogger.m */; };
		6C6335572816A09600AF8CCF /* VLCFileLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C6335532816A09600AF8CCF /* VLCFileLogger.m */; };
		6C63355A2816D75D00AF8CCF /* VLCLogMessageFormatter.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C6335582816D75D00AF8CCF /* VLCLogMessageFormatter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C63355B2816D75D00AF8CCF /* VLCLogMessageFormatter.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C6335582816D75D00AF8CCF /* VLCLogMessageFormatter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C63355C2816D75D00AF8CCF /* VLCLogMessageFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C6335592816D75D00AF8CCF /* VLCLogMessageFormatter.m */; };
		6C63355D2816D75D00AF8CCF /* VLCLogMessageFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C6335592816D75D00AF8CCF /* VLCLogMessageFormatter.m */; };
		6C6ABBD127C66410008FDB2D /* VLCFilter+Internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C6ABBD027C6639D008FDB2D /* VLCFilter+Internal.h */; settings = {ATTRIBUTES = (Private, ); }; };
		6C6ABBD227C66410008FDB2D /* VLCFilter+Internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C6ABBD027C6639D008FDB2D /* VLCFilter+Internal.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7D06ED1A25FBA9B3005F7551 /* VLCTranscoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 41E1959621BEA28F00F10277 /* VLCTranscoder.m */; };
		7D06ED1B25FBA9B3005F7551 /* VLCRendererDiscoverer.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D2CE648203DCC48004BB7F6 /* VLCRendererDiscoverer.m */; };
		7D06ED1C25FBA9B3005F7551 /* VLCRendererItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 8D2CE64F203EEA47004BB7F6 /* VLCRendererItem.m */; };
		7D1908D6274FE1FC00A8A947 /* libvlc_dialog.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D1908D4274FE1F300A8A947 /* libvlc_dialog.h */; };
		7D1908D9274FE21000A8A947 /* libvlc_picture.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D1908D7274FE21000A8A947 /* libvlc_picture.h */; };
		7D1908DA274FE2FD00A8A947 /* libvlc_dialog.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7D1908D4274FE1F300A8A947 /* libvlc_dialog.h */; };
		7D1908DB274FE30400A8A947 /* libvlc_picture.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7D1908D7274FE21000A8A947 /* libvlc_picture.h */; };
		7D1908DE274FE37100A8A947 /* libvlc_version.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D1908DC274FE37100A8A947 /* libvlc_version.h */; };
		7D1908DF274FE37800A8A947 /* libvlc_version.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7D1908DC274FE37100A8A947 /* libvlc_version.h */; };
		7D1908E0274FE38400A8A947 /* libvlc_dialog.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7D1908D4274FE1F300A8A947 /* libvlc_dialog.h */; };
		7D1908E1274FE38A00A8A947 /* libvlc_picture.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7D1908D7274FE21000A8A947 /* libvlc_picture.h */; };
		7D1908E2274FE39200A8A947 /* libvlc_version.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7D1908DC274FE37100A8A947 /* libvlc_version.h */; };
		7D22946F25FA3E8F00939476 /* TVVLCKit.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D797FB21DF41E8400AD93ED /* TVVLCKit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7D22947825FA3EBC00939476 /* MobileVLCKit.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECB9D11DE975700F66AF3 /* MobileVLCKit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7D24C2B61EC0A3390047E700 /* libbz2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D24C2B51EC0A3390047E700 /* libbz2.tbd */; };
		7D24C2BA1EC0A3460047E700 /* libbz2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D24C2B91EC0A3460047E700 /* libbz2.tbd */; };
		7D34F5531C909DDF008A39F0 /* VLCCustomDialogProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D34F5501C909DDF008A39F0 /* VLCCustomDialogProvider.m */; };
		7D6C89201C0CA81F00321894 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D6C891F1C0CA81F00321894 /* AVFoundation.framework */; };
		7D6C89211C0CA90000321894 /* VLCAudio.m in Sources */ = {isa = PBXBuildFile; fileRef = 4B49C5FB1BC3D7870052D40C /* VLCAudio.m */; };
		7D797FB51DF41EBA00AD93ED /* StaticLibVLC.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D797FB41DF41EBA00AD93ED /* StaticLibVLC.m */; };
		7D797FB61DF41EBA00AD93ED /* StaticLibVLC.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D797FB41DF41EBA00AD93ED /* StaticLibVLC.m */; };
		7D797FBC1DF41F0300AD93ED /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D797FBB1DF41F0300AD93ED /* libc++.tbd */; };
		7D797FBE1DF41F1B00AD93ED /* libiconv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D797FBD1DF41F1B00AD93ED /* libiconv.tbd */; };
		7D797FC01DF41F2000AD93ED /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D797FBF1DF41F2000AD93ED /* libz.tbd */; };
		7D797FC41DF41F9100AD93ED /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D797FC31DF41F9100AD93ED /* libc++.tbd */; };
		7D797FC61DF41F9500AD93ED /* libiconv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D797FC51DF41F9500AD93ED /* libiconv.tbd */; };
		7D803EC11C8F2AB400864A9C /* VLCEmbeddedDialogProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D803EBE1C8F2AB400864A9C /* VLCEmbeddedDialogProvider.m */; };
		7D803EC71C8F2AF900864A9C /* VLCiOSLegacyDialogProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D803EC51C8F2AF900864A9C /* VLCiOSLegacyDialogProvider.m */; };
		7D8939411B500D50008F2B14 /* VLCLibrary.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A5ECAC511DE8F7300F66AF3 /* VLCLibrary.m */; };
		7D8939421B500D50008F2B14 /* VLCMedia.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A5ECAC611DE8F7300F66AF3 /* VLCMedia.m */; };
		7D8939431B500D50008F2B14 /* VLCMediaList.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A5ECAC711DE8F7300F66AF3 /* VLCMediaList.m */; };
		7D8939441B500D51008F2B14 /* VLCMediaPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A5ECAC811DE8F7300F66AF3 /* VLCMediaPlayer.m */; };
		7D8939451B500D51008F2B14 /* VLCMediaListPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D5F8009185B72EB00C2CD75 /* VLCMediaListPlayer.m */; };
		7D8939461B500D51008F2B14 /* VLCMediaThumbnailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 636E975711EBC67A002FE8A9 /* VLCMediaThumbnailer.m */; };
		7D8939471B500D51008F2B14 /* VLCMediaDiscoverer.m in Sources */ = {isa = PBXBuildFile; fileRef = 6360B0E111E7F0C000EAD790 /* VLCMediaDiscoverer.m */; };
		7D8939481B500D51008F2B14 /* VLCTime.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A5ECAC911DE8F7300F66AF3 /* VLCTime.m */; };
		7D8939651B500F13008F2B14 /* libStaticLibVLC.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D89394E1B500DBC008F2B14 /* libStaticLibVLC.a */; };
		7D8939671B500F38008F2B14 /* CoreFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8939011B5009DD008F2B14 /* CoreFoundation.framework */; };
		7D8939691B500F38008F2B14 /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8938FD1B5009D2008F2B14 /* OpenGLES.framework */; };
		7D89396A1B500F38008F2B14 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8938FB1B5009CB008F2B14 /* UIKit.framework */; };
		7D89396B1B500F38008F2B14 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8938F91B5009C2008F2B14 /* Security.framework */; };
		7D89396C1B500F38008F2B14 /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8938F71B5009BE008F2B14 /* VideoToolbox.framework */; };
		7D89396D1B500F38008F2B14 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8938F51B5009B4008F2B14 /* CoreMedia.framework */; };
		7D89396E1B500F38008F2B14 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8938F21B5009A7008F2B14 /* CoreVideo.framework */; };
		7D89396F1B500F38008F2B14 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8938F01B5009A2008F2B14 /* AudioToolbox.framework */; };
		7D8939701B500F38008F2B14 /* CoreText.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8938EE1B500998008F2B14 /* CoreText.framework */; };
		7D8939711B500F38008F2B14 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8938EC1B500991008F2B14 /* CoreGraphics.framework */; };
		7D8939721B500F38008F2B14 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8938EA1B50098C008F2B14 /* CFNetwork.framework */; };
		7D8939731B500F38008F2B14 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8938E81B500984008F2B14 /* QuartzCore.framework */; };
		7D8939741B500F38008F2B14 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = AACBBE490F95108600F1A2B1 /* Foundation.framework */; };
		7D8DEBF125FBA52200826083 /* VLCTranscoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 41E1959821BEA2AF00F10277 /* VLCTranscoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7D8DEBF625FBA52C00826083 /* VLCRendererDiscoverer.h in Headers */ = {isa = PBXBuildFile; fileRef = 8D2CE647203DCC48004BB7F6 /* VLCRendererDiscoverer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7D8DEBF725FBA52C00826083 /* VLCRendererItem.h in Headers */ = {isa = PBXBuildFile; fileRef = 8D2CE64E203EEA47004BB7F6 /* VLCRendererItem.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DA7F2EE28D87E2300366EEB /* VLCMediaMetaData.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DA7F2ED28D87E2300366EEB /* VLCMediaMetaData.m */; };
		7DA7F2EF28D87E2300366EEB /* VLCMediaMetaData.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DA7F2ED28D87E2300366EEB /* VLCMediaMetaData.m */; };
		7DA7F2F128D87E2C00366EEB /* VLCMediaMetaData.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DA7F2F028D87E2C00366EEB /* VLCMediaMetaData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DA7F2F228D87E2C00366EEB /* VLCMediaMetaData.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DA7F2F028D87E2C00366EEB /* VLCMediaMetaData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DAD5C542A835EED009419DC /* VLCEventsConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DAD5C522A835EED009419DC /* VLCEventsConfiguration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DAD5C552A835EED009419DC /* VLCEventsConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DAD5C522A835EED009419DC /* VLCEventsConfiguration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DAD5C562A835EED009419DC /* VLCEventsConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DAD5C532A835EED009419DC /* VLCEventsConfiguration.m */; };
		7DAD5C572A835EED009419DC /* VLCEventsConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DAD5C532A835EED009419DC /* VLCEventsConfiguration.m */; };
		7DB683871C995D76000C70BE /* VLCAudio.m in Sources */ = {isa = PBXBuildFile; fileRef = 4B49C5FB1BC3D7870052D40C /* VLCAudio.m */; };
		7DB683891C995D76000C70BE /* VLCLibrary.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A5ECAC511DE8F7300F66AF3 /* VLCLibrary.m */; };
		7DB6838A1C995D76000C70BE /* VLCMedia.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A5ECAC611DE8F7300F66AF3 /* VLCMedia.m */; };
		7DB6838B1C995D76000C70BE /* VLCMediaList.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A5ECAC711DE8F7300F66AF3 /* VLCMediaList.m */; };
		7DB6838C1C995D76000C70BE /* VLCMediaPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A5ECAC811DE8F7300F66AF3 /* VLCMediaPlayer.m */; };
		7DB6838D1C995D76000C70BE /* VLCMediaListPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D5F8009185B72EB00C2CD75 /* VLCMediaListPlayer.m */; };
		7DB6838E1C995D76000C70BE /* VLCMediaThumbnailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 636E975711EBC67A002FE8A9 /* VLCMediaThumbnailer.m */; };
		7DB6838F1C995D76000C70BE /* VLCMediaDiscoverer.m in Sources */ = {isa = PBXBuildFile; fileRef = 6360B0E111E7F0C000EAD790 /* VLCMediaDiscoverer.m */; };
		7DB683901C995D76000C70BE /* VLCTime.m in Sources */ = {isa = PBXBuildFile; fileRef = 7A5ECAC911DE8F7300F66AF3 /* VLCTime.m */; };
		7DB683911C995D76000C70BE /* VLCDialogProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D803EB51C8F21D200864A9C /* VLCDialogProvider.m */; };
		7DB683921C995D76000C70BE /* VLCCustomDialogProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D34F5501C909DDF008A39F0 /* VLCCustomDialogProvider.m */; };
		7DB683931C995D76000C70BE /* VLCEmbeddedDialogProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D803EBE1C8F2AB400864A9C /* VLCEmbeddedDialogProvider.m */; };
		7DB683B21C995E1D000C70BE /* VLCAudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 4B49C5FD1BC3D7960052D40C /* VLCAudio.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DB683B31C995E1D000C70BE /* VLCLibrary.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECAD411DE8FAB00F66AF3 /* VLCLibrary.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DB683B41C995E1D000C70BE /* VLCMedia.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECAD511DE8FAB00F66AF3 /* VLCMedia.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DB683B51C995E1D000C70BE /* VLCMediaThumbnailer.h in Headers */ = {isa = PBXBuildFile; fileRef = 636E979011EBC96D002FE8A9 /* VLCMediaThumbnailer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DB683B61C995E1D000C70BE /* VLCMediaList.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECAD611DE8FAB00F66AF3 /* VLCMediaList.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DB683B71C995E1D000C70BE /* VLCMediaPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECAD711DE8FAB00F66AF3 /* VLCMediaPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DB683B81C995E1D000C70BE /* VLCMediaListPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D5F800B185B730F00C2CD75 /* VLCMediaListPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DB683B91C995E1D000C70BE /* VLCMediaDiscoverer.h in Headers */ = {isa = PBXBuildFile; fileRef = 6360B0E311E7F0D300EAD790 /* VLCMediaDiscoverer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DB683BA1C995E1D000C70BE /* VLCDialogProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D803EB71C8F21EF00864A9C /* VLCDialogProvider.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DB683BB1C995E1D000C70BE /* VLCTime.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECAD811DE8FAB00F66AF3 /* VLCTime.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DB683BC1C995E34000C70BE /* VLCLibVLCBridging.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECAE411DE8FEF00F66AF3 /* VLCLibVLCBridging.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DB683BE1C995E34000C70BE /* VLCEmbeddedDialogProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D803EC31C8F2AEF00864A9C /* VLCEmbeddedDialogProvider.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DB683BF1C995E34000C70BE /* VLCiOSLegacyDialogProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D803EC41C8F2AEF00864A9C /* VLCiOSLegacyDialogProvider.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DB683C01C995E34000C70BE /* VLCCustomDialogProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D34F5551C909DF6008A39F0 /* VLCCustomDialogProvider.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DB683D71C996187000C70BE /* VLCHelperCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DB683D31C996187000C70BE /* VLCHelperCode.m */; };
		7DB683D81C996187000C70BE /* VLCHelperCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DB683D31C996187000C70BE /* VLCHelperCode.m */; };
		7DB683DA1C9961BA000C70BE /* VLCHelperCode.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DB683D91C9961BA000C70BE /* VLCHelperCode.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DB683DB1C9961BA000C70BE /* VLCHelperCode.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DB683D91C9961BA000C70BE /* VLCHelperCode.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DDC020B1B501ECF0078FC84 /* VLCLibrary.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECAD411DE8FAB00F66AF3 /* VLCLibrary.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DDC020C1B501ECF0078FC84 /* VLCMedia.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECAD511DE8FAB00F66AF3 /* VLCMedia.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DDC020D1B501ECF0078FC84 /* VLCMediaThumbnailer.h in Headers */ = {isa = PBXBuildFile; fileRef = 636E979011EBC96D002FE8A9 /* VLCMediaThumbnailer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DDC020E1B501ECF0078FC84 /* VLCMediaList.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECAD611DE8FAB00F66AF3 /* VLCMediaList.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DDC020F1B501ECF0078FC84 /* VLCMediaPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECAD711DE8FAB00F66AF3 /* VLCMediaPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DDC02101B501ECF0078FC84 /* VLCMediaListPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D5F800B185B730F00C2CD75 /* VLCMediaListPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DDC02111B501ECF0078FC84 /* VLCMediaDiscoverer.h in Headers */ = {isa = PBXBuildFile; fileRef = 6360B0E311E7F0D300EAD790 /* VLCMediaDiscoverer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DDC02121B501ECF0078FC84 /* VLCTime.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECAD811DE8FAB00F66AF3 /* VLCTime.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DDC02151B501ED70078FC84 /* VLCLibVLCBridging.h in Headers */ = {isa = PBXBuildFile; fileRef = 7A5ECAE411DE8FEF00F66AF3 /* VLCLibVLCBridging.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DE71F7B25FA18F200D8767B /* libStaticTVLibvlc.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DB683C61C995E9A000C70BE /* libStaticTVLibvlc.a */; };
		7DE9EAD41FB9E00E00859664 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DE9EAD31FB9E00E00859664 /* CoreMedia.framework */; };
		7DE9EAD61FB9E01700859664 /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DE9EAD51FB9E01700859664 /* VideoToolbox.framework */; };
		7DE9EAD81FB9E02200859664 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DE9EAD71FB9E02200859664 /* AudioToolbox.framework */; };
		7DE9EADA1FB9E02800859664 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DE9EAD91FB9E02700859664 /* AVFoundation.framework */; };
		7DEBDAF8203C5713000A7D2F /* deprecated.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE0203C56D4000A7D2F /* deprecated.h */; };
		7DEBDAF9203C5713000A7D2F /* libvlc_events.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE9203C56D5000A7D2F /* libvlc_events.h */; };
		7DEBDAFA203C5713000A7D2F /* libvlc_media_discoverer.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAEA203C56D5000A7D2F /* libvlc_media_discoverer.h */; };
		7DEBDAFB203C5713000A7D2F /* libvlc_media_library.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE1203C56D4000A7D2F /* libvlc_media_library.h */; };
		7DEBDAFC203C5713000A7D2F /* libvlc_media_list_player.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE2203C56D5000A7D2F /* libvlc_media_list_player.h */; };
		7DEBDAFD203C5713000A7D2F /* libvlc_media_list.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDADF203C56D4000A7D2F /* libvlc_media_list.h */; };
		7DEBDAFE203C5713000A7D2F /* libvlc_media_player.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE3203C56D5000A7D2F /* libvlc_media_player.h */; };
		7DEBDAFF203C5713000A7D2F /* libvlc_media.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE5203C56D5000A7D2F /* libvlc_media.h */; };
		7DEBDB00203C5713000A7D2F /* libvlc_renderer_discoverer.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE4203C56D5000A7D2F /* libvlc_renderer_discoverer.h */; };
		7DEBDB01203C5713000A7D2F /* libvlc_vlm.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE8203C56D5000A7D2F /* libvlc_vlm.h */; };
		7DEBDB02203C5713000A7D2F /* libvlc.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE6203C56D5000A7D2F /* libvlc.h */; };
		7DEBDB03203C5713000A7D2F /* vlc.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE7203C56D5000A7D2F /* vlc.h */; };
		7DEBDB05203C5727000A7D2F /* deprecated.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE0203C56D4000A7D2F /* deprecated.h */; };
		7DEBDB06203C5727000A7D2F /* libvlc_events.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE9203C56D5000A7D2F /* libvlc_events.h */; };
		7DEBDB07203C5727000A7D2F /* libvlc_media_discoverer.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAEA203C56D5000A7D2F /* libvlc_media_discoverer.h */; };
		7DEBDB08203C5727000A7D2F /* libvlc_media_library.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE1203C56D4000A7D2F /* libvlc_media_library.h */; };
		7DEBDB09203C5727000A7D2F /* libvlc_media_list_player.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE2203C56D5000A7D2F /* libvlc_media_list_player.h */; };
		7DEBDB0A203C5727000A7D2F /* libvlc_media_list.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDADF203C56D4000A7D2F /* libvlc_media_list.h */; };
		7DEBDB0B203C5727000A7D2F /* libvlc_media_player.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE3203C56D5000A7D2F /* libvlc_media_player.h */; };
		7DEBDB0C203C5727000A7D2F /* libvlc_media.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE5203C56D5000A7D2F /* libvlc_media.h */; };
		7DEBDB0D203C5727000A7D2F /* libvlc_renderer_discoverer.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE4203C56D5000A7D2F /* libvlc_renderer_discoverer.h */; };
		7DEBDB0E203C5727000A7D2F /* libvlc_vlm.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE8203C56D5000A7D2F /* libvlc_vlm.h */; };
		7DEBDB0F203C5727000A7D2F /* libvlc.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE6203C56D5000A7D2F /* libvlc.h */; };
		7DEBDB10203C5727000A7D2F /* vlc.h in CopyFiles */ = {isa = PBXBuildFile; fileRef = 7DEBDAE7203C56D5000A7D2F /* vlc.h */; };
		7DFDF4E21C9AF17800BA86A6 /* VLCDialogProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D803EB51C8F21D200864A9C /* VLCDialogProvider.m */; };
		7DFDF4E71C9AF24700BA86A6 /* VLCAudio.h in Headers */ = {isa = PBXBuildFile; fileRef = 4B49C5FD1BC3D7960052D40C /* VLCAudio.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DFDF4E81C9AF25600BA86A6 /* VLCEmbeddedDialogProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D803EC31C8F2AEF00864A9C /* VLCEmbeddedDialogProvider.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DFDF4E91C9AF25600BA86A6 /* VLCiOSLegacyDialogProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D803EC41C8F2AEF00864A9C /* VLCiOSLegacyDialogProvider.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DFDF4EA1C9AF25600BA86A6 /* VLCCustomDialogProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D34F5551C909DF6008A39F0 /* VLCCustomDialogProvider.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DFDF4EB1C9AF27A00BA86A6 /* VLCDialogProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D803EB71C8F21EF00864A9C /* VLCDialogProvider.h */; settings = {ATTRIBUTES = (Public, ); }; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		7D89394C1B500DBC008F2B14 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7DB683C41C995E9A000C70BE /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7DEBDAF7203C5701000A7D2F /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = Headers/vlc;
			dstSubfolderSpec = 7;
			files = (
				7D1908E2274FE39200A8A947 /* libvlc_version.h in CopyFiles */,
				7D1908E1274FE38A00A8A947 /* libvlc_picture.h in CopyFiles */,
				7D1908E0274FE38400A8A947 /* libvlc_dialog.h in CopyFiles */,
				7DEBDAF8203C5713000A7D2F /* deprecated.h in CopyFiles */,
				7DEBDAF9203C5713000A7D2F /* libvlc_events.h in CopyFiles */,
				7DEBDAFA203C5713000A7D2F /* libvlc_media_discoverer.h in CopyFiles */,
				7DEBDAFB203C5713000A7D2F /* libvlc_media_library.h in CopyFiles */,
				7DEBDAFC203C5713000A7D2F /* libvlc_media_list_player.h in CopyFiles */,
				7DEBDAFD203C5713000A7D2F /* libvlc_media_list.h in CopyFiles */,
				7DEBDAFE203C5713000A7D2F /* libvlc_media_player.h in CopyFiles */,
				7DEBDAFF203C5713000A7D2F /* libvlc_media.h in CopyFiles */,
				7DEBDB00203C5713000A7D2F /* libvlc_renderer_discoverer.h in CopyFiles */,
				7DEBDB01203C5713000A7D2F /* libvlc_vlm.h in CopyFiles */,
				7DEBDB02203C5713000A7D2F /* libvlc.h in CopyFiles */,
				7DEBDB03203C5713000A7D2F /* vlc.h in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7DEBDB04203C5722000A7D2F /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = Headers/vlc;
			dstSubfolderSpec = 7;
			files = (
				7D1908DF274FE37800A8A947 /* libvlc_version.h in CopyFiles */,
				7D1908DB274FE30400A8A947 /* libvlc_picture.h in CopyFiles */,
				7D1908DA274FE2FD00A8A947 /* libvlc_dialog.h in CopyFiles */,
				7DEBDB05203C5727000A7D2F /* deprecated.h in CopyFiles */,
				7DEBDB06203C5727000A7D2F /* libvlc_events.h in CopyFiles */,
				7DEBDB07203C5727000A7D2F /* libvlc_media_discoverer.h in CopyFiles */,
				7DEBDB08203C5727000A7D2F /* libvlc_media_library.h in CopyFiles */,
				7DEBDB09203C5727000A7D2F /* libvlc_media_list_player.h in CopyFiles */,
				7DEBDB0A203C5727000A7D2F /* libvlc_media_list.h in CopyFiles */,
				7DEBDB0B203C5727000A7D2F /* libvlc_media_player.h in CopyFiles */,
				7DEBDB0C203C5727000A7D2F /* libvlc_media.h in CopyFiles */,
				7DEBDB0D203C5727000A7D2F /* libvlc_renderer_discoverer.h in CopyFiles */,
				7DEBDB0E203C5727000A7D2F /* libvlc_vlm.h in CopyFiles */,
				7DEBDB0F203C5727000A7D2F /* libvlc.h in CopyFiles */,
				7DEBDB10203C5727000A7D2F /* vlc.h in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		3C411D07297969390057ACA1 /* VLCAudioEqualizer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCAudioEqualizer.m; path = Sources/VLCAudioEqualizer.m; sourceTree = "<group>"; };
		3C411D0A2979694C0057ACA1 /* VLCAudioEqualizer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCAudioEqualizer.h; path = Headers/Public/VLCAudioEqualizer.h; sourceTree = "<group>"; };
		3F7CC79621D69CB40094B074 /* module.modulemap */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.module-map"; name = module.modulemap; path = Resources/TVVLCKit/module.modulemap; sourceTree = SOURCE_ROOT; };
		3F7CC79721D69D9A0094B074 /* module.modulemap */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = "sourcecode.module-map"; name = module.modulemap; path = Resources/MobileVLCKit/module.modulemap; sourceTree = "<group>"; };
		41E1959621BEA28F00F10277 /* VLCTranscoder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCTranscoder.m; path = Sources/VLCTranscoder.m; sourceTree = "<group>"; };
		41E1959821BEA2AF00F10277 /* VLCTranscoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCTranscoder.h; path = Headers/Public/VLCTranscoder.h; sourceTree = "<group>"; };
		4B49C5FB1BC3D7870052D40C /* VLCAudio.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCAudio.m; path = Sources/VLCAudio.m; sourceTree = "<group>"; };
		4B49C5FD1BC3D7960052D40C /* VLCAudio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCAudio.h; path = Headers/Public/VLCAudio.h; sourceTree = "<group>"; };
		6360B0E111E7F0C000EAD790 /* VLCMediaDiscoverer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCMediaDiscoverer.m; path = Sources/VLCMediaDiscoverer.m; sourceTree = "<group>"; };
		6360B0E311E7F0D300EAD790 /* VLCMediaDiscoverer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaDiscoverer.h; path = Headers/Public/VLCMediaDiscoverer.h; sourceTree = "<group>"; };
		636E975711EBC67A002FE8A9 /* VLCMediaThumbnailer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCMediaThumbnailer.m; path = Sources/VLCMediaThumbnailer.m; sourceTree = "<group>"; };
		636E979011EBC96D002FE8A9 /* VLCMediaThumbnailer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaThumbnailer.h; path = Headers/Public/VLCMediaThumbnailer.h; sourceTree = "<group>"; };
		6C121E0027BA565A00769422 /* VLCFilter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCFilter.h; path = Headers/Public/VLCFilter.h; sourceTree = "<group>"; };
		6C121E0327BA584200769422 /* VLCAdjustFilter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCAdjustFilter.h; path = Headers/Public/VLCAdjustFilter.h; sourceTree = "<group>"; };
		6C121E0627BA59C500769422 /* VLCFilter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = VLCFilter.m; path = Sources/VLCFilter.m; sourceTree = "<group>"; };
		6C121E0927BA60BE00769422 /* VLCAdjustFilter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = VLCAdjustFilter.m; path = Sources/VLCAdjustFilter.m; sourceTree = "<group>"; };
		6C13FFBA29BF8D4300C4C68E /* VLCEventsHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCEventsHandler.h; path = Headers/Internal/VLCEventsHandler.h; sourceTree = "<group>"; };
		6C13FFBD29BF8D5300C4C68E /* VLCEventsHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCEventsHandler.m; path = Sources/VLCEventsHandler.m; sourceTree = "<group>"; };
		6C57B71727C4CFF1002E5A3A /* VLCMediaPlayer+Internal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = "VLCMediaPlayer+Internal.h"; path = "Headers/Internal/VLCMediaPlayer+Internal.h"; sourceTree = "<group>"; };
		6C63354928169D9A00AF8CCF /* VLCLogging.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCLogging.h; path = Headers/Public/VLCLogging.h; sourceTree = "<group>"; };
		6C63354C28169EDE00AF8CCF /* VLCConsoleLogger.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCConsoleLogger.h; path = Headers/Public/VLCConsoleLogger.h; sourceTree = "<group>"; };
		6C63354D28169EDE00AF8CCF /* VLCConsoleLogger.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = VLCConsoleLogger.m; path = Sources/VLCConsoleLogger.m; sourceTree = "<group>"; };
		6C6335522816A09600AF8CCF /* VLCFileLogger.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCFileLogger.h; path = Headers/Public/VLCFileLogger.h; sourceTree = "<group>"; };
		6C6335532816A09600AF8CCF /* VLCFileLogger.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = VLCFileLogger.m; path = Sources/VLCFileLogger.m; sourceTree = "<group>"; };
		6C6335582816D75D00AF8CCF /* VLCLogMessageFormatter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCLogMessageFormatter.h; path = Headers/Public/VLCLogMessageFormatter.h; sourceTree = "<group>"; };
		6C6335592816D75D00AF8CCF /* VLCLogMessageFormatter.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = VLCLogMessageFormatter.m; path = Sources/VLCLogMessageFormatter.m; sourceTree = "<group>"; };
		6C6ABBD027C6639D008FDB2D /* VLCFilter+Internal.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = "VLCFilter+Internal.h"; path = "Headers/Internal/VLCFilter+Internal.h"; sourceTree = "<group>"; };
		7A5ECAC511DE8F7300F66AF3 /* VLCLibrary.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCLibrary.m; path = Sources/VLCLibrary.m; sourceTree = "<group>"; };
		7A5ECAC611DE8F7300F66AF3 /* VLCMedia.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCMedia.m; path = Sources/VLCMedia.m; sourceTree = "<group>"; };
		7A5ECAC711DE8F7300F66AF3 /* VLCMediaList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCMediaList.m; path = Sources/VLCMediaList.m; sourceTree = "<group>"; };
		7A5ECAC811DE8F7300F66AF3 /* VLCMediaPlayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCMediaPlayer.m; path = Sources/VLCMediaPlayer.m; sourceTree = "<group>"; };
		7A5ECAC911DE8F7300F66AF3 /* VLCTime.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCTime.m; path = Sources/VLCTime.m; sourceTree = "<group>"; };
		7A5ECAD411DE8FAB00F66AF3 /* VLCLibrary.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCLibrary.h; path = Headers/Public/VLCLibrary.h; sourceTree = "<group>"; };
		7A5ECAD511DE8FAB00F66AF3 /* VLCMedia.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMedia.h; path = Headers/Public/VLCMedia.h; sourceTree = "<group>"; };
		7A5ECAD611DE8FAB00F66AF3 /* VLCMediaList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaList.h; path = Headers/Public/VLCMediaList.h; sourceTree = "<group>"; };
		7A5ECAD711DE8FAB00F66AF3 /* VLCMediaPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaPlayer.h; path = Headers/Public/VLCMediaPlayer.h; sourceTree = "<group>"; };
		7A5ECAD811DE8FAB00F66AF3 /* VLCTime.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCTime.h; path = Headers/Public/VLCTime.h; sourceTree = "<group>"; };
		7A5ECAE411DE8FEF00F66AF3 /* VLCLibVLCBridging.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCLibVLCBridging.h; path = Headers/Internal/VLCLibVLCBridging.h; sourceTree = "<group>"; };
		7A5ECB9D11DE975700F66AF3 /* MobileVLCKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = MobileVLCKit.h; path = Headers/Public/MobileVLCKit.h; sourceTree = "<group>"; };
		7D1329751BA305D600BE647E /* vlc-plugins-AppleTV.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = "vlc-plugins-AppleTV.xcconfig"; path = "Resources/MobileVLCKit/vlc-plugins-AppleTV.xcconfig"; sourceTree = "<group>"; };
		7D1329761BA306DE00BE647E /* vlc-plugins-iPhone.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "vlc-plugins-iPhone.h"; path = "Resources/MobileVLCKit/vlc-plugins-iPhone.h"; sourceTree = "<group>"; };
		7D1329771BA306E600BE647E /* vlc-plugins-AppleTV.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "vlc-plugins-AppleTV.h"; path = "Resources/MobileVLCKit/vlc-plugins-AppleTV.h"; sourceTree = "<group>"; };
		7D1329791BA3074100BE647E /* vlc-plugins-iPhone.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = "vlc-plugins-iPhone.xcconfig"; path = "Resources/MobileVLCKit/vlc-plugins-iPhone.xcconfig"; sourceTree = "<group>"; };
		7D1908D4274FE1F300A8A947 /* libvlc_dialog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_dialog.h; path = libvlc/vlc/include/vlc/libvlc_dialog.h; sourceTree = "<group>"; };
		7D1908D7274FE21000A8A947 /* libvlc_picture.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_picture.h; path = libvlc/vlc/include/vlc/libvlc_picture.h; sourceTree = "<group>"; };
		7D1908DC274FE37100A8A947 /* libvlc_version.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_version.h; path = Headers/libvlc_version.h; sourceTree = "<group>"; };
		7D24C2B51EC0A3390047E700 /* libbz2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.tbd; path = usr/lib/libbz2.tbd; sourceTree = SDKROOT; };
		7D24C2B71EC0A3400047E700 /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS10.2.sdk/System/Library/Frameworks/VideoToolbox.framework; sourceTree = DEVELOPER_DIR; };
		7D24C2B91EC0A3460047E700 /* libbz2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.tbd; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS10.2.sdk/usr/lib/libbz2.tbd; sourceTree = DEVELOPER_DIR; };
		7D34F5501C909DDF008A39F0 /* VLCCustomDialogProvider.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCCustomDialogProvider.m; path = Sources/VLCCustomDialogProvider.m; sourceTree = "<group>"; };
		7D34F5551C909DF6008A39F0 /* VLCCustomDialogProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCCustomDialogProvider.h; path = Headers/Internal/VLCCustomDialogProvider.h; sourceTree = "<group>"; };
		7D5F8009185B72EB00C2CD75 /* VLCMediaListPlayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCMediaListPlayer.m; path = Sources/VLCMediaListPlayer.m; sourceTree = "<group>"; };
		7D5F800B185B730F00C2CD75 /* VLCMediaListPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaListPlayer.h; path = Headers/Public/VLCMediaListPlayer.h; sourceTree = "<group>"; };
		7D6C891F1C0CA81F00321894 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		7D797FB21DF41E8400AD93ED /* TVVLCKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = TVVLCKit.h; path = Headers/Public/TVVLCKit.h; sourceTree = SOURCE_ROOT; };
		7D797FB41DF41EBA00AD93ED /* StaticLibVLC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = StaticLibVLC.m; path = Sources/StaticLibVLC.m; sourceTree = SOURCE_ROOT; };
		7D797FB71DF41EF800AD93ED /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS10.0.sdk/System/Library/Frameworks/AVFoundation.framework; sourceTree = DEVELOPER_DIR; };
		7D797FB91DF41EFB00AD93ED /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS10.0.sdk/System/Library/Frameworks/AudioToolbox.framework; sourceTree = DEVELOPER_DIR; };
		7D797FBB1DF41F0300AD93ED /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS10.0.sdk/usr/lib/libc++.tbd"; sourceTree = DEVELOPER_DIR; };
		7D797FBD1DF41F1B00AD93ED /* libiconv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.tbd; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS10.0.sdk/usr/lib/libiconv.tbd; sourceTree = DEVELOPER_DIR; };
		7D797FBF1DF41F2000AD93ED /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS10.0.sdk/usr/lib/libz.tbd; sourceTree = DEVELOPER_DIR; };
		7D797FC31DF41F9100AD93ED /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		7D797FC51DF41F9500AD93ED /* libiconv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.tbd; path = usr/lib/libiconv.tbd; sourceTree = SDKROOT; };
		7D803EB51C8F21D200864A9C /* VLCDialogProvider.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCDialogProvider.m; path = Sources/VLCDialogProvider.m; sourceTree = "<group>"; };
		7D803EB71C8F21EF00864A9C /* VLCDialogProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCDialogProvider.h; path = Headers/Public/VLCDialogProvider.h; sourceTree = "<group>"; };
		7D803EBE1C8F2AB400864A9C /* VLCEmbeddedDialogProvider.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCEmbeddedDialogProvider.m; path = Sources/VLCEmbeddedDialogProvider.m; sourceTree = "<group>"; };
		7D803EC31C8F2AEF00864A9C /* VLCEmbeddedDialogProvider.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCEmbeddedDialogProvider.h; path = Headers/Internal/VLCEmbeddedDialogProvider.h; sourceTree = "<group>"; };
		7D803EC41C8F2AEF00864A9C /* VLCiOSLegacyDialogProvider.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCiOSLegacyDialogProvider.h; path = Headers/Internal/VLCiOSLegacyDialogProvider.h; sourceTree = "<group>"; };
		7D803EC51C8F2AF900864A9C /* VLCiOSLegacyDialogProvider.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCiOSLegacyDialogProvider.m; path = Sources/VLCiOSLegacyDialogProvider.m; sourceTree = "<group>"; };
		7D8938E81B500984008F2B14 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		7D8938EA1B50098C008F2B14 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		7D8938EC1B500991008F2B14 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		7D8938EE1B500998008F2B14 /* CoreText.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreText.framework; path = System/Library/Frameworks/CoreText.framework; sourceTree = SDKROOT; };
		7D8938F01B5009A2008F2B14 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		7D8938F21B5009A7008F2B14 /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = System/Library/Frameworks/CoreVideo.framework; sourceTree = SDKROOT; };
		7D8938F51B5009B4008F2B14 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		7D8938F71B5009BE008F2B14 /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = System/Library/Frameworks/VideoToolbox.framework; sourceTree = SDKROOT; };
		7D8938F91B5009C2008F2B14 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		7D8938FB1B5009CB008F2B14 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		7D8938FD1B5009D2008F2B14 /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		7D8939011B5009DD008F2B14 /* CoreFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreFoundation.framework; path = System/Library/Frameworks/CoreFoundation.framework; sourceTree = SDKROOT; };
		7D8939271B500D1C008F2B14 /* MobileVLCKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = MobileVLCKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7D89394E1B500DBC008F2B14 /* libStaticLibVLC.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libStaticLibVLC.a; sourceTree = BUILT_PRODUCTS_DIR; };
		7DA7F2ED28D87E2300366EEB /* VLCMediaMetaData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCMediaMetaData.m; path = Sources/VLCMediaMetaData.m; sourceTree = "<group>"; };
		7DA7F2F028D87E2C00366EEB /* VLCMediaMetaData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaMetaData.h; path = Headers/Public/VLCMediaMetaData.h; sourceTree = "<group>"; };
		7DAD5C522A835EED009419DC /* VLCEventsConfiguration.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCEventsConfiguration.h; path = Headers/Public/VLCEventsConfiguration.h; sourceTree = "<group>"; };
		7DAD5C532A835EED009419DC /* VLCEventsConfiguration.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = VLCEventsConfiguration.m; path = Sources/VLCEventsConfiguration.m; sourceTree = "<group>"; };
		7DB6837F1C995D39000C70BE /* TVVLCKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = TVVLCKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7DB683C61C995E9A000C70BE /* libStaticTVLibvlc.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libStaticTVLibvlc.a; sourceTree = BUILT_PRODUCTS_DIR; };
		7DB683D31C996187000C70BE /* VLCHelperCode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VLCHelperCode.m; path = Sources/VLCHelperCode.m; sourceTree = "<group>"; };
		7DB683D91C9961BA000C70BE /* VLCHelperCode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCHelperCode.h; path = Headers/Internal/VLCHelperCode.h; sourceTree = "<group>"; };
		7DE9EAD31FB9E00E00859664 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS11.1.sdk/System/Library/Frameworks/CoreMedia.framework; sourceTree = DEVELOPER_DIR; };
		7DE9EAD51FB9E01700859664 /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS11.1.sdk/System/Library/Frameworks/VideoToolbox.framework; sourceTree = DEVELOPER_DIR; };
		7DE9EAD71FB9E02200859664 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS11.1.sdk/System/Library/Frameworks/AudioToolbox.framework; sourceTree = DEVELOPER_DIR; };
		7DE9EAD91FB9E02700859664 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = Platforms/AppleTVOS.platform/Developer/SDKs/AppleTVOS11.1.sdk/System/Library/Frameworks/AVFoundation.framework; sourceTree = DEVELOPER_DIR; };
		7DEBDADF203C56D4000A7D2F /* libvlc_media_list.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_media_list.h; path = libvlc/vlc/include/vlc/libvlc_media_list.h; sourceTree = "<group>"; };
		7DEBDAE0203C56D4000A7D2F /* deprecated.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = deprecated.h; path = libvlc/vlc/include/vlc/deprecated.h; sourceTree = "<group>"; };
		7DEBDAE1203C56D4000A7D2F /* libvlc_media_library.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_media_library.h; path = libvlc/vlc/include/vlc/libvlc_media_library.h; sourceTree = "<group>"; };
		7DEBDAE2203C56D5000A7D2F /* libvlc_media_list_player.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_media_list_player.h; path = libvlc/vlc/include/vlc/libvlc_media_list_player.h; sourceTree = "<group>"; };
		7DEBDAE3203C56D5000A7D2F /* libvlc_media_player.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_media_player.h; path = libvlc/vlc/include/vlc/libvlc_media_player.h; sourceTree = "<group>"; };
		7DEBDAE4203C56D5000A7D2F /* libvlc_renderer_discoverer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_renderer_discoverer.h; path = libvlc/vlc/include/vlc/libvlc_renderer_discoverer.h; sourceTree = "<group>"; };
		7DEBDAE5203C56D5000A7D2F /* libvlc_media.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_media.h; path = libvlc/vlc/include/vlc/libvlc_media.h; sourceTree = "<group>"; };
		7DEBDAE6203C56D5000A7D2F /* libvlc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc.h; path = libvlc/vlc/include/vlc/libvlc.h; sourceTree = "<group>"; };
		7DEBDAE7203C56D5000A7D2F /* vlc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = vlc.h; path = libvlc/vlc/include/vlc/vlc.h; sourceTree = "<group>"; };
		7DEBDAE8203C56D5000A7D2F /* libvlc_vlm.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_vlm.h; path = libvlc/vlc/include/vlc/libvlc_vlm.h; sourceTree = "<group>"; };
		7DEBDAE9203C56D5000A7D2F /* libvlc_events.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_events.h; path = libvlc/vlc/include/vlc/libvlc_events.h; sourceTree = "<group>"; };
		7DEBDAEA203C56D5000A7D2F /* libvlc_media_discoverer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_media_discoverer.h; path = libvlc/vlc/include/vlc/libvlc_media_discoverer.h; sourceTree = "<group>"; };
		8D2CE647203DCC48004BB7F6 /* VLCRendererDiscoverer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCRendererDiscoverer.h; path = Headers/Public/VLCRendererDiscoverer.h; sourceTree = "<group>"; };
		8D2CE648203DCC48004BB7F6 /* VLCRendererDiscoverer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = VLCRendererDiscoverer.m; path = Sources/VLCRendererDiscoverer.m; sourceTree = "<group>"; };
		8D2CE64E203EEA47004BB7F6 /* VLCRendererItem.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCRendererItem.h; path = Headers/Public/VLCRendererItem.h; sourceTree = "<group>"; };
		8D2CE64F203EEA47004BB7F6 /* VLCRendererItem.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; name = VLCRendererItem.m; path = Sources/VLCRendererItem.m; sourceTree = "<group>"; };
		AA747D9E0F9514B9006C5449 /* MobilePrefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = MobilePrefix.pch; path = Headers/PCH/MobilePrefix.pch; sourceTree = SOURCE_ROOT; };
		AACBBE490F95108600F1A2B1 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7D8939231B500D1C008F2B14 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				7D24C2B61EC0A3390047E700 /* libbz2.tbd in Frameworks */,
				7D797FC61DF41F9500AD93ED /* libiconv.tbd in Frameworks */,
				7D797FC41DF41F9100AD93ED /* libc++.tbd in Frameworks */,
				7D6C89201C0CA81F00321894 /* AVFoundation.framework in Frameworks */,
				7D8939651B500F13008F2B14 /* libStaticLibVLC.a in Frameworks */,
				7D8939671B500F38008F2B14 /* CoreFoundation.framework in Frameworks */,
				7D8939691B500F38008F2B14 /* OpenGLES.framework in Frameworks */,
				7D89396A1B500F38008F2B14 /* UIKit.framework in Frameworks */,
				7D89396B1B500F38008F2B14 /* Security.framework in Frameworks */,
				7D89396C1B500F38008F2B14 /* VideoToolbox.framework in Frameworks */,
				7D89396D1B500F38008F2B14 /* CoreMedia.framework in Frameworks */,
				7D89396E1B500F38008F2B14 /* CoreVideo.framework in Frameworks */,
				7D89396F1B500F38008F2B14 /* AudioToolbox.framework in Frameworks */,
				7D8939701B500F38008F2B14 /* CoreText.framework in Frameworks */,
				7D8939711B500F38008F2B14 /* CoreGraphics.framework in Frameworks */,
				7D8939721B500F38008F2B14 /* CFNetwork.framework in Frameworks */,
				7D8939731B500F38008F2B14 /* QuartzCore.framework in Frameworks */,
				7D8939741B500F38008F2B14 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D89394B1B500DBC008F2B14 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7DB6837B1C995D39000C70BE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				7DE71F7B25FA18F200D8767B /* libStaticTVLibvlc.a in Frameworks */,
				7DE9EADA1FB9E02800859664 /* AVFoundation.framework in Frameworks */,
				7DE9EAD81FB9E02200859664 /* AudioToolbox.framework in Frameworks */,
				7DE9EAD61FB9E01700859664 /* VideoToolbox.framework in Frameworks */,
				7DE9EAD41FB9E00E00859664 /* CoreMedia.framework in Frameworks */,
				7D24C2BA1EC0A3460047E700 /* libbz2.tbd in Frameworks */,
				7D797FC01DF41F2000AD93ED /* libz.tbd in Frameworks */,
				7D797FBE1DF41F1B00AD93ED /* libiconv.tbd in Frameworks */,
				7D797FBC1DF41F0300AD93ED /* libc++.tbd in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7DB683C31C995E9A000C70BE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		034768DFFF38A50411DB9C8B /* Products */ = {
			isa = PBXGroup;
			children = (
				7D8939271B500D1C008F2B14 /* MobileVLCKit.framework */,
				7D89394E1B500DBC008F2B14 /* libStaticLibVLC.a */,
				7DB6837F1C995D39000C70BE /* TVVLCKit.framework */,
				7DB683C61C995E9A000C70BE /* libStaticTVLibvlc.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0867D691FE84028FC02AAC07 /* MobileVLCKit */ = {
			isa = PBXGroup;
			children = (
				7A5ECAE211DE8FD200F66AF3 /* Headers */,
				08FB77AEFE84172EC02AAC07 /* Sources */,
				32C88DFF0371C24200C91783 /* Other Sources */,
				7D89394F1B500DBC008F2B14 /* StaticLibVLC */,
				7D13295A1BA304D900BE647E /* TVVLCKit */,
				0867D69AFE84028FC02AAC07 /* Frameworks */,
				034768DFFF38A50411DB9C8B /* Products */,
			);
			name = MobileVLCKit;
			sourceTree = "<group>";
		};
		0867D69AFE84028FC02AAC07 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				7DE9EAD91FB9E02700859664 /* AVFoundation.framework */,
				7DE9EAD71FB9E02200859664 /* AudioToolbox.framework */,
				7DE9EAD51FB9E01700859664 /* VideoToolbox.framework */,
				7DE9EAD31FB9E00E00859664 /* CoreMedia.framework */,
				7D24C2B91EC0A3460047E700 /* libbz2.tbd */,
				7D24C2B71EC0A3400047E700 /* VideoToolbox.framework */,
				7D24C2B51EC0A3390047E700 /* libbz2.tbd */,
				7D797FC51DF41F9500AD93ED /* libiconv.tbd */,
				7D797FC31DF41F9100AD93ED /* libc++.tbd */,
				7D797FBF1DF41F2000AD93ED /* libz.tbd */,
				7D797FBD1DF41F1B00AD93ED /* libiconv.tbd */,
				7D797FBB1DF41F0300AD93ED /* libc++.tbd */,
				7D797FB91DF41EFB00AD93ED /* AudioToolbox.framework */,
				7D797FB71DF41EF800AD93ED /* AVFoundation.framework */,
				7D6C891F1C0CA81F00321894 /* AVFoundation.framework */,
				7D8939011B5009DD008F2B14 /* CoreFoundation.framework */,
				7D8938FD1B5009D2008F2B14 /* OpenGLES.framework */,
				7D8938FB1B5009CB008F2B14 /* UIKit.framework */,
				7D8938F91B5009C2008F2B14 /* Security.framework */,
				7D8938F71B5009BE008F2B14 /* VideoToolbox.framework */,
				7D8938F51B5009B4008F2B14 /* CoreMedia.framework */,
				7D8938F21B5009A7008F2B14 /* CoreVideo.framework */,
				7D8938F01B5009A2008F2B14 /* AudioToolbox.framework */,
				7D8938EE1B500998008F2B14 /* CoreText.framework */,
				7D8938EC1B500991008F2B14 /* CoreGraphics.framework */,
				7D8938EA1B50098C008F2B14 /* CFNetwork.framework */,
				7D8938E81B500984008F2B14 /* QuartzCore.framework */,
				AACBBE490F95108600F1A2B1 /* Foundation.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		08FB77AEFE84172EC02AAC07 /* Sources */ = {
			isa = PBXGroup;
			children = (
				6C13FFBD29BF8D5300C4C68E /* VLCEventsHandler.m */,
				7DAD5C532A835EED009419DC /* VLCEventsConfiguration.m */,
				4B49C5FB1BC3D7870052D40C /* VLCAudio.m */,
				7A5ECAC511DE8F7300F66AF3 /* VLCLibrary.m */,
				7A5ECAC611DE8F7300F66AF3 /* VLCMedia.m */,
				7A5ECAC711DE8F7300F66AF3 /* VLCMediaList.m */,
				7A5ECAC811DE8F7300F66AF3 /* VLCMediaPlayer.m */,
				3C411D07297969390057ACA1 /* VLCAudioEqualizer.m */,
				7D5F8009185B72EB00C2CD75 /* VLCMediaListPlayer.m */,
				636E975711EBC67A002FE8A9 /* VLCMediaThumbnailer.m */,
				6360B0E111E7F0C000EAD790 /* VLCMediaDiscoverer.m */,
				7DA7F2ED28D87E2300366EEB /* VLCMediaMetaData.m */,
				7A5ECAC911DE8F7300F66AF3 /* VLCTime.m */,
				41E1959621BEA28F00F10277 /* VLCTranscoder.m */,
				7D34F5571C909E13008A39F0 /* Dialogs */,
				7DB683D31C996187000C70BE /* VLCHelperCode.m */,
				8D826AE0203F3CA700536EF7 /* Renderer */,
				6C121E0627BA59C500769422 /* VLCFilter.m */,
				6C121E0927BA60BE00769422 /* VLCAdjustFilter.m */,
				6C63354D28169EDE00AF8CCF /* VLCConsoleLogger.m */,
				6C6335532816A09600AF8CCF /* VLCFileLogger.m */,
				6C6335592816D75D00AF8CCF /* VLCLogMessageFormatter.m */,
			);
			name = Sources;
			sourceTree = "<group>";
		};
		32C88DFF0371C24200C91783 /* Other Sources */ = {
			isa = PBXGroup;
			children = (
				3F7CC79721D69D9A0094B074 /* module.modulemap */,
				7D1329791BA3074100BE647E /* vlc-plugins-iPhone.xcconfig */,
				AA747D9E0F9514B9006C5449 /* MobilePrefix.pch */,
				7D1329751BA305D600BE647E /* vlc-plugins-AppleTV.xcconfig */,
			);
			name = "Other Sources";
			sourceTree = "<group>";
		};
		7A5ECAE211DE8FD200F66AF3 /* Headers */ = {
			isa = PBXGroup;
			children = (
				7A5ECB9D11DE975700F66AF3 /* MobileVLCKit.h */,
				7A5ECAE311DE8FDF00F66AF3 /* Internal */,
				4B49C5FD1BC3D7960052D40C /* VLCAudio.h */,
				7A5ECAD411DE8FAB00F66AF3 /* VLCLibrary.h */,
				7DAD5C522A835EED009419DC /* VLCEventsConfiguration.h */,
				7A5ECAD511DE8FAB00F66AF3 /* VLCMedia.h */,
				636E979011EBC96D002FE8A9 /* VLCMediaThumbnailer.h */,
				7A5ECAD611DE8FAB00F66AF3 /* VLCMediaList.h */,
				7A5ECAD711DE8FAB00F66AF3 /* VLCMediaPlayer.h */,
				3C411D0A2979694C0057ACA1 /* VLCAudioEqualizer.h */,
				7D5F800B185B730F00C2CD75 /* VLCMediaListPlayer.h */,
				6360B0E311E7F0D300EAD790 /* VLCMediaDiscoverer.h */,
				7D803EB71C8F21EF00864A9C /* VLCDialogProvider.h */,
				7A5ECAD811DE8FAB00F66AF3 /* VLCTime.h */,
				41E1959821BEA2AF00F10277 /* VLCTranscoder.h */,
				8D2CE647203DCC48004BB7F6 /* VLCRendererDiscoverer.h */,
				8D2CE64E203EEA47004BB7F6 /* VLCRendererItem.h */,
				7DA7F2F028D87E2C00366EEB /* VLCMediaMetaData.h */,
				7DEBDADE203C56BC000A7D2F /* libvlc */,
				6C121E0027BA565A00769422 /* VLCFilter.h */,
				6C121E0327BA584200769422 /* VLCAdjustFilter.h */,
				6C63354928169D9A00AF8CCF /* VLCLogging.h */,
				6C63354C28169EDE00AF8CCF /* VLCConsoleLogger.h */,
				6C6335522816A09600AF8CCF /* VLCFileLogger.h */,
				6C6335582816D75D00AF8CCF /* VLCLogMessageFormatter.h */,
			);
			name = Headers;
			sourceTree = "<group>";
		};
		7A5ECAE311DE8FDF00F66AF3 /* Internal */ = {
			isa = PBXGroup;
			children = (
				6C13FFBA29BF8D4300C4C68E /* VLCEventsHandler.h */,
				7D1329761BA306DE00BE647E /* vlc-plugins-iPhone.h */,
				7D1329771BA306E600BE647E /* vlc-plugins-AppleTV.h */,
				7A5ECAE411DE8FEF00F66AF3 /* VLCLibVLCBridging.h */,
				7D803EC31C8F2AEF00864A9C /* VLCEmbeddedDialogProvider.h */,
				7D803EC41C8F2AEF00864A9C /* VLCiOSLegacyDialogProvider.h */,
				7D34F5551C909DF6008A39F0 /* VLCCustomDialogProvider.h */,
				7DB683D91C9961BA000C70BE /* VLCHelperCode.h */,
				6C57B71727C4CFF1002E5A3A /* VLCMediaPlayer+Internal.h */,
				6C6ABBD027C6639D008FDB2D /* VLCFilter+Internal.h */,
			);
			name = Internal;
			sourceTree = "<group>";
		};
		7D13295A1BA304D900BE647E /* TVVLCKit */ = {
			isa = PBXGroup;
			children = (
				3F7CC79621D69CB40094B074 /* module.modulemap */,
				7D797FB21DF41E8400AD93ED /* TVVLCKit.h */,
			);
			path = TVVLCKit;
			sourceTree = "<group>";
		};
		7D34F5571C909E13008A39F0 /* Dialogs */ = {
			isa = PBXGroup;
			children = (
				7D803EB51C8F21D200864A9C /* VLCDialogProvider.m */,
				7D34F5501C909DDF008A39F0 /* VLCCustomDialogProvider.m */,
				7D803EC51C8F2AF900864A9C /* VLCiOSLegacyDialogProvider.m */,
				7D803EBE1C8F2AB400864A9C /* VLCEmbeddedDialogProvider.m */,
			);
			name = Dialogs;
			sourceTree = "<group>";
		};
		7D89394F1B500DBC008F2B14 /* StaticLibVLC */ = {
			isa = PBXGroup;
			children = (
				7D797FB41DF41EBA00AD93ED /* StaticLibVLC.m */,
			);
			path = StaticLibVLC;
			sourceTree = "<group>";
		};
		7DEBDADE203C56BC000A7D2F /* libvlc */ = {
			isa = PBXGroup;
			children = (
				7DEBDAE0203C56D4000A7D2F /* deprecated.h */,
				7DEBDAE9203C56D5000A7D2F /* libvlc_events.h */,
				7DEBDAEA203C56D5000A7D2F /* libvlc_media_discoverer.h */,
				7DEBDAE1203C56D4000A7D2F /* libvlc_media_library.h */,
				7DEBDAE2203C56D5000A7D2F /* libvlc_media_list_player.h */,
				7DEBDADF203C56D4000A7D2F /* libvlc_media_list.h */,
				7DEBDAE3203C56D5000A7D2F /* libvlc_media_player.h */,
				7DEBDAE5203C56D5000A7D2F /* libvlc_media.h */,
				7DEBDAE4203C56D5000A7D2F /* libvlc_renderer_discoverer.h */,
				7D1908D4274FE1F300A8A947 /* libvlc_dialog.h */,
				7D1908D7274FE21000A8A947 /* libvlc_picture.h */,
				7DEBDAE8203C56D5000A7D2F /* libvlc_vlm.h */,
				7DEBDAE6203C56D5000A7D2F /* libvlc.h */,
				7D1908DC274FE37100A8A947 /* libvlc_version.h */,
				7DEBDAE7203C56D5000A7D2F /* vlc.h */,
			);
			name = libvlc;
			sourceTree = "<group>";
		};
		8D826AE0203F3CA700536EF7 /* Renderer */ = {
			isa = PBXGroup;
			children = (
				8D2CE648203DCC48004BB7F6 /* VLCRendererDiscoverer.m */,
				8D2CE64F203EEA47004BB7F6 /* VLCRendererItem.m */,
			);
			name = Renderer;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		7D8939241B500D1C008F2B14 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				7DFDF4E71C9AF24700BA86A6 /* VLCAudio.h in Headers */,
				7DDC020B1B501ECF0078FC84 /* VLCLibrary.h in Headers */,
				7DDC020C1B501ECF0078FC84 /* VLCMedia.h in Headers */,
				6C121E0427BA584200769422 /* VLCAdjustFilter.h in Headers */,
				7DDC02111B501ECF0078FC84 /* VLCMediaDiscoverer.h in Headers */,
				6C63354A28169D9A00AF8CCF /* VLCLogging.h in Headers */,
				7DDC020E1B501ECF0078FC84 /* VLCMediaList.h in Headers */,
				7DDC020F1B501ECF0078FC84 /* VLCMediaPlayer.h in Headers */,
				6C63354E28169EDE00AF8CCF /* VLCConsoleLogger.h in Headers */,
				7DA7F2F128D87E2C00366EEB /* VLCMediaMetaData.h in Headers */,
				7DDC02101B501ECF0078FC84 /* VLCMediaListPlayer.h in Headers */,
				7DDC020D1B501ECF0078FC84 /* VLCMediaThumbnailer.h in Headers */,
				7DFDF4EB1C9AF27A00BA86A6 /* VLCDialogProvider.h in Headers */,
				3C411D0B2979694C0057ACA1 /* VLCAudioEqualizer.h in Headers */,
				7DDC02121B501ECF0078FC84 /* VLCTime.h in Headers */,
				7D8DEBF625FBA52C00826083 /* VLCRendererDiscoverer.h in Headers */,
				7D8DEBF725FBA52C00826083 /* VLCRendererItem.h in Headers */,
				7D8DEBF125FBA52200826083 /* VLCTranscoder.h in Headers */,
				6C63355A2816D75D00AF8CCF /* VLCLogMessageFormatter.h in Headers */,
				7D22947825FA3EBC00939476 /* MobileVLCKit.h in Headers */,
				7DAD5C542A835EED009419DC /* VLCEventsConfiguration.h in Headers */,
				6C6335542816A09600AF8CCF /* VLCFileLogger.h in Headers */,
				7DDC02151B501ED70078FC84 /* VLCLibVLCBridging.h in Headers */,
				7DFDF4E81C9AF25600BA86A6 /* VLCEmbeddedDialogProvider.h in Headers */,
				7DFDF4E91C9AF25600BA86A6 /* VLCiOSLegacyDialogProvider.h in Headers */,
				6C6ABBD127C66410008FDB2D /* VLCFilter+Internal.h in Headers */,
				6C57B71827C4D024002E5A3A /* VLCMediaPlayer+Internal.h in Headers */,
				6C13FFBB29BF8D4300C4C68E /* VLCEventsHandler.h in Headers */,
				7DFDF4EA1C9AF25600BA86A6 /* VLCCustomDialogProvider.h in Headers */,
				7DB683DA1C9961BA000C70BE /* VLCHelperCode.h in Headers */,
				6C121E0127BA566500769422 /* VLCFilter.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7DB6837C1C995D39000C70BE /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				7DB683B21C995E1D000C70BE /* VLCAudio.h in Headers */,
				7DB683B31C995E1D000C70BE /* VLCLibrary.h in Headers */,
				7DB683B41C995E1D000C70BE /* VLCMedia.h in Headers */,
				6C121E0527BA584200769422 /* VLCAdjustFilter.h in Headers */,
				7DB683B51C995E1D000C70BE /* VLCMediaThumbnailer.h in Headers */,
				6C63354B28169D9A00AF8CCF /* VLCLogging.h in Headers */,
				7D1908D6274FE1FC00A8A947 /* libvlc_dialog.h in Headers */,
				7DB683B61C995E1D000C70BE /* VLCMediaList.h in Headers */,
				7DA7F2F228D87E2C00366EEB /* VLCMediaMetaData.h in Headers */,
				6C63354F28169EDE00AF8CCF /* VLCConsoleLogger.h in Headers */,
				7DB683B71C995E1D000C70BE /* VLCMediaPlayer.h in Headers */,
				7DB683B81C995E1D000C70BE /* VLCMediaListPlayer.h in Headers */,
				7DB683B91C995E1D000C70BE /* VLCMediaDiscoverer.h in Headers */,
				3C411D0C2979694C0057ACA1 /* VLCAudioEqualizer.h in Headers */,
				7DB683BA1C995E1D000C70BE /* VLCDialogProvider.h in Headers */,
				7DB683BB1C995E1D000C70BE /* VLCTime.h in Headers */,
				7D22946F25FA3E8F00939476 /* TVVLCKit.h in Headers */,
				7DAD5C552A835EED009419DC /* VLCEventsConfiguration.h in Headers */,
				7DB683BC1C995E34000C70BE /* VLCLibVLCBridging.h in Headers */,
				6C63355B2816D75D00AF8CCF /* VLCLogMessageFormatter.h in Headers */,
				6C6335552816A09600AF8CCF /* VLCFileLogger.h in Headers */,
				7D1908D9274FE21000A8A947 /* libvlc_picture.h in Headers */,
				6C6ABBD227C66410008FDB2D /* VLCFilter+Internal.h in Headers */,
				6C57B71927C4D024002E5A3A /* VLCMediaPlayer+Internal.h in Headers */,
				6C13FFBC29BF8D4300C4C68E /* VLCEventsHandler.h in Headers */,
				7D1908DE274FE37100A8A947 /* libvlc_version.h in Headers */,
				7DB683BE1C995E34000C70BE /* VLCEmbeddedDialogProvider.h in Headers */,
				7DB683BF1C995E34000C70BE /* VLCiOSLegacyDialogProvider.h in Headers */,
				7DB683DB1C9961BA000C70BE /* VLCHelperCode.h in Headers */,
				7DB683C01C995E34000C70BE /* VLCCustomDialogProvider.h in Headers */,
				6C121E0227BA566D00769422 /* VLCFilter.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		7D8939261B500D1C008F2B14 /* MobileVLCKit */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7D89393A1B500D1D008F2B14 /* Build configuration list for PBXNativeTarget "MobileVLCKit" */;
			buildPhases = (
				7D8939221B500D1C008F2B14 /* Sources */,
				7D8939231B500D1C008F2B14 /* Frameworks */,
				7D8939241B500D1C008F2B14 /* Headers */,
				7DEBDB04203C5722000A7D2F /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MobileVLCKit;
			productName = MobileVLCKit;
			productReference = 7D8939271B500D1C008F2B14 /* MobileVLCKit.framework */;
			productType = "com.apple.product-type.framework";
		};
		7D89394D1B500DBC008F2B14 /* StaticLibVLC */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7D89395F1B500DBC008F2B14 /* Build configuration list for PBXNativeTarget "StaticLibVLC" */;
			buildPhases = (
				7D89394A1B500DBC008F2B14 /* Sources */,
				7D89394B1B500DBC008F2B14 /* Frameworks */,
				7D89394C1B500DBC008F2B14 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = StaticLibVLC;
			productName = StaticLibVLC;
			productReference = 7D89394E1B500DBC008F2B14 /* libStaticLibVLC.a */;
			productType = "com.apple.product-type.library.static";
		};
		7DB6837E1C995D39000C70BE /* TVVLCKit */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7DB683861C995D39000C70BE /* Build configuration list for PBXNativeTarget "TVVLCKit" */;
			buildPhases = (
				7DB6837A1C995D39000C70BE /* Sources */,
				7DB6837B1C995D39000C70BE /* Frameworks */,
				7DB6837C1C995D39000C70BE /* Headers */,
				7DEBDAF7203C5701000A7D2F /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = TVVLCKit;
			productName = TVVLCKit;
			productReference = 7DB6837F1C995D39000C70BE /* TVVLCKit.framework */;
			productType = "com.apple.product-type.framework";
		};
		7DB683C51C995E9A000C70BE /* StaticTVLibvlc */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7DB683CC1C995E9A000C70BE /* Build configuration list for PBXNativeTarget "StaticTVLibvlc" */;
			buildPhases = (
				7DB683C21C995E9A000C70BE /* Sources */,
				7DB683C31C995E9A000C70BE /* Frameworks */,
				7DB683C41C995E9A000C70BE /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = StaticTVLibvlc;
			productName = StaticTVLibvlc;
			productReference = 7DB683C61C995E9A000C70BE /* libStaticTVLibvlc.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0867D690FE84028FC02AAC07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0420;
				TargetAttributes = {
					7D8939261B500D1C008F2B14 = {
						CreatedOnToolsVersion = 6.4;
					};
					7D89394D1B500DBC008F2B14 = {
						CreatedOnToolsVersion = 6.4;
					};
					7DB6837E1C995D39000C70BE = {
						CreatedOnToolsVersion = 7.3;
					};
					7DB683C51C995E9A000C70BE = {
						CreatedOnToolsVersion = 7.3;
					};
				};
			};
			buildConfigurationList = 1DEB922208733DC00010E9CD /* Build configuration list for PBXProject "MobileVLCKit" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 0867D691FE84028FC02AAC07 /* MobileVLCKit */;
			productRefGroup = 034768DFFF38A50411DB9C8B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7D8939261B500D1C008F2B14 /* MobileVLCKit */,
				7D89394D1B500DBC008F2B14 /* StaticLibVLC */,
				7DB6837E1C995D39000C70BE /* TVVLCKit */,
				7DB683C51C995E9A000C70BE /* StaticTVLibvlc */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		7D8939221B500D1C008F2B14 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7D06ED1A25FBA9B3005F7551 /* VLCTranscoder.m in Sources */,
				6C63355C2816D75D00AF8CCF /* VLCLogMessageFormatter.m in Sources */,
				7D06ED1B25FBA9B3005F7551 /* VLCRendererDiscoverer.m in Sources */,
				7D06ED1C25FBA9B3005F7551 /* VLCRendererItem.m in Sources */,
				7D803EC11C8F2AB400864A9C /* VLCEmbeddedDialogProvider.m in Sources */,
				7D6C89211C0CA90000321894 /* VLCAudio.m in Sources */,
				7D803EC71C8F2AF900864A9C /* VLCiOSLegacyDialogProvider.m in Sources */,
				7D8939411B500D50008F2B14 /* VLCLibrary.m in Sources */,
				7DFDF4E21C9AF17800BA86A6 /* VLCDialogProvider.m in Sources */,
				6C63355028169EDE00AF8CCF /* VLCConsoleLogger.m in Sources */,
				7D8939421B500D50008F2B14 /* VLCMedia.m in Sources */,
				7DB683D71C996187000C70BE /* VLCHelperCode.m in Sources */,
				7D8939431B500D50008F2B14 /* VLCMediaList.m in Sources */,
				7DA7F2EE28D87E2300366EEB /* VLCMediaMetaData.m in Sources */,
				6C121E0727BA59C500769422 /* VLCFilter.m in Sources */,
				7DAD5C562A835EED009419DC /* VLCEventsConfiguration.m in Sources */,
				3C411D08297969390057ACA1 /* VLCAudioEqualizer.m in Sources */,
				7D8939441B500D51008F2B14 /* VLCMediaPlayer.m in Sources */,
				6C121E0A27BA60BE00769422 /* VLCAdjustFilter.m in Sources */,
				7D8939451B500D51008F2B14 /* VLCMediaListPlayer.m in Sources */,
				7D8939461B500D51008F2B14 /* VLCMediaThumbnailer.m in Sources */,
				7D8939471B500D51008F2B14 /* VLCMediaDiscoverer.m in Sources */,
				6C6335562816A09600AF8CCF /* VLCFileLogger.m in Sources */,
				7D34F5531C909DDF008A39F0 /* VLCCustomDialogProvider.m in Sources */,
				7D8939481B500D51008F2B14 /* VLCTime.m in Sources */,
				6C13FFBE29BF8D5300C4C68E /* VLCEventsHandler.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7D89394A1B500DBC008F2B14 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7D797FB51DF41EBA00AD93ED /* StaticLibVLC.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7DB6837A1C995D39000C70BE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7DB683871C995D76000C70BE /* VLCAudio.m in Sources */,
				6C6335572816A09600AF8CCF /* VLCFileLogger.m in Sources */,
				6C121E0B27BA60BE00769422 /* VLCAdjustFilter.m in Sources */,
				7DAD5C572A835EED009419DC /* VLCEventsConfiguration.m in Sources */,
				3C411D09297969390057ACA1 /* VLCAudioEqualizer.m in Sources */,
				7DB683891C995D76000C70BE /* VLCLibrary.m in Sources */,
				7DB6838A1C995D76000C70BE /* VLCMedia.m in Sources */,
				7DB6838B1C995D76000C70BE /* VLCMediaList.m in Sources */,
				7DA7F2EF28D87E2300366EEB /* VLCMediaMetaData.m in Sources */,
				6C121E0827BA59C500769422 /* VLCFilter.m in Sources */,
				7DB6838C1C995D76000C70BE /* VLCMediaPlayer.m in Sources */,
				7DB683D81C996187000C70BE /* VLCHelperCode.m in Sources */,
				6C13FFBF29BF8D5300C4C68E /* VLCEventsHandler.m in Sources */,
				7DB6838D1C995D76000C70BE /* VLCMediaListPlayer.m in Sources */,
				7DB6838E1C995D76000C70BE /* VLCMediaThumbnailer.m in Sources */,
				7DB6838F1C995D76000C70BE /* VLCMediaDiscoverer.m in Sources */,
				6C63355D2816D75D00AF8CCF /* VLCLogMessageFormatter.m in Sources */,
				7DB683901C995D76000C70BE /* VLCTime.m in Sources */,
				7DB683911C995D76000C70BE /* VLCDialogProvider.m in Sources */,
				6C63355128169EDE00AF8CCF /* VLCConsoleLogger.m in Sources */,
				7DB683921C995D76000C70BE /* VLCCustomDialogProvider.m in Sources */,
				7DB683931C995D76000C70BE /* VLCEmbeddedDialogProvider.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7DB683C21C995E9A000C70BE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7D797FB61DF41EBA00AD93ED /* StaticLibVLC.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1DEB922408733DC00010E9CD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_32_BIT)";
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.4;
				OTHER_LDFLAGS = "-ObjC";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TVOS_DEPLOYMENT_TARGET = 10.2;
			};
			name = Release;
		};
		7D8938931B500013008F2B14 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD_32_BIT)";
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.4;
				OTHER_LDFLAGS = "-ObjC";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TVOS_DEPLOYMENT_TARGET = 10.2;
			};
			name = Debug;
		};
		7D89393B1B500D1D008F2B14 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREFIX_HEADER = Headers/PCH/MobilePrefix.pch;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(PROJECT_DIR)/libvlc/vlc/include",
				);
				INFOPLIST_FILE = "$(SRCROOT)/Resources/MobileVLCKit/Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.4;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = Resources/MobileVLCKit/module.modulemap;
				MTL_ENABLE_DEBUG_INFO = NO;
				"OTHER_LDFLAGS[sdk=iphonesimulator*]" = (
					"-ObjC",
					"-read_only_relocs",
					suppress,
				);
				"OTHER_LIBTOOLFLAGS[sdk=iphonesimulator*]" = "-read_only_relocs suppress";
				PRODUCT_BUNDLE_IDENTIFIER = org.videolan.mobilevlckit;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		7D89393C1B500D1D008F2B14 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREFIX_HEADER = Headers/PCH/MobilePrefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(PROJECT_DIR)/libvlc/vlc/include",
				);
				INFOPLIST_FILE = "$(SRCROOT)/Resources/MobileVLCKit/Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 8.4;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = Resources/MobileVLCKit/module.modulemap;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				"OTHER_LDFLAGS[sdk=iphonesimulator*]" = (
					"-ObjC",
					"-read_only_relocs",
					suppress,
				);
				"OTHER_LIBTOOLFLAGS[sdk=iphonesimulator*]" = "-read_only_relocs suppress";
				PRODUCT_BUNDLE_IDENTIFIER = org.videolan.mobilevlckit;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		7D8939601B500DBC008F2B14 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7D1329791BA3074100BE647E /* vlc-plugins-iPhone.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_LDFLAGS = "-ObjC";
				"OTHER_LIBTOOLFLAGS[sdk=iphoneos*]" = "$(VLC_PLUGINS_DEVICE_LDFLAGS)";
				"OTHER_LIBTOOLFLAGS[sdk=iphonesimulator*]" = "$(VLC_PLUGINS_SIMULATOR_LDFLAGS)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				STRIP_INSTALLED_PRODUCT = NO;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7D8939611B500DBC008F2B14 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7D1329791BA3074100BE647E /* vlc-plugins-iPhone.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "-ObjC";
				"OTHER_LIBTOOLFLAGS[sdk=iphoneos*]" = "$(VLC_PLUGINS_DEVICE_LDFLAGS)";
				"OTHER_LIBTOOLFLAGS[sdk=iphonesimulator*]" = "$(VLC_PLUGINS_SIMULATOR_LDFLAGS)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				STRIP_INSTALLED_PRODUCT = NO;
			};
			name = Debug;
		};
		7DB683841C995D39000C70BE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREFIX_HEADER = Headers/PCH/MobilePrefix.pch;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/libvlc/vlc/include",
				);
				INFOPLIST_FILE = "$(SRCROOT)/Resources/MobileVLCKit/Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = Resources/TVVLCKit/module.modulemap;
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = org.videolan.tvvlckit;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 10.2;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		7DB683851C995D39000C70BE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				BITCODE_GENERATION_MODE = marker;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREFIX_HEADER = Headers/PCH/MobilePrefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/libvlc/vlc/include",
				);
				INFOPLIST_FILE = "$(SRCROOT)/Resources/MobileVLCKit/Info.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				MODULEMAP_FILE = Resources/TVVLCKit/module.modulemap;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = org.videolan.tvvlckit;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 10.2;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		7DB683CD1C995E9A000C70BE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7D1329751BA305D600BE647E /* vlc-plugins-AppleTV.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				BITCODE_GENERATION_MODE = bitcode;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_LDFLAGS = "-ObjC";
				OTHER_LIBTOOLFLAGS = "$(PROJECT_DIR)/libvlc/vlc/install-AppleTVSimulator/x86_64/lib/libvlc.a $(PROJECT_DIR)/libvlc/vlc/install-AppleTVSimulator/x86_64/lib/libvlccore.a $(PROJECT_DIR)/libvlc/vlc/install-AppleTVSimulator/x86_64/lib/vlc/libcompat.a $(VLC_PLUGINS_LDFLAGS)";
				"OTHER_LIBTOOLFLAGS[sdk=appletvos*]" = "$(VLC_PLUGINS_DEVICE_LDFLAGS)";
				"OTHER_LIBTOOLFLAGS[sdk=appletvsimulator*]" = "$(VLC_PLUGINS_SIMULATOR_LDFLAGS)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				STRIP_INSTALLED_PRODUCT = NO;
				TVOS_DEPLOYMENT_TARGET = 10.2;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7DB683CE1C995E9A000C70BE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7D1329751BA305D600BE647E /* vlc-plugins-AppleTV.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				BITCODE_GENERATION_MODE = marker;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_LDFLAGS = "-ObjC";
				"OTHER_LIBTOOLFLAGS[sdk=appletvos*]" = "$(VLC_PLUGINS_DEVICE_LDFLAGS)";
				"OTHER_LIBTOOLFLAGS[sdk=appletvsimulator*]" = "$(VLC_PLUGINS_SIMULATOR_LDFLAGS)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				STRIP_INSTALLED_PRODUCT = NO;
				TVOS_DEPLOYMENT_TARGET = 10.2;
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1DEB922208733DC00010E9CD /* Build configuration list for PBXProject "MobileVLCKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1DEB922408733DC00010E9CD /* Release */,
				7D8938931B500013008F2B14 /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7D89393A1B500D1D008F2B14 /* Build configuration list for PBXNativeTarget "MobileVLCKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D89393B1B500D1D008F2B14 /* Release */,
				7D89393C1B500D1D008F2B14 /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7D89395F1B500DBC008F2B14 /* Build configuration list for PBXNativeTarget "StaticLibVLC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D8939601B500DBC008F2B14 /* Release */,
				7D8939611B500DBC008F2B14 /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7DB683861C995D39000C70BE /* Build configuration list for PBXNativeTarget "TVVLCKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7DB683841C995D39000C70BE /* Release */,
				7DB683851C995D39000C70BE /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7DB683CC1C995E9A000C70BE /* Build configuration list for PBXNativeTarget "StaticTVLibvlc" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7DB683CD1C995E9A000C70BE /* Release */,
				7DB683CE1C995E9A000C70BE /* Debug */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0867D690FE84028FC02AAC07 /* Project object */;
}
