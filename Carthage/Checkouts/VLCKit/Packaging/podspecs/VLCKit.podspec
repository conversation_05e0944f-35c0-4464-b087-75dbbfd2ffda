Pod::Spec.new do |s|
  s.name      = 'VLCKit'
  s.version   = '3.6.0b12'
  s.summary   = "VLCKit is an Objective-C wrapper for libvlc's external interface on macOS."
  s.homepage  = 'https://code.videolan.org/videolan/VLCKit'
  s.license   = {
    :type => 'LGPL v2.1', :file => 'COPYING.txt'
  }
  s.documentation_url = 'https://wiki.videolan.org/VLCKit/'
  s.social_media_url = 'https://twitter.com/videolan'
  s.platform  = :osx
  s.authors   = { "<PERSON>" => "<EMAIL>", "<PERSON>" => "<EMAIL>", "<PERSON>" => "<EMAIL>", "<PERSON>" => "<EMAIL>", "Maxime Chapelet" => "<EMAIL>", "<PERSON><PERSON>" => "<EMAIL>", "<PERSON><PERSON><PERSON>" => "<EMAIL>", "<PERSON><PERSON><PERSON>" => "<EMAIL>", "<PERSON><PERSON>" => "<EMAIL>", "<PERSON>émi <PERSON>-<PERSON>ur<PERSON>" => "<EMAIL>", "Faustino Osuna" => "<EMAIL>", "Tanguy Krotoff" => "<EMAIL>", "VideoLAN" => "<EMAIL>", "Derk-<PERSON> <PERSON>man" => "<EMAIL>", "Jean-Paul Saman" => "<EMAIL>", "Malte Tancred" => "<EMAIL>", "Mike Schrag" => "<EMAIL>", "Sebastien Zwickert" => "<EMAIL>", "Toralf Niebuhr" => "<EMAIL>", "Emmanuel de Roux" => "<EMAIL>", "Daniel Mierswa" => "<EMAIL>", "Rune Botten" => "<EMAIL>", "Konstantin Pavlov" => "<EMAIL>", "Pere Orga" => "<EMAIL>", "Philippe Coent" => "<EMAIL>", "Andrey Utkin" => "<EMAIL>", "Brendon Justin" => "<EMAIL>", "Sylver Bruneau" => "<EMAIL>", "Gleb Pinigin" => "<EMAIL>", "Kuang Rufan" => "<EMAIL>", "Paul Williamson" => "<EMAIL>", "David Fuhrmann" => "<EMAIL>", "Brion Vibber" => "<EMAIL>", "Martin Storsjö" => "<EMAIL>", "Winston Weinert" => "<EMAIL>", "Florent Pillet" => "<EMAIL>", "Paulo Vitor Magacho da Silva" => "<EMAIL>", "James Dumay" => "<EMAIL>", "Jörg Bleyel" => "<EMAIL>", "Aleksandr Matuzok" => "<EMAIL>", "Pierre SAGASPE" => "<EMAIL>", "Shenggang Hu" => "<EMAIL>", "Filipe Cabecinhas" => "<EMAIL>", "Jeremy Marchand" => "<EMAIL>", "Andre Silva" => "<EMAIL>", "Stefan Schmidt-Bilkenroth" => "<EMAIL>", "Benjamin Adolphi" => "<EMAIL>" }
  s.source    = {
    :http => 'https://download.videolan.org/pub/cocoapods/unstable/VLCKit-3.6.0b12-8a6d7aeb-6ceb56b8.tar.xz',
    :sha256 => '1502d891732555710543058636c30d177e3274cf18356fd5f78292ad8de8a510'
  }
  s.osx.vendored_framework = 'VLCKit.xcframework'
  s.osx.deployment_target = '10.9'
  s.frameworks = 'Foundation'
  s.libraries = 'iconv'
  s.requires_arc = false
  s.pod_target_xcconfig = {
    'CLANG_CXX_LANGUAGE_STANDARD' => 'c++14',
    'CLANG_CXX_LIBRARY' => 'libc++'
  }
end
