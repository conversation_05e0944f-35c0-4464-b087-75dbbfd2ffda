Pod::Spec.new do |s|
  s.name      = 'TVVLCKit'
  s.version   = '3.6.0b12'
  s.summary   = "TVVLCKit is an Objective-C wrapper for libvlc's external interface on tvOS."
  s.homepage  = 'https://code.videolan.org/videolan/VLCKit'
  s.license   = {
    :type => 'LGPLv2.1', :file => 'COPYING.txt'
  }
  s.documentation_url = 'https://wiki.videolan.org/VLCKit/'
  s.social_media_url = 'https://twitter.com/videolan'
  s.platform  = :tvos
  s.authors   = { "<PERSON>" => "<EMAIL>", "<PERSON>" => "<EMAIL>", "<PERSON>" => "ajan<PERSON>@videolabs.io", "<PERSON>" => "<EMAIL>", "Maxime Chapelet" => "<EMAIL>", "<PERSON><PERSON>" => "<EMAIL>", "<PERSON><PERSON><PERSON>" => "<EMAIL>", "<PERSON><PERSON><PERSON>" => "<EMAIL>", "<PERSON><PERSON>" => "<EMAIL>", "Rémi <PERSON>-Courmont" => "<EMAIL>", "Faustino Osuna" => "<EMAIL>", "Tanguy Krotoff" => "<EMAIL>", "VideoLAN" => "<EMAIL>", "Derk-Jan Hartman" => "<EMAIL>", "Jean-Paul Saman" => "<EMAIL>", "Malte Tancred" => "<EMAIL>", "Mike Schrag" => "<EMAIL>", "Sebastien Zwickert" => "<EMAIL>", "Toralf Niebuhr" => "<EMAIL>", "Emmanuel de Roux" => "<EMAIL>", "Daniel Mierswa" => "<EMAIL>", "Rune Botten" => "<EMAIL>", "Konstantin Pavlov" => "<EMAIL>", "Pere Orga" => "<EMAIL>", "Philippe Coent" => "<EMAIL>", "Andrey Utkin" => "<EMAIL>", "Brendon Justin" => "<EMAIL>", "Sylver Bruneau" => "<EMAIL>", "Gleb Pinigin" => "<EMAIL>", "Kuang Rufan" => "<EMAIL>", "Paul Williamson" => "<EMAIL>", "David Fuhrmann" => "<EMAIL>", "Brion Vibber" => "<EMAIL>", "Martin Storsjö" => "<EMAIL>", "Winston Weinert" => "<EMAIL>", "Florent Pillet" => "<EMAIL>", "Paulo Vitor Magacho da Silva" => "<EMAIL>", "James Dumay" => "<EMAIL>", "Jörg Bleyel" => "<EMAIL>", "Aleksandr Matuzok" => "<EMAIL>", "Pierre SAGASPE" => "<EMAIL>", "Shenggang Hu" => "<EMAIL>", "Filipe Cabecinhas" => "<EMAIL>", "Jeremy Marchand" => "<EMAIL>", "Andre Silva" => "<EMAIL>", "Stefan Schmidt-Bilkenroth" => "<EMAIL>", "Benjamin Adolphi" => "<EMAIL>" }
  s.source    = {
    :http => 'https://download.videolan.org/cocoapods/unstable/TVVLCKit-3.6.0b12-8a6d7aeb-6ceb56b8.tar.xz',
    :sha256 => '8a4b4c55d1d0a978bab507f34cba3b3d139cb8299ee121aafc771b5dc0fd49da'
  }
  s.tvos.vendored_framework = 'TVVLCKit.xcframework'
  s.tvos.deployment_target = '10.2'
  s.frameworks = 'CoreText', 'AVFoundation', 'AudioToolbox', 'OpenGLES', 'VideoToolbox', 'CoreMedia'
  s.libraries = 'c++', 'xml2', 'z', 'bz2', 'iconv'
  s.requires_arc = false
  s.pod_target_xcconfig = {
    'CLANG_CXX_LANGUAGE_STANDARD' => 'c++14',
    'CLANG_CXX_LIBRARY' => 'libc++'
  }
end
