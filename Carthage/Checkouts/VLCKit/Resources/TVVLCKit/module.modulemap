framework module TVVLCKit {
  umbrella header "TVVLCKit.h"

  exclude header "vlc/libvlc_dialog.h"
  exclude header "vlc/libvlc_renderer_discoverer.h"
  exclude header "vlc/libvlc_media.h"
  exclude header "vlc/libvlc_media_list_player.h"
  exclude header "vlc/libvlc_media_player.h"
  exclude header "vlc/libvlc_events.h"
  exclude header "vlc/vlc.h"
  exclude header "vlc/libvlc_media_discoverer.h"
  exclude header "vlc/deprecated.h"
  exclude header "vlc/libvlc.h"
  exclude header "vlc/libvlc_media_list.h"
  exclude header "vlc/libvlc_version.h"
  exclude header "vlc/libvlc_vlm.h"
  exclude header "vlc/libvlc_picture.h"
  exclude header "vlc/libvlc_media_library.h"

  export *
  module * { export * }
}
