<?xml version="1.0" encoding="UTF-8"?>
<archive type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="8.00">
	<data>
		<int key="IBDocument.SystemTarget">1552</int>
		<string key="IBDocument.SystemVersion">12F45</string>
		<string key="IBDocument.InterfaceBuilderVersion">3084</string>
		<string key="IBDocument.AppKitVersion">1187.40</string>
		<string key="IBDocument.HIToolboxVersion">626.00</string>
		<object class="NSMutableDictionary" key="IBDocument.PluginVersions">
			<string key="NS.key.0">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
			<string key="NS.object.0">2083</string>
		</object>
		<array key="IBDocument.IntegratedClassDependencies">
			<string>IBNSLayoutConstraint</string>
			<string>IBProxyObject</string>
			<string>IB<PERSON><PERSON>utton</string>
			<string>IBUIView</string>
		</array>
		<array key="IBDocument.PluginDependencies">
			<string>com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
		</array>
		<object class="NSMutableDictionary" key="IBDocument.Metadata">
			<string key="NS.key.0">PluginDependencyRecalculationVersion</string>
			<integer value="1" key="NS.object.0"/>
		</object>
		<array class="NSMutableArray" key="IBDocument.RootObjects" id="1000">
			<object class="IBProxyObject" id="372490531">
				<string key="IBProxiedObjectIdentifier">IBFilesOwner</string>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
			<object class="IBProxyObject" id="843779117">
				<string key="IBProxiedObjectIdentifier">IBFirstResponder</string>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
			<object class="IBUIView" id="774585933">
				<reference key="NSNextResponder"/>
				<int key="NSvFlags">274</int>
				<array class="NSMutableArray" key="NSSubviews">
					<object class="IBUIView" id="970364256">
						<reference key="NSNextResponder" ref="774585933"/>
						<int key="NSvFlags">292</int>
						<string key="NSFrame">{{0, -20}, {320, 568}}</string>
						<reference key="NSSuperview" ref="774585933"/>
						<reference key="NSWindow"/>
						<string key="NSReuseIdentifierKey">_NS:9</string>
						<object class="NSColor" key="IBUIBackgroundColor">
							<int key="NSColorSpace">3</int>
							<bytes key="NSWhite">MQA</bytes>
							<object class="NSColorSpace" key="NSCustomColorSpace" id="73608252">
								<int key="NSID">2</int>
							</object>
						</object>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
					</object>
					<object class="IBUIButton" id="640051777">
						<reference key="NSNextResponder" ref="774585933"/>
						<int key="NSvFlags">292</int>
						<string key="NSFrame">{{133, 419}, {55, 44}}</string>
						<reference key="NSSuperview" ref="774585933"/>
						<reference key="NSWindow"/>
						<string key="NSReuseIdentifierKey">_NS:9</string>
						<bool key="IBUIOpaque">NO</bool>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
						<int key="IBUIContentHorizontalAlignment">0</int>
						<int key="IBUIContentVerticalAlignment">0</int>
						<int key="IBUIButtonType">1</int>
						<string key="IBUINormalTitle">Play</string>
						<object class="NSColor" key="IBUIHighlightedTitleColor">
							<int key="NSColorSpace">3</int>
							<bytes key="NSWhite">MQA</bytes>
						</object>
						<object class="NSColor" key="IBUINormalTitleColor">
							<int key="NSColorSpace">1</int>
							<bytes key="NSRGB">MC4xOTYwNzg0MzQ2IDAuMzA5ODAzOTMyOSAwLjUyMTU2ODY1NgA</bytes>
						</object>
						<object class="NSColor" key="IBUINormalTitleShadowColor">
							<int key="NSColorSpace">3</int>
							<bytes key="NSWhite">MC41AA</bytes>
						</object>
						<object class="IBUIFontDescription" key="IBUIFontDescription">
							<int key="type">2</int>
							<double key="pointSize">15</double>
						</object>
						<object class="NSFont" key="IBUIFont">
							<string key="NSName">Helvetica-Bold</string>
							<double key="NSSize">15</double>
							<int key="NSfFlags">16</int>
						</object>
					</object>
				</array>
				<string key="NSFrame">{{0, 20}, {320, 548}}</string>
				<reference key="NSSuperview"/>
				<reference key="NSWindow"/>
				<object class="NSColor" key="IBUIBackgroundColor">
					<int key="NSColorSpace">3</int>
					<bytes key="NSWhite">MC43NQA</bytes>
					<reference key="NSCustomColorSpace" ref="73608252"/>
				</object>
				<bool key="IBUIClearsContextBeforeDrawing">NO</bool>
				<object class="IBUISimulatedStatusBarMetrics" key="IBUISimulatedStatusBarMetrics"/>
				<object class="IBUIScreenMetrics" key="IBUISimulatedDestinationMetrics">
					<string key="IBUISimulatedSizeMetricsClass">IBUIScreenMetrics</string>
					<object class="NSMutableDictionary" key="IBUINormalizedOrientationToSizeMap">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<array key="dict.sortedKeys">
							<integer value="1"/>
							<integer value="3"/>
						</array>
						<array key="dict.values">
							<string>{320, 568}</string>
							<string>{568, 320}</string>
						</array>
					</object>
					<string key="IBUITargetRuntime">IBCocoaTouchFramework</string>
					<string key="IBUIDisplayName">Retina 4 Full Screen</string>
					<int key="IBUIType">2</int>
				</object>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
		</array>
		<object class="IBObjectContainer" key="IBDocument.Objects">
			<array class="NSMutableArray" key="connectionRecords">
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">view</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="774585933"/>
					</object>
					<int key="connectionID">7</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">movieView</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="970364256"/>
					</object>
					<int key="connectionID">29</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchEventConnection" key="connection">
						<string key="label">playandPause:</string>
						<reference key="source" ref="640051777"/>
						<reference key="destination" ref="372490531"/>
						<int key="IBEventType">7</int>
					</object>
					<int key="connectionID">12</int>
				</object>
			</array>
			<object class="IBMutableOrderedSet" key="objectRecords">
				<array key="orderedObjects">
					<object class="IBObjectRecord">
						<int key="objectID">0</int>
						<array key="object" id="0"/>
						<reference key="children" ref="1000"/>
						<nil key="parent"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-1</int>
						<reference key="object" ref="372490531"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">File's Owner</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-2</int>
						<reference key="object" ref="843779117"/>
						<reference key="parent" ref="0"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">6</int>
						<reference key="object" ref="774585933"/>
						<array class="NSMutableArray" key="children">
							<reference ref="640051777"/>
							<object class="IBNSLayoutConstraint" id="592814880">
								<reference key="firstItem" ref="640051777"/>
								<int key="firstAttribute">9</int>
								<int key="relation">0</int>
								<reference key="secondItem" ref="970364256"/>
								<int key="secondAttribute">9</int>
								<float key="multiplier">1</float>
								<object class="IBLayoutConstant" key="constant">
									<double key="value">0.0</double>
								</object>
								<float key="priority">1000</float>
								<reference key="containingView" ref="774585933"/>
								<int key="scoringType">6</int>
								<float key="scoringTypeFloat">24</float>
								<int key="contentType">2</int>
							</object>
							<object class="IBNSLayoutConstraint" id="942009081">
								<reference key="firstItem" ref="774585933"/>
								<int key="firstAttribute">4</int>
								<int key="relation">0</int>
								<reference key="secondItem" ref="640051777"/>
								<int key="secondAttribute">4</int>
								<float key="multiplier">1</float>
								<object class="IBLayoutConstant" key="constant">
									<double key="value">86</double>
								</object>
								<float key="priority">1000</float>
								<reference key="containingView" ref="774585933"/>
								<int key="scoringType">3</int>
								<float key="scoringTypeFloat">9</float>
								<int key="contentType">3</int>
							</object>
							<object class="IBNSLayoutConstraint" id="432115303">
								<reference key="firstItem" ref="970364256"/>
								<int key="firstAttribute">4</int>
								<int key="relation">0</int>
								<reference key="secondItem" ref="774585933"/>
								<int key="secondAttribute">4</int>
								<float key="multiplier">1</float>
								<object class="IBLayoutConstant" key="constant">
									<double key="value">0.0</double>
								</object>
								<float key="priority">1000</float>
								<reference key="containingView" ref="774585933"/>
								<int key="scoringType">8</int>
								<float key="scoringTypeFloat">29</float>
								<int key="contentType">3</int>
							</object>
							<object class="IBNSLayoutConstraint" id="113430229">
								<reference key="firstItem" ref="970364256"/>
								<int key="firstAttribute">6</int>
								<int key="relation">0</int>
								<reference key="secondItem" ref="774585933"/>
								<int key="secondAttribute">6</int>
								<float key="multiplier">1</float>
								<object class="IBLayoutConstant" key="constant">
									<double key="value">0.0</double>
								</object>
								<float key="priority">1000</float>
								<reference key="containingView" ref="774585933"/>
								<int key="scoringType">8</int>
								<float key="scoringTypeFloat">29</float>
								<int key="contentType">3</int>
							</object>
							<object class="IBNSLayoutConstraint" id="178090514">
								<reference key="firstItem" ref="970364256"/>
								<int key="firstAttribute">5</int>
								<int key="relation">0</int>
								<reference key="secondItem" ref="774585933"/>
								<int key="secondAttribute">5</int>
								<float key="multiplier">1</float>
								<object class="IBLayoutConstant" key="constant">
									<double key="value">0.0</double>
								</object>
								<float key="priority">1000</float>
								<reference key="containingView" ref="774585933"/>
								<int key="scoringType">8</int>
								<float key="scoringTypeFloat">29</float>
								<int key="contentType">3</int>
							</object>
							<object class="IBNSLayoutConstraint" id="499898361">
								<reference key="firstItem" ref="970364256"/>
								<int key="firstAttribute">3</int>
								<int key="relation">0</int>
								<reference key="secondItem" ref="774585933"/>
								<int key="secondAttribute">3</int>
								<float key="multiplier">1</float>
								<object class="IBLayoutConstant" key="constant">
									<double key="value">-20</double>
								</object>
								<float key="priority">1000</float>
								<reference key="containingView" ref="774585933"/>
								<int key="scoringType">3</int>
								<float key="scoringTypeFloat">9</float>
								<int key="contentType">3</int>
							</object>
							<reference ref="970364256"/>
						</array>
						<reference key="parent" ref="0"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">8</int>
						<reference key="object" ref="640051777"/>
						<reference key="parent" ref="774585933"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">14</int>
						<reference key="object" ref="970364256"/>
						<reference key="parent" ref="774585933"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">20</int>
						<reference key="object" ref="942009081"/>
						<reference key="parent" ref="774585933"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">21</int>
						<reference key="object" ref="499898361"/>
						<reference key="parent" ref="774585933"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">23</int>
						<reference key="object" ref="178090514"/>
						<reference key="parent" ref="774585933"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">25</int>
						<reference key="object" ref="592814880"/>
						<reference key="parent" ref="774585933"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">26</int>
						<reference key="object" ref="113430229"/>
						<reference key="parent" ref="774585933"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">28</int>
						<reference key="object" ref="432115303"/>
						<reference key="parent" ref="774585933"/>
					</object>
				</array>
			</object>
			<dictionary class="NSMutableDictionary" key="flattenedProperties">
				<string key="-1.CustomClassName">VDLViewController</string>
				<string key="-1.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="-2.CustomClassName">UIResponder</string>
				<string key="-2.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="14.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<boolean value="NO" key="14.IBViewMetadataTranslatesAutoresizingMaskIntoConstraints"/>
				<string key="20.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="21.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="23.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="25.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="26.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="28.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="6.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<array class="NSMutableArray" key="6.IBViewMetadataConstraints">
					<reference ref="499898361"/>
					<reference ref="178090514"/>
					<reference ref="113430229"/>
					<reference ref="432115303"/>
					<reference ref="942009081"/>
					<reference ref="592814880"/>
				</array>
				<string key="8.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<boolean value="NO" key="8.IBViewMetadataTranslatesAutoresizingMaskIntoConstraints"/>
			</dictionary>
			<dictionary class="NSMutableDictionary" key="unlocalizedProperties"/>
			<nil key="activeLocalization"/>
			<dictionary class="NSMutableDictionary" key="localizations"/>
			<nil key="sourceID"/>
			<int key="maxID">29</int>
		</object>
		<object class="IBClassDescriber" key="IBDocument.Classes">
			<array class="NSMutableArray" key="referencedPartialClassDescriptions">
				<object class="IBPartialClassDescription">
					<string key="className">NSLayoutConstraint</string>
					<string key="superclassName">NSObject</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBProjectSource</string>
						<string key="minorKey">./Classes/NSLayoutConstraint.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">VDLViewController</string>
					<string key="superclassName">UIViewController</string>
					<object class="NSMutableDictionary" key="actions">
						<string key="NS.key.0">playandPause:</string>
						<string key="NS.object.0">id</string>
					</object>
					<object class="NSMutableDictionary" key="actionInfosByName">
						<string key="NS.key.0">playandPause:</string>
						<object class="IBActionInfo" key="NS.object.0">
							<string key="name">playandPause:</string>
							<string key="candidateClassName">id</string>
						</object>
					</object>
					<object class="NSMutableDictionary" key="outlets">
						<string key="NS.key.0">movieView</string>
						<string key="NS.object.0">UIView</string>
					</object>
					<object class="NSMutableDictionary" key="toOneOutletInfosByName">
						<string key="NS.key.0">movieView</string>
						<object class="IBToOneOutletInfo" key="NS.object.0">
							<string key="name">movieView</string>
							<string key="candidateClassName">UIView</string>
						</object>
					</object>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBProjectSource</string>
						<string key="minorKey">./Classes/VDLViewController.h</string>
					</object>
				</object>
			</array>
		</object>
		<int key="IBDocument.localizationMode">0</int>
		<string key="IBDocument.TargetRuntimeIdentifier">IBCocoaTouchFramework</string>
		<bool key="IBDocument.PluginDeclaredDependenciesTrackSystemTargetVersion">YES</bool>
		<int key="IBDocument.defaultPropertyAccessControl">3</int>
		<bool key="IBDocument.UseAutolayout">YES</bool>
		<string key="IBCocoaTouchPluginVersion">2083</string>
	</data>
</archive>
