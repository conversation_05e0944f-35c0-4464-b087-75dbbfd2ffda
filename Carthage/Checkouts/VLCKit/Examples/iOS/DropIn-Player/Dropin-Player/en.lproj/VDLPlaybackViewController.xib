<?xml version="1.0" encoding="UTF-8"?>
<archive type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="8.00">
	<data>
		<int key="IBDocument.SystemTarget">1552</int>
		<string key="IBDocument.SystemVersion">12F45</string>
		<string key="IBDocument.InterfaceBuilderVersion">4514</string>
		<string key="IBDocument.AppKitVersion">1187.40</string>
		<string key="IBDocument.HIToolboxVersion">626.00</string>
		<object class="NSMutableDictionary" key="IBDocument.PluginVersions">
			<string key="NS.key.0">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
			<string key="NS.object.0">3747</string>
		</object>
		<array key="IBDocument.IntegratedClassDependencies">
			<string>IBProxyObject</string>
			<string>IBUIBarButtonItem</string>
			<string>IBUIButton</string>
			<string>IBUINavigationBar</string>
			<string>IBUINavigationItem</string>
			<string>IBUISlider</string>
			<string>IBUIView</string>
		</array>
		<array key="IBDocument.PluginDependencies">
			<string>com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
		</array>
		<object class="NSMutableDictionary" key="IBDocument.Metadata">
			<string key="NS.key.0">PluginDependencyRecalculationVersion</string>
			<integer value="1" key="NS.object.0"/>
		</object>
		<array class="NSMutableArray" key="IBDocument.RootObjects" id="1000">
			<object class="IBProxyObject" id="372490531">
				<string key="IBProxiedObjectIdentifier">IBFilesOwner</string>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
			<object class="IBProxyObject" id="843779117">
				<string key="IBProxiedObjectIdentifier">IBFirstResponder</string>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
			<object class="IBUIView" id="774585933">
				<reference key="NSNextResponder"/>
				<int key="NSvFlags">274</int>
				<array class="NSMutableArray" key="NSSubviews">
					<object class="IBUIView" id="970364256">
						<reference key="NSNextResponder" ref="774585933"/>
						<int key="NSvFlags">274</int>
						<string key="NSFrameSize">{320, 550}</string>
						<reference key="NSSuperview" ref="774585933"/>
						<reference key="NSWindow"/>
						<reference key="NSNextKeyView" ref="254588830"/>
						<string key="NSReuseIdentifierKey">_NS:9</string>
						<object class="NSColor" key="IBUIBackgroundColor">
							<int key="NSColorSpace">3</int>
							<bytes key="NSWhite">MAA</bytes>
						</object>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
					</object>
					<object class="IBUINavigationBar" id="254588830">
						<reference key="NSNextResponder" ref="774585933"/>
						<int key="NSvFlags">290</int>
						<array class="NSMutableArray" key="NSSubviews"/>
						<string key="NSFrameSize">{320, 44}</string>
						<reference key="NSSuperview" ref="774585933"/>
						<reference key="NSWindow"/>
						<reference key="NSNextKeyView" ref="897908297"/>
						<string key="NSReuseIdentifierKey">_NS:9</string>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
						<int key="IBUIBarStyle">2</int>
						<array key="IBUIItems">
							<object class="IBUINavigationItem" id="231808411">
								<reference key="IBUINavigationBar" ref="254588830"/>
								<string key="IBUITitle">Title</string>
								<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
							</object>
						</array>
					</object>
					<object class="IBUIView" id="897908297">
						<reference key="NSNextResponder" ref="774585933"/>
						<int key="NSvFlags">269</int>
						<array class="NSMutableArray" key="NSSubviews">
							<object class="IBUIButton" id="695335035">
								<reference key="NSNextResponder" ref="897908297"/>
								<int key="NSvFlags">292</int>
								<string key="NSFrame">{{139, 13}, {44, 26}}</string>
								<reference key="NSSuperview" ref="897908297"/>
								<reference key="NSWindow"/>
								<reference key="NSNextKeyView" ref="776049213"/>
								<string key="NSReuseIdentifierKey">_NS:9</string>
								<object class="NSColor" key="IBUIBackgroundColor" id="665591321">
									<int key="NSColorSpace">3</int>
									<bytes key="NSWhite">MCAwAA</bytes>
								</object>
								<bool key="IBUIOpaque">NO</bool>
								<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
								<int key="IBUIContentHorizontalAlignment">0</int>
								<int key="IBUIContentVerticalAlignment">0</int>
								<double key="IBUIContentEdgeInsets.top">4</double>
								<double key="IBUIContentEdgeInsets.bottom">4</double>
								<double key="IBUIContentEdgeInsets.left">0.0</double>
								<double key="IBUIContentEdgeInsets.right">0.0</double>
								<object class="NSColor" key="IBUIHighlightedTitleColor" id="733852039">
									<int key="NSColorSpace">3</int>
									<bytes key="NSWhite">MQA</bytes>
								</object>
								<string key="IBUINormalTitle">Pause</string>
								<object class="NSColor" key="IBUINormalTitleShadowColor" id="733518820">
									<int key="NSColorSpace">3</int>
									<bytes key="NSWhite">MC41AA</bytes>
								</object>
								<object class="IBUIFontDescription" key="IBUIFontDescription" id="18318103">
									<int key="type">2</int>
									<double key="pointSize">15</double>
								</object>
								<object class="NSFont" key="IBUIFont" id="909620577">
									<string key="NSName">HelveticaNeue-Bold</string>
									<double key="NSSize">15</double>
									<int key="NSfFlags">16</int>
								</object>
							</object>
							<object class="IBUIButton" id="288523396">
								<reference key="NSNextResponder" ref="897908297"/>
								<int key="NSvFlags">292</int>
								<string key="NSFrame">{{20, 13}, {41, 26}}</string>
								<reference key="NSSuperview" ref="897908297"/>
								<reference key="NSWindow"/>
								<reference key="NSNextKeyView" ref="695335035"/>
								<string key="NSReuseIdentifierKey">_NS:9</string>
								<reference key="IBUIBackgroundColor" ref="665591321"/>
								<bool key="IBUIOpaque">NO</bool>
								<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
								<int key="IBUIContentHorizontalAlignment">0</int>
								<int key="IBUIContentVerticalAlignment">0</int>
								<double key="IBUIContentEdgeInsets.top">4</double>
								<double key="IBUIContentEdgeInsets.bottom">4</double>
								<double key="IBUIContentEdgeInsets.left">0.0</double>
								<double key="IBUIContentEdgeInsets.right">0.0</double>
								<reference key="IBUIHighlightedTitleColor" ref="733852039"/>
								<string key="IBUINormalTitle">Audio</string>
								<reference key="IBUINormalTitleShadowColor" ref="733518820"/>
								<reference key="IBUIFontDescription" ref="18318103"/>
								<reference key="IBUIFont" ref="909620577"/>
							</object>
							<object class="IBUIButton" id="776049213">
								<reference key="NSNextResponder" ref="897908297"/>
								<int key="NSvFlags">292</int>
								<string key="NSFrame">{{238, 13}, {62, 26}}</string>
								<reference key="NSSuperview" ref="897908297"/>
								<reference key="NSWindow"/>
								<reference key="NSNextKeyView" ref="396325696"/>
								<string key="NSReuseIdentifierKey">_NS:9</string>
								<reference key="IBUIBackgroundColor" ref="665591321"/>
								<bool key="IBUIOpaque">NO</bool>
								<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
								<int key="IBUIContentHorizontalAlignment">0</int>
								<int key="IBUIContentVerticalAlignment">0</int>
								<double key="IBUIContentEdgeInsets.top">4</double>
								<double key="IBUIContentEdgeInsets.bottom">4</double>
								<double key="IBUIContentEdgeInsets.left">0.0</double>
								<double key="IBUIContentEdgeInsets.right">0.0</double>
								<reference key="IBUIHighlightedTitleColor" ref="733852039"/>
								<string key="IBUINormalTitle">Subtitles</string>
								<reference key="IBUINormalTitleShadowColor" ref="733518820"/>
								<reference key="IBUIFontDescription" ref="18318103"/>
								<reference key="IBUIFont" ref="909620577"/>
							</object>
							<object class="IBUIView" id="396325696">
								<reference key="NSNextResponder" ref="897908297"/>
								<int key="NSvFlags">292</int>
								<string key="NSFrame">{{20, 47}, {284, 22}}</string>
								<reference key="NSSuperview" ref="897908297"/>
								<reference key="NSWindow"/>
								<string key="NSReuseIdentifierKey">_NS:9</string>
								<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
							</object>
						</array>
						<string key="NSFrame">{{0, 408}, {320, 82}}</string>
						<reference key="NSSuperview" ref="774585933"/>
						<reference key="NSWindow"/>
						<reference key="NSNextKeyView" ref="288523396"/>
						<string key="NSReuseIdentifierKey">_NS:10</string>
						<object class="NSColor" key="IBUIBackgroundColor">
							<int key="NSColorSpace">3</int>
							<bytes key="NSWhite">MC42NjY2NjY2NjY3AA</bytes>
						</object>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
					</object>
				</array>
				<string key="NSFrame">{{0, 20}, {320, 548}}</string>
				<reference key="NSSuperview"/>
				<reference key="NSWindow"/>
				<reference key="NSNextKeyView" ref="970364256"/>
				<bool key="IBUIClearsContextBeforeDrawing">NO</bool>
				<object class="IBUISimulatedStatusBarMetrics" key="IBUISimulatedStatusBarMetrics"/>
				<object class="IBUIScreenMetrics" key="IBUISimulatedDestinationMetrics">
					<string key="IBUISimulatedSizeMetricsClass">IBUIScreenMetrics</string>
					<object class="NSMutableDictionary" key="IBUINormalizedOrientationToSizeMap">
						<bool key="EncodedWithXMLCoder">YES</bool>
						<array key="dict.sortedKeys">
							<integer value="1"/>
							<integer value="3"/>
						</array>
						<array key="dict.values">
							<string>{320, 568}</string>
							<string>{568, 320}</string>
						</array>
					</object>
					<string key="IBUITargetRuntime">IBCocoaTouchFramework</string>
					<string key="IBUIDisplayName">Retina 4-inch Full Screen</string>
					<int key="IBUIType">2</int>
				</object>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
			<object class="IBUIView" id="646556287">
				<reference key="NSNextResponder"/>
				<int key="NSvFlags">290</int>
				<array class="NSMutableArray" key="NSSubviews">
					<object class="IBUISlider" id="134433983">
						<reference key="NSNextResponder" ref="646556287"/>
						<int key="NSvFlags">290</int>
						<string key="NSFrame">{{5, 10}, {188, 23}}</string>
						<reference key="NSSuperview" ref="646556287"/>
						<reference key="NSWindow"/>
						<reference key="NSNextKeyView" ref="438248784"/>
						<string key="NSReuseIdentifierKey">_NS:9</string>
						<string key="NSHuggingPriority">{250, 250}</string>
						<bool key="IBUIOpaque">NO</bool>
						<object class="IBUIAccessibilityConfiguration" key="IBUIAccessibilityConfiguration">
							<integer value="512" key="IBUIAccessibilityTraits"/>
						</object>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
						<int key="IBUIContentHorizontalAlignment">0</int>
						<int key="IBUIContentVerticalAlignment">0</int>
						<float key="IBUIValue">0.5</float>
					</object>
					<object class="IBUIButton" id="637398771">
						<reference key="NSNextResponder" ref="646556287"/>
						<int key="NSvFlags">289</int>
						<string key="NSFrame">{{241, 6}, {59, 29}}</string>
						<reference key="NSSuperview" ref="646556287"/>
						<reference key="NSWindow"/>
						<string key="NSReuseIdentifierKey">_NS:9</string>
						<bool key="IBUIOpaque">NO</bool>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
						<int key="IBUIContentHorizontalAlignment">0</int>
						<int key="IBUIContentVerticalAlignment">0</int>
						<reference key="IBUIHighlightedTitleColor" ref="733852039"/>
						<string key="IBUINormalTitle">VidDi</string>
						<reference key="IBUIFontDescription" ref="18318103"/>
						<reference key="IBUIFont" ref="909620577"/>
					</object>
					<object class="IBUIButton" id="438248784">
						<reference key="NSNextResponder" ref="646556287"/>
						<int key="NSvFlags">289</int>
						<string key="NSFrame">{{193, 11}, {50, 20}}</string>
						<reference key="NSSuperview" ref="646556287"/>
						<reference key="NSWindow"/>
						<reference key="NSNextKeyView" ref="637398771"/>
						<string key="NSReuseIdentifierKey">_NS:9</string>
						<bool key="IBUIOpaque">NO</bool>
						<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
						<int key="IBUIContentHorizontalAlignment">0</int>
						<int key="IBUIContentVerticalAlignment">0</int>
						<reference key="IBUIHighlightedTitleColor" ref="733852039"/>
						<string key="IBUINormalTitle">--:--</string>
						<reference key="IBUINormalTitleShadowColor" ref="733518820"/>
						<object class="IBUIFontDescription" key="IBUIFontDescription">
							<int key="type">2</int>
							<double key="pointSize">13</double>
						</object>
						<object class="NSFont" key="IBUIFont">
							<string key="NSName">HelveticaNeue-Bold</string>
							<double key="NSSize">13</double>
							<int key="NSfFlags">16</int>
						</object>
					</object>
				</array>
				<string key="NSFrameSize">{300, 40}</string>
				<reference key="NSSuperview"/>
				<reference key="NSWindow"/>
				<reference key="NSNextKeyView" ref="134433983"/>
				<string key="NSReuseIdentifierKey">_NS:9</string>
				<reference key="IBUIBackgroundColor" ref="665591321"/>
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
			</object>
			<object class="IBUIBarButtonItem" id="272455831">
				<string key="targetRuntimeIdentifier">IBCocoaTouchFramework</string>
				<int key="IBUIStyle">1</int>
				<int key="IBUISystemItemIdentifier">0</int>
			</object>
		</array>
		<object class="IBObjectContainer" key="IBDocument.Objects">
			<array class="NSMutableArray" key="connectionRecords">
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">view</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="774585933"/>
					</object>
					<int key="connectionID">7</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">movieView</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="970364256"/>
					</object>
					<int key="connectionID">29</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">positionSlider</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="134433983"/>
					</object>
					<int key="connectionID">179</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">timeDisplay</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="438248784"/>
					</object>
					<int key="connectionID">181</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">volumeView</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="396325696"/>
					</object>
					<int key="connectionID">182</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">controllerPanel</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="897908297"/>
					</object>
					<int key="connectionID">183</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">toolbar</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="254588830"/>
					</object>
					<int key="connectionID">184</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">audioSwitcherButton</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="288523396"/>
					</object>
					<int key="connectionID">190</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">subtitleSwitcherButton</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="776049213"/>
					</object>
					<int key="connectionID">191</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">playPauseButton</string>
						<reference key="source" ref="372490531"/>
						<reference key="destination" ref="695335035"/>
					</object>
					<int key="connectionID">192</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchEventConnection" key="connection">
						<string key="label">toggleTimeDisplay:</string>
						<reference key="source" ref="438248784"/>
						<reference key="destination" ref="372490531"/>
						<int key="IBEventType">7</int>
					</object>
					<int key="connectionID">180</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchEventConnection" key="connection">
						<string key="label">switchVideoDimensions:</string>
						<reference key="source" ref="637398771"/>
						<reference key="destination" ref="372490531"/>
						<int key="IBEventType">7</int>
					</object>
					<int key="connectionID">177</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchEventConnection" key="connection">
						<string key="label">positionSliderAction:</string>
						<reference key="source" ref="134433983"/>
						<reference key="destination" ref="372490531"/>
						<int key="IBEventType">13</int>
					</object>
					<int key="connectionID">178</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchEventConnection" key="connection">
						<string key="label">positionSliderDrag:</string>
						<reference key="source" ref="134433983"/>
						<reference key="destination" ref="372490531"/>
						<int key="IBEventType">3</int>
					</object>
					<int key="connectionID">193</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchEventConnection" key="connection">
						<string key="label">positionSliderDrag:</string>
						<reference key="source" ref="134433983"/>
						<reference key="destination" ref="372490531"/>
						<int key="IBEventType">4</int>
					</object>
					<int key="connectionID">194</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchEventConnection" key="connection">
						<string key="label">switchSubtitleTrack:</string>
						<reference key="source" ref="776049213"/>
						<reference key="destination" ref="372490531"/>
						<int key="IBEventType">7</int>
					</object>
					<int key="connectionID">176</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchEventConnection" key="connection">
						<string key="label">playandPause:</string>
						<reference key="source" ref="695335035"/>
						<reference key="destination" ref="372490531"/>
						<int key="IBEventType">7</int>
					</object>
					<int key="connectionID">174</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchEventConnection" key="connection">
						<string key="label">switchAudioTrack:</string>
						<reference key="source" ref="288523396"/>
						<reference key="destination" ref="372490531"/>
						<int key="IBEventType">7</int>
					</object>
					<int key="connectionID">175</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">titleView</string>
						<reference key="source" ref="231808411"/>
						<reference key="destination" ref="646556287"/>
					</object>
					<int key="connectionID">186</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchOutletConnection" key="connection">
						<string key="label">leftBarButtonItem</string>
						<reference key="source" ref="231808411"/>
						<reference key="destination" ref="272455831"/>
					</object>
					<int key="connectionID">188</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBCocoaTouchEventConnection" key="connection">
						<string key="label">closePlayback:</string>
						<reference key="source" ref="272455831"/>
						<reference key="destination" ref="372490531"/>
					</object>
					<int key="connectionID">189</int>
				</object>
			</array>
			<object class="IBMutableOrderedSet" key="objectRecords">
				<array key="orderedObjects">
					<object class="IBObjectRecord">
						<int key="objectID">0</int>
						<array key="object" id="0"/>
						<reference key="children" ref="1000"/>
						<nil key="parent"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-1</int>
						<reference key="object" ref="372490531"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">File's Owner</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-2</int>
						<reference key="object" ref="843779117"/>
						<reference key="parent" ref="0"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">6</int>
						<reference key="object" ref="774585933"/>
						<array class="NSMutableArray" key="children">
							<reference ref="254588830"/>
							<reference ref="970364256"/>
							<reference ref="897908297"/>
						</array>
						<reference key="parent" ref="0"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">14</int>
						<reference key="object" ref="970364256"/>
						<reference key="parent" ref="774585933"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">30</int>
						<reference key="object" ref="646556287"/>
						<array class="NSMutableArray" key="children">
							<reference ref="438248784"/>
							<reference ref="637398771"/>
							<reference ref="134433983"/>
						</array>
						<reference key="parent" ref="0"/>
						<string key="objectName">Time view</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">31</int>
						<reference key="object" ref="438248784"/>
						<array class="NSMutableArray" key="children"/>
						<reference key="parent" ref="646556287"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">32</int>
						<reference key="object" ref="637398771"/>
						<array class="NSMutableArray" key="children"/>
						<reference key="parent" ref="646556287"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">33</int>
						<reference key="object" ref="134433983"/>
						<array class="NSMutableArray" key="children"/>
						<reference key="parent" ref="646556287"/>
						<string key="objectName">Position Slider</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">45</int>
						<reference key="object" ref="254588830"/>
						<array class="NSMutableArray" key="children">
							<reference ref="231808411"/>
						</array>
						<reference key="parent" ref="774585933"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">59</int>
						<reference key="object" ref="231808411"/>
						<reference key="parent" ref="254588830"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">47</int>
						<reference key="object" ref="897908297"/>
						<array class="NSMutableArray" key="children">
							<reference ref="396325696"/>
							<reference ref="695335035"/>
							<reference ref="776049213"/>
							<reference ref="288523396"/>
						</array>
						<reference key="parent" ref="774585933"/>
						<string key="objectName">Controls panel</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">56</int>
						<reference key="object" ref="396325696"/>
						<reference key="parent" ref="897908297"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">52</int>
						<reference key="object" ref="288523396"/>
						<array class="NSMutableArray" key="children"/>
						<reference key="parent" ref="897908297"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">50</int>
						<reference key="object" ref="695335035"/>
						<array class="NSMutableArray" key="children"/>
						<reference key="parent" ref="897908297"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">48</int>
						<reference key="object" ref="776049213"/>
						<array class="NSMutableArray" key="children"/>
						<reference key="parent" ref="897908297"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">187</int>
						<reference key="object" ref="272455831"/>
						<reference key="parent" ref="0"/>
					</object>
				</array>
			</object>
			<dictionary class="NSMutableDictionary" key="flattenedProperties">
				<string key="-1.CustomClassName">VDLPlaybackViewController</string>
				<string key="-1.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="-2.CustomClassName">UIResponder</string>
				<string key="-2.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="14.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="187.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="30.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="31.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="32.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="33.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="45.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="47.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="48.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="50.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="52.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="56.CustomClassName">MPVolumeView</string>
				<string key="56.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="59.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
				<string key="6.IBPluginDependency">com.apple.InterfaceBuilder.IBCocoaTouchPlugin</string>
			</dictionary>
			<dictionary class="NSMutableDictionary" key="unlocalizedProperties"/>
			<nil key="activeLocalization"/>
			<dictionary class="NSMutableDictionary" key="localizations"/>
			<nil key="sourceID"/>
			<int key="maxID">194</int>
		</object>
		<object class="IBClassDescriber" key="IBDocument.Classes">
			<array class="NSMutableArray" key="referencedPartialClassDescriptions">
				<object class="IBPartialClassDescription">
					<string key="className">MPVolumeView</string>
					<string key="superclassName">UIView</string>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBProjectSource</string>
						<string key="minorKey">./Classes/MPVolumeView.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">VDLPlaybackViewController</string>
					<string key="superclassName">UIViewController</string>
					<dictionary class="NSMutableDictionary" key="actions">
						<string key="closePlayback:">id</string>
						<string key="playandPause:">id</string>
						<string key="positionSliderAction:">UISlider</string>
						<string key="positionSliderDrag:">id</string>
						<string key="switchAudioTrack:">id</string>
						<string key="switchSubtitleTrack:">id</string>
						<string key="switchVideoDimensions:">id</string>
						<string key="toggleTimeDisplay:">id</string>
						<string key="volumeSliderAction:">id</string>
					</dictionary>
					<dictionary class="NSMutableDictionary" key="actionInfosByName">
						<object class="IBActionInfo" key="closePlayback:">
							<string key="name">closePlayback:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="playandPause:">
							<string key="name">playandPause:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="positionSliderAction:">
							<string key="name">positionSliderAction:</string>
							<string key="candidateClassName">UISlider</string>
						</object>
						<object class="IBActionInfo" key="positionSliderDrag:">
							<string key="name">positionSliderDrag:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="switchAudioTrack:">
							<string key="name">switchAudioTrack:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="switchSubtitleTrack:">
							<string key="name">switchSubtitleTrack:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="switchVideoDimensions:">
							<string key="name">switchVideoDimensions:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="toggleTimeDisplay:">
							<string key="name">toggleTimeDisplay:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="volumeSliderAction:">
							<string key="name">volumeSliderAction:</string>
							<string key="candidateClassName">id</string>
						</object>
					</dictionary>
					<dictionary class="NSMutableDictionary" key="outlets">
						<string key="audioSwitcherButton">UIButton</string>
						<string key="controllerPanel">UIView</string>
						<string key="movieView">UIView</string>
						<string key="playPauseButton">UIButton</string>
						<string key="positionSlider">UISlider</string>
						<string key="subtitleSwitcherButton">UIButton</string>
						<string key="timeDisplay">UIButton</string>
						<string key="toolbar">UINavigationBar</string>
						<string key="volumeView">MPVolumeView</string>
					</dictionary>
					<dictionary class="NSMutableDictionary" key="toOneOutletInfosByName">
						<object class="IBToOneOutletInfo" key="audioSwitcherButton">
							<string key="name">audioSwitcherButton</string>
							<string key="candidateClassName">UIButton</string>
						</object>
						<object class="IBToOneOutletInfo" key="controllerPanel">
							<string key="name">controllerPanel</string>
							<string key="candidateClassName">UIView</string>
						</object>
						<object class="IBToOneOutletInfo" key="movieView">
							<string key="name">movieView</string>
							<string key="candidateClassName">UIView</string>
						</object>
						<object class="IBToOneOutletInfo" key="playPauseButton">
							<string key="name">playPauseButton</string>
							<string key="candidateClassName">UIButton</string>
						</object>
						<object class="IBToOneOutletInfo" key="positionSlider">
							<string key="name">positionSlider</string>
							<string key="candidateClassName">UISlider</string>
						</object>
						<object class="IBToOneOutletInfo" key="subtitleSwitcherButton">
							<string key="name">subtitleSwitcherButton</string>
							<string key="candidateClassName">UIButton</string>
						</object>
						<object class="IBToOneOutletInfo" key="timeDisplay">
							<string key="name">timeDisplay</string>
							<string key="candidateClassName">UIButton</string>
						</object>
						<object class="IBToOneOutletInfo" key="toolbar">
							<string key="name">toolbar</string>
							<string key="candidateClassName">UINavigationBar</string>
						</object>
						<object class="IBToOneOutletInfo" key="volumeView">
							<string key="name">volumeView</string>
							<string key="candidateClassName">MPVolumeView</string>
						</object>
					</dictionary>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBProjectSource</string>
						<string key="minorKey">./Classes/VDLPlaybackViewController.h</string>
					</object>
				</object>
			</array>
		</object>
		<int key="IBDocument.localizationMode">0</int>
		<string key="IBDocument.TargetRuntimeIdentifier">IBCocoaTouchFramework</string>
		<bool key="IBDocument.previouslyAttemptedUpgradeToXcode5">YES</bool>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDependencyDefaults">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaTouchPlugin.iPhoneOS</string>
			<real value="1552" key="NS.object.0"/>
		</object>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDevelopmentDependencies">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaTouchPlugin.InterfaceBuilder3</string>
			<integer value="4600" key="NS.object.0"/>
		</object>
		<bool key="IBDocument.PluginDeclaredDependenciesTrackSystemTargetVersion">YES</bool>
		<int key="IBDocument.defaultPropertyAccessControl">3</int>
		<string key="IBCocoaTouchPluginVersion">3747</string>
	</data>
</archive>
