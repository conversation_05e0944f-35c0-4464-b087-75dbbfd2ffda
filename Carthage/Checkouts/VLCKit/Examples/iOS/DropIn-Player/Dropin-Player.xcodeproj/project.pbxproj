// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		7D11B36A183BA71600FF0D25 /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D11B369183BA71600FF0D25 /* UIKit.framework */; };
		7D11B36C183BA71600FF0D25 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D11B36B183BA71600FF0D25 /* Foundation.framework */; };
		7D7B6C491B70C40F00E879CF /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D7B6C481B70C40F00E879CF /* libc++.tbd */; };
		7D7B6C4B1B70C41700E879CF /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D7B6C4A1B70C41700E879CF /* VideoToolbox.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		7D7B6C4D1B70C41D00E879CF /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D7B6C4C1B70C41D00E879CF /* CoreMedia.framework */; };
		7D7B6C4F1B70C43200E879CF /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D7B6C4E1B70C43200E879CF /* CoreVideo.framework */; };
		7D7B6C7D1B70D0BA00E879CF /* libMobileVLCKit.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D7B6C7C1B70D0BA00E879CF /* libMobileVLCKit.a */; };
		7D8745B8183BABE800FD09E0 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8745B6183BABE800FD09E0 /* AudioToolbox.framework */; };
		7D8745BB183BABEE00FD09E0 /* OpenGLES.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8745BA183BABEE00FD09E0 /* OpenGLES.framework */; };
		7D8745BD183BAC0D00FD09E0 /* CFNetwork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8745BC183BAC0D00FD09E0 /* CFNetwork.framework */; };
		7D8745BF183BAC1200FD09E0 /* CoreText.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8745BE183BAC1200FD09E0 /* CoreText.framework */; };
		7D8745C1183BAC2300FD09E0 /* libbz2.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8745C0183BAC2300FD09E0 /* libbz2.dylib */; };
		7D8745C3183BAC2700FD09E0 /* libiconv.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8745C2183BAC2700FD09E0 /* libiconv.dylib */; };
		7D8745C7183BACAE00FD09E0 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D8745C6183BACAE00FD09E0 /* QuartzCore.framework */; };
		7D8745C8183BACBF00FD09E0 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D11B36D183BA71600FF0D25 /* CoreGraphics.framework */; };
		7DBC87FD1FE2C0BE002A5ADC /* CoreImage.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DBC87FC1FE2C0BD002A5ADC /* CoreImage.framework */; };
		7DEBFE68183BEA9E00BC53D5 /* VDLAppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DEBFE65183BEA9E00BC53D5 /* VDLAppDelegate.m */; };
		7DEBFE69183BEA9E00BC53D5 /* VDLPlaybackViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DEBFE67183BEA9E00BC53D5 /* VDLPlaybackViewController.m */; };
		7DEBFE6D183BEAB500BC53D5 /* VDLPlaybackViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 7DEBFE6B183BEAB500BC53D5 /* VDLPlaybackViewController.xib */; };
		7DEBFE70183BEAC300BC53D5 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 7DEBFE6E183BEAC300BC53D5 /* InfoPlist.strings */; };
		7DEBFE77183BEAD500BC53D5 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 7DEBFE71183BEAD500BC53D5 /* <EMAIL> */; };
		7DEBFE78183BEAD500BC53D5 /* Default.png in Resources */ = {isa = PBXBuildFile; fileRef = 7DEBFE72183BEAD500BC53D5 /* Default.png */; };
		7DEBFE79183BEAD500BC53D5 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 7DEBFE73183BEAD500BC53D5 /* <EMAIL> */; };
		7DEBFE7B183BEAD500BC53D5 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DEBFE76183BEAD500BC53D5 /* main.m */; };
		7DEBFE7F183BEE7500BC53D5 /* MediaPlayer.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DEBFE7E183BEE7500BC53D5 /* MediaPlayer.framework */; };
		7DEBFE81183BF0AA00BC53D5 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DEBFE80183BF0AA00BC53D5 /* AVFoundation.framework */; };
		7DEBFE85183BFC6300BC53D5 /* VDLMainViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DEBFE83183BFC6300BC53D5 /* VDLMainViewController.m */; };
		7DEBFE86183BFC6300BC53D5 /* VDLMainViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = 7DEBFE84183BFC6300BC53D5 /* VDLMainViewController.xib */; };
		D4771D93189848E70087441B /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D4771D92189848E70087441B /* Security.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		7D11B366183BA71600FF0D25 /* Dropin-Player.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Dropin-Player.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		7D11B369183BA71600FF0D25 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		7D11B36B183BA71600FF0D25 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		7D11B36D183BA71600FF0D25 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		7D7B6C481B70C40F00E879CF /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		7D7B6C4A1B70C41700E879CF /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = System/Library/Frameworks/VideoToolbox.framework; sourceTree = SDKROOT; };
		7D7B6C4C1B70C41D00E879CF /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		7D7B6C4E1B70C43200E879CF /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = System/Library/Frameworks/CoreVideo.framework; sourceTree = SDKROOT; };
		7D7B6C7C1B70D0BA00E879CF /* libMobileVLCKit.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libMobileVLCKit.a; path = "../../build/Debug-iphoneos/libMobileVLCKit.a"; sourceTree = "<group>"; };
		7D8745B4183BA8F900FD09E0 /* libMobileVLCKit.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libMobileVLCKit.a; path = "../../build/Release-iphonesimulator/libMobileVLCKit.a"; sourceTree = "<group>"; };
		7D8745B6183BABE800FD09E0 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		7D8745B7183BABE800FD09E0 /* AudioUnit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioUnit.framework; path = System/Library/Frameworks/AudioUnit.framework; sourceTree = SDKROOT; };
		7D8745BA183BABEE00FD09E0 /* OpenGLES.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGLES.framework; path = System/Library/Frameworks/OpenGLES.framework; sourceTree = SDKROOT; };
		7D8745BC183BAC0D00FD09E0 /* CFNetwork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CFNetwork.framework; path = System/Library/Frameworks/CFNetwork.framework; sourceTree = SDKROOT; };
		7D8745BE183BAC1200FD09E0 /* CoreText.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreText.framework; path = System/Library/Frameworks/CoreText.framework; sourceTree = SDKROOT; };
		7D8745C0183BAC2300FD09E0 /* libbz2.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libbz2.dylib; path = usr/lib/libbz2.dylib; sourceTree = SDKROOT; };
		7D8745C2183BAC2700FD09E0 /* libiconv.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libiconv.dylib; path = usr/lib/libiconv.dylib; sourceTree = SDKROOT; };
		7D8745C6183BACAE00FD09E0 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		7DBC87FC1FE2C0BD002A5ADC /* CoreImage.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreImage.framework; path = System/Library/Frameworks/CoreImage.framework; sourceTree = SDKROOT; };
		7DEBFE64183BEA9E00BC53D5 /* VDLAppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VDLAppDelegate.h; path = "Dropin-Player/VDLAppDelegate.h"; sourceTree = SOURCE_ROOT; };
		7DEBFE65183BEA9E00BC53D5 /* VDLAppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VDLAppDelegate.m; path = "Dropin-Player/VDLAppDelegate.m"; sourceTree = SOURCE_ROOT; };
		7DEBFE66183BEA9E00BC53D5 /* VDLPlaybackViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VDLPlaybackViewController.h; path = "Dropin-Player/VDLPlaybackViewController.h"; sourceTree = SOURCE_ROOT; };
		7DEBFE67183BEA9E00BC53D5 /* VDLPlaybackViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = VDLPlaybackViewController.m; path = "Dropin-Player/VDLPlaybackViewController.m"; sourceTree = SOURCE_ROOT; };
		7DEBFE6C183BEAB500BC53D5 /* en */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = en; path = "Dropin-Player/en.lproj/VDLPlaybackViewController.xib"; sourceTree = SOURCE_ROOT; };
		7DEBFE6F183BEAC300BC53D5 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = "Dropin-Player/en.lproj/InfoPlist.strings"; sourceTree = SOURCE_ROOT; };
		7DEBFE71183BEAD500BC53D5 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "<EMAIL>"; path = "Dropin-Player/<EMAIL>"; sourceTree = SOURCE_ROOT; };
		7DEBFE72183BEAD500BC53D5 /* Default.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = Default.png; path = "Dropin-Player/Default.png"; sourceTree = SOURCE_ROOT; };
		7DEBFE73183BEAD500BC53D5 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; name = "<EMAIL>"; path = "Dropin-Player/<EMAIL>"; sourceTree = SOURCE_ROOT; };
		7DEBFE75183BEAD500BC53D5 /* Dropin-Player-Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "Dropin-Player-Prefix.pch"; path = "Dropin-Player/Dropin-Player-Prefix.pch"; sourceTree = SOURCE_ROOT; };
		7DEBFE76183BEAD500BC53D5 /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = "Dropin-Player/main.m"; sourceTree = SOURCE_ROOT; };
		7DEBFE7C183BECDE00BC53D5 /* Dropin-Player-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = "Dropin-Player-Info.plist"; path = "Dropin-Player/Dropin-Player-Info.plist"; sourceTree = SOURCE_ROOT; };
		7DEBFE7E183BEE7500BC53D5 /* MediaPlayer.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MediaPlayer.framework; path = System/Library/Frameworks/MediaPlayer.framework; sourceTree = SDKROOT; };
		7DEBFE80183BF0AA00BC53D5 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		7DEBFE82183BFC6300BC53D5 /* VDLMainViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = VDLMainViewController.h; sourceTree = SOURCE_ROOT; };
		7DEBFE83183BFC6300BC53D5 /* VDLMainViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VDLMainViewController.m; sourceTree = SOURCE_ROOT; };
		7DEBFE84183BFC6300BC53D5 /* VDLMainViewController.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = VDLMainViewController.xib; sourceTree = SOURCE_ROOT; };
		D4771D92189848E70087441B /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7D11B363183BA71600FF0D25 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7DBC87FD1FE2C0BE002A5ADC /* CoreImage.framework in Frameworks */,
				7D7B6C7D1B70D0BA00E879CF /* libMobileVLCKit.a in Frameworks */,
				7D7B6C4F1B70C43200E879CF /* CoreVideo.framework in Frameworks */,
				7D7B6C4D1B70C41D00E879CF /* CoreMedia.framework in Frameworks */,
				7D7B6C4B1B70C41700E879CF /* VideoToolbox.framework in Frameworks */,
				7D7B6C491B70C40F00E879CF /* libc++.tbd in Frameworks */,
				D4771D93189848E70087441B /* Security.framework in Frameworks */,
				7DEBFE81183BF0AA00BC53D5 /* AVFoundation.framework in Frameworks */,
				7DEBFE7F183BEE7500BC53D5 /* MediaPlayer.framework in Frameworks */,
				7D8745C8183BACBF00FD09E0 /* CoreGraphics.framework in Frameworks */,
				7D8745C7183BACAE00FD09E0 /* QuartzCore.framework in Frameworks */,
				7D8745C3183BAC2700FD09E0 /* libiconv.dylib in Frameworks */,
				7D8745C1183BAC2300FD09E0 /* libbz2.dylib in Frameworks */,
				7D8745BF183BAC1200FD09E0 /* CoreText.framework in Frameworks */,
				7D8745BD183BAC0D00FD09E0 /* CFNetwork.framework in Frameworks */,
				7D8745BB183BABEE00FD09E0 /* OpenGLES.framework in Frameworks */,
				7D8745B8183BABE800FD09E0 /* AudioToolbox.framework in Frameworks */,
				7D11B36A183BA71600FF0D25 /* UIKit.framework in Frameworks */,
				7D11B36C183BA71600FF0D25 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7D11B35D183BA71600FF0D25 = {
			isa = PBXGroup;
			children = (
				7D11B36F183BA71600FF0D25 /* Dropin-Player */,
				7D11B368183BA71600FF0D25 /* Frameworks */,
				7D11B367183BA71600FF0D25 /* Products */,
			);
			sourceTree = "<group>";
		};
		7D11B367183BA71600FF0D25 /* Products */ = {
			isa = PBXGroup;
			children = (
				7D11B366183BA71600FF0D25 /* Dropin-Player.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7D11B368183BA71600FF0D25 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				7DBC87FC1FE2C0BD002A5ADC /* CoreImage.framework */,
				7D7B6C7C1B70D0BA00E879CF /* libMobileVLCKit.a */,
				7D7B6C4E1B70C43200E879CF /* CoreVideo.framework */,
				7D7B6C4C1B70C41D00E879CF /* CoreMedia.framework */,
				7D7B6C4A1B70C41700E879CF /* VideoToolbox.framework */,
				7D7B6C481B70C40F00E879CF /* libc++.tbd */,
				D4771D92189848E70087441B /* Security.framework */,
				7DEBFE80183BF0AA00BC53D5 /* AVFoundation.framework */,
				7DEBFE7E183BEE7500BC53D5 /* MediaPlayer.framework */,
				7D8745B4183BA8F900FD09E0 /* libMobileVLCKit.a */,
				7D8745C6183BACAE00FD09E0 /* QuartzCore.framework */,
				7D8745C2183BAC2700FD09E0 /* libiconv.dylib */,
				7D8745C0183BAC2300FD09E0 /* libbz2.dylib */,
				7D8745BE183BAC1200FD09E0 /* CoreText.framework */,
				7D8745BC183BAC0D00FD09E0 /* CFNetwork.framework */,
				7D8745BA183BABEE00FD09E0 /* OpenGLES.framework */,
				7D8745B6183BABE800FD09E0 /* AudioToolbox.framework */,
				7D8745B7183BABE800FD09E0 /* AudioUnit.framework */,
				7D11B369183BA71600FF0D25 /* UIKit.framework */,
				7D11B36B183BA71600FF0D25 /* Foundation.framework */,
				7D11B36D183BA71600FF0D25 /* CoreGraphics.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		7D11B36F183BA71600FF0D25 /* Dropin-Player */ = {
			isa = PBXGroup;
			children = (
				7DEBFE64183BEA9E00BC53D5 /* VDLAppDelegate.h */,
				7DEBFE65183BEA9E00BC53D5 /* VDLAppDelegate.m */,
				7DEBFE82183BFC6300BC53D5 /* VDLMainViewController.h */,
				7DEBFE83183BFC6300BC53D5 /* VDLMainViewController.m */,
				7DEBFE84183BFC6300BC53D5 /* VDLMainViewController.xib */,
				7DEBFE66183BEA9E00BC53D5 /* VDLPlaybackViewController.h */,
				7DEBFE67183BEA9E00BC53D5 /* VDLPlaybackViewController.m */,
				7DEBFE6B183BEAB500BC53D5 /* VDLPlaybackViewController.xib */,
				7D11B370183BA71600FF0D25 /* Supporting Files */,
			);
			name = "Dropin-Player";
			path = SimplePlayback;
			sourceTree = "<group>";
		};
		7D11B370183BA71600FF0D25 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				7DEBFE7C183BECDE00BC53D5 /* Dropin-Player-Info.plist */,
				7DEBFE71183BEAD500BC53D5 /* <EMAIL> */,
				7DEBFE72183BEAD500BC53D5 /* Default.png */,
				7DEBFE73183BEAD500BC53D5 /* <EMAIL> */,
				7DEBFE75183BEAD500BC53D5 /* Dropin-Player-Prefix.pch */,
				7DEBFE76183BEAD500BC53D5 /* main.m */,
				7DEBFE6E183BEAC300BC53D5 /* InfoPlist.strings */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7D11B365183BA71600FF0D25 /* Dropin-Player */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7D11B389183BA71600FF0D25 /* Build configuration list for PBXNativeTarget "Dropin-Player" */;
			buildPhases = (
				7D11B362183BA71600FF0D25 /* Sources */,
				7D11B363183BA71600FF0D25 /* Frameworks */,
				7D11B364183BA71600FF0D25 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Dropin-Player";
			productName = SimplePlayback;
			productReference = 7D11B366183BA71600FF0D25 /* Dropin-Player.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7D11B35E183BA71600FF0D25 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				CLASSPREFIX = VDL;
				LastUpgradeCheck = 0500;
				ORGANIZATIONNAME = VideoLAN;
			};
			buildConfigurationList = 7D11B361183BA71600FF0D25 /* Build configuration list for PBXProject "Dropin-Player" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
			);
			mainGroup = 7D11B35D183BA71600FF0D25;
			productRefGroup = 7D11B367183BA71600FF0D25 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7D11B365183BA71600FF0D25 /* Dropin-Player */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7D11B364183BA71600FF0D25 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7DEBFE70183BEAC300BC53D5 /* InfoPlist.strings in Resources */,
				7DEBFE86183BFC6300BC53D5 /* VDLMainViewController.xib in Resources */,
				7DEBFE6D183BEAB500BC53D5 /* VDLPlaybackViewController.xib in Resources */,
				7DEBFE79183BEAD500BC53D5 /* <EMAIL> in Resources */,
				7DEBFE77183BEAD500BC53D5 /* <EMAIL> in Resources */,
				7DEBFE78183BEAD500BC53D5 /* Default.png in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7D11B362183BA71600FF0D25 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7DEBFE85183BFC6300BC53D5 /* VDLMainViewController.m in Sources */,
				7DEBFE68183BEA9E00BC53D5 /* VDLAppDelegate.m in Sources */,
				7DEBFE7B183BEAD500BC53D5 /* main.m in Sources */,
				7DEBFE69183BEA9E00BC53D5 /* VDLPlaybackViewController.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		7DEBFE6B183BEAB500BC53D5 /* VDLPlaybackViewController.xib */ = {
			isa = PBXVariantGroup;
			children = (
				7DEBFE6C183BEAB500BC53D5 /* en */,
			);
			name = VDLPlaybackViewController.xib;
			sourceTree = "<group>";
		};
		7DEBFE6E183BEAC300BC53D5 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				7DEBFE6F183BEAC300BC53D5 /* en */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		7D11B387183BA71600FF0D25 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_BITCODE = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.4;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		7D11B388183BA71600FF0D25 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_BITCODE = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.4;
				OTHER_CFLAGS = "-DNS_BLOCK_ASSERTIONS=1";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7D11B38A183BA71600FF0D25 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				DEBUG_INFORMATION_FORMAT = dwarf;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Dropin-Player/Dropin-Player-Prefix.pch";
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(CONFIGURATION_BUILD_DIR)",
				);
				INFOPLIST_FILE = "Dropin-Player/Dropin-Player-Info.plist";
				PRODUCT_NAME = "Dropin-Player";
				WRAPPER_EXTENSION = app;
			};
			name = Debug;
		};
		7D11B38B183BA71600FF0D25 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				DEBUG_INFORMATION_FORMAT = dwarf;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Dropin-Player/Dropin-Player-Prefix.pch";
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(CONFIGURATION_BUILD_DIR)",
				);
				INFOPLIST_FILE = "Dropin-Player/Dropin-Player-Info.plist";
				PRODUCT_NAME = "Dropin-Player";
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7D11B361183BA71600FF0D25 /* Build configuration list for PBXProject "Dropin-Player" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D11B387183BA71600FF0D25 /* Debug */,
				7D11B388183BA71600FF0D25 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7D11B389183BA71600FF0D25 /* Build configuration list for PBXNativeTarget "Dropin-Player" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D11B38A183BA71600FF0D25 /* Debug */,
				7D11B38B183BA71600FF0D25 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7D11B35E183BA71600FF0D25 /* Project object */;
}
