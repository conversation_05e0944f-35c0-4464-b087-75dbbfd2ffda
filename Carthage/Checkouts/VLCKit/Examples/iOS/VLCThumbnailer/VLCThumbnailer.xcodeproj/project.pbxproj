// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		7D3035951D37CDE400C39EC3 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D3035941D37CDE400C39EC3 /* main.m */; };
		7D3035981D37CDE400C39EC3 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D3035971D37CDE400C39EC3 /* AppDelegate.m */; };
		7D30359B1D37CDE400C39EC3 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D30359A1D37CDE400C39EC3 /* ViewController.m */; };
		7D30359E1D37CDE400C39EC3 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 7D30359C1D37CDE400C39EC3 /* Main.storyboard */; };
		7D3035A01D37CDE400C39EC3 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 7D30359F1D37CDE400C39EC3 /* Assets.xcassets */; };
		7D3035A31D37CDE400C39EC3 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 7D3035A11D37CDE400C39EC3 /* LaunchScreen.storyboard */; };
		7D3035AB1D37CEA900C39EC3 /* libMobileVLCKit.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D3035AA1D37CEA900C39EC3 /* libMobileVLCKit.a */; };
		7D3035AF1D37CF7F00C39EC3 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D3035AE1D37CF7F00C39EC3 /* AudioToolbox.framework */; };
		7D3035B11D37CF8600C39EC3 /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D3035B01D37CF8600C39EC3 /* VideoToolbox.framework */; };
		7D3035B31D37CF8A00C39EC3 /* CoreVideo.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D3035B21D37CF8A00C39EC3 /* CoreVideo.framework */; };
		7D3035B51D37CF9300C39EC3 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D3035B41D37CF9300C39EC3 /* libc++.tbd */; };
		7D3035B71D37CFB600C39EC3 /* libiconv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D3035B61D37CFB600C39EC3 /* libiconv.tbd */; };
		7D3035B91D37CFBD00C39EC3 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D3035B81D37CFBD00C39EC3 /* AVFoundation.framework */; };
		7D3035BB1D37CFCD00C39EC3 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7D3035BA1D37CFCD00C39EC3 /* CoreMedia.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		7D3035901D37CDE400C39EC3 /* VLCThumbnailer.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = VLCThumbnailer.app; sourceTree = BUILT_PRODUCTS_DIR; };
		7D3035941D37CDE400C39EC3 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		7D3035961D37CDE400C39EC3 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		7D3035971D37CDE400C39EC3 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		7D3035991D37CDE400C39EC3 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		7D30359A1D37CDE400C39EC3 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		7D30359D1D37CDE400C39EC3 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		7D30359F1D37CDE400C39EC3 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		7D3035A21D37CDE400C39EC3 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		7D3035A41D37CDE400C39EC3 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		7D3035AA1D37CEA900C39EC3 /* libMobileVLCKit.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libMobileVLCKit.a; path = "../../build/Debug-iphoneos/libMobileVLCKit.a"; sourceTree = "<group>"; };
		7D3035AC1D37CF7A00C39EC3 /* AudioUnit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioUnit.framework; path = System/Library/Frameworks/AudioUnit.framework; sourceTree = SDKROOT; };
		7D3035AE1D37CF7F00C39EC3 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		7D3035B01D37CF8600C39EC3 /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = System/Library/Frameworks/VideoToolbox.framework; sourceTree = SDKROOT; };
		7D3035B21D37CF8A00C39EC3 /* CoreVideo.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreVideo.framework; path = System/Library/Frameworks/CoreVideo.framework; sourceTree = SDKROOT; };
		7D3035B41D37CF9300C39EC3 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		7D3035B61D37CFB600C39EC3 /* libiconv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.tbd; path = usr/lib/libiconv.tbd; sourceTree = SDKROOT; };
		7D3035B81D37CFBD00C39EC3 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		7D3035BA1D37CFCD00C39EC3 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7D30358D1D37CDE400C39EC3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7D3035BB1D37CFCD00C39EC3 /* CoreMedia.framework in Frameworks */,
				7D3035B91D37CFBD00C39EC3 /* AVFoundation.framework in Frameworks */,
				7D3035B71D37CFB600C39EC3 /* libiconv.tbd in Frameworks */,
				7D3035B51D37CF9300C39EC3 /* libc++.tbd in Frameworks */,
				7D3035B31D37CF8A00C39EC3 /* CoreVideo.framework in Frameworks */,
				7D3035B11D37CF8600C39EC3 /* VideoToolbox.framework in Frameworks */,
				7D3035AF1D37CF7F00C39EC3 /* AudioToolbox.framework in Frameworks */,
				7D3035AB1D37CEA900C39EC3 /* libMobileVLCKit.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		7D3035871D37CDE300C39EC3 = {
			isa = PBXGroup;
			children = (
				7D3035BC1D37CFF500C39EC3 /* Frameworks */,
				7D3035921D37CDE400C39EC3 /* VLCThumbnailer */,
				7D3035911D37CDE400C39EC3 /* Products */,
			);
			sourceTree = "<group>";
		};
		7D3035911D37CDE400C39EC3 /* Products */ = {
			isa = PBXGroup;
			children = (
				7D3035901D37CDE400C39EC3 /* VLCThumbnailer.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7D3035921D37CDE400C39EC3 /* VLCThumbnailer */ = {
			isa = PBXGroup;
			children = (
				7D3035961D37CDE400C39EC3 /* AppDelegate.h */,
				7D3035971D37CDE400C39EC3 /* AppDelegate.m */,
				7D3035991D37CDE400C39EC3 /* ViewController.h */,
				7D30359A1D37CDE400C39EC3 /* ViewController.m */,
				7D30359C1D37CDE400C39EC3 /* Main.storyboard */,
				7D30359F1D37CDE400C39EC3 /* Assets.xcassets */,
				7D3035A11D37CDE400C39EC3 /* LaunchScreen.storyboard */,
				7D3035A41D37CDE400C39EC3 /* Info.plist */,
				7D3035931D37CDE400C39EC3 /* Supporting Files */,
			);
			path = VLCThumbnailer;
			sourceTree = "<group>";
		};
		7D3035931D37CDE400C39EC3 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				7D3035941D37CDE400C39EC3 /* main.m */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		7D3035BC1D37CFF500C39EC3 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				7D3035BA1D37CFCD00C39EC3 /* CoreMedia.framework */,
				7D3035B81D37CFBD00C39EC3 /* AVFoundation.framework */,
				7D3035B61D37CFB600C39EC3 /* libiconv.tbd */,
				7D3035B41D37CF9300C39EC3 /* libc++.tbd */,
				7D3035B21D37CF8A00C39EC3 /* CoreVideo.framework */,
				7D3035B01D37CF8600C39EC3 /* VideoToolbox.framework */,
				7D3035AE1D37CF7F00C39EC3 /* AudioToolbox.framework */,
				7D3035AC1D37CF7A00C39EC3 /* AudioUnit.framework */,
				7D3035AA1D37CEA900C39EC3 /* libMobileVLCKit.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7D30358F1D37CDE400C39EC3 /* VLCThumbnailer */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7D3035A71D37CDE400C39EC3 /* Build configuration list for PBXNativeTarget "VLCThumbnailer" */;
			buildPhases = (
				7D30358C1D37CDE400C39EC3 /* Sources */,
				7D30358D1D37CDE400C39EC3 /* Frameworks */,
				7D30358E1D37CDE400C39EC3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = VLCThumbnailer;
			productName = VLCThumbnailer;
			productReference = 7D3035901D37CDE400C39EC3 /* VLCThumbnailer.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7D3035881D37CDE300C39EC3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0730;
				ORGANIZATIONNAME = VideoLAN;
				TargetAttributes = {
					7D30358F1D37CDE400C39EC3 = {
						CreatedOnToolsVersion = 7.3.1;
					};
				};
			};
			buildConfigurationList = 7D30358B1D37CDE300C39EC3 /* Build configuration list for PBXProject "VLCThumbnailer" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7D3035871D37CDE300C39EC3;
			productRefGroup = 7D3035911D37CDE400C39EC3 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7D30358F1D37CDE400C39EC3 /* VLCThumbnailer */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7D30358E1D37CDE400C39EC3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7D3035A31D37CDE400C39EC3 /* LaunchScreen.storyboard in Resources */,
				7D3035A01D37CDE400C39EC3 /* Assets.xcassets in Resources */,
				7D30359E1D37CDE400C39EC3 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7D30358C1D37CDE400C39EC3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7D30359B1D37CDE400C39EC3 /* ViewController.m in Sources */,
				7D3035981D37CDE400C39EC3 /* AppDelegate.m in Sources */,
				7D3035951D37CDE400C39EC3 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		7D30359C1D37CDE400C39EC3 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				7D30359D1D37CDE400C39EC3 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		7D3035A11D37CDE400C39EC3 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				7D3035A21D37CDE400C39EC3 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		7D3035A51D37CDE400C39EC3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.3;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		7D3035A61D37CDE400C39EC3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 9.3;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7D3035A81D37CDE400C39EC3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ENABLE_BITCODE = NO;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(CONFIGURATION_BUILD_DIR)",
				);
				INFOPLIST_FILE = VLCThumbnailer/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = org.videolan.VLCThumbnailer;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		7D3035A91D37CDE400C39EC3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ENABLE_BITCODE = NO;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"$(CONFIGURATION_BUILD_DIR)",
				);
				INFOPLIST_FILE = VLCThumbnailer/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = org.videolan.VLCThumbnailer;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7D30358B1D37CDE300C39EC3 /* Build configuration list for PBXProject "VLCThumbnailer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D3035A51D37CDE400C39EC3 /* Debug */,
				7D3035A61D37CDE400C39EC3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7D3035A71D37CDE400C39EC3 /* Build configuration list for PBXNativeTarget "VLCThumbnailer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7D3035A81D37CDE400C39EC3 /* Debug */,
				7D3035A91D37CDE400C39EC3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 7D3035881D37CDE300C39EC3 /* Project object */;
}
