// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		8DD4AA512167AA3100F3EF5A /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4AA502167AA3100F3EF5A /* AppDelegate.swift */; };
		8DD4AA562167AA3100F3EF5A /* Home.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8DD4AA542167AA3100F3EF5A /* Home.storyboard */; };
		8DD4AA582167AA3300F3EF5A /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8DD4AA572167AA3300F3EF5A /* Assets.xcassets */; };
		8DD4AA5B2167AA3300F3EF5A /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8DD4AA592167AA3300F3EF5A /* LaunchScreen.storyboard */; };
		8DD4AA672167AB4900F3EF5A /* HomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4AA662167AB4900F3EF5A /* HomeViewController.swift */; };
		8DD4AA692167AE0C00F3EF5A /* PlaybackViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8DD4AA682167AE0C00F3EF5A /* PlaybackViewController.swift */; };
		8DD4AA6C2167B46C00F3EF5A /* Playback.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8DD4AA6B2167B46C00F3EF5A /* Playback.storyboard */; };
		F94236A0189E951F4ED61D5E /* Pods_SwiftSimplePlayback.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E7D63A357ABAA9287A1FD9E6 /* Pods_SwiftSimplePlayback.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		01CA1F570680619B3C32ADAB /* Pods-SwiftSimplePlayback.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SwiftSimplePlayback.release.xcconfig"; path = "Pods/Target Support Files/Pods-SwiftSimplePlayback/Pods-SwiftSimplePlayback.release.xcconfig"; sourceTree = "<group>"; };
		4184D98DCE5F0D9E79A7338D /* Pods-SwiftSimplePlayback.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SwiftSimplePlayback.debug.xcconfig"; path = "Pods/Target Support Files/Pods-SwiftSimplePlayback/Pods-SwiftSimplePlayback.debug.xcconfig"; sourceTree = "<group>"; };
		8DD4AA4D2167AA3100F3EF5A /* SwiftSimplePlayback.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SwiftSimplePlayback.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8DD4AA502167AA3100F3EF5A /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		8DD4AA552167AA3100F3EF5A /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Home.storyboard; sourceTree = "<group>"; };
		8DD4AA572167AA3300F3EF5A /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		8DD4AA5A2167AA3300F3EF5A /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8DD4AA5C2167AA3300F3EF5A /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8DD4AA662167AB4900F3EF5A /* HomeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeViewController.swift; sourceTree = "<group>"; };
		8DD4AA682167AE0C00F3EF5A /* PlaybackViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PlaybackViewController.swift; sourceTree = "<group>"; };
		8DD4AA6A2167AF9A00F3EF5A /* SwiftSimplePlayback-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "SwiftSimplePlayback-Bridging-Header.h"; sourceTree = "<group>"; };
		8DD4AA6B2167B46C00F3EF5A /* Playback.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = Playback.storyboard; sourceTree = "<group>"; };
		E7D63A357ABAA9287A1FD9E6 /* Pods_SwiftSimplePlayback.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_SwiftSimplePlayback.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8DD4AA4A2167AA3100F3EF5A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F94236A0189E951F4ED61D5E /* Pods_SwiftSimplePlayback.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		5739E475059BFD9D18412FDB /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E7D63A357ABAA9287A1FD9E6 /* Pods_SwiftSimplePlayback.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		8DD4AA442167AA3100F3EF5A = {
			isa = PBXGroup;
			children = (
				8DD4AA4F2167AA3100F3EF5A /* SwiftSimplePlayback */,
				8DD4AA4E2167AA3100F3EF5A /* Products */,
				C62EB4E79E5659AB2FD57816 /* Pods */,
				5739E475059BFD9D18412FDB /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		8DD4AA4E2167AA3100F3EF5A /* Products */ = {
			isa = PBXGroup;
			children = (
				8DD4AA4D2167AA3100F3EF5A /* SwiftSimplePlayback.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8DD4AA4F2167AA3100F3EF5A /* SwiftSimplePlayback */ = {
			isa = PBXGroup;
			children = (
				8DD4AA642167AA9A00F3EF5A /* Sources */,
				8DD4AA622167AA5C00F3EF5A /* Resources */,
			);
			path = SwiftSimplePlayback;
			sourceTree = "<group>";
		};
		8DD4AA622167AA5C00F3EF5A /* Resources */ = {
			isa = PBXGroup;
			children = (
				8DD4AA632167AA8900F3EF5A /* Storyboards */,
				8DD4AA572167AA3300F3EF5A /* Assets.xcassets */,
				8DD4AA5C2167AA3300F3EF5A /* Info.plist */,
				8DD4AA6A2167AF9A00F3EF5A /* SwiftSimplePlayback-Bridging-Header.h */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		8DD4AA632167AA8900F3EF5A /* Storyboards */ = {
			isa = PBXGroup;
			children = (
				8DD4AA542167AA3100F3EF5A /* Home.storyboard */,
				8DD4AA592167AA3300F3EF5A /* LaunchScreen.storyboard */,
				8DD4AA6B2167B46C00F3EF5A /* Playback.storyboard */,
			);
			path = Storyboards;
			sourceTree = "<group>";
		};
		8DD4AA642167AA9A00F3EF5A /* Sources */ = {
			isa = PBXGroup;
			children = (
				8DD4AA502167AA3100F3EF5A /* AppDelegate.swift */,
				8DD4AA662167AB4900F3EF5A /* HomeViewController.swift */,
				8DD4AA682167AE0C00F3EF5A /* PlaybackViewController.swift */,
			);
			path = Sources;
			sourceTree = "<group>";
		};
		C62EB4E79E5659AB2FD57816 /* Pods */ = {
			isa = PBXGroup;
			children = (
				4184D98DCE5F0D9E79A7338D /* Pods-SwiftSimplePlayback.debug.xcconfig */,
				01CA1F570680619B3C32ADAB /* Pods-SwiftSimplePlayback.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8DD4AA4C2167AA3100F3EF5A /* SwiftSimplePlayback */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8DD4AA5F2167AA3300F3EF5A /* Build configuration list for PBXNativeTarget "SwiftSimplePlayback" */;
			buildPhases = (
				9A50F34036361C661339757B /* [CP] Check Pods Manifest.lock */,
				8DD4AA492167AA3100F3EF5A /* Sources */,
				8DD4AA4A2167AA3100F3EF5A /* Frameworks */,
				8DD4AA4B2167AA3100F3EF5A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SwiftSimplePlayback;
			productName = SwiftSimplePlayback;
			productReference = 8DD4AA4D2167AA3100F3EF5A /* SwiftSimplePlayback.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8DD4AA452167AA3100F3EF5A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1000;
				LastUpgradeCheck = 1000;
				TargetAttributes = {
					8DD4AA4C2167AA3100F3EF5A = {
						CreatedOnToolsVersion = 10.0;
					};
				};
			};
			buildConfigurationList = 8DD4AA482167AA3100F3EF5A /* Build configuration list for PBXProject "SwiftSimplePlayback" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8DD4AA442167AA3100F3EF5A;
			productRefGroup = 8DD4AA4E2167AA3100F3EF5A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8DD4AA4C2167AA3100F3EF5A /* SwiftSimplePlayback */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8DD4AA4B2167AA3100F3EF5A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8DD4AA5B2167AA3300F3EF5A /* LaunchScreen.storyboard in Resources */,
				8DD4AA6C2167B46C00F3EF5A /* Playback.storyboard in Resources */,
				8DD4AA582167AA3300F3EF5A /* Assets.xcassets in Resources */,
				8DD4AA562167AA3100F3EF5A /* Home.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		9A50F34036361C661339757B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-SwiftSimplePlayback-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8DD4AA492167AA3100F3EF5A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8DD4AA692167AE0C00F3EF5A /* PlaybackViewController.swift in Sources */,
				8DD4AA512167AA3100F3EF5A /* AppDelegate.swift in Sources */,
				8DD4AA672167AB4900F3EF5A /* HomeViewController.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		8DD4AA542167AA3100F3EF5A /* Home.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8DD4AA552167AA3100F3EF5A /* Base */,
			);
			name = Home.storyboard;
			sourceTree = "<group>";
		};
		8DD4AA592167AA3300F3EF5A /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8DD4AA5A2167AA3300F3EF5A /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		8DD4AA5D2167AA3300F3EF5A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8DD4AA5E2167AA3300F3EF5A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		8DD4AA602167AA3300F3EF5A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4184D98DCE5F0D9E79A7338D /* Pods-SwiftSimplePlayback.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = "$(SRCROOT)/SwiftSimplePlayback/Resources/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.example.SwiftSimplePlayback;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/SwiftSimplePlayback/Resources/SwiftSimplePlayback-Bridging-Header.h";
				SWIFT_VERSION = 4.2;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		8DD4AA612167AA3300F3EF5A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 01CA1F570680619B3C32ADAB /* Pods-SwiftSimplePlayback.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_LAUNCHIMAGE_NAME = LaunchImage;
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = "$(SRCROOT)/SwiftSimplePlayback/Resources/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.example.SwiftSimplePlayback;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_OBJC_BRIDGING_HEADER = "$(SRCROOT)/SwiftSimplePlayback/Resources/SwiftSimplePlayback-Bridging-Header.h";
				SWIFT_VERSION = 4.2;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8DD4AA482167AA3100F3EF5A /* Build configuration list for PBXProject "SwiftSimplePlayback" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8DD4AA5D2167AA3300F3EF5A /* Debug */,
				8DD4AA5E2167AA3300F3EF5A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8DD4AA5F2167AA3300F3EF5A /* Build configuration list for PBXNativeTarget "SwiftSimplePlayback" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8DD4AA602167AA3300F3EF5A /* Debug */,
				8DD4AA612167AA3300F3EF5A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8DD4AA452167AA3100F3EF5A /* Project object */;
}
