<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="14313.18" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14283.14"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Home View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController id="BYZ-38-t0r" customClass="HomeViewController" customModule="SwiftSimplePlayback" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1aU-GE-H2g">
                                <rect key="frame" x="172.5" y="501" width="30" height="30"/>
                                <state key="normal" title="Play"/>
                                <connections>
                                    <action selector="startPlayback:" destination="BYZ-38-t0r" eventType="touchUpInside" id="0fd-oW-eVz"/>
                                    <segue destination="etS-xJ-QeL" kind="show" id="SGL-cr-Y7l"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Sample Playback in Swift!" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ILr-wV-FjM">
                                <rect key="frame" x="89.5" y="157" width="196" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="1aU-GE-H2g" firstAttribute="centerX" secondItem="6Tk-OE-BBY" secondAttribute="centerX" id="7qG-e5-Mie"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="bottom" secondItem="1aU-GE-H2g" secondAttribute="bottom" constant="136" id="EjW-cP-SPJ"/>
                            <constraint firstItem="ILr-wV-FjM" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" constant="137" id="dtd-zc-8kp"/>
                            <constraint firstItem="ILr-wV-FjM" firstAttribute="centerX" secondItem="6Tk-OE-BBY" secondAttribute="centerX" id="lPh-UU-0QS"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="53.600000000000001" y="26.53673163418291"/>
        </scene>
        <!--Playback.storyboard-->
        <scene sceneID="gVo-ka-FtE">
            <objects>
                <viewControllerPlaceholder storyboardName="Playback" id="etS-xJ-QeL" userLabel="Playback.storyboard" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Egb-VZ-ZaT" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="785" y="6"/>
        </scene>
    </scenes>
</document>
