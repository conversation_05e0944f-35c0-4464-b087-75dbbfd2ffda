<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="14313.18" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="HGc-G6-ZL5">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14283.14"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Playback View Controller-->
        <scene sceneID="JpN-Jc-R5s">
            <objects>
                <viewController storyboardIdentifier="Playback" id="HGc-G6-ZL5" customClass="PlaybackViewController" customModule="SwiftSimplePlayback" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="3O4-cU-Yvn">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aLL-ew-e1a" userLabel="Movie View">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hvv-Rg-xyx">
                                <rect key="frame" x="165" y="501" width="45" height="30"/>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                                <state key="normal" title="Pause"/>
                                <state key="selected" title="Play"/>
                                <connections>
                                    <action selector="handlePlayPause:" destination="HGc-G6-ZL5" eventType="touchUpInside" id="cuq-cH-eWJ"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="hvv-Rg-xyx" firstAttribute="centerX" secondItem="haG-g0-pgP" secondAttribute="centerX" id="25b-2F-SL7"/>
                            <constraint firstItem="aLL-ew-e1a" firstAttribute="top" secondItem="haG-g0-pgP" secondAttribute="top" id="395-cS-Hum"/>
                            <constraint firstItem="aLL-ew-e1a" firstAttribute="leading" secondItem="haG-g0-pgP" secondAttribute="leading" id="JIO-nJ-wAX"/>
                            <constraint firstItem="aLL-ew-e1a" firstAttribute="trailing" secondItem="haG-g0-pgP" secondAttribute="trailing" id="o58-o3-MHz"/>
                            <constraint firstItem="haG-g0-pgP" firstAttribute="bottom" secondItem="hvv-Rg-xyx" secondAttribute="bottom" constant="136" id="qbU-wZ-bCY"/>
                            <constraint firstItem="aLL-ew-e1a" firstAttribute="bottom" secondItem="haG-g0-pgP" secondAttribute="bottom" id="rNF-jR-goY"/>
                        </constraints>
                        <viewLayoutGuide key="safeArea" id="haG-g0-pgP"/>
                    </view>
                    <connections>
                        <outlet property="movieView" destination="aLL-ew-e1a" id="cew-n6-uFh"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="vYb-uN-ZbY" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
        </scene>
    </scenes>
</document>
