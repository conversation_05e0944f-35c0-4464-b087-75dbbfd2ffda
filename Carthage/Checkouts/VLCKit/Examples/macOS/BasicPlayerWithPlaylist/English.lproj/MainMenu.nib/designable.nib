<?xml version="1.0" encoding="UTF-8"?>
<archive type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="8.00">
	<data>
		<int key="IBDocument.SystemTarget">1090</int>
		<string key="IBDocument.SystemVersion">12F45</string>
		<string key="IBDocument.InterfaceBuilderVersion">4514</string>
		<string key="IBDocument.AppKitVersion">1187.40</string>
		<string key="IBDocument.HIToolboxVersion">626.00</string>
		<object class="NSMutableDictionary" key="IBDocument.PluginVersions">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaPlugin</string>
			<string key="NS.object.0">4514</string>
		</object>
		<array key="IBDocument.IntegratedClassDependencies">
			<string>NSButton</string>
			<string>NSButtonCell</string>
			<string>NSCustomObject</string>
			<string>NSCustomView</string>
			<string>NSMenu</string>
			<string>NSMenuItem</string>
			<string>NSPopUpButton</string>
			<string>NSPopUpButtonCell</string>
			<string>NSScrollView</string>
			<string>NSScroller</string>
			<string>NSTableColumn</string>
			<string>NSTableHeaderView</string>
			<string>NSTableView</string>
			<string>NSTextFieldCell</string>
			<string>NSView</string>
			<string>NSWindowTemplate</string>
		</array>
		<array key="IBDocument.PluginDependencies">
			<string>com.apple.InterfaceBuilder.CocoaPlugin</string>
		</array>
		<object class="NSMutableDictionary" key="IBDocument.Metadata">
			<string key="NS.key.0">PluginDependencyRecalculationVersion</string>
			<integer value="1" key="NS.object.0"/>
		</object>
		<array class="NSMutableArray" key="IBDocument.RootObjects" id="1038916788">
			<object class="NSCustomObject" id="1040500158">
				<object class="NSMutableString" key="NSClassName">
					<characters key="NS.bytes">NSApplication</characters>
				</object>
			</object>
			<object class="NSCustomObject" id="267684775">
				<string key="NSClassName">FirstResponder</string>
			</object>
			<object class="NSCustomObject" id="376421538">
				<string key="NSClassName">NSApplication</string>
			</object>
			<object class="NSWindowTemplate" id="685881732">
				<int key="NSWindowStyleMask">15</int>
				<int key="NSWindowBacking">2</int>
				<string key="NSWindowRect">{{300, 293}, {779, 429}}</string>
				<int key="NSWTFlags">1886912512</int>
				<string key="NSWindowTitle">VLCMovieView Example</string>
				<string key="NSWindowClass">NSWindow</string>
				<object class="NSMutableString" key="NSViewClass">
					<characters key="NS.bytes">View</characters>
				</object>
				<nil key="NSUserInterfaceItemIdentifier"/>
				<object class="NSView" key="NSWindowView" id="1065405168">
					<reference key="NSNextResponder"/>
					<int key="NSvFlags">256</int>
					<array class="NSMutableArray" key="NSSubviews">
						<object class="NSCustomView" id="326902688">
							<reference key="NSNextResponder" ref="1065405168"/>
							<int key="NSvFlags">274</int>
							<string key="NSFrame">{{194, 46}, {585, 383}}</string>
							<reference key="NSSuperview" ref="1065405168"/>
							<reference key="NSWindow"/>
							<object class="NSMutableString" key="NSClassName">
								<characters key="NS.bytes">NSView</characters>
							</object>
							<string key="NSExtension">NSResponder</string>
						</object>
						<object class="NSButton" id="549134629">
							<reference key="NSNextResponder" ref="1065405168"/>
							<int key="NSvFlags">256</int>
							<string key="NSFrame">{{683, 6}, {82, 32}}</string>
							<reference key="NSSuperview" ref="1065405168"/>
							<reference key="NSWindow"/>
							<bool key="NSEnabled">YES</bool>
							<object class="NSButtonCell" key="NSCell" id="451252014">
								<int key="NSCellFlags">67108864</int>
								<int key="NSCellFlags2">134217728</int>
								<string key="NSContents">Pause</string>
								<object class="NSFont" key="NSSupport" id="689181702">
									<string key="NSName">LucidaGrande</string>
									<double key="NSSize">13</double>
									<int key="NSfFlags">1044</int>
								</object>
								<reference key="NSControlView" ref="549134629"/>
								<int key="NSButtonFlags">-2038284288</int>
								<int key="NSButtonFlags2">1</int>
								<reference key="NSAlternateImage" ref="689181702"/>
								<string key="NSAlternateContents"/>
								<object class="NSMutableString" key="NSKeyEquivalent">
									<characters key="NS.bytes"/>
								</object>
								<int key="NSPeriodicDelay">200</int>
								<int key="NSPeriodicInterval">25</int>
							</object>
							<bool key="NSAllowsLogicalLayoutDirection">NO</bool>
						</object>
						<object class="NSButton" id="136170242">
							<reference key="NSNextResponder" ref="1065405168"/>
							<int key="NSvFlags">256</int>
							<string key="NSFrame">{{610, 6}, {82, 32}}</string>
							<reference key="NSSuperview" ref="1065405168"/>
							<reference key="NSWindow"/>
							<bool key="NSEnabled">YES</bool>
							<object class="NSButtonCell" key="NSCell" id="755927330">
								<int key="NSCellFlags">67108864</int>
								<int key="NSCellFlags2">134217728</int>
								<string key="NSContents">Play</string>
								<reference key="NSSupport" ref="689181702"/>
								<reference key="NSControlView" ref="136170242"/>
								<int key="NSButtonFlags">-2038284288</int>
								<int key="NSButtonFlags2">1</int>
								<reference key="NSAlternateImage" ref="689181702"/>
								<string key="NSAlternateContents"/>
								<object class="NSMutableString" key="NSKeyEquivalent">
									<characters key="NS.bytes"/>
								</object>
								<int key="NSPeriodicDelay">200</int>
								<int key="NSPeriodicInterval">25</int>
							</object>
							<bool key="NSAllowsLogicalLayoutDirection">NO</bool>
						</object>
						<object class="NSScrollView" id="825537634">
							<reference key="NSNextResponder" ref="1065405168"/>
							<int key="NSvFlags">256</int>
							<array class="NSMutableArray" key="NSSubviews">
								<object class="NSClipView" id="1072801383">
									<reference key="NSNextResponder" ref="825537634"/>
									<int key="NSvFlags">2322</int>
									<array class="NSMutableArray" key="NSSubviews">
										<object class="NSTableView" id="690384467">
											<reference key="NSNextResponder" ref="1072801383"/>
											<int key="NSvFlags">4352</int>
											<string key="NSFrameSize">{193, 365}</string>
											<reference key="NSSuperview" ref="1072801383"/>
											<reference key="NSWindow"/>
											<bool key="NSEnabled">YES</bool>
											<bool key="NSAllowsLogicalLayoutDirection">NO</bool>
											<bool key="NSControlAllowsExpansionToolTips">YES</bool>
											<object class="NSTableHeaderView" key="NSHeaderView" id="71094015">
												<reference key="NSNextResponder" ref="692441416"/>
												<int key="NSvFlags">256</int>
												<string key="NSFrameSize">{193, 17}</string>
												<reference key="NSSuperview" ref="692441416"/>
												<reference key="NSWindow"/>
												<reference key="NSTableView" ref="690384467"/>
											</object>
											<object class="_NSCornerView" key="NSCornerView">
												<nil key="NSNextResponder"/>
												<int key="NSvFlags">256</int>
												<string key="NSFrame">{{183, 0}, {12, 17}}</string>
											</object>
											<array class="NSMutableArray" key="NSTableColumns">
												<object class="NSTableColumn" id="232857741">
													<double key="NSWidth">179</double>
													<double key="NSMinWidth">40</double>
													<double key="NSMaxWidth">1000</double>
													<object class="NSTableHeaderCell" key="NSHeaderCell">
														<int key="NSCellFlags">75497536</int>
														<int key="NSCellFlags2">2048</int>
														<string key="NSContents"/>
														<object class="NSFont" key="NSSupport">
															<string key="NSName">LucidaGrande</string>
															<double key="NSSize">11</double>
															<int key="NSfFlags">3100</int>
														</object>
														<object class="NSColor" key="NSBackgroundColor">
															<int key="NSColorSpace">3</int>
															<bytes key="NSWhite">MC4zMzMzMzI5OQA</bytes>
														</object>
														<object class="NSColor" key="NSTextColor">
															<int key="NSColorSpace">6</int>
															<string key="NSCatalogName">System</string>
															<string key="NSColorName">headerTextColor</string>
															<object class="NSColor" key="NSColor" id="175623724">
																<int key="NSColorSpace">3</int>
																<bytes key="NSWhite">MAA</bytes>
															</object>
														</object>
													</object>
													<object class="NSTextFieldCell" key="NSDataCell" id="215679856">
														<int key="NSCellFlags">337641536</int>
														<int key="NSCellFlags2">2048</int>
														<reference key="NSSupport" ref="689181702"/>
														<reference key="NSControlView" ref="690384467"/>
														<object class="NSColor" key="NSBackgroundColor" id="565899671">
															<int key="NSColorSpace">6</int>
															<string key="NSCatalogName">System</string>
															<string key="NSColorName">controlBackgroundColor</string>
															<object class="NSColor" key="NSColor">
																<int key="NSColorSpace">3</int>
																<bytes key="NSWhite">MC42NjY2NjY2NjY3AA</bytes>
															</object>
														</object>
														<object class="NSColor" key="NSTextColor">
															<int key="NSColorSpace">6</int>
															<string key="NSCatalogName">System</string>
															<string key="NSColorName">controlTextColor</string>
															<reference key="NSColor" ref="175623724"/>
														</object>
													</object>
													<reference key="NSTableView" ref="690384467"/>
												</object>
											</array>
											<double key="NSIntercellSpacingWidth">3</double>
											<double key="NSIntercellSpacingHeight">2</double>
											<object class="NSColor" key="NSBackgroundColor">
												<int key="NSColorSpace">3</int>
												<bytes key="NSWhite">MQA</bytes>
											</object>
											<object class="NSColor" key="NSGridColor">
												<int key="NSColorSpace">6</int>
												<string key="NSCatalogName">System</string>
												<string key="NSColorName">gridColor</string>
												<object class="NSColor" key="NSColor">
													<int key="NSColorSpace">3</int>
													<bytes key="NSWhite">MC41AA</bytes>
												</object>
											</object>
											<double key="NSRowHeight">17</double>
											<int key="NSTvFlags">-702545920</int>
											<reference key="NSDelegate"/>
											<reference key="NSDataSource"/>
											<int key="NSColumnAutoresizingStyle">4</int>
											<int key="NSDraggingSourceMaskForLocal">15</int>
											<int key="NSDraggingSourceMaskForNonLocal">0</int>
											<bool key="NSAllowsTypeSelect">YES</bool>
											<int key="NSTableViewDraggingDestinationStyle">0</int>
											<int key="NSTableViewGroupRowStyle">1</int>
										</object>
									</array>
									<string key="NSFrame">{{1, 17}, {193, 365}}</string>
									<reference key="NSSuperview" ref="825537634"/>
									<reference key="NSWindow"/>
									<reference key="NSNextKeyView" ref="690384467"/>
									<reference key="NSDocView" ref="690384467"/>
									<reference key="NSBGColor" ref="565899671"/>
									<int key="NScvFlags">4</int>
								</object>
								<object class="NSScroller" id="1038890907">
									<reference key="NSNextResponder" ref="825537634"/>
									<int key="NSvFlags">256</int>
									<string key="NSFrame">{{180, 17}, {14, 365}}</string>
									<reference key="NSSuperview" ref="825537634"/>
									<reference key="NSWindow"/>
									<bool key="NSAllowsLogicalLayoutDirection">NO</bool>
									<int key="NSsFlags">256</int>
									<reference key="NSTarget" ref="825537634"/>
									<string key="NSAction">_doScroller:</string>
									<double key="NSPercent">0.95789474248886108</double>
								</object>
								<object class="NSScroller" id="1006009745">
									<reference key="NSNextResponder" ref="825537634"/>
									<int key="NSvFlags">256</int>
									<string key="NSFrame">{{1, 368}, {193, 14}}</string>
									<reference key="NSSuperview" ref="825537634"/>
									<reference key="NSWindow"/>
									<bool key="NSAllowsLogicalLayoutDirection">NO</bool>
									<int key="NSsFlags">257</int>
									<reference key="NSTarget" ref="825537634"/>
									<string key="NSAction">_doScroller:</string>
									<double key="NSPercent">0.99047619104385376</double>
								</object>
								<object class="NSClipView" id="692441416">
									<reference key="NSNextResponder" ref="825537634"/>
									<int key="NSvFlags">2304</int>
									<array class="NSMutableArray" key="NSSubviews">
										<reference ref="71094015"/>
									</array>
									<string key="NSFrame">{{1, 0}, {193, 17}}</string>
									<reference key="NSSuperview" ref="825537634"/>
									<reference key="NSWindow"/>
									<reference key="NSNextKeyView" ref="71094015"/>
									<reference key="NSDocView" ref="71094015"/>
									<reference key="NSBGColor" ref="565899671"/>
									<int key="NScvFlags">4</int>
								</object>
							</array>
							<string key="NSFrame">{{0, 46}, {195, 383}}</string>
							<reference key="NSSuperview" ref="1065405168"/>
							<reference key="NSWindow"/>
							<reference key="NSNextKeyView" ref="1072801383"/>
							<int key="NSsFlags">133170</int>
							<reference key="NSVScroller" ref="1038890907"/>
							<reference key="NSHScroller" ref="1006009745"/>
							<reference key="NSContentView" ref="1072801383"/>
							<reference key="NSHeaderClipView" ref="692441416"/>
							<bytes key="NSScrollAmts">QSAAAEEgAABBmAAAQZgAAA</bytes>
							<double key="NSMinMagnification">0.25</double>
							<double key="NSMaxMagnification">4</double>
							<double key="NSMagnification">1</double>
						</object>
						<object class="NSPopUpButton" id="234891018">
							<reference key="NSNextResponder" ref="1065405168"/>
							<int key="NSvFlags">268</int>
							<string key="NSFrame">{{459, 10}, {152, 26}}</string>
							<reference key="NSSuperview" ref="1065405168"/>
							<reference key="NSWindow"/>
							<bool key="NSEnabled">YES</bool>
							<object class="NSPopUpButtonCell" key="NSCell" id="790695695">
								<int key="NSCellFlags">-2076180416</int>
								<int key="NSCellFlags2">2048</int>
								<reference key="NSSupport" ref="689181702"/>
								<reference key="NSControlView" ref="234891018"/>
								<int key="NSButtonFlags">109199360</int>
								<int key="NSButtonFlags2">129</int>
								<string key="NSAlternateContents"/>
								<string key="NSKeyEquivalent"/>
								<int key="NSPeriodicDelay">400</int>
								<int key="NSPeriodicInterval">75</int>
								<object class="NSMenuItem" key="NSMenuItem" id="158621198">
									<reference key="NSMenu" ref="849953390"/>
									<string key="NSTitle">Item 1</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<int key="NSState">1</int>
									<object class="NSCustomResource" key="NSOnImage" id="1048292030">
										<string key="NSClassName">NSImage</string>
										<string key="NSResourceName">NSMenuCheckmark</string>
									</object>
									<object class="NSCustomResource" key="NSMixedImage" id="369028431">
										<string key="NSClassName">NSImage</string>
										<string key="NSResourceName">NSMenuMixedState</string>
									</object>
									<string key="NSAction">_popUpItemAction:</string>
									<reference key="NSTarget" ref="790695695"/>
								</object>
								<bool key="NSMenuItemRespectAlignment">YES</bool>
								<object class="NSMenu" key="NSMenu" id="849953390">
									<string key="NSTitle">OtherViews</string>
									<array class="NSMutableArray" key="NSMenuItems">
										<reference ref="158621198"/>
										<object class="NSMenuItem" id="1013813959">
											<reference key="NSMenu" ref="849953390"/>
											<string key="NSTitle">Item 2</string>
											<string key="NSKeyEquiv"/>
											<int key="NSKeyEquivModMask">1048576</int>
											<int key="NSMnemonicLoc">2147483647</int>
											<reference key="NSOnImage" ref="1048292030"/>
											<reference key="NSMixedImage" ref="369028431"/>
											<string key="NSAction">_popUpItemAction:</string>
											<reference key="NSTarget" ref="790695695"/>
										</object>
										<object class="NSMenuItem" id="379903749">
											<reference key="NSMenu" ref="849953390"/>
											<string key="NSTitle">Item 3</string>
											<string key="NSKeyEquiv"/>
											<int key="NSKeyEquivModMask">1048576</int>
											<int key="NSMnemonicLoc">2147483647</int>
											<reference key="NSOnImage" ref="1048292030"/>
											<reference key="NSMixedImage" ref="369028431"/>
											<string key="NSAction">_popUpItemAction:</string>
											<reference key="NSTarget" ref="790695695"/>
										</object>
									</array>
									<reference key="NSMenuFont" ref="689181702"/>
								</object>
								<int key="NSPreferredEdge">1</int>
								<bool key="NSUsesItemFromMenu">YES</bool>
								<bool key="NSAltersState">YES</bool>
								<int key="NSArrowPosition">2</int>
							</object>
							<bool key="NSAllowsLogicalLayoutDirection">NO</bool>
						</object>
					</array>
					<string key="NSFrameSize">{779, 429}</string>
					<reference key="NSSuperview"/>
					<reference key="NSWindow"/>
				</object>
				<string key="NSScreenRect">{{0, 0}, {1280, 778}}</string>
				<string key="NSMaxSize">{10000000000000, 10000000000000}</string>
				<bool key="NSWindowIsRestorable">YES</bool>
			</object>
			<object class="NSMenu" id="297869185">
				<string key="NSTitle">MainMenu</string>
				<array class="NSMutableArray" key="NSMenuItems">
					<object class="NSMenuItem" id="117182161">
						<reference key="NSMenu" ref="297869185"/>
						<string key="NSTitle">NewApplication</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="1048292030"/>
						<reference key="NSMixedImage" ref="369028431"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="575604144">
							<string key="NSTitle">NewApplication</string>
							<array class="NSMutableArray" key="NSMenuItems">
								<object class="NSMenuItem" id="292858397">
									<reference key="NSMenu" ref="575604144"/>
									<string key="NSTitle">About NewApplication</string>
									<string key="NSKeyEquiv"/>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="817551412">
									<reference key="NSMenu" ref="575604144"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="537674438">
									<reference key="NSMenu" ref="575604144"/>
									<string key="NSTitle">Preferences…</string>
									<string key="NSKeyEquiv">,</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="412901610">
									<reference key="NSMenu" ref="575604144"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="949890269">
									<reference key="NSMenu" ref="575604144"/>
									<string key="NSTitle">Services</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="837556560">
										<object class="NSMutableString" key="NSTitle">
											<characters key="NS.bytes">Services</characters>
										</object>
										<array class="NSMutableArray" key="NSMenuItems"/>
										<string key="NSName">_NSServicesMenu</string>
									</object>
								</object>
								<object class="NSMenuItem" id="700341395">
									<reference key="NSMenu" ref="575604144"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="839114105">
									<reference key="NSMenu" ref="575604144"/>
									<string key="NSTitle">Hide NewApplication</string>
									<string key="NSKeyEquiv">h</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="515890964">
									<reference key="NSMenu" ref="575604144"/>
									<string key="NSTitle">Hide Others</string>
									<string key="NSKeyEquiv">h</string>
									<int key="NSKeyEquivModMask">1572864</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="429027640">
									<reference key="NSMenu" ref="575604144"/>
									<string key="NSTitle">Show All</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="1049329156">
									<reference key="NSMenu" ref="575604144"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="149933402">
									<reference key="NSMenu" ref="575604144"/>
									<string key="NSTitle">Quit NewApplication</string>
									<string key="NSKeyEquiv">q</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
							</array>
							<string key="NSName">_NSAppleMenu</string>
						</object>
					</object>
					<object class="NSMenuItem" id="155728425">
						<reference key="NSMenu" ref="297869185"/>
						<string key="NSTitle">File</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="1048292030"/>
						<reference key="NSMixedImage" ref="369028431"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="509583233">
							<object class="NSMutableString" key="NSTitle">
								<characters key="NS.bytes">File</characters>
							</object>
							<array class="NSMutableArray" key="NSMenuItems">
								<object class="NSMenuItem" id="854309318">
									<reference key="NSMenu" ref="509583233"/>
									<string key="NSTitle">New</string>
									<string key="NSKeyEquiv">n</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="444052652">
									<reference key="NSMenu" ref="509583233"/>
									<string key="NSTitle">Open...</string>
									<string key="NSKeyEquiv">o</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="525613171">
									<reference key="NSMenu" ref="509583233"/>
									<string key="NSTitle">Open Recent</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="967985765">
										<object class="NSMutableString" key="NSTitle">
											<characters key="NS.bytes">Open Recent</characters>
										</object>
										<array class="NSMutableArray" key="NSMenuItems">
											<object class="NSMenuItem" id="450222093">
												<reference key="NSMenu" ref="967985765"/>
												<string key="NSTitle">Clear Menu</string>
												<string key="NSKeyEquiv"/>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="1048292030"/>
												<reference key="NSMixedImage" ref="369028431"/>
											</object>
										</array>
										<string key="NSName">_NSRecentDocumentsMenu</string>
									</object>
								</object>
								<object class="NSMenuItem" id="348360378">
									<reference key="NSMenu" ref="509583233"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="702518987">
									<reference key="NSMenu" ref="509583233"/>
									<string key="NSTitle">Close</string>
									<string key="NSKeyEquiv">w</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="476632045">
									<reference key="NSMenu" ref="509583233"/>
									<string key="NSTitle">Save</string>
									<string key="NSKeyEquiv">s</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="1043084507">
									<reference key="NSMenu" ref="509583233"/>
									<string key="NSTitle">Save As…</string>
									<string key="NSKeyEquiv">S</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="15477557">
									<reference key="NSMenu" ref="509583233"/>
									<string key="NSTitle">Revert</string>
									<string key="NSKeyEquiv"/>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="446326576">
									<reference key="NSMenu" ref="509583233"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="1013792391">
									<reference key="NSMenu" ref="509583233"/>
									<string key="NSTitle">Page Setup…</string>
									<string key="NSKeyEquiv">P</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="958066245">
									<reference key="NSMenu" ref="509583233"/>
									<string key="NSTitle">Print…</string>
									<string key="NSKeyEquiv">p</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
							</array>
						</object>
					</object>
					<object class="NSMenuItem" id="567403254">
						<reference key="NSMenu" ref="297869185"/>
						<string key="NSTitle">Edit</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="1048292030"/>
						<reference key="NSMixedImage" ref="369028431"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="738602787">
							<object class="NSMutableString" key="NSTitle">
								<characters key="NS.bytes">Edit</characters>
							</object>
							<array class="NSMutableArray" key="NSMenuItems">
								<object class="NSMenuItem" id="162667957">
									<reference key="NSMenu" ref="738602787"/>
									<string key="NSTitle">Undo</string>
									<string key="NSKeyEquiv">z</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="382488015">
									<reference key="NSMenu" ref="738602787"/>
									<string key="NSTitle">Redo</string>
									<string key="NSKeyEquiv">Z</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="316514825">
									<reference key="NSMenu" ref="738602787"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="11142583">
									<reference key="NSMenu" ref="738602787"/>
									<string key="NSTitle">Cut</string>
									<string key="NSKeyEquiv">x</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="99439887">
									<reference key="NSMenu" ref="738602787"/>
									<string key="NSTitle">Copy</string>
									<string key="NSKeyEquiv">c</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="386458390">
									<reference key="NSMenu" ref="738602787"/>
									<string key="NSTitle">Paste</string>
									<string key="NSKeyEquiv">v</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="34045290">
									<reference key="NSMenu" ref="738602787"/>
									<string key="NSTitle">Paste and Match Style</string>
									<string key="NSKeyEquiv">V</string>
									<int key="NSKeyEquivModMask">1572864</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="712819308">
									<reference key="NSMenu" ref="738602787"/>
									<string key="NSTitle">Delete</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="246032602">
									<reference key="NSMenu" ref="738602787"/>
									<string key="NSTitle">Select All</string>
									<string key="NSKeyEquiv">a</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="1048640993">
									<reference key="NSMenu" ref="738602787"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="49519850">
									<reference key="NSMenu" ref="738602787"/>
									<string key="NSTitle">Find</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="836553275">
										<object class="NSMutableString" key="NSTitle">
											<characters key="NS.bytes">Find</characters>
										</object>
										<array class="NSMutableArray" key="NSMenuItems">
											<object class="NSMenuItem" id="596760929">
												<reference key="NSMenu" ref="836553275"/>
												<string key="NSTitle">Find…</string>
												<string key="NSKeyEquiv">f</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="1048292030"/>
												<reference key="NSMixedImage" ref="369028431"/>
												<int key="NSTag">1</int>
											</object>
											<object class="NSMenuItem" id="377678273">
												<reference key="NSMenu" ref="836553275"/>
												<string key="NSTitle">Find Next</string>
												<string key="NSKeyEquiv">g</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="1048292030"/>
												<reference key="NSMixedImage" ref="369028431"/>
												<int key="NSTag">2</int>
											</object>
											<object class="NSMenuItem" id="740861002">
												<reference key="NSMenu" ref="836553275"/>
												<string key="NSTitle">Find Previous</string>
												<string key="NSKeyEquiv">G</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="1048292030"/>
												<reference key="NSMixedImage" ref="369028431"/>
												<int key="NSTag">3</int>
											</object>
											<object class="NSMenuItem" id="556272487">
												<reference key="NSMenu" ref="836553275"/>
												<string key="NSTitle">Use Selection for Find</string>
												<string key="NSKeyEquiv">e</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="1048292030"/>
												<reference key="NSMixedImage" ref="369028431"/>
												<int key="NSTag">7</int>
											</object>
											<object class="NSMenuItem" id="535949330">
												<reference key="NSMenu" ref="836553275"/>
												<string key="NSTitle">Jump to Selection</string>
												<string key="NSKeyEquiv">j</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="1048292030"/>
												<reference key="NSMixedImage" ref="369028431"/>
											</object>
										</array>
									</object>
								</object>
								<object class="NSMenuItem" id="1002395558">
									<reference key="NSMenu" ref="738602787"/>
									<string key="NSTitle">Spelling</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
									<string key="NSAction">submenuAction:</string>
									<object class="NSMenu" key="NSSubmenu" id="1033255381">
										<string key="NSTitle">Spelling</string>
										<array class="NSMutableArray" key="NSMenuItems">
											<object class="NSMenuItem" id="404650802">
												<reference key="NSMenu" ref="1033255381"/>
												<string key="NSTitle">Spelling…</string>
												<string key="NSKeyEquiv">:</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="1048292030"/>
												<reference key="NSMixedImage" ref="369028431"/>
											</object>
											<object class="NSMenuItem" id="137216164">
												<reference key="NSMenu" ref="1033255381"/>
												<string key="NSTitle">Check Spelling</string>
												<string key="NSKeyEquiv">;</string>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="1048292030"/>
												<reference key="NSMixedImage" ref="369028431"/>
											</object>
											<object class="NSMenuItem" id="468467318">
												<reference key="NSMenu" ref="1033255381"/>
												<string key="NSTitle">Check Spelling as You Type</string>
												<string key="NSKeyEquiv"/>
												<int key="NSKeyEquivModMask">1048576</int>
												<int key="NSMnemonicLoc">2147483647</int>
												<reference key="NSOnImage" ref="1048292030"/>
												<reference key="NSMixedImage" ref="369028431"/>
											</object>
										</array>
									</object>
								</object>
							</array>
						</object>
					</object>
					<object class="NSMenuItem" id="803651563">
						<reference key="NSMenu" ref="297869185"/>
						<string key="NSTitle">Window</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="1048292030"/>
						<reference key="NSMixedImage" ref="369028431"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="158272057">
							<object class="NSMutableString" key="NSTitle">
								<characters key="NS.bytes">Window</characters>
							</object>
							<array class="NSMutableArray" key="NSMenuItems">
								<object class="NSMenuItem" id="838171665">
									<reference key="NSMenu" ref="158272057"/>
									<string key="NSTitle">Minimize</string>
									<string key="NSKeyEquiv">m</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="978049551">
									<reference key="NSMenu" ref="158272057"/>
									<string key="NSTitle">Zoom</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="166870008">
									<reference key="NSMenu" ref="158272057"/>
									<bool key="NSIsDisabled">YES</bool>
									<bool key="NSIsSeparator">YES</bool>
									<string key="NSTitle"/>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
								<object class="NSMenuItem" id="47725233">
									<reference key="NSMenu" ref="158272057"/>
									<string key="NSTitle">Bring All to Front</string>
									<string key="NSKeyEquiv"/>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
							</array>
							<string key="NSName">_NSWindowsMenu</string>
						</object>
					</object>
					<object class="NSMenuItem" id="151219046">
						<reference key="NSMenu" ref="297869185"/>
						<string key="NSTitle">Help</string>
						<string key="NSKeyEquiv"/>
						<int key="NSKeyEquivModMask">1048576</int>
						<int key="NSMnemonicLoc">2147483647</int>
						<reference key="NSOnImage" ref="1048292030"/>
						<reference key="NSMixedImage" ref="369028431"/>
						<string key="NSAction">submenuAction:</string>
						<object class="NSMenu" key="NSSubmenu" id="366928111">
							<string key="NSTitle">Help</string>
							<array class="NSMutableArray" key="NSMenuItems">
								<object class="NSMenuItem" id="421309308">
									<reference key="NSMenu" ref="366928111"/>
									<string key="NSTitle">NewApplication Help</string>
									<string key="NSKeyEquiv">?</string>
									<int key="NSKeyEquivModMask">1048576</int>
									<int key="NSMnemonicLoc">2147483647</int>
									<reference key="NSOnImage" ref="1048292030"/>
									<reference key="NSMixedImage" ref="369028431"/>
								</object>
							</array>
						</object>
					</object>
				</array>
				<string key="NSName">_NSMainMenu</string>
			</object>
			<object class="NSCustomObject" id="523028822">
				<string key="NSClassName">Controller</string>
			</object>
		</array>
		<object class="IBObjectContainer" key="IBDocument.Objects">
			<array class="NSMutableArray" key="connectionRecords">
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">terminate:</string>
						<reference key="source" ref="1040500158"/>
						<reference key="destination" ref="149933402"/>
					</object>
					<int key="connectionID">139</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">orderFrontStandardAboutPanel:</string>
						<reference key="source" ref="1040500158"/>
						<reference key="destination" ref="292858397"/>
					</object>
					<int key="connectionID">142</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">hideOtherApplications:</string>
						<reference key="source" ref="1040500158"/>
						<reference key="destination" ref="515890964"/>
					</object>
					<int key="connectionID">146</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">hide:</string>
						<reference key="source" ref="1040500158"/>
						<reference key="destination" ref="839114105"/>
					</object>
					<int key="connectionID">152</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">unhideAllApplications:</string>
						<reference key="source" ref="1040500158"/>
						<reference key="destination" ref="429027640"/>
					</object>
					<int key="connectionID">153</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performMiniaturize:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="838171665"/>
					</object>
					<int key="connectionID">37</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">arrangeInFront:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="47725233"/>
					</object>
					<int key="connectionID">39</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">print:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="958066245"/>
					</object>
					<int key="connectionID">86</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">runPageLayout:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="1013792391"/>
					</object>
					<int key="connectionID">87</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">showHelp:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="421309308"/>
					</object>
					<int key="connectionID">122</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">clearRecentDocuments:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="450222093"/>
					</object>
					<int key="connectionID">127</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">cut:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="11142583"/>
					</object>
					<int key="connectionID">175</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">paste:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="386458390"/>
					</object>
					<int key="connectionID">176</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">redo:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="382488015"/>
					</object>
					<int key="connectionID">178</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">selectAll:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="246032602"/>
					</object>
					<int key="connectionID">179</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">undo:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="162667957"/>
					</object>
					<int key="connectionID">180</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">copy:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="99439887"/>
					</object>
					<int key="connectionID">181</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">showGuessPanel:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="404650802"/>
					</object>
					<int key="connectionID">188</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">checkSpelling:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="137216164"/>
					</object>
					<int key="connectionID">190</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">toggleContinuousSpellChecking:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="468467318"/>
					</object>
					<int key="connectionID">192</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performClose:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="702518987"/>
					</object>
					<int key="connectionID">193</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">delete:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="712819308"/>
					</object>
					<int key="connectionID">195</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performZoom:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="978049551"/>
					</object>
					<int key="connectionID">198</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performFindPanelAction:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="596760929"/>
					</object>
					<int key="connectionID">199</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performFindPanelAction:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="377678273"/>
					</object>
					<int key="connectionID">200</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performFindPanelAction:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="740861002"/>
					</object>
					<int key="connectionID">201</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">performFindPanelAction:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="556272487"/>
					</object>
					<int key="connectionID">202</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">centerSelectionInVisibleArea:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="535949330"/>
					</object>
					<int key="connectionID">203</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">pasteAsPlainText:</string>
						<reference key="source" ref="267684775"/>
						<reference key="destination" ref="34045290"/>
					</object>
					<int key="connectionID">205</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">window</string>
						<reference key="source" ref="523028822"/>
						<reference key="destination" ref="685881732"/>
					</object>
					<int key="connectionID">207</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">videoHolderView</string>
						<reference key="source" ref="523028822"/>
						<reference key="destination" ref="326902688"/>
					</object>
					<int key="connectionID">224</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">play:</string>
						<reference key="source" ref="523028822"/>
						<reference key="destination" ref="136170242"/>
					</object>
					<int key="connectionID">254</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">pause:</string>
						<reference key="source" ref="523028822"/>
						<reference key="destination" ref="549134629"/>
					</object>
					<int key="connectionID">255</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">playlistOutline</string>
						<reference key="source" ref="523028822"/>
						<reference key="destination" ref="690384467"/>
					</object>
					<int key="connectionID">264</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBActionConnection" key="connection">
						<string key="label">setSPU:</string>
						<reference key="source" ref="523028822"/>
						<reference key="destination" ref="234891018"/>
					</object>
					<int key="connectionID">278</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">spuPopup</string>
						<reference key="source" ref="523028822"/>
						<reference key="destination" ref="234891018"/>
					</object>
					<int key="connectionID">279</int>
				</object>
				<object class="IBConnectionRecord">
					<object class="IBOutletConnection" key="connection">
						<string key="label">dataSource</string>
						<reference key="source" ref="690384467"/>
						<reference key="destination" ref="523028822"/>
					</object>
					<int key="connectionID">261</int>
				</object>
			</array>
			<object class="IBMutableOrderedSet" key="objectRecords">
				<array key="orderedObjects">
					<object class="IBObjectRecord">
						<int key="objectID">0</int>
						<array key="object" id="0"/>
						<reference key="children" ref="1038916788"/>
						<nil key="parent"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-2</int>
						<reference key="object" ref="1040500158"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">File's Owner</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-1</int>
						<reference key="object" ref="267684775"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">First Responder</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">21</int>
						<reference key="object" ref="685881732"/>
						<array class="NSMutableArray" key="children">
							<reference ref="1065405168"/>
						</array>
						<reference key="parent" ref="0"/>
						<string key="objectName">Window</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">2</int>
						<reference key="object" ref="1065405168"/>
						<array class="NSMutableArray" key="children">
							<reference ref="326902688"/>
							<reference ref="549134629"/>
							<reference ref="136170242"/>
							<reference ref="825537634"/>
							<reference ref="234891018"/>
						</array>
						<reference key="parent" ref="685881732"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">217</int>
						<reference key="object" ref="326902688"/>
						<reference key="parent" ref="1065405168"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">251</int>
						<reference key="object" ref="549134629"/>
						<array class="NSMutableArray" key="children">
							<reference ref="451252014"/>
						</array>
						<reference key="parent" ref="1065405168"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">252</int>
						<reference key="object" ref="136170242"/>
						<array class="NSMutableArray" key="children">
							<reference ref="755927330"/>
						</array>
						<reference key="parent" ref="1065405168"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">256</int>
						<reference key="object" ref="825537634"/>
						<array class="NSMutableArray" key="children">
							<reference ref="690384467"/>
							<reference ref="1006009745"/>
							<reference ref="71094015"/>
							<reference ref="1038890907"/>
						</array>
						<reference key="parent" ref="1065405168"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">257</int>
						<reference key="object" ref="690384467"/>
						<array class="NSMutableArray" key="children">
							<reference ref="232857741"/>
						</array>
						<reference key="parent" ref="825537634"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">258</int>
						<reference key="object" ref="232857741"/>
						<array class="NSMutableArray" key="children">
							<reference ref="215679856"/>
						</array>
						<reference key="parent" ref="690384467"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">29</int>
						<reference key="object" ref="297869185"/>
						<array class="NSMutableArray" key="children">
							<reference ref="803651563"/>
							<reference ref="117182161"/>
							<reference ref="155728425"/>
							<reference ref="151219046"/>
							<reference ref="567403254"/>
						</array>
						<reference key="parent" ref="0"/>
						<string key="objectName">MainMenu</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">19</int>
						<reference key="object" ref="803651563"/>
						<array class="NSMutableArray" key="children">
							<reference ref="158272057"/>
						</array>
						<reference key="parent" ref="297869185"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">24</int>
						<reference key="object" ref="158272057"/>
						<array class="NSMutableArray" key="children">
							<reference ref="47725233"/>
							<reference ref="838171665"/>
							<reference ref="166870008"/>
							<reference ref="978049551"/>
						</array>
						<reference key="parent" ref="803651563"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">5</int>
						<reference key="object" ref="47725233"/>
						<reference key="parent" ref="158272057"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">23</int>
						<reference key="object" ref="838171665"/>
						<reference key="parent" ref="158272057"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">92</int>
						<reference key="object" ref="166870008"/>
						<reference key="parent" ref="158272057"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">197</int>
						<reference key="object" ref="978049551"/>
						<reference key="parent" ref="158272057"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">56</int>
						<reference key="object" ref="117182161"/>
						<array class="NSMutableArray" key="children">
							<reference ref="575604144"/>
						</array>
						<reference key="parent" ref="297869185"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">57</int>
						<reference key="object" ref="575604144"/>
						<array class="NSMutableArray" key="children">
							<reference ref="292858397"/>
							<reference ref="537674438"/>
							<reference ref="949890269"/>
							<reference ref="839114105"/>
							<reference ref="149933402"/>
							<reference ref="412901610"/>
							<reference ref="700341395"/>
							<reference ref="515890964"/>
							<reference ref="1049329156"/>
							<reference ref="429027640"/>
							<reference ref="817551412"/>
						</array>
						<reference key="parent" ref="117182161"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">58</int>
						<reference key="object" ref="292858397"/>
						<reference key="parent" ref="575604144"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">129</int>
						<reference key="object" ref="537674438"/>
						<reference key="parent" ref="575604144"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">131</int>
						<reference key="object" ref="949890269"/>
						<array class="NSMutableArray" key="children">
							<reference ref="837556560"/>
						</array>
						<reference key="parent" ref="575604144"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">130</int>
						<reference key="object" ref="837556560"/>
						<reference key="parent" ref="949890269"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">134</int>
						<reference key="object" ref="839114105"/>
						<reference key="parent" ref="575604144"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">136</int>
						<reference key="object" ref="149933402"/>
						<reference key="parent" ref="575604144"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">143</int>
						<reference key="object" ref="412901610"/>
						<reference key="parent" ref="575604144"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">144</int>
						<reference key="object" ref="700341395"/>
						<reference key="parent" ref="575604144"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">145</int>
						<reference key="object" ref="515890964"/>
						<reference key="parent" ref="575604144"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">149</int>
						<reference key="object" ref="1049329156"/>
						<reference key="parent" ref="575604144"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">150</int>
						<reference key="object" ref="429027640"/>
						<reference key="parent" ref="575604144"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">196</int>
						<reference key="object" ref="817551412"/>
						<reference key="parent" ref="575604144"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">83</int>
						<reference key="object" ref="155728425"/>
						<array class="NSMutableArray" key="children">
							<reference ref="509583233"/>
						</array>
						<reference key="parent" ref="297869185"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">81</int>
						<reference key="object" ref="509583233"/>
						<array class="NSMutableArray" key="children">
							<reference ref="444052652"/>
							<reference ref="702518987"/>
							<reference ref="446326576"/>
							<reference ref="476632045"/>
							<reference ref="1013792391"/>
							<reference ref="958066245"/>
							<reference ref="348360378"/>
							<reference ref="1043084507"/>
							<reference ref="854309318"/>
							<reference ref="15477557"/>
							<reference ref="525613171"/>
						</array>
						<reference key="parent" ref="155728425"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">72</int>
						<reference key="object" ref="444052652"/>
						<reference key="parent" ref="509583233"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">73</int>
						<reference key="object" ref="702518987"/>
						<reference key="parent" ref="509583233"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">74</int>
						<reference key="object" ref="446326576"/>
						<reference key="parent" ref="509583233"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">75</int>
						<reference key="object" ref="476632045"/>
						<reference key="parent" ref="509583233"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">77</int>
						<reference key="object" ref="1013792391"/>
						<reference key="parent" ref="509583233"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">78</int>
						<reference key="object" ref="958066245"/>
						<reference key="parent" ref="509583233"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">79</int>
						<reference key="object" ref="348360378"/>
						<reference key="parent" ref="509583233"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">80</int>
						<reference key="object" ref="1043084507"/>
						<reference key="parent" ref="509583233"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">82</int>
						<reference key="object" ref="854309318"/>
						<reference key="parent" ref="509583233"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">112</int>
						<reference key="object" ref="15477557"/>
						<reference key="parent" ref="509583233"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">124</int>
						<reference key="object" ref="525613171"/>
						<array class="NSMutableArray" key="children">
							<reference ref="967985765"/>
						</array>
						<reference key="parent" ref="509583233"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">125</int>
						<reference key="object" ref="967985765"/>
						<array class="NSMutableArray" key="children">
							<reference ref="450222093"/>
						</array>
						<reference key="parent" ref="525613171"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">126</int>
						<reference key="object" ref="450222093"/>
						<reference key="parent" ref="967985765"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">103</int>
						<reference key="object" ref="151219046"/>
						<array class="NSMutableArray" key="children">
							<reference ref="366928111"/>
						</array>
						<reference key="parent" ref="297869185"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">106</int>
						<reference key="object" ref="366928111"/>
						<array class="NSMutableArray" key="children">
							<reference ref="421309308"/>
						</array>
						<reference key="parent" ref="151219046"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">111</int>
						<reference key="object" ref="421309308"/>
						<reference key="parent" ref="366928111"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">163</int>
						<reference key="object" ref="567403254"/>
						<array class="NSMutableArray" key="children">
							<reference ref="738602787"/>
						</array>
						<reference key="parent" ref="297869185"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">169</int>
						<reference key="object" ref="738602787"/>
						<array class="NSMutableArray" key="children">
							<reference ref="316514825"/>
							<reference ref="99439887"/>
							<reference ref="162667957"/>
							<reference ref="11142583"/>
							<reference ref="712819308"/>
							<reference ref="49519850"/>
							<reference ref="386458390"/>
							<reference ref="246032602"/>
							<reference ref="382488015"/>
							<reference ref="1048640993"/>
							<reference ref="1002395558"/>
							<reference ref="34045290"/>
						</array>
						<reference key="parent" ref="567403254"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">156</int>
						<reference key="object" ref="316514825"/>
						<reference key="parent" ref="738602787"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">157</int>
						<reference key="object" ref="99439887"/>
						<reference key="parent" ref="738602787"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">158</int>
						<reference key="object" ref="162667957"/>
						<reference key="parent" ref="738602787"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">160</int>
						<reference key="object" ref="11142583"/>
						<reference key="parent" ref="738602787"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">164</int>
						<reference key="object" ref="712819308"/>
						<reference key="parent" ref="738602787"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">168</int>
						<reference key="object" ref="49519850"/>
						<array class="NSMutableArray" key="children">
							<reference ref="836553275"/>
						</array>
						<reference key="parent" ref="738602787"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">159</int>
						<reference key="object" ref="836553275"/>
						<array class="NSMutableArray" key="children">
							<reference ref="596760929"/>
							<reference ref="535949330"/>
							<reference ref="556272487"/>
							<reference ref="740861002"/>
							<reference ref="377678273"/>
						</array>
						<reference key="parent" ref="49519850"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">154</int>
						<reference key="object" ref="596760929"/>
						<reference key="parent" ref="836553275"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">155</int>
						<reference key="object" ref="535949330"/>
						<reference key="parent" ref="836553275"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">161</int>
						<reference key="object" ref="556272487"/>
						<reference key="parent" ref="836553275"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">162</int>
						<reference key="object" ref="740861002"/>
						<reference key="parent" ref="836553275"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">167</int>
						<reference key="object" ref="377678273"/>
						<reference key="parent" ref="836553275"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">171</int>
						<reference key="object" ref="386458390"/>
						<reference key="parent" ref="738602787"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">172</int>
						<reference key="object" ref="246032602"/>
						<reference key="parent" ref="738602787"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">173</int>
						<reference key="object" ref="382488015"/>
						<reference key="parent" ref="738602787"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">174</int>
						<reference key="object" ref="1048640993"/>
						<reference key="parent" ref="738602787"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">184</int>
						<reference key="object" ref="1002395558"/>
						<array class="NSMutableArray" key="children">
							<reference ref="1033255381"/>
						</array>
						<reference key="parent" ref="738602787"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">185</int>
						<reference key="object" ref="1033255381"/>
						<array class="NSMutableArray" key="children">
							<reference ref="404650802"/>
							<reference ref="137216164"/>
							<reference ref="468467318"/>
						</array>
						<reference key="parent" ref="1002395558"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">187</int>
						<reference key="object" ref="404650802"/>
						<reference key="parent" ref="1033255381"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">189</int>
						<reference key="object" ref="137216164"/>
						<reference key="parent" ref="1033255381"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">191</int>
						<reference key="object" ref="468467318"/>
						<reference key="parent" ref="1033255381"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">204</int>
						<reference key="object" ref="34045290"/>
						<reference key="parent" ref="738602787"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">206</int>
						<reference key="object" ref="523028822"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">Controller</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">266</int>
						<reference key="object" ref="451252014"/>
						<reference key="parent" ref="549134629"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">267</int>
						<reference key="object" ref="755927330"/>
						<reference key="parent" ref="136170242"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">268</int>
						<reference key="object" ref="215679856"/>
						<reference key="parent" ref="232857741"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">269</int>
						<reference key="object" ref="1006009745"/>
						<reference key="parent" ref="825537634"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">270</int>
						<reference key="object" ref="71094015"/>
						<reference key="parent" ref="825537634"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">271</int>
						<reference key="object" ref="1038890907"/>
						<reference key="parent" ref="825537634"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">-3</int>
						<reference key="object" ref="376421538"/>
						<reference key="parent" ref="0"/>
						<string key="objectName">Application</string>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">272</int>
						<reference key="object" ref="234891018"/>
						<array class="NSMutableArray" key="children">
							<reference ref="790695695"/>
						</array>
						<reference key="parent" ref="1065405168"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">273</int>
						<reference key="object" ref="790695695"/>
						<array class="NSMutableArray" key="children">
							<reference ref="849953390"/>
						</array>
						<reference key="parent" ref="234891018"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">274</int>
						<reference key="object" ref="849953390"/>
						<array class="NSMutableArray" key="children">
							<reference ref="158621198"/>
							<reference ref="1013813959"/>
							<reference ref="379903749"/>
						</array>
						<reference key="parent" ref="790695695"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">275</int>
						<reference key="object" ref="158621198"/>
						<reference key="parent" ref="849953390"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">276</int>
						<reference key="object" ref="1013813959"/>
						<reference key="parent" ref="849953390"/>
					</object>
					<object class="IBObjectRecord">
						<int key="objectID">277</int>
						<reference key="object" ref="379903749"/>
						<reference key="parent" ref="849953390"/>
					</object>
				</array>
			</object>
			<dictionary class="NSMutableDictionary" key="flattenedProperties">
				<string key="-1.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="-2.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="-3.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="103.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="106.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="111.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="112.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="124.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="125.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="126.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="129.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="130.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="131.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="134.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="136.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="143.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="144.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="145.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="149.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="150.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="154.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="155.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="156.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="157.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="158.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="159.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="160.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="161.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="162.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="163.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="164.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="167.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="168.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="169.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="171.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="172.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="173.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="174.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="184.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="185.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="187.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="189.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="19.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="191.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="196.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="197.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="2.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="204.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="206.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="21.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<boolean value="YES" key="21.NSWindowTemplate.visibleAtLaunch"/>
				<string key="217.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="23.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="24.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="251.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="252.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="256.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="257.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="258.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="266.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="267.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="268.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="269.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="270.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="271.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="272.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="273.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="274.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="275.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="276.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="277.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="29.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="5.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="56.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="57.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="58.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="72.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="73.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="74.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="75.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="77.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="78.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="79.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="80.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="81.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="82.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="83.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
				<string key="92.IBPluginDependency">com.apple.InterfaceBuilder.CocoaPlugin</string>
			</dictionary>
			<dictionary class="NSMutableDictionary" key="unlocalizedProperties"/>
			<nil key="activeLocalization"/>
			<dictionary class="NSMutableDictionary" key="localizations"/>
			<nil key="sourceID"/>
			<int key="maxID">279</int>
		</object>
		<object class="IBClassDescriber" key="IBDocument.Classes">
			<array class="NSMutableArray" key="referencedPartialClassDescriptions">
				<object class="IBPartialClassDescription">
					<string key="className">Controller</string>
					<string key="superclassName">NSObject</string>
					<object class="NSMutableDictionary" key="actions">
						<string key="NS.key.0">setSPU:</string>
						<string key="NS.object.0">id</string>
					</object>
					<object class="NSMutableDictionary" key="actionInfosByName">
						<string key="NS.key.0">setSPU:</string>
						<object class="IBActionInfo" key="NS.object.0">
							<string key="name">setSPU:</string>
							<string key="candidateClassName">id</string>
						</object>
					</object>
					<dictionary class="NSMutableDictionary" key="outlets">
						<string key="playlistOutline">id</string>
						<string key="spuPopup">id</string>
						<string key="videoHolderView">id</string>
						<string key="window">id</string>
					</dictionary>
					<dictionary class="NSMutableDictionary" key="toOneOutletInfosByName">
						<object class="IBToOneOutletInfo" key="playlistOutline">
							<string key="name">playlistOutline</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBToOneOutletInfo" key="spuPopup">
							<string key="name">spuPopup</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBToOneOutletInfo" key="videoHolderView">
							<string key="name">videoHolderView</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBToOneOutletInfo" key="window">
							<string key="name">window</string>
							<string key="candidateClassName">id</string>
						</object>
					</dictionary>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBProjectSource</string>
						<string key="minorKey">./Classes/Controller.h</string>
					</object>
				</object>
				<object class="IBPartialClassDescription">
					<string key="className">NSDocument</string>
					<dictionary class="NSMutableDictionary" key="actions">
						<string key="browseDocumentVersions:">id</string>
						<string key="duplicateDocument:">id</string>
						<string key="lockDocument:">id</string>
						<string key="moveDocument:">id</string>
						<string key="moveDocumentToUbiquityContainer:">id</string>
						<string key="printDocument:">id</string>
						<string key="renameDocument:">id</string>
						<string key="revertDocumentToSaved:">id</string>
						<string key="runPageLayout:">id</string>
						<string key="saveDocument:">id</string>
						<string key="saveDocumentAs:">id</string>
						<string key="saveDocumentTo:">id</string>
						<string key="saveDocumentToPDF:">id</string>
						<string key="unlockDocument:">id</string>
					</dictionary>
					<dictionary class="NSMutableDictionary" key="actionInfosByName">
						<object class="IBActionInfo" key="browseDocumentVersions:">
							<string key="name">browseDocumentVersions:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="duplicateDocument:">
							<string key="name">duplicateDocument:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="lockDocument:">
							<string key="name">lockDocument:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="moveDocument:">
							<string key="name">moveDocument:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="moveDocumentToUbiquityContainer:">
							<string key="name">moveDocumentToUbiquityContainer:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="printDocument:">
							<string key="name">printDocument:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="renameDocument:">
							<string key="name">renameDocument:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="revertDocumentToSaved:">
							<string key="name">revertDocumentToSaved:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="runPageLayout:">
							<string key="name">runPageLayout:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="saveDocument:">
							<string key="name">saveDocument:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="saveDocumentAs:">
							<string key="name">saveDocumentAs:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="saveDocumentTo:">
							<string key="name">saveDocumentTo:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="saveDocumentToPDF:">
							<string key="name">saveDocumentToPDF:</string>
							<string key="candidateClassName">id</string>
						</object>
						<object class="IBActionInfo" key="unlockDocument:">
							<string key="name">unlockDocument:</string>
							<string key="candidateClassName">id</string>
						</object>
					</dictionary>
					<object class="IBClassDescriptionSource" key="sourceIdentifier">
						<string key="majorKey">IBProjectSource</string>
						<string key="minorKey">./Classes/NSDocument.h</string>
					</object>
				</object>
			</array>
		</object>
		<int key="IBDocument.localizationMode">0</int>
		<string key="IBDocument.TargetRuntimeIdentifier">IBCocoaFramework</string>
		<bool key="IBDocument.previouslyAttemptedUpgradeToXcode5">YES</bool>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDependencies">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaPlugin.macosx</string>
			<integer value="1090" key="NS.object.0"/>
		</object>
		<object class="NSMutableDictionary" key="IBDocument.PluginDeclaredDevelopmentDependencies">
			<string key="NS.key.0">com.apple.InterfaceBuilder.CocoaPlugin.InterfaceBuilder3</string>
			<integer value="4600" key="NS.object.0"/>
		</object>
		<bool key="IBDocument.PluginDeclaredDependenciesTrackSystemTargetVersion">YES</bool>
		<int key="IBDocument.defaultPropertyAccessControl">3</int>
		<dictionary class="NSMutableDictionary" key="IBDocument.LastKnownImageSizes">
			<string key="NSMenuCheckmark">{11, 11}</string>
			<string key="NSMenuMixedState">{10, 3}</string>
		</dictionary>
	</data>
</archive>
