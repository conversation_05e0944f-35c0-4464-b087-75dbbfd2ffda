// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 42;
	objects = {

/* Begin PBXBuildFile section */
		6333027B0BD24CDB00193D7D /* Controller.m in Sources */ = {isa = PBXBuildFile; fileRef = 633302790BD24CDB00193D7D /* Controller.m */; };
		7DAE7546183A7DCC00ED6F89 /* VLCKit.framework in CopyFiles */ = {isa = PBXBuildFile; fileRef = CC786BB31715AAB50024EDC0 /* VLCKit.framework */; };
		7DD54F38183A749F004149F5 /* GradientBackgroundView.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DD54F36183A749F004149F5 /* GradientBackgroundView.m */; };
		7DD54F39183A749F004149F5 /* MovieReceiver.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DD54F37183A749F004149F5 /* MovieReceiver.m */; };
		7DD54F3D183A74E9004149F5 /* MainMenu.nib in Resources */ = {isa = PBXBuildFile; fileRef = 7DD54F3B183A74E9004149F5 /* MainMenu.nib */; };
		8D11072B0486CEB800E47090 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 089C165CFE840E0CC02AAC07 /* InfoPlist.strings */; };
		8D11072D0486CEB800E47090 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 29B97316FDCFA39411CA2CEA /* main.m */; settings = {ATTRIBUTES = (); }; };
		8D11072F0486CEB800E47090 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1058C7A1FEA54F0111CA2CBB /* Cocoa.framework */; };
		CC786BB41715AAB50024EDC0 /* VLCKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = CC786BB31715AAB50024EDC0 /* VLCKit.framework */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		7DAE7545183A7DC200ED6F89 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				7DAE7546183A7DCC00ED6F89 /* VLCKit.framework in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		089C165DFE840E0CC02AAC07 /* English */ = {isa = PBXFileReference; fileEncoding = 10; lastKnownFileType = text.plist.strings; name = English; path = English.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		1058C7A1FEA54F0111CA2CBB /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = /System/Library/Frameworks/Cocoa.framework; sourceTree = "<absolute>"; };
		13E42FB307B3F0F600E4EEF1 /* CoreData.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreData.framework; path = /System/Library/Frameworks/CoreData.framework; sourceTree = "<absolute>"; };
		29B97316FDCFA39411CA2CEA /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		29B97324FDCFA39411CA2CEA /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = /System/Library/Frameworks/AppKit.framework; sourceTree = "<absolute>"; };
		29B97325FDCFA39411CA2CEA /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = /System/Library/Frameworks/Foundation.framework; sourceTree = "<absolute>"; };
		633302780BD24CDB00193D7D /* Controller.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = Controller.h; sourceTree = "<group>"; };
		633302790BD24CDB00193D7D /* Controller.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = Controller.m; sourceTree = "<group>"; };
		7DD54F34183A749F004149F5 /* GradientBackgroundView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = GradientBackgroundView.h; sourceTree = "<group>"; };
		7DD54F35183A749F004149F5 /* MovieReceiver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MovieReceiver.h; sourceTree = "<group>"; };
		7DD54F36183A749F004149F5 /* GradientBackgroundView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GradientBackgroundView.m; sourceTree = "<group>"; };
		7DD54F37183A749F004149F5 /* MovieReceiver.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MovieReceiver.m; sourceTree = "<group>"; };
		7DD54F3A183A74A9004149F5 /* iPodConverter_Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = iPodConverter_Prefix.pch; sourceTree = "<group>"; };
		7DD54F3C183A74E9004149F5 /* English */ = {isa = PBXFileReference; lastKnownFileType = wrapper.nib; name = English; path = English.lproj/MainMenu.nib; sourceTree = "<group>"; };
		8D1107310486CEB800E47090 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		8D1107320486CEB800E47090 /* iPodConverter.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = iPodConverter.app; sourceTree = BUILT_PRODUCTS_DIR; };
		CC786BB31715AAB50024EDC0 /* VLCKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VLCKit.framework; path = ../../build/Debug/VLCKit.framework; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8D11072E0486CEB800E47090 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8D11072F0486CEB800E47090 /* Cocoa.framework in Frameworks */,
				CC786BB41715AAB50024EDC0 /* VLCKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		080E96DDFE201D6D7F000001 /* Classes */ = {
			isa = PBXGroup;
			children = (
				633302780BD24CDB00193D7D /* Controller.h */,
				633302790BD24CDB00193D7D /* Controller.m */,
				7DD54F35183A749F004149F5 /* MovieReceiver.h */,
				7DD54F37183A749F004149F5 /* MovieReceiver.m */,
				7DD54F34183A749F004149F5 /* GradientBackgroundView.h */,
				7DD54F36183A749F004149F5 /* GradientBackgroundView.m */,
			);
			name = Classes;
			sourceTree = "<group>";
		};
		1058C7A0FEA54F0111CA2CBB /* Linked Frameworks */ = {
			isa = PBXGroup;
			children = (
				CC786BB31715AAB50024EDC0 /* VLCKit.framework */,
				1058C7A1FEA54F0111CA2CBB /* Cocoa.framework */,
			);
			name = "Linked Frameworks";
			sourceTree = "<group>";
		};
		1058C7A2FEA54F0111CA2CBB /* Other Frameworks */ = {
			isa = PBXGroup;
			children = (
				29B97324FDCFA39411CA2CEA /* AppKit.framework */,
				13E42FB307B3F0F600E4EEF1 /* CoreData.framework */,
				29B97325FDCFA39411CA2CEA /* Foundation.framework */,
			);
			name = "Other Frameworks";
			sourceTree = "<group>";
		};
		19C28FACFE9D520D11CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				8D1107320486CEB800E47090 /* iPodConverter.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		29B97314FDCFA39411CA2CEA /* test */ = {
			isa = PBXGroup;
			children = (
				080E96DDFE201D6D7F000001 /* Classes */,
				29B97315FDCFA39411CA2CEA /* Other Sources */,
				29B97317FDCFA39411CA2CEA /* Resources */,
				29B97323FDCFA39411CA2CEA /* Frameworks */,
				19C28FACFE9D520D11CA2CBB /* Products */,
			);
			name = test;
			sourceTree = "<group>";
		};
		29B97315FDCFA39411CA2CEA /* Other Sources */ = {
			isa = PBXGroup;
			children = (
				7DD54F3A183A74A9004149F5 /* iPodConverter_Prefix.pch */,
				29B97316FDCFA39411CA2CEA /* main.m */,
			);
			name = "Other Sources";
			sourceTree = "<group>";
		};
		29B97317FDCFA39411CA2CEA /* Resources */ = {
			isa = PBXGroup;
			children = (
				7DD54F3B183A74E9004149F5 /* MainMenu.nib */,
				8D1107310486CEB800E47090 /* Info.plist */,
				089C165CFE840E0CC02AAC07 /* InfoPlist.strings */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		29B97323FDCFA39411CA2CEA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1058C7A0FEA54F0111CA2CBB /* Linked Frameworks */,
				1058C7A2FEA54F0111CA2CBB /* Other Frameworks */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8D1107260486CEB800E47090 /* iPodConverter */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C01FCF4A08A954540054247B /* Build configuration list for PBXNativeTarget "iPodConverter" */;
			buildPhases = (
				8D1107290486CEB800E47090 /* Resources */,
				8D11072C0486CEB800E47090 /* Sources */,
				8D11072E0486CEB800E47090 /* Frameworks */,
				7DAE7545183A7DC200ED6F89 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = iPodConverter;
			productInstallPath = "$(HOME)/Applications";
			productName = test;
			productReference = 8D1107320486CEB800E47090 /* iPodConverter.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		29B97313FDCFA39411CA2CEA /* Project object */ = {
			isa = PBXProject;
			buildConfigurationList = C01FCF4E08A954540054247B /* Build configuration list for PBXProject "iPodConverter" */;
			compatibilityVersion = "Xcode 2.4";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				en,
				English,
			);
			mainGroup = 29B97314FDCFA39411CA2CEA /* test */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8D1107260486CEB800E47090 /* iPodConverter */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8D1107290486CEB800E47090 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8D11072B0486CEB800E47090 /* InfoPlist.strings in Resources */,
				7DD54F3D183A74E9004149F5 /* MainMenu.nib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8D11072C0486CEB800E47090 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8D11072D0486CEB800E47090 /* main.m in Sources */,
				6333027B0BD24CDB00193D7D /* Controller.m in Sources */,
				7DD54F38183A749F004149F5 /* GradientBackgroundView.m in Sources */,
				7DD54F39183A749F004149F5 /* MovieReceiver.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		089C165CFE840E0CC02AAC07 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				089C165DFE840E0CC02AAC07 /* English */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		7DD54F3B183A74E9004149F5 /* MainMenu.nib */ = {
			isa = PBXVariantGroup;
			children = (
				7DD54F3C183A74E9004149F5 /* English */,
			);
			name = MainMenu.nib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		C01FCF4B08A954540054247B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(NATIVE_ARCH_ACTUAL)";
				COPY_PHASE_STRIP = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(FRAMEWORK_SEARCH_PATHS_QUOTED_FOR_TARGET_1)",
				);
				FRAMEWORK_SEARCH_PATHS_QUOTED_FOR_TARGET_1 = "\"$(SRCROOT)/../../build/Debug\"";
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_FIX_AND_CONTINUE = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				INFOPLIST_FILE = Info.plist;
				INSTALL_PATH = "$(HOME)/Applications";
				LD_FLAGS_LIBINTL = "-dylib_file @loader_path/../lib/vlc_libintl.dylib:$(VLC_FRAMEWORK)/lib/vlc_libintl.dylib";
				LD_FLAGS_LIBVLC = "-dylib_file @loader_path/lib/libvlc.dylib:$(VLC_FRAMEWORK)/lib/libvlc.dylib -dylib_file @loader_path/../lib/libvlc.dylib:$(VLC_FRAMEWORK)/lib/libvlc.dylib";
				LD_FLAGS_LIBVLC_CONTROL = "-dylib_file @loader_path/lib/libvlc-control.dylib:$(VLC_FRAMEWORK)/lib/libvlc-control.dylib";
				OTHER_LDFLAGS = (
					"$(LD_FLAGS_LIBINTL)",
					"$(LD_FLAGS_LIBVLC)",
					"$(LD_FLAGS_LIBVLC_CONTROL)",
				);
				PRODUCT_NAME = iPodConverter;
				SYMROOT = ../../build;
				VLC_FRAMEWORK = "$(TARGET_BUILD_DIR)/VLC.framework";
				WRAPPER_EXTENSION = app;
				ZERO_LINK = YES;
			};
			name = Debug;
		};
		C01FCF4C08A954540054247B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(NATIVE_ARCH_ACTUAL)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(FRAMEWORK_SEARCH_PATHS_QUOTED_FOR_TARGET_1)",
				);
				FRAMEWORK_SEARCH_PATHS_QUOTED_FOR_TARGET_1 = "\"$(SRCROOT)/../../build/Debug\"";
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				INFOPLIST_FILE = Info.plist;
				INSTALL_PATH = "$(HOME)/Applications";
				LD_FLAGS_LIBINTL = "-dylib_file @loader_path/../lib/vlc_libintl.dylib:$(VLC_FRAMEWORK)/lib/vlc_libintl.dylib";
				LD_FLAGS_LIBVLC = "-dylib_file @loader_path/lib/libvlc.dylib:$(VLC_FRAMEWORK)/lib/libvlc.dylib -dylib_file @loader_path/../lib/libvlc.dylib:$(VLC_FRAMEWORK)/lib/libvlc.dylib";
				LD_FLAGS_LIBVLC_CONTROL = "-dylib_file @loader_path/lib/libvlc-control.dylib:$(VLC_FRAMEWORK)/lib/libvlc-control.dylib";
				OTHER_LDFLAGS = (
					"$(LD_FLAGS_LIBINTL)",
					"$(LD_FLAGS_LIBVLC)",
					"$(LD_FLAGS_LIBVLC_CONTROL)",
				);
				PRODUCT_NAME = iPodConverter;
				SYMROOT = ../../build;
				VLC_FRAMEWORK = "$(TARGET_BUILD_DIR)/VLC.framework";
				WRAPPER_EXTENSION = app;
			};
			name = Release;
		};
		C01FCF4F08A954540054247B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				PREBINDING = NO;
			};
			name = Debug;
		};
		C01FCF5008A954540054247B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(NATIVE_ARCH)";
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				PREBINDING = NO;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C01FCF4A08A954540054247B /* Build configuration list for PBXNativeTarget "iPodConverter" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C01FCF4B08A954540054247B /* Debug */,
				C01FCF4C08A954540054247B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		C01FCF4E08A954540054247B /* Build configuration list for PBXProject "iPodConverter" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C01FCF4F08A954540054247B /* Debug */,
				C01FCF5008A954540054247B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 29B97313FDCFA39411CA2CEA /* Project object */;
}
