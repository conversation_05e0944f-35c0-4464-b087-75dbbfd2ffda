From 8bdcbdc423f838d209e189fe51c0e879d1415d05 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Mon, 15 Feb 2021 13:57:17 +0100
Subject: [PATCH 33/49] contrib/gpg-error: remove obj lock hackfor appleOS

---
 contrib/src/gpg-error/rules.mak | 6 ------
 1 file changed, 6 deletions(-)

diff --git a/contrib/src/gpg-error/rules.mak b/contrib/src/gpg-error/rules.mak
index 6a3e874fb7..21de17c422 100644
--- a/contrib/src/gpg-error/rules.mak
+++ b/contrib/src/gpg-error/rules.mak
@@ -41,12 +41,6 @@ endif
 ifdef HAVE_DARWIN_OS
 ifdef HAVE_ARMV7A
 	cp $@/src/syscfg/lock-obj-pub.arm-apple-darwin.h $@/src/syscfg/lock-obj-pub.$(HOST).h
-else
-ifeq ($(ARCH),aarch64)
-	cp $@/src/syscfg/lock-obj-pub.aarch64-apple-darwin.h $@/src/syscfg/lock-obj-pub.$(HOST).h
-else
-	cp $@/src/syscfg/lock-obj-pub.x86_64-apple-darwin.h $@/src/syscfg/lock-obj-pub.$(HOST).h
-endif
 endif
 endif
 
-- 
2.39.3 (Apple Git-146)

