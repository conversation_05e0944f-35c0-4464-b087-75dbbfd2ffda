From 4acf458bffd10f5a3e87233c7fe0282f61179a2a Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Tue, 9 Mar 2021 15:24:27 +0100
Subject: [PATCH 41/49] contrib/smb2: prevent MD5 symbol conflict

---
 .../0001-smb2-avoid-MD5-symbol-conflict.patch | 136 ++++++++++++++++++
 contrib/src/smb2/rules.mak                    |   1 +
 2 files changed, 137 insertions(+)
 create mode 100644 contrib/src/smb2/0001-smb2-avoid-MD5-symbol-conflict.patch

diff --git a/contrib/src/smb2/0001-smb2-avoid-MD5-symbol-conflict.patch b/contrib/src/smb2/0001-smb2-avoid-MD5-symbol-conflict.patch
new file mode 100644
index 0000000000..9a480b67fd
--- /dev/null
+++ b/contrib/src/smb2/0001-smb2-avoid-MD5-symbol-conflict.patch
@@ -0,0 +1,136 @@
+diff -ru smb2-clean/lib/hmac-md5.c smb2/lib/hmac-md5.c
+--- smb2-clean/lib/hmac-md5.c	2020-11-15 00:07:07.000000000 +0100
++++ smb2/lib/hmac-md5.c	2021-03-09 12:09:08.000000000 +0100
+@@ -41,9 +41,9 @@
+         if (key_len > 64) {
+ 		struct MD5Context tctx;
+ 
+-                MD5Init(&tctx);
+-                MD5Update(&tctx, key, key_len);
+-                MD5Final(tk, &tctx);
++                smb2_MD5Init(&tctx);
++                smb2_MD5Update(&tctx, key, key_len);
++                smb2_MD5Final(tk, &tctx);
+ 
+                 key = tk;
+                 key_len = 16;
+@@ -73,18 +73,18 @@
+         /*
+          * perform inner MD5
+          */
+-        MD5Init(&context);                   /* init context for 1st
++        smb2_MD5Init(&context);                   /* init context for 1st
+                                               * pass */
+-        MD5Update(&context, k_ipad, 64);     /* start with inner pad */
+-        MD5Update(&context, text, text_len); /* then text of datagram */
+-        MD5Final(digest, &context);          /* finish up 1st pass */
++        smb2_MD5Update(&context, k_ipad, 64);     /* start with inner pad */
++        smb2_MD5Update(&context, text, text_len); /* then text of datagram */
++        smb2_MD5Final(digest, &context);          /* finish up 1st pass */
+         /*
+          * perform outer MD5
+          */
+-        MD5Init(&context);                   /* init context for 2nd
++        smb2_MD5Init(&context);                   /* init context for 2nd
+                                               * pass */
+-        MD5Update(&context, k_opad, 64);     /* start with outer pad */
+-        MD5Update(&context, digest, 16);     /* then results of 1st
++        smb2_MD5Update(&context, k_opad, 64);     /* start with outer pad */
++        smb2_MD5Update(&context, digest, 16);     /* then results of 1st
+                                               * hash */
+-        MD5Final(digest, &context);          /* finish up 2nd pass */
++        smb2_MD5Final(digest, &context);          /* finish up 2nd pass */
+ }
+diff -ru smb2-clean/lib/md5.c smb2/lib/md5.c
+--- smb2-clean/lib/md5.c	2020-11-15 00:07:07.000000000 +0100
++++ smb2/lib/md5.c	2021-03-09 12:08:14.000000000 +0100
+@@ -56,7 +56,7 @@
+  * initialization constants.
+  */
+ void
+-MD5Init(struct MD5Context *ctx)
++smb2_MD5Init(struct MD5Context *ctx)
+ {
+ 	ctx->buf[0] = 0x67452301;
+ 	ctx->buf[1] = 0xefcdab89;
+@@ -72,7 +72,7 @@
+  * of bytes.
+  */
+ void
+-MD5Update(struct MD5Context *ctx, md5byte const *buf, unsigned len)
++smb2_MD5Update(struct MD5Context *ctx, md5byte const *buf, unsigned len)
+ {
+ 	UWORD32 t;
+ 
+@@ -90,7 +90,7 @@
+ 	/* First chunk is an odd size */
+ 	memcpy((md5byte *)ctx->in + 64 - t, buf, t);
+ 	byteSwap(ctx->in, 16);
+-	MD5Transform(ctx->buf, ctx->in);
++	smb2_MD5Transform(ctx->buf, ctx->in);
+ 	buf += t;
+ 	len -= t;
+ 
+@@ -98,7 +98,7 @@
+ 	while (len >= 64) {
+ 		memcpy(ctx->in, buf, 64);
+ 		byteSwap(ctx->in, 16);
+-		MD5Transform(ctx->buf, ctx->in);
++		smb2_MD5Transform(ctx->buf, ctx->in);
+ 		buf += 64;
+ 		len -= 64;
+ 	}
+@@ -112,7 +112,7 @@
+  * 1 0* (64-bit count of bits processed, MSB-first)
+  */
+ void
+-MD5Final(md5byte digest[16], struct MD5Context *ctx)
++smb2_MD5Final(md5byte digest[16], struct MD5Context *ctx)
+ {
+ 	int count = ctx->bytes[0] & 0x3f;	/* Number of bytes in ctx->in */
+ 	md5byte *p = (md5byte *)ctx->in + count;
+@@ -126,7 +126,7 @@
+ 	if (count < 0) {	/* Padding forces an extra block */
+ 		memset(p, 0, count + 8);
+ 		byteSwap(ctx->in, 16);
+-		MD5Transform(ctx->buf, ctx->in);
++		smb2_MD5Transform(ctx->buf, ctx->in);
+ 		p = (md5byte *)ctx->in;
+ 		count = 56;
+ 	}
+@@ -136,7 +136,7 @@
+ 	/* Append length in bits and transform */
+ 	ctx->in[14] = ctx->bytes[0] << 3;
+ 	ctx->in[15] = ctx->bytes[1] << 3 | ctx->bytes[0] >> 29;
+-	MD5Transform(ctx->buf, ctx->in);
++	smb2_MD5Transform(ctx->buf, ctx->in);
+ 
+ 	byteSwap(ctx->buf, 4);
+ 	memcpy(digest, ctx->buf, 16);
+@@ -163,7 +163,7 @@
+  * the data and converts bytes into longwords for this routine.
+  */
+ void
+-MD5Transform(UWORD32 buf[4], UWORD32 const in[16])
++smb2_MD5Transform(UWORD32 buf[4], UWORD32 const in[16])
+ {
+ 	register UWORD32 a, b, c, d;
+ 
+diff -ru smb2-clean/lib/md5.h smb2/lib/md5.h
+--- smb2-clean/lib/md5.h	2020-11-15 00:07:07.000000000 +0100
++++ smb2/lib/md5.h	2021-03-09 12:07:16.000000000 +0100
+@@ -58,10 +58,10 @@
+ 	UWORD32 in[16];
+ };
+ 
+-void MD5Init(struct MD5Context *context);
+-void MD5Update(struct MD5Context *context, md5byte const *buf, unsigned len);
+-void MD5Final(unsigned char digest[16], struct MD5Context *context);
+-void MD5Transform(UWORD32 buf[4], UWORD32 const in[16]);
++void smb2_MD5Init(struct MD5Context *context);
++void smb2_MD5Update(struct MD5Context *context, md5byte const *buf, unsigned len);
++void smb2_MD5Final(unsigned char digest[16], struct MD5Context *context);
++void smb2_MD5Transform(UWORD32 buf[4], UWORD32 const in[16]);
+ 
+ 
+ #ifdef __cplusplus
diff --git a/contrib/src/smb2/rules.mak b/contrib/src/smb2/rules.mak
index 54fbc80d60..dbcebacdee 100644
--- a/contrib/src/smb2/rules.mak
+++ b/contrib/src/smb2/rules.mak
@@ -13,6 +13,7 @@ $(TARBALLS)/libsmb2-$(SMB2_VERSION).tar.gz:
 
 smb2: libsmb2-$(SMB2_VERSION).tar.gz .sum-smb2
 	$(UNPACK)
+	$(APPLY) $(SRC)/smb2/0001-smb2-avoid-MD5-symbol-conflict.patch
 	$(MOVE)
 
 .smb2: smb2
-- 
2.39.3 (Apple Git-146)

