From a0fca173cd736eb4488b2e223163f512c7776245 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 20 Oct 2020 11:33:23 +0200
Subject: [PATCH 27/49] lib: media_player: add stop/set_media async support

This commit adds 2 new function into 3.0.12:
 - libvlc_media_player_stop_async()
 - libvlc_media_player_set_media_async()

These 2 new functions won't wait for the input_thread termination. This
will allow LibVLC users to set media and stop them from the mainthread
without blocking. In that case (*_async() function is called), the input
thread will be terminated from a background thread: the
destructor_thread.

libvlc_media_player_release() will wait for this destructor thread
termination. That is why, it is advised to release the media_player at
the end of the program (when UI is destroyed).

This functionality is backport of the async stop functionality of the
new VLC 4.0 player.
---
 include/vlc/libvlc_media_player.h |  28 +++-
 lib/libvlc.sym                    |   2 +
 lib/media_list_player.c           |   4 +-
 lib/media_player.c                | 238 +++++++++++++++++++++++-------
 lib/media_player_internal.h       |   9 ++
 test/libvlc/media_player.c        |  48 ++++++
 6 files changed, 272 insertions(+), 57 deletions(-)

diff --git a/include/vlc/libvlc_media_player.h b/include/vlc/libvlc_media_player.h
index 564001c695..d499b3d3b8 100644
--- a/include/vlc/libvlc_media_player.h
+++ b/include/vlc/libvlc_media_player.h
@@ -192,6 +192,10 @@ LIBVLC_API libvlc_media_player_t * libvlc_media_player_new_from_media( libvlc_me
  * release the media player object. If the media player object
  * has been released, then it should not be used again.
  *
+ * If the reference count is 0, the media_player will stop the current media
+ * and all medias that are still stopping asynchronously (cf.
+ * libvlc_media_player_stop_async() and libvlc_media_player_set_media_async()).
+ *
  * \param p_mi the Media Player to free
  */
 LIBVLC_API void libvlc_media_player_release( libvlc_media_player_t *p_mi );
@@ -206,7 +210,9 @@ LIBVLC_API void libvlc_media_player_retain( libvlc_media_player_t *p_mi );
 
 /**
  * Set the media that will be used by the media_player. If any,
- * previous md will be released.
+ * previous md will be released synchronously.
+ *
+ * \warning The function will block until the previous media is released.
  *
  * \param p_mi the Media Player
  * \param p_md the Media. Afterwards the p_md can be safely
@@ -215,6 +221,18 @@ LIBVLC_API void libvlc_media_player_retain( libvlc_media_player_t *p_mi );
 LIBVLC_API void libvlc_media_player_set_media( libvlc_media_player_t *p_mi,
                                                    libvlc_media_t *p_md );
 
+/**
+ * Set the media that will be used by the media_player. If any,
+ * previous md will be released asynchronously.
+ *
+ * \param p_mi the Media Player
+ * \param p_md the Media. Afterwards the p_md can be safely
+ *        destroyed.
+ * \version LibVLC 3.0.12 or later
+ */
+LIBVLC_API void libvlc_media_player_set_media_async( libvlc_media_player_t *p_mi,
+                                                     libvlc_media_t *p_md );
+
 /**
  * Get the media used by the media_player.
  *
@@ -274,6 +292,14 @@ LIBVLC_API void libvlc_media_player_pause ( libvlc_media_player_t *p_mi );
  */
 LIBVLC_API void libvlc_media_player_stop ( libvlc_media_player_t *p_mi );
 
+/**
+ * Stop asynchronously (no effect if there is no media)
+ *
+ * \param p_mi the Media Player
+ * \version LibVLC 3.0.12 or later
+ */
+LIBVLC_API void libvlc_media_player_stop_async ( libvlc_media_player_t *p_mi );
+
 /**
  * Set a renderer to the media player
  *
diff --git a/lib/libvlc.sym b/lib/libvlc.sym
index 9f1e9dade6..a4f36a40b7 100644
--- a/lib/libvlc.sym
+++ b/lib/libvlc.sym
@@ -192,6 +192,7 @@ libvlc_media_player_set_chapter
 libvlc_media_player_set_equalizer
 libvlc_media_player_set_hwnd
 libvlc_media_player_set_media
+libvlc_media_player_set_media_async
 libvlc_media_player_set_nsobject
 libvlc_media_player_set_position
 libvlc_media_player_set_rate
@@ -201,6 +202,7 @@ libvlc_media_player_set_time
 libvlc_media_player_set_title
 libvlc_media_player_set_xwindow
 libvlc_media_player_stop
+libvlc_media_player_stop_async
 libvlc_media_player_will_play
 libvlc_media_player_navigate
 libvlc_media_player_set_video_title_display
diff --git a/lib/media_list_player.c b/lib/media_list_player.c
index 64b5c0964e..f79b526dc7 100644
--- a/lib/media_list_player.c
+++ b/lib/media_list_player.c
@@ -465,7 +465,7 @@ set_current_playing_item(libvlc_media_list_player_t * p_mlp, libvlc_media_list_p
     /* Make sure media_player_reached_end() won't get called */
     uninstall_media_player_observer(p_mlp);
 
-    libvlc_media_player_set_media(p_mlp->p_mi, p_md);
+    libvlc_media_player_set_media_async(p_mlp->p_mi, p_md);
 
     install_media_player_observer(p_mlp);
     libvlc_media_release(p_md); /* for libvlc_media_list_item_at_index */
@@ -746,7 +746,7 @@ static void stop(libvlc_media_list_player_t * p_mlp)
 
     /* We are not interested in getting media stop event now */
     uninstall_media_player_observer(p_mlp);
-    libvlc_media_player_stop(p_mlp->p_mi);
+    libvlc_media_player_stop_async(p_mlp->p_mi);
     install_media_player_observer(p_mlp);
 
     free(p_mlp->current_playing_item_path);
diff --git a/lib/media_player.c b/lib/media_player.c
index 5e530182c7..33dc76b971 100644
--- a/lib/media_player.c
+++ b/lib/media_player.c
@@ -46,6 +46,8 @@
 
 #define ES_INIT (-2) /* -1 is reserved for ES deselect */
 
+static void *worker_thread(void *data);
+
 static int
 input_seekable_changed( vlc_object_t * p_this, char const * psz_cmd,
                         vlc_value_t oldval, vlc_value_t newval,
@@ -160,13 +162,14 @@ static void media_detach_preparsed_event( libvlc_media_t *p_md )
  * Object lock is NOT held.
  * Input lock is held or instance is being destroyed.
  */
-static void release_input_thread( libvlc_media_player_t *p_mi )
+static input_thread_t *
+stop_input_thread( libvlc_media_player_t *p_mi )
 {
     assert( p_mi );
 
     input_thread_t *p_input_thread = p_mi->input.p_thread;
-    if( !p_input_thread )
-        return;
+    if( p_input_thread == NULL )
+        return NULL;
     p_mi->input.p_thread = NULL;
 
     media_detach_preparsed_event( p_mi->p_md );
@@ -183,8 +186,28 @@ static void release_input_thread( libvlc_media_player_t *p_mi )
 
     /* We owned this one */
     input_Stop( p_input_thread );
-    input_Close( p_input_thread );
-    config_AutoSaveConfigFile( p_mi );
+
+    return p_input_thread;
+}
+
+static void
+release_input_thread(libvlc_media_player_t *mp, bool async)
+{
+    input_thread_t *input = stop_input_thread(mp);
+    if (input != NULL)
+    {
+        if (!async)
+        {
+            input_Close(input);
+            config_AutoSaveConfigFile(mp);
+        }
+        else
+        {
+            assert(mp->worker.close_input == NULL);
+            mp->worker.close_input = input;
+            vlc_cond_signal(&mp->worker.wait);
+        }
+    }
 }
 
 /*
@@ -800,6 +823,18 @@ libvlc_media_player_new( libvlc_instance_t *instance )
     var_AddCallback(mp->obj.libvlc, "snapshot-file", snapshot_was_taken, mp);
 
     libvlc_retain(instance);
+
+    vlc_cond_init(&mp->worker.wait);
+    mp->worker.running = true;
+    mp->worker.open_next = false;
+    mp->worker.close_input = NULL;
+    if (vlc_clone(&mp->worker.thread, worker_thread,
+                  mp, VLC_THREAD_PRIORITY_LOW) != 0)
+    {
+        libvlc_media_player_destroy(mp);
+        return NULL;
+    }
+
     return mp;
 }
 
@@ -840,10 +875,16 @@ static void libvlc_media_player_destroy( libvlc_media_player_t *p_mi )
     var_DelCallback( p_mi, "audio-device", audio_device_changed, NULL );
     var_DelCallback( p_mi, "corks", corks_changed, NULL );
 
-    /* No need for lock_input() because no other threads knows us anymore */
-    if( p_mi->input.p_thread )
-        release_input_thread(p_mi);
-    input_resource_Terminate( p_mi->input.p_resource );
+    lock_input( p_mi );
+
+    release_input_thread(p_mi, true);
+
+    /* Stop and wait for the worker thread */
+    p_mi->worker.running = false;
+    vlc_cond_signal( &p_mi->worker.wait );
+    unlock_input( p_mi );
+    vlc_join(p_mi->worker.thread, NULL);
+
     input_resource_Release( p_mi->input.p_resource );
     if( p_mi->input.p_renderer )
         vlc_renderer_item_release( p_mi->input.p_renderer );
@@ -903,13 +944,13 @@ void libvlc_media_player_retain( libvlc_media_player_t *p_mi )
  *
  * Enter without lock -- function will lock the object.
  **************************************************************************/
-void libvlc_media_player_set_media(
+static void libvlc_media_player_set_media_common(
                             libvlc_media_player_t *p_mi,
-                            libvlc_media_t *p_md )
+                            libvlc_media_t *p_md, bool async )
 {
     lock_input(p_mi);
 
-    release_input_thread( p_mi );
+    release_input_thread( p_mi, async );
 
     lock( p_mi );
     set_state( p_mi, libvlc_NothingSpecial, true );
@@ -934,7 +975,20 @@ void libvlc_media_player_set_media(
     event.type = libvlc_MediaPlayerMediaChanged;
     event.u.media_player_media_changed.new_media = p_md;
     libvlc_event_send( &p_mi->event_manager, &event );
+}
 
+void libvlc_media_player_set_media(
+                            libvlc_media_player_t *p_mi,
+                            libvlc_media_t *p_md )
+{
+    libvlc_media_player_set_media_common( p_mi, p_md, false );
+}
+
+void libvlc_media_player_set_media_async(
+                            libvlc_media_player_t *p_mi,
+                            libvlc_media_t *p_md )
+{
+    libvlc_media_player_set_media_common( p_mi, p_md, true );
 }
 
 /**************************************************************************
@@ -977,33 +1031,9 @@ static void del_es_callbacks( input_thread_t *p_input_thread, libvlc_media_playe
     var_DelListCallback( p_input_thread, "spu-es", input_es_changed, p_mi );
 }
 
-/**************************************************************************
- * Tell media player to start playing.
- **************************************************************************/
-int libvlc_media_player_play( libvlc_media_player_t *p_mi )
+static input_thread_t *
+create_input_thread( libvlc_media_player_t *p_mi )
 {
-    lock_input( p_mi );
-
-    input_thread_t *p_input_thread = p_mi->input.p_thread;
-    if( p_input_thread )
-    {
-        /* A thread already exists, send it a play message */
-        input_Control( p_input_thread, INPUT_SET_STATE, PLAYING_S );
-        unlock_input( p_mi );
-        return 0;
-    }
-
-    /* Ignore previous exception */
-    lock(p_mi);
-
-    if( !p_mi->p_md )
-    {
-        unlock(p_mi);
-        unlock_input( p_mi );
-        libvlc_printerr( "No associated media descriptor" );
-        return -1;
-    }
-
     for( size_t i = 0; i < ARRAY_SIZE( p_mi->selected_es ); ++i )
         p_mi->selected_es[i] = ES_INIT;
 
@@ -1015,16 +1045,13 @@ int libvlc_media_player_play( libvlc_media_player_t *p_mi )
 
     media_attach_preparsed_event( p_mi->p_md );
 
-    p_input_thread = input_Create( p_mi, p_mi->p_md->p_input_item, NULL,
-                                   p_mi->input.p_resource,
-                                   p_mi->input.p_renderer );
-    unlock(p_mi);
+    input_thread_t *p_input_thread =
+        input_Create( p_mi, p_mi->p_md->p_input_item, NULL,
+                      p_mi->input.p_resource, p_mi->input.p_renderer );
     if( !p_input_thread )
     {
-        unlock_input(p_mi);
         media_detach_preparsed_event( p_mi->p_md );
-        libvlc_printerr( "Not enough memory" );
-        return -1;
+        return NULL;
     }
 
     var_AddCallback( p_input_thread, "can-seek", input_seekable_changed, p_mi );
@@ -1035,7 +1062,6 @@ int libvlc_media_player_play( libvlc_media_player_t *p_mi )
 
     if( input_Start( p_input_thread ) )
     {
-        unlock_input(p_mi);
         del_es_callbacks( p_input_thread, p_mi );
         var_DelCallback( p_input_thread, "intf-event", input_event_changed, p_mi );
         var_DelCallback( p_input_thread, "can-pause", input_pausable_changed, p_mi );
@@ -1044,11 +1070,54 @@ int libvlc_media_player_play( libvlc_media_player_t *p_mi )
         input_Close( p_input_thread );
         media_detach_preparsed_event( p_mi->p_md );
         libvlc_printerr( "Input initialization failure" );
+        return NULL;
+    }
+
+    return p_input_thread;
+}
+
+/**************************************************************************
+ * Tell media player to start playing.
+ **************************************************************************/
+int libvlc_media_player_play( libvlc_media_player_t *p_mi )
+{
+    lock_input( p_mi );
+
+    input_thread_t *p_input_thread = p_mi->input.p_thread;
+    if( p_input_thread )
+    {
+        /* A thread already exists, send it a play message */
+        input_Control( p_input_thread, INPUT_SET_STATE, PLAYING_S );
+        unlock_input( p_mi );
+        return 0;
+    }
+
+    /* Ignore previous exception */
+    lock(p_mi);
+
+    if( !p_mi->p_md )
+    {
+        unlock(p_mi);
+        unlock_input( p_mi );
+        libvlc_printerr( "No associated media descriptor" );
         return -1;
     }
-    p_mi->input.p_thread = p_input_thread;
+
+    int ret;
+    if (p_mi->worker.close_input != NULL)
+    {
+        p_mi->worker.open_next = true;
+        ret = 0;
+    }
+    else
+    {
+        p_mi->input.p_thread = create_input_thread( p_mi );
+        ret = p_mi->input.p_thread == NULL ? -1 : 0;
+    }
+
+    unlock(p_mi);
     unlock_input(p_mi);
-    return 0;
+    return ret;
 }
 
 void libvlc_media_player_set_pause( libvlc_media_player_t *p_mi, int paused )
@@ -1089,14 +1158,52 @@ int libvlc_media_player_is_playing( libvlc_media_player_t *p_mi )
     return libvlc_Playing == state;
 }
 
-/**************************************************************************
- * Stop playing.
- **************************************************************************/
-void libvlc_media_player_stop( libvlc_media_player_t *p_mi )
+static void *
+worker_thread(void *data)
 {
-    lock_input(p_mi);
-    release_input_thread( p_mi ); /* This will stop the input thread */
+    libvlc_media_player_t *mp = data;
+
+    lock_input(mp);
+
+    while (mp->worker.running || mp->worker.close_input != NULL)
+    {
+        vlc_cond_wait(&mp->worker.wait, &mp->input.lock);
+
+        if (mp->worker.close_input == NULL)
+            continue;
+
+        input_thread_t *input = mp->worker.close_input;
+        unlock_input(mp);
+
+        input_Close(input);
+        config_AutoSaveConfigFile(mp);
+
+        lock_input(mp);
+        mp->worker.close_input = NULL;
+
+        if (mp->worker.open_next)
+        {
+            mp->worker.open_next = false;
+            lock(mp);
+            if (mp->p_md != NULL)
+                mp->input.p_thread = create_input_thread(mp);
+            unlock(mp);
+        }
+
+        if (mp->input.p_thread == NULL)
+        {
+            /* No media currently playing, we can terminate all resources */
+            input_resource_Terminate(mp->input.p_resource);
+        }
+    }
+    unlock_input(mp);
+
+    return NULL;
+}
 
+static void
+set_stopped_state( libvlc_media_player_t *p_mi )
+{
     /* Force to go to stopped state, in case we were in Ended, or Error
      * state. */
     if( p_mi->state != libvlc_Stopped )
@@ -1108,11 +1215,34 @@ void libvlc_media_player_stop( libvlc_media_player_t *p_mi )
         event.type = libvlc_MediaPlayerStopped;
         libvlc_event_send( &p_mi->event_manager, &event );
     }
+    p_mi->worker.open_next = false;
+}
+
+/**************************************************************************
+ * Stop playing.
+ **************************************************************************/
+void libvlc_media_player_stop( libvlc_media_player_t *p_mi )
+{
+    lock_input(p_mi);
+    release_input_thread( p_mi, false ); /* This will stop the input thread */
+
+    set_stopped_state( p_mi );
 
     input_resource_Terminate( p_mi->input.p_resource );
     unlock_input(p_mi);
 }
 
+void libvlc_media_player_stop_async( libvlc_media_player_t *p_mi )
+{
+    lock_input(p_mi);
+
+    release_input_thread( p_mi, true );
+
+    set_stopped_state( p_mi );
+
+    unlock_input(p_mi);
+}
+
 int libvlc_media_player_set_renderer( libvlc_media_player_t *p_mi,
                                       libvlc_renderer_item_t *p_litem )
 {
diff --git a/lib/media_player_internal.h b/lib/media_player_internal.h
index a9acff1766..27894a9c39 100644
--- a/lib/media_player_internal.h
+++ b/lib/media_player_internal.h
@@ -51,6 +51,15 @@ struct libvlc_media_player_t
         vlc_mutex_t       lock;
     } input;
 
+    struct
+    {
+        vlc_thread_t thread;
+        vlc_cond_t   wait;
+        input_thread_t *close_input;
+        bool open_next;
+        bool running;
+    } worker;
+
     struct libvlc_instance_t * p_libvlc_instance; /* Parent instance */
     libvlc_media_t * p_md; /* current media descriptor */
     libvlc_event_manager_t event_manager;
diff --git a/test/libvlc/media_player.c b/test/libvlc/media_player.c
index f3198b5bcf..7f9b9d4b83 100644
--- a/test/libvlc/media_player.c
+++ b/test/libvlc/media_player.c
@@ -150,6 +150,53 @@ static void test_media_player_play_stop(const char** argv, int argc)
     libvlc_release (vlc);
 }
 
+static void test_media_player_async(const char** argv, int argc)
+{
+    libvlc_instance_t *vlc;
+    libvlc_media_t *md;
+    libvlc_media_player_t *mi;
+    const char * file = test_default_sample;
+
+    log ("Testing stop/set_media async %s\n", file);
+
+    vlc = libvlc_new (argc, argv);
+    assert (vlc != NULL);
+
+    mi = libvlc_media_player_new (vlc);
+    assert (mi != NULL);
+
+    for (unsigned i = 0; i < 100; ++i)
+    {
+        md = libvlc_media_new_path (vlc, file);
+        assert (md != NULL);
+
+        libvlc_media_player_set_media_async (mi, md);
+
+        libvlc_media_release (md);
+
+        libvlc_media_player_play (mi);
+    }
+
+    libvlc_media_player_stop_async (mi);
+
+    for (unsigned i = 0; i < 100; ++i)
+    {
+        md = libvlc_media_new_path (vlc, file);
+        assert (md != NULL);
+
+        libvlc_media_player_set_media (mi, md);
+
+        libvlc_media_release (md);
+
+        libvlc_media_player_play (mi);
+
+        libvlc_media_player_stop_async (mi);
+    }
+
+    libvlc_media_player_release (mi);
+    libvlc_release (vlc);
+}
+
 static void test_media_player_pause_stop(const char** argv, int argc)
 {
     libvlc_instance_t *vlc;
@@ -197,6 +244,7 @@ int main (void)
 
     test_media_player_set_media (test_defaults_args, test_defaults_nargs);
     test_media_player_play_stop (test_defaults_args, test_defaults_nargs);
+    test_media_player_async (test_defaults_args, test_defaults_nargs);
     test_media_player_pause_stop (test_defaults_args, test_defaults_nargs);
 
     return 0;
-- 
2.39.3 (Apple Git-146)

