From d41dda36ed75a4dcef29c930a26929ab99f555e8 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 13 Aug 2020 15:55:24 +0200
Subject: [PATCH 28/49] aout: add "audio-meter" filter type

---
 include/vlc_aout.h         | 14 ++++++++++++++
 src/audio_output/filters.c |  5 +++++
 src/audio_output/output.c  | 18 ++++++++++++++++++
 3 files changed, 37 insertions(+)

diff --git a/include/vlc_aout.h b/include/vlc_aout.h
index 5025c70b95..a958c1b6d4 100644
--- a/include/vlc_aout.h
+++ b/include/vlc_aout.h
@@ -112,6 +112,20 @@
 #include <vlc_block.h>
 
 /** Audio output object */
+struct vlc_audio_loudness_meter
+{
+    /** Momentary loudness, in LUFS */
+    double loudness_momentary;
+    /** Short term loudness, in LUFS (VLC_PLAYER_AOUT_LOUDNESS_METER_FULL) */
+    double loudness_shortterm;
+    /** Integrated loudness, in LUFS (VLC_PLAYER_AOUT_LOUDNESS_METER_FULL) */
+    double loudness_integrated;
+    /** Loudness range, in LU (VLC_PLAYER_AOUT_LOUDNESS_METER_FULL) */
+    double loudness_range;
+    /** True Peak, in dBTP (VLC_PLAYER_AOUT_LOUDNESS_METER_FULL) */
+    double truepeak;
+};
+
 struct audio_output
 {
     VLC_COMMON_MEMBERS
diff --git a/src/audio_output/filters.c b/src/audio_output/filters.c
index 585379e7c3..43bd4ff5d4 100644
--- a/src/audio_output/filters.c
+++ b/src/audio_output/filters.c
@@ -627,6 +627,11 @@ aout_filters_t *aout_FiltersNew (vlc_object_t *obj,
         free (visual);
     }
 
+    char *meter = var_InheritString(obj, "audio-meter");
+    if (meter != NULL && strcasecmp(meter, "none"))
+        AppendFilter(obj, "audio meter", meter, filters, NULL, &input_format,
+                     &output_format, NULL);
+
     /* convert to the output format (minus resampling) if necessary */
     output_format.i_rate = input_format.i_rate;
     if (aout_FiltersPipelineCreate (obj, filters->tab, &filters->count,
diff --git a/src/audio_output/output.c b/src/audio_output/output.c
index bb8e4c3c9e..ee835ed9a2 100644
--- a/src/audio_output/output.c
+++ b/src/audio_output/output.c
@@ -171,6 +171,18 @@ static int FilterCallback (vlc_object_t *obj, const char *var,
     return VLC_SUCCESS;
 }
 
+static int MeterCallback (vlc_object_t *obj, const char *var,
+                          vlc_value_t prev, vlc_value_t cur, void *data)
+{
+    /* Don't request a restart when the meter is removed to avoid an audio
+     * glitch */
+    if (cur.psz_string != NULL)
+        aout_InputRequestRestart ((audio_output_t *)obj);
+
+    (void) var; (void) prev; (void) data;
+    return VLC_SUCCESS;
+}
+
 static int StereoModeCallback (vlc_object_t *obj, const char *varname,
                                vlc_value_t oldval, vlc_value_t newval, void *data)
 {
@@ -226,6 +238,7 @@ audio_output_t *aout_New (vlc_object_t *parent)
     var_AddCallback (aout, "device", var_CopyDevice, parent);
     /* TODO: 3.0 HACK: only way to signal DTS_HD to aout modules. */
     var_Create (aout, "dtshd", VLC_VAR_BOOL);
+    var_Create (aout, "loudness-meter", VLC_VAR_ADDRESS);
 
     aout->event.volume_report = aout_VolumeNotify;
     aout->event.mute_report = aout_MuteNotify;
@@ -321,6 +334,10 @@ audio_output_t *aout_New (vlc_object_t *parent)
     text.psz_string = _("Audio visualizations");
     var_Change (aout, "audio-visual", VLC_VAR_SETTEXT, &text, NULL);
 
+    var_Create (aout, "audio-meter", VLC_VAR_STRING);
+    var_AddCallback (aout, "audio-meter", MeterCallback, NULL);
+    var_Create (aout, "ebur128-fullmeter", VLC_VAR_BOOL);
+
     /* Replay gain */
     var_Create (aout, "audio-replay-gain-mode",
                 VLC_VAR_STRING | VLC_VAR_DOINHERIT );
@@ -372,6 +389,7 @@ void aout_Destroy (audio_output_t *aout)
     var_DelCallback (aout, "audio-filter", FilterCallback, NULL);
     var_DelCallback (aout, "device", var_CopyDevice, aout->obj.parent);
     var_DelCallback (aout, "mute", var_Copy, aout->obj.parent);
+    var_DelCallback (aout, "audio-meter", MeterCallback, NULL);
     var_SetFloat (aout, "volume", -1.f);
     var_DelCallback (aout, "volume", var_Copy, aout->obj.parent);
     var_DelCallback (aout, "stereo-mode", StereoModeCallback, NULL);
-- 
2.39.3 (Apple Git-146)

