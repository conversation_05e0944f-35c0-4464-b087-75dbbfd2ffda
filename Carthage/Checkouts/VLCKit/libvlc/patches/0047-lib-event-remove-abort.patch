From 1cd736b36a4ef236d8e4b338cb83eb628df563fc Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Mon, 21 Aug 2023 14:44:24 +0200
Subject: [PATCH 47/49] lib/event: remove abort

Leaking is better than aborting
---
 lib/event.c | 1 -
 1 file changed, 1 deletion(-)

diff --git a/lib/event.c b/lib/event.c
index f027754181..8d4f1f2c53 100644
--- a/lib/event.c
+++ b/lib/event.c
@@ -277,5 +277,4 @@ void libvlc_event_detach(libvlc_event_manager_t *em, libvlc_event_type_t type,
              return;
          }
     }
-    abort();
 }
-- 
2.39.3 (Apple Git-146)

