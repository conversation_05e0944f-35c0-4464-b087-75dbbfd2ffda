From 5043dead3d2d95fd8d76d9f1de39280d27c512e3 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Fri, 19 Nov 2021 13:23:20 +0100
Subject: [PATCH 37/49] upnp: accept IPv4 interfaces only for iOS and use the
 last discovered

---
 modules/services_discovery/upnp.cpp | 12 ++++++++----
 1 file changed, 8 insertions(+), 4 deletions(-)

diff --git a/modules/services_discovery/upnp.cpp b/modules/services_discovery/upnp.cpp
index 428aeff0d7..fe0c5503ee 100644
--- a/modules/services_discovery/upnp.cpp
+++ b/modules/services_discovery/upnp.cpp
@@ -1633,10 +1633,14 @@ inline char *getPreferedAdapter()
 
     anInterface = listOfInterfaces;
     while (anInterface != NULL) {
-        bool ret = necessaryFlagsSetOnInterface(anInterface);
-        if (ret) {
-            adapterName = strdup(anInterface->ifa_name);
-            break;
+        if (anInterface->ifa_addr->sa_family == AF_INET) {
+            bool ret = necessaryFlagsSetOnInterface(anInterface);
+            if (ret) {
+                if (adapterName) {
+                    FREENULL(adapterName);
+                }
+                adapterName = strdup(anInterface->ifa_name);
+            }
         }
 
         anInterface = anInterface->ifa_next;
-- 
2.39.3 (Apple Git-146)

