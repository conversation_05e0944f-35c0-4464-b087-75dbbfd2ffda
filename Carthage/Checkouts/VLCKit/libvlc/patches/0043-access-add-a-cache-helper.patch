From 401f5d5fbfa590fd55061c25270a5471fc37a103 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 10 Jun 2018 20:36:06 +0300
Subject: [PATCH 43/49] access: add a cache helper

Helper that can be used by any accesses, that will save up to 5 contexts
up to 5 seconds (both configurable with a define).

This helper create its own thread that will release the resources when
unused.

This helper also use the gcc destructor attribute to clean everything
when the library is unloaded.

This helper need a destructor, thus it can only be used with gcc and
clang.

(cherry picked from commit aa3829778b8f2008be6a64439e9ebdaaccbd2e8f)
(cherry picked from commit 6dcd24dc085e8942d68edd9555c353a9ba4aede0)
Signed-off-by: <PERSON> <thoma<PERSON>@gllm.fr>
---
 include/vlc_list.h         | 381 +++++++++++++++++++++++++++++++++++++
 modules/access/Makefile.am |   8 +
 modules/access/cache.c     | 211 ++++++++++++++++++++
 modules/access/cache.h     | 135 +++++++++++++
 src/Makefile.am            |   1 +
 5 files changed, 736 insertions(+)
 create mode 100644 include/vlc_list.h
 create mode 100644 modules/access/cache.c
 create mode 100644 modules/access/cache.h

diff --git a/include/vlc_list.h b/include/vlc_list.h
new file mode 100644
index **********..9ace4d07ce
--- /dev/null
+++ b/include/vlc_list.h
@@ -0,0 +1,381 @@
+/******************************************************************************
+ * vlc_list.h
+ ******************************************************************************
+ * Copyright © 2018 Rémi Denis-Courmont
+ *
+ * This program is free software; you can redistribute it and/or modify it
+ * under the terms of the GNU Lesser General Public License as published by
+ * the Free Software Foundation; either version 2.1 of the License, or
+ * (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU Lesser General Public License for more details.
+ *
+ * You should have received a copy of the GNU Lesser General Public License
+ * along with this program; if not, write to the Free Software Foundation,
+ * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
+ *****************************************************************************/
+
+#ifndef VLC_LIST_H
+# define VLC_LIST_H 1
+
+# include <stdalign.h>
+# include <stdbool.h>
+
+/**
+ * \defgroup list Linked lists
+ * \ingroup cext
+ * @{
+ * \file
+ * This provides convenience helpers for linked lists.
+ */
+
+/**
+ * Doubly-linked list node.
+ *
+ * This data structure provides a doubly-linked list node.
+ * It must be embedded in each member of a list as a node proper.
+ * It also serves as a list head in the object containing the list.
+ */
+struct vlc_list
+{
+    struct vlc_list *prev;
+    struct vlc_list *next;
+};
+
+/**
+ * Static initializer for a list head.
+ */
+#define VLC_LIST_INITIALIZER(h) { h, h }
+
+/**
+ * Initializes an empty list head.
+ */
+static inline void vlc_list_init(struct vlc_list *restrict head)
+{
+    head->prev = head;
+    head->next = head;
+}
+
+/**
+ * Inserts an element in a list.
+ *
+ * \param node Node pointer of the element to insert [OUT].
+ * \param prev Node pointer of the previous element.
+ * \param next Node pointer of the next element.
+ */
+static inline void vlc_list_add_between(struct vlc_list *restrict node,
+                                        struct vlc_list *prev,
+                                        struct vlc_list *next)
+{
+    node->prev = prev;
+    node->next = next;
+    prev->next = node;
+    next->prev = node;
+}
+
+/**
+ * Inserts an element after another.
+ *
+ * \param node Node pointer of the element to insert [OUT].
+ * \param prev Node pointer of the previous element.
+ */
+static inline void vlc_list_add_after(struct vlc_list *restrict node,
+                                      struct vlc_list *prev)
+{
+    vlc_list_add_between(node, prev, prev->next);
+}
+
+/**
+ * Inserts an element before another.
+ *
+ * \param node Node pointer of the element to insert [OUT].
+ * \param next Node pointer of the next element.
+ */
+static inline void vlc_list_add_before(struct vlc_list *restrict node,
+                                       struct vlc_list *next)
+{
+    vlc_list_add_between(node, next->prev, next);
+}
+
+/**
+ * Appends an element into a list.
+ *
+ * \param node Node pointer of the element to append to the list [OUT].
+ * \param head Head pointer of the list to append the element to.
+ */
+static inline void vlc_list_append(struct vlc_list *restrict node,
+                                   struct vlc_list *head)
+{
+    vlc_list_add_before(node, head);
+}
+
+/**
+ * Prepends an element into a list.
+ *
+ * \param node Node pointer of the element to prepend to the list [OUT].
+ * \param head Head pointer of the list to prepend the element to.
+ */
+static inline void vlc_list_prepend(struct vlc_list *restrict node,
+                                    struct vlc_list *head)
+{
+    vlc_list_add_after(node, head);
+}
+
+/**
+ * Removes an element from a list.
+ *
+ * \param node Node of the element to remove from a list.
+ * \warning The element must be inside a list.
+ * Otherwise the behaviour is undefined.
+ */
+static inline void vlc_list_remove(struct vlc_list *restrict node)
+{
+    struct vlc_list *prev = node->prev;
+    struct vlc_list *next = node->next;
+
+    prev->next = next;
+    next->prev = prev;
+}
+
+/**
+ * Replaces an element with another one.
+ *
+ * \param original Node pointer of the element to remove from the list [IN].
+ * \param substitute Node pointer of the replacement [OUT].
+ */
+static inline void vlc_list_replace(const struct vlc_list *original,
+                                    struct vlc_list *restrict substitute)
+{
+    vlc_list_add_between(substitute, original->prev, original->next);
+}
+
+/**
+ * Checks if a list is empty.
+ *
+ * \param head Head of the list to be checked [IN].
+ *
+ * \retval false The list is not empty.
+ * \retval true The list is empty.
+ *
+ * \note Obviously the list must have been initialized.
+ * Otherwise, the behaviour is undefined.
+ */
+static inline bool vlc_list_is_empty(const struct vlc_list *head)
+{
+    return head->next == head;
+}
+
+/**
+ * Checks if an element is first in a list.
+ *
+ * \param node List node of the element [IN].
+ * \param head Head of the list to be checked [IN].
+ *
+ * \retval false The element is not first (or is in another list).
+ * \retval true The element is first.
+ */
+static inline bool vlc_list_is_first(const struct vlc_list *node,
+                                     const struct vlc_list *head)
+{
+    return node->prev == head;
+}
+
+/**
+ * Checks if an element is last in a list.
+ *
+ * \param node List node of the element [IN].
+ * \param head Head of the list to be checked [IN].
+ *
+ * \retval false The element is not last (or is in another list).
+ * \retval true The element is last.
+ */
+static inline bool vlc_list_is_last(const struct vlc_list *node,
+                                    const struct vlc_list *head)
+{
+    return node->next == head;
+}
+
+/**
+ * List iterator.
+ */
+struct vlc_list_it
+{
+    const struct vlc_list *head;
+    struct vlc_list *current;
+    struct vlc_list *next;
+};
+
+static inline
+struct vlc_list_it vlc_list_it_start(const struct vlc_list *head)
+{
+    struct vlc_list *first = head->next;
+
+    struct vlc_list_it it = { head, first, first->next };
+    return it;
+}
+
+static inline
+struct vlc_list_it vlc_list_it_reverse_start(const struct vlc_list *head)
+{
+    struct vlc_list *first = head->prev;
+
+    return (struct vlc_list_it){ head, first, first->prev };
+}
+
+static inline bool vlc_list_it_continue(const struct vlc_list_it *restrict it)
+{
+    return it->current != it->head;
+}
+
+static inline void vlc_list_it_next(struct vlc_list_it *restrict it)
+{
+    struct vlc_list *next = it->next;
+
+    it->current = next;
+    it->next = next->next;
+}
+
+static inline void vlc_list_it_prev(struct vlc_list_it *restrict it)
+{
+    struct vlc_list *next = it->next;
+
+    it->current = next;
+    it->next = next->prev;
+}
+
+#define vlc_list_entry_aligned_size(p) \
+    ((sizeof (*(p)) + sizeof (max_align_t) - 1) / sizeof (max_align_t))
+
+#define vlc_list_entry_dummy(p) \
+    (0 ? (p) : ((void *)( \
+        &(max_align_t[vlc_list_entry_aligned_size(p)]){ (max_align_t){0} } \
+    )))
+
+#define vlc_list_offset_p(p, member) \
+    ((p) = vlc_list_entry_dummy(p), (char *)(&(p)->member) - (char *)(p))
+
+#define vlc_list_entry_p(node, p, member) \
+    (0 ? (p) : (void *)(((char *)(node)) - vlc_list_offset_p(p, member)))
+
+/**
+ * List iteration macro.
+ *
+ * This macro iterates over all elements (excluding the head) of a list,
+ * in order from the first to the last.
+ *
+ * For each iteration, it sets the cursor variable to the current element.
+ *
+ * \param pos Cursor pointer variable identifier.
+ * \param head Head pointer of the list to iterate [IN].
+ * \param member Identifier of the member of the data type
+ *               serving as list node.
+ * \note It it safe to delete the current item while iterating.
+ * It is however <b>not</b> safe to delete another item.
+ */
+#define vlc_list_foreach(pos, head, member) \
+    for (struct vlc_list_it vlc_list_it__##pos = vlc_list_it_start(head); \
+         vlc_list_it_continue(&(vlc_list_it__##pos)) \
+          && ((pos) = vlc_list_entry_p((vlc_list_it__##pos).current, \
+                                       pos, member), true); \
+         vlc_list_it_next(&(vlc_list_it__##pos)))
+
+/**
+ * List iteration macro.
+ *
+ * This macro iterates over all elements (excluding the head) of a list,
+ * in reversed order from the first to the last.
+ *
+ * For each iteration, it sets the cursor variable to the current element.
+ *
+ * \param pos Cursor pointer variable identifier.
+ * \param head Head pointer of the list to iterate [IN].
+ * \param member Identifier of the member of the data type
+ *               serving as list node.
+ * \note It it safe to delete the current item while iterating.
+ * It is however <b>not</b> safe to delete another item.
+ */
+#define vlc_list_reverse_foreach(pos, head, member) \
+    for (struct vlc_list_it vlc_list_it_##pos = vlc_list_it_reverse_start(head); \
+         vlc_list_it_continue(&(vlc_list_it_##pos)) \
+          && ((pos) = vlc_list_entry_p((vlc_list_it_##pos).current, \
+                                       pos, member), true); \
+         vlc_list_it_prev(&(vlc_list_it_##pos)))
+
+/**
+ * Converts a list node pointer to an element pointer.
+ *
+ * \param ptr list node pointer
+ * \param type list data element type name
+ * \param member list node member within the data element compound type
+ */
+#define vlc_list_entry(ptr, type, member) container_of(ptr, type, member)
+
+static inline void *vlc_list_first_or_null(const struct vlc_list *head,
+                                           size_t offset)
+{
+    if (vlc_list_is_empty(head))
+        return NULL;
+    return ((char *)(head->next)) - offset;
+}
+
+static inline void *vlc_list_last_or_null(const struct vlc_list *head,
+                                          size_t offset)
+{
+    if (vlc_list_is_empty(head))
+        return NULL;
+    return ((char *)(head->prev)) - offset;
+}
+
+static inline void *vlc_list_prev_or_null(const struct vlc_list *head,
+                                          struct vlc_list *node,
+                                          size_t offset)
+{
+    if (vlc_list_is_first(node, head))
+        return NULL;
+    return ((char *)(node->prev)) - offset;
+}
+
+static inline void *vlc_list_next_or_null(const struct vlc_list *head,
+                                          struct vlc_list *node,
+                                          size_t offset)
+{
+    if (vlc_list_is_last(node, head))
+        return NULL;
+    return ((char *)(node->next)) - offset;
+}
+
+/**
+ * Gets the first element.
+ *
+ * \param head Head of list whose last element to get [IN].
+ *
+ * \return the first entry in a list or NULL if empty.
+ */
+#define vlc_list_first_entry_or_null(head, type, member) \
+        ((type *)vlc_list_first_or_null(head, offsetof (type, member)))
+
+/**
+ * Gets the last element.
+ *
+ * \param head Head of list whose last element to get [IN].
+ *
+ * \return the last entry in a list or NULL if empty.
+ */
+#define vlc_list_last_entry_or_null(head, type, member) \
+        ((type *)vlc_list_last_or_null(head, offsetof (type, member)))
+
+#define vlc_list_prev_entry_or_null(head, entry, type, member) \
+        ((type *)vlc_list_prev_or_null(head, &(entry)->member, \
+                                       offsetof (type, member)))
+#define vlc_list_next_entry_or_null(head, entry, type, member) \
+        ((type *)vlc_list_next_or_null(head, &(entry)->member, \
+                                       offsetof (type, member)))
+
+/** \todo Merging lists, splitting lists. */
+
+/** @} */
+
+#endif /* VLC_LIST_H */
diff --git a/modules/access/Makefile.am b/modules/access/Makefile.am
index f295947233..8be84055fa 100644
--- a/modules/access/Makefile.am
+++ b/modules/access/Makefile.am
@@ -440,3 +440,11 @@ librist_plugin_la_SOURCES = access/rist.c access/rist.h
 librist_plugin_la_CFLAGS = $(AM_CFLAGS)
 librist_plugin_la_LIBADD = $(SOCKET_LIBS)
 access_LTLIBRARIES += librist_plugin.la
+
+### TOOLS ###
+
+libvlc_access_cache_la_SOURCES = access/cache.c access/cache.h
+libvlc_access_cache_la_LIBADD = $(LTLIBVLCCORE)
+libvlc_access_cache_la_LDFLAGS = -static
+libvlc_access_cache_la_CPPFLAGS = -Dneedsomethinghere
+noinst_LTLIBRARIES += libvlc_access_cache.la
diff --git a/modules/access/cache.c b/modules/access/cache.c
new file mode 100644
index **********..a50cd4206a
--- /dev/null
+++ b/modules/access/cache.c
@@ -0,0 +1,211 @@
+/*****************************************************************************
+ * cache.c: access cache helper
+ *****************************************************************************
+ * Copyright (C) 2022 VLC authors and VideoLAN
+ *
+ * This program is free software; you can redistribute it and/or modify it
+ * under the terms of the GNU Lesser General Public License as published by
+ * the Free Software Foundation; either version 2.1 of the License, or
+ * (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU Lesser General Public License for more details.
+ *
+ * You should have received a copy of the GNU Lesser General Public License
+ * along with this program; if not, write to the Free Software Foundation,
+ * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
+ *****************************************************************************/
+
+#ifdef HAVE_CONFIG_H
+# include "config.h"
+#endif
+
+#include <vlc_common.h>
+#include <vlc_threads.h>
+
+#include "access/cache.h"
+
+#include <assert.h>
+
+#define VLC_ACCESS_CACHE_TTL 5000000LL
+#define VLC_ACCESS_CACHE_MAX_ENTRY 5
+
+void
+vlc_access_cache_entry_Delete(struct vlc_access_cache_entry *entry)
+{
+    free(entry->url);
+    free(entry->username);
+
+    free(entry);
+}
+
+struct vlc_access_cache_entry *
+vlc_access_cache_entry_New(void *context, const char *url, const char *username,
+                           void (*free_cb)(void *context))
+{
+    struct vlc_access_cache_entry *entry = malloc(sizeof(*entry));
+    if (unlikely(entry == NULL))
+        return NULL;
+
+    entry->url = strdup(url);
+    entry->username = username ? strdup(username) : NULL;
+    if (!entry->url || (entry->username == NULL) != (username == NULL))
+    {
+        free(entry->url);
+        free(entry);
+        return NULL;
+    }
+
+    entry->context = context;
+    entry->free_cb = free_cb;
+
+    return entry;
+}
+
+static void *
+vlc_access_cache_Thread(void *data)
+{
+    struct vlc_access_cache *cache = data;
+
+    vlc_mutex_lock(&cache->lock);
+    while (cache->running)
+    {
+        if (!vlc_list_is_empty(&cache->entries))
+        {
+            struct vlc_access_cache_entry *entry =
+                vlc_list_first_entry_or_null(&cache->entries,
+                                             struct vlc_access_cache_entry, node);
+
+            if (entry->timeout == 0 ||
+                vlc_cond_timedwait(&cache->cond, &cache->lock, entry->timeout) != 0)
+            {
+                vlc_list_remove(&entry->node);
+
+                vlc_mutex_unlock(&cache->lock);
+
+                entry->free_cb(entry->context);
+                vlc_access_cache_entry_Delete(entry);
+
+                vlc_mutex_lock(&cache->lock);
+            }
+        }
+        else
+            vlc_cond_wait(&cache->cond, &cache->lock);
+    }
+    vlc_mutex_unlock(&cache->lock);
+
+    return NULL;
+}
+
+static void
+vlc_access_cache_InitOnce(void *data)
+{
+    struct vlc_access_cache *cache = data;
+
+    if (cache->init)
+        return;
+    cache->init = true;
+
+    vlc_cond_init(&cache->cond);
+
+#ifdef VLC_ACCESS_CACHE_CAN_REGISTER
+
+    cache->running = true;
+    int ret = vlc_clone(&cache->thread, vlc_access_cache_Thread, cache,
+                        VLC_THREAD_PRIORITY_LOW);
+    if (ret != 0)
+        cache->running = false;
+#endif
+}
+
+void
+vlc_access_cache_Destroy(struct vlc_access_cache *cache)
+{
+    vlc_mutex_lock(&cache->lock);
+    if (cache->running)
+    {
+        cache->running = false;
+        vlc_cond_signal(&cache->cond);
+        vlc_mutex_unlock(&cache->lock);
+        vlc_join(cache->thread, NULL);
+    }
+    else
+        vlc_mutex_unlock(&cache->lock);
+
+    struct vlc_access_cache_entry *entry;
+    vlc_list_foreach(entry, &cache->entries, node)
+    {
+        entry->free_cb(entry->context);
+        vlc_access_cache_entry_Delete(entry);
+    }
+
+    vlc_mutex_destroy(&cache->lock);
+    vlc_cond_destroy(&cache->cond);
+}
+
+void
+vlc_access_cache_AddEntry(struct vlc_access_cache *cache,
+                          struct vlc_access_cache_entry *entry)
+{
+    vlc_mutex_lock(&cache->lock);
+
+    vlc_access_cache_InitOnce(cache);
+
+    if (!cache->running)
+    {
+        vlc_mutex_unlock(&cache->lock);
+        entry->free_cb(entry->context);
+        vlc_access_cache_entry_Delete(entry);
+        return;
+    }
+
+    struct vlc_access_cache_entry *it;
+    size_t count = 0;
+    vlc_list_foreach(it, &cache->entries, node)
+        count++;
+
+    if (count >= VLC_ACCESS_CACHE_MAX_ENTRY)
+    {
+        /* Too many entries, signal the thread that will delete the first one */
+        it = vlc_list_first_entry_or_null(&cache->entries,
+                                          struct vlc_access_cache_entry, node);
+        it->timeout = 0;
+    }
+
+    entry->timeout = mdate() + VLC_ACCESS_CACHE_TTL;
+    vlc_list_append(&entry->node, &cache->entries);
+
+    vlc_cond_signal(&cache->cond);
+    vlc_mutex_unlock(&cache->lock);
+}
+
+struct vlc_access_cache_entry *
+vlc_access_cache_GetEntry(struct vlc_access_cache *cache,
+                          const char *url, const char *username)
+{
+    vlc_mutex_lock(&cache->lock);
+
+    vlc_access_cache_InitOnce(cache);
+
+    struct vlc_access_cache_entry *it;
+
+    vlc_list_foreach(it, &cache->entries, node)
+    {
+
+        if (strcmp(url, it->url) == 0
+         && (username == NULL) == (it->username == NULL)
+         && (username != NULL ? strcmp(username, it->username) == 0 : true))
+        {
+            vlc_list_remove(&it->node);
+            vlc_cond_signal(&cache->cond);
+            vlc_mutex_unlock(&cache->lock);
+            return it;
+        }
+    }
+
+    vlc_mutex_unlock(&cache->lock);
+
+    return NULL;
+}
diff --git a/modules/access/cache.h b/modules/access/cache.h
new file mode 100644
index **********..4c1c136719
--- /dev/null
+++ b/modules/access/cache.h
@@ -0,0 +1,135 @@
+/*****************************************************************************
+ * cache.h: access cache helper
+ *****************************************************************************
+ * Copyright (C) 2022 VLC authors and VideoLAN
+ *
+ * This program is free software; you can redistribute it and/or modify it
+ * under the terms of the GNU Lesser General Public License as published by
+ * the Free Software Foundation; either version 2.1 of the License, or
+ * (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU Lesser General Public License for more details.
+ *
+ * You should have received a copy of the GNU Lesser General Public License
+ * along with this program; if not, write to the Free Software Foundation,
+ * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
+ *****************************************************************************/
+
+#ifdef HAVE_CONFIG_H
+# include "config.h"
+#endif
+
+#include <vlc_common.h>
+#include <vlc_list.h>
+
+struct vlc_access_cache_entry
+{
+    void *context;
+
+    char *url;
+    char *username;
+
+    mtime_t timeout;
+    void (*free_cb)(void *context);
+
+    struct vlc_list node;
+};
+
+struct vlc_access_cache
+{
+    bool init;
+    vlc_mutex_t lock;
+    vlc_cond_t cond;
+    vlc_thread_t thread;
+    bool running;
+
+    struct vlc_list entries;
+};
+
+#define VLC_ACCESS_CACHE_INITIALIZER(name) { \
+    .init = false, \
+    .lock = VLC_STATIC_MUTEX, \
+    .running = false, \
+    .entries = VLC_LIST_INITIALIZER(&name.entries), \
+}
+
+static inline char *
+vlc_access_cache_entry_CreateSmbUrl(const char *server, const char *share)
+{
+    char *url;
+    if (asprintf(&url, "smb://%s/%s", server, share) == -1)
+        return NULL;
+    return url;
+}
+
+struct vlc_access_cache_entry *
+vlc_access_cache_entry_New(void *context, const char *url, const char *username,
+                           void (*free_cb)(void *context));
+
+static inline struct vlc_access_cache_entry *
+vlc_access_cache_entry_NewSmb(void *context, const char *server,
+                              const char *share, const char *username,
+                              void (*free_cb)(void *context))
+{
+    char *url = vlc_access_cache_entry_CreateSmbUrl(server, share);
+    if (url == NULL)
+        return NULL;
+
+    struct vlc_access_cache_entry *entry =
+        vlc_access_cache_entry_New(context, url, username, free_cb);
+    free(url);
+    return entry;
+}
+
+/* Delete the cache entry without firing the free_cb */
+void
+vlc_access_cache_entry_Delete(struct vlc_access_cache_entry *entry);
+
+void
+vlc_access_cache_Destroy(struct vlc_access_cache *cache);
+
+void
+vlc_access_cache_AddEntry(struct vlc_access_cache *cache,
+                          struct vlc_access_cache_entry *entry);
+
+struct vlc_access_cache_entry *
+vlc_access_cache_GetEntry(struct vlc_access_cache *cache,
+                          const char *url, const char *username);
+
+static inline struct vlc_access_cache_entry *
+vlc_access_cache_GetSmbEntry(struct vlc_access_cache *cache,
+                             const char *server, const char *share,
+                             const char *username)
+{
+    char *url = vlc_access_cache_entry_CreateSmbUrl(server, share);
+    if (url == NULL)
+        return NULL;
+
+    struct vlc_access_cache_entry *entry =
+        vlc_access_cache_GetEntry(cache, url, username);
+    free(url);
+
+    return entry;
+}
+
+#ifdef __has_attribute
+  #if __has_attribute(destructor)
+    #define VLC_ACCESS_CACHE_CAN_REGISTER
+  #endif
+#endif
+
+#ifdef VLC_ACCESS_CACHE_CAN_REGISTER
+#define VLC_ACCESS_CACHE_REGISTER(name) \
+static struct vlc_access_cache name = VLC_ACCESS_CACHE_INITIALIZER(name); \
+__attribute__((destructor)) static void vlc_access_cache_destructor_##name(void) \
+{ \
+    vlc_access_cache_Destroy(&name); \
+}
+#else
+#define VLC_ACCESS_CACHE_REGISTER(name) \
+static struct vlc_access_cache name = VLC_ACCESS_CACHE_INITIALIZER(name);
+#warning "can't register access cache"
+#endif
diff --git a/src/Makefile.am b/src/Makefile.am
index db7f93a6bb..dc19a5473c 100644
--- a/src/Makefile.am
+++ b/src/Makefile.am
@@ -61,6 +61,7 @@ pluginsinclude_HEADERS = \
 	../include/vlc_interface.h \
 	../include/vlc_keystore.h \
 	../include/vlc_main.h \
+	../include/vlc_list.h \
 	../include/vlc_md5.h \
 	../include/vlc_messages.h \
 	../include/vlc_meta.h \
-- 
2.39.3 (Apple Git-146)

