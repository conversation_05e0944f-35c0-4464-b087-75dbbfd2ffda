From 75ac3e752e4185f75f5f90f980c16f8622584d69 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Fri, 29 Sep 2017 14:49:02 +0200
Subject: [PATCH 10/49] contrib: use live555 version that is compatible with
 LGPL2

---
 contrib/src/live555/SHA512SUMS | 2 +-
 contrib/src/live555/rules.mak  | 5 +----
 2 files changed, 2 insertions(+), 5 deletions(-)

diff --git a/contrib/src/live555/SHA512SUMS b/contrib/src/live555/SHA512SUMS
index f459fb9d16..02b2a69734 100644
--- a/contrib/src/live555/SHA512SUMS
+++ b/contrib/src/live555/SHA512SUMS
@@ -1 +1 @@
-319639acef6474b2eec0bdfa3416ca3c88a60f57d9d22911eee018fc494978fde93a241556bc0ea309f0b6a35e0242bd44a8f4de83a845f80d9ca5f94254ade6  live.2016.11.28.tar.gz
+10846fd6d5482bbea131ae805137077997e9dec242665e3c01d699d5584154c65049e8c520ea855599e554154a148e61fea77b592d97c814a4a98c773658d8f5  live.2016.10.21.tar.gz
\ No newline at end of file
diff --git a/contrib/src/live555/rules.mak b/contrib/src/live555/rules.mak
index 262fb867bb..6bf42d4b42 100644
--- a/contrib/src/live555/rules.mak
+++ b/contrib/src/live555/rules.mak
@@ -1,14 +1,12 @@
 # live555
 
-LIVE555_VERSION := 2016.11.28
+LIVE555_VERSION := 2016.10.21
 LIVE555_FILE := live.$(LIVE555_VERSION).tar.gz
 LIVEDOTCOM_URL := http://live555.com/liveMedia/public/$(LIVE555_FILE)
 
 ifdef BUILD_NETWORK
-ifdef GNUV3
 PKGS += live555
 endif
-endif
 
 ifeq ($(call need_pkg,"live555"),)
 PKGS_FOUND += live555
@@ -100,7 +98,6 @@ endif
 SUBDIRS=groupsock liveMedia UsageEnvironment BasicUsageEnvironment
 
 .live555: live555
-	$(REQUIRE_GNUV3)
 	cd $< && for subdir in $(SUBDIRS); do \
 		echo "PREFIX = $(PREFIX)" >> $$subdir/Makefile.head && \
 		echo "LIBDIR = $(PREFIX)/lib" >> $$subdir/Makefile.head ; done
-- 
2.39.3 (Apple Git-146)

