From 4a9f8c5e649c3c5746c139d6359abb88b2890ab8 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Sat, 16 Apr 2022 00:44:03 +0200
Subject: [PATCH 42/49] smb2: remove unneeded compat code

thread_local is unavailable to 32bit on iOS and the code in question is
not needed as we have the latest libsmb2, so let's remove it.
---
 modules/access/smb2.c | 28 ----------------------------
 1 <USER> <GROUP>, 28 deletions(-)

diff --git a/modules/access/smb2.c b/modules/access/smb2.c
index b1bebf0b6b..3f4c96fddb 100644
--- a/modules/access/smb2.c
+++ b/modules/access/smb2.c
@@ -201,34 +201,6 @@ smb2_set_error(struct vlc_smb2_op *op, const char *psz_func, int err)
 
 #define VLC_SMB2_STATUS_DENIED(x) (x == -ECONNREFUSED || x == -EACCES)
 
-#if defined (__ELF__) || defined (__MACH__) /* weak support */
-/* There is no way to know if libsmb2 has these new symbols and we don't want
- * to increase the version requirement on VLC 3.0, therefore implement a weak
- * compat version. */
-const t_socket *
-smb2_get_fds(struct smb2_context *smb2, size_t *fd_count, int *timeout);
-int
-smb2_service_fd(struct smb2_context *smb2, int fd, int revents);
-
-__attribute__((weak)) const t_socket *
-smb2_get_fds(struct smb2_context *smb2, size_t *fd_count, int *timeout)
-{
-    (void) timeout;
-    static thread_local t_socket fd;
-
-    *fd_count = 1;
-    fd = smb2_get_fd(smb2);
-    return &fd;
-}
-
-__attribute__((weak)) int
-smb2_service_fd(struct smb2_context *smb2, int fd, int revents)
-{
-    (void) fd;
-    return smb2_service(smb2, revents);
-}
-#endif
-
 static int
 vlc_smb2_mainloop(struct vlc_smb2_op *op)
 {
-- 
2.39.3 (Apple Git-146)

