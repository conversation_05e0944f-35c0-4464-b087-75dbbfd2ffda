From 5428d750bd64e898e96035ba412d211a09bcbdd9 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 21 Feb 2020 12:34:07 +0200
Subject: [PATCH 25/49] Add property to configure an http access token

---
 modules/access/http/access.c   | 5 +++++
 modules/access/http/resource.c | 5 ++++-
 modules/access/http/resource.h | 1 +
 src/libvlc.c                   | 2 ++
 4 files changed, 12 insertions(+), 1 deletion(-)

diff --git a/modules/access/http/access.c b/modules/access/http/access.c
index 7eb9f0cc37..22edb93062 100644
--- a/modules/access/http/access.c
+++ b/modules/access/http/access.c
@@ -194,6 +194,8 @@ static int Open(vlc_object_t *obj)
     if (sys->resource == NULL)
         goto error;
 
+    sys->resource->token = var_InheritString(obj, "http-token");
+
     if (vlc_credential_get(&crd, obj, NULL, NULL, NULL, NULL))
         vlc_http_res_set_login(sys->resource,
                                crd.psz_username, crd.psz_password);
@@ -310,4 +312,7 @@ vlc_module_begin()
                   "e.g. \"FooBar/1.2.3\"."), true)
         change_safe()
         change_private()
+    add_string("http-token", NULL, N_("Token"), N_("Token"), true)
+        change_safe()
+        change_private()
 vlc_module_end()
diff --git a/modules/access/http/resource.c b/modules/access/http/resource.c
index 9a28bb50f3..9cc63f2bad 100644
--- a/modules/access/http/resource.c
+++ b/modules/access/http/resource.c
@@ -58,7 +58,9 @@ vlc_http_res_req(const struct vlc_http_resource *res, void *opaque)
     }
 
     /* Authentication */
-    if (res->username != NULL && res->password != NULL)
+    if (res->token != NULL)
+        vlc_http_msg_add_header(req, "Authorization", "Bearer %s", res->token);
+    else if (res->username != NULL && res->password != NULL)
         vlc_http_msg_add_creds_basic(req, false, res->username, res->password);
 
     /* Request context */
@@ -154,6 +156,7 @@ static void vlc_http_res_deinit(struct vlc_http_resource *res)
     free(res->path);
     free(res->authority);
     free(res->host);
+    free(res->token);
 
     if (res->response != NULL)
         vlc_http_msg_destroy(res->response);
diff --git a/modules/access/http/resource.h b/modules/access/http/resource.h
index 22b5abac64..fa924259bc 100644
--- a/modules/access/http/resource.h
+++ b/modules/access/http/resource.h
@@ -56,6 +56,7 @@ struct vlc_http_resource
     char *password;
     char *agent;
     char *referrer;
+    char *token;
 };
 
 int vlc_http_res_init(struct vlc_http_resource *,
diff --git a/src/libvlc.c b/src/libvlc.c
index 25f7c7d6d2..4c25865df4 100644
--- a/src/libvlc.c
+++ b/src/libvlc.c
@@ -280,6 +280,8 @@ int libvlc_InternalInit( libvlc_int_t *p_libvlc, int i_argc,
     var_Create( p_libvlc, "app-version", VLC_VAR_STRING );
     var_SetString( p_libvlc, "app-version", PACKAGE_VERSION );
 
+    var_Create( p_libvlc, "http-token", VLC_VAR_STRING );
+
     /* System specific configuration */
     system_Configure( p_libvlc, i_argc - vlc_optind, ppsz_argv + vlc_optind );
 
-- 
2.39.3 (Apple Git-146)

