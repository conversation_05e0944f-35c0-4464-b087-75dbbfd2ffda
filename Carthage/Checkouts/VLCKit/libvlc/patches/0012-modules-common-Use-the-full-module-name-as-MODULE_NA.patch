From 90e5209d8e140361e87da9594402de4429465aa3 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Hugo=20Beauz=C3=A9e-<PERSON><PERSON><PERSON>?= <<EMAIL>>
Date: Mon, 26 Mar 2018 16:44:44 +0200
Subject: [PATCH 12/49] modules:common: Use the full module name as MODULE_NAME

This avoid conflicts when linking modules staticly on platforms that don't
have objdump
---
 modules/common.am | 4 ++--
 1 file changed, 2 insertions(+), 2 deletions(-)

diff --git a/modules/common.am b/modules/common.am
index 1b9117c98d..fc67a39bca 100644
--- a/modules/common.am
+++ b/modules/common.am
@@ -16,12 +16,12 @@ LTLIBVLCCORE = $(top_builddir)/src/libvlccore.la
 
 # Module name from object or executable file name.
 MODULE_NAME = $$(p="$@"; p="$${p\#\#*/}"; p="$${p\#lib}"; p="$${p%_plugin*}"; p=$$(echo "$$p"|sed 's/-/_/g'); p="$${p%.lo}"; echo "$$p")
-
+MODULE_FULLNAME = $$(p="$@"; p="$${p\#lib}"; p="$${p%_plugin*}"; p=$$(echo "$$p"|sed 's/[-\/]/_/g'); p="$${p%.lo}"; echo "$$p")
 AM_CPPFLAGS = -DMODULE_STRING=\"$(MODULE_NAME)\"
 if HAVE_DYNAMIC_PLUGINS
 AM_CPPFLAGS += -D__PLUGIN__
 else
-AM_CPPFLAGS += -DMODULE_NAME=$(MODULE_NAME)
+AM_CPPFLAGS += -DMODULE_NAME=$(MODULE_FULLNAME)
 endif
 AM_CFLAGS =
 AM_CXXFLAGS =
-- 
2.39.3 (Apple Git-146)

