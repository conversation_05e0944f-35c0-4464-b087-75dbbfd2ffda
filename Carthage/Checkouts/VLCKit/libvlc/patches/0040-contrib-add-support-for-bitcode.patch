From bb872e2cc203a997815bc53bfeee08df0fb0299e Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Wed, 9 Mar 2022 08:15:53 +0100
Subject: [PATCH 40/49] contrib: add support for bitcode

---
 contrib/bootstrap    | 4 ++++
 contrib/src/main.mak | 8 ++++++++
 2 files changed, 12 insertions(+)

diff --git a/contrib/bootstrap b/contrib/bootstrap
index 4e0fd82a88..16fce10829 100755
--- a/contrib/bootstrap
+++ b/contrib/bootstrap
@@ -320,6 +320,10 @@ case "${OS}" in
 		then
 			add_make_enabled "HAVE_TVOS"
 		fi
+		if test "$BUILDWITHBITCODE"
+		then
+			add_make_enabled "HAVE_BITCODE_ENABLED"
+		fi
 		;;
 	*bsd*)
 		add_make_enabled "HAVE_BSD"
diff --git a/contrib/src/main.mak b/contrib/src/main.mak
index 2a7776caa1..6e59b1b98d 100644
--- a/contrib/src/main.mak
+++ b/contrib/src/main.mak
@@ -314,6 +314,11 @@ CFLAGS := $(CFLAGS) -g -O2
 CXXFLAGS := $(CXXFLAGS) -g -O2
 endif
 
+ifdef HAVE_BITCODE_ENABLED
+CFLAGS := $(CFLAGS) -fembed-bitcode
+CXXFLAGS := $(CXXFLAGS) -fembed-bitcode
+endif
+
 ifdef ENABLE_PDB
 ifdef HAVE_CLANG
 ifneq ($(findstring $(ARCH),i686 x86_64),)
@@ -404,6 +409,9 @@ MESONFLAGS += --buildtype debug
 else
 MESONFLAGS += --buildtype debugoptimized
 endif
+ifdef HAVE_BITCODE_ENABLED
+MESONFLAGS += -Db_bitcode=true
+endif
 
 ifdef HAVE_CROSS_COMPILE
 # When cross-compiling meson uses the env vars like
-- 
2.39.3 (Apple Git-146)

