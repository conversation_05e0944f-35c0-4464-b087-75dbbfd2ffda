From 6b3896841aa66ecf902276f82593af90c6d78fe4 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Wed, 31 Oct 2018 10:08:55 +0100
Subject: [PATCH 20/49] libvlc: media_player: Add record method

---
 include/vlc/libvlc_media_player.h | 13 +++++++++++++
 lib/media_player.c                | 21 +++++++++++++++++++++
 2 files changed, 34 insertions(+)

diff --git a/include/vlc/libvlc_media_player.h b/include/vlc/libvlc_media_player.h
index 6991ce94c7..564001c695 100644
--- a/include/vlc/libvlc_media_player.h
+++ b/include/vlc/libvlc_media_player.h
@@ -2110,6 +2110,19 @@ LIBVLC_API int libvlc_media_player_get_role(libvlc_media_player_t *p_mi);
  */
 LIBVLC_API int libvlc_media_player_set_role(libvlc_media_player_t *p_mi,
                                             unsigned role);
+/**
+ * Start/stop recording
+ *
+ * \version LibVLC 4.0.0 and later.
+ *
+ * \param p_mi media player
+ * \param enable true to start recording, false to stop
+ * \param path the path of the recording directory
+ * \return 0 on success, -1 on error
+ */
+LIBVLC_API int libvlc_media_player_record(libvlc_media_player_t *p_mi,
+                                          bool enable,
+                                          const char *path);
 
 /** @} audio */
 
diff --git a/lib/media_player.c b/lib/media_player.c
index e3b79eb697..c6250f1e4a 100644
--- a/lib/media_player.c
+++ b/lib/media_player.c
@@ -623,6 +623,7 @@ libvlc_media_player_new( libvlc_instance_t *instance )
     var_Create (mp, "rate", VLC_VAR_FLOAT|VLC_VAR_DOINHERIT);
     var_Create (mp, "sout", VLC_VAR_STRING);
     var_Create (mp, "demux-filter", VLC_VAR_STRING);
+    var_Create (mp, "input-record-path", VLC_VAR_STRING|VLC_VAR_DOINHERIT);
     var_Create (mp, "http-cookies", VLC_VAR_ADDRESS);
 
     /* Video */
@@ -2089,3 +2090,23 @@ int libvlc_media_player_get_role(libvlc_media_player_t *mp)
     free(str);
     return ret;
 }
+
+int libvlc_media_player_record( libvlc_media_player_t *p_mi,
+                                bool enable,
+                                const char *path)
+{
+    vlc_value_t val = { .psz_string = (char *)path };
+    input_thread_t *p_input_thread;
+
+    p_input_thread = libvlc_get_input_thread ( p_mi );
+    if( !p_input_thread )
+        return VLC_EGENERIC;
+
+    if( enable )
+        var_Set( p_mi, "input-record-path", val );
+
+    var_SetBool( p_input_thread, "record", enable);
+
+    vlc_object_release( p_input_thread );
+    return VLC_SUCCESS;
+}
-- 
2.39.3 (Apple Git-146)

