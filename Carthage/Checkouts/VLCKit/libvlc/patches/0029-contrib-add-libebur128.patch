From 01901a6ced1cd9a34eff2940dd03de0210adef08 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 12 Aug 2020 14:43:56 +0200
Subject: [PATCH 29/49] contrib: add libebur128

EBU R128 standard for loudness normalisation. It will be used for a future
ebur128 audio-filter that will measure loudness.
---
 contrib/src/libebur128/SHA512SUMS |  1 +
 contrib/src/libebur128/rules.mak  | 23 +++++++++++++++++++++++
 2 files changed, 24 insertions(+)
 create mode 100644 contrib/src/libebur128/SHA512SUMS
 create mode 100644 contrib/src/libebur128/rules.mak

diff --git a/contrib/src/libebur128/SHA512SUMS b/contrib/src/libebur128/SHA512SUMS
new file mode 100644
index 0000000000..37e9774d9c
--- /dev/null
+++ b/contrib/src/libebur128/SHA512SUMS
@@ -0,0 +1 @@
+d9aec133aca9240f7e859aae30cfcab07ba7aa20378187d53dfeec4cd3840d49bb117fa0698591bac04d95f9d8b5cd0b1fbe41c364694f2fa8267dd690b2dc02  libebur128-1.2.4.tar.gz
diff --git a/contrib/src/libebur128/rules.mak b/contrib/src/libebur128/rules.mak
new file mode 100644
index 0000000000..3ac03e8ce6
--- /dev/null
+++ b/contrib/src/libebur128/rules.mak
@@ -0,0 +1,23 @@
+# EBU R128 standard for loudness normalisation
+
+LIBEBUR128_VERSION := 1.2.4
+LIBEBUR128_URL := https://github.com/jiixyj/libebur128/archive/v$(LIBEBUR128_VERSION).tar.gz
+
+PKGS += libebur128
+ifeq ($(call need_pkg,"libebur128"),)
+PKGS_FOUND += libebur128
+endif
+
+$(TARBALLS)/libebur128-$(LIBEBUR128_VERSION).tar.gz:
+	$(call download_pkg,$(LIBEBUR128_URL),libebur128)
+
+.sum-libebur128: libebur128-$(LIBEBUR128_VERSION).tar.gz
+
+libebur128: libebur128-$(LIBEBUR128_VERSION).tar.gz .sum-libebur128
+	$(UNPACK)
+	$(MOVE)
+
+.libebur128: libebur128 toolchain.cmake
+	cd $< && $(HOSTVARS_PIC) $(CMAKE)
+	cd $< && $(MAKE) install
+	touch $@
-- 
2.39.3 (Apple Git-146)

