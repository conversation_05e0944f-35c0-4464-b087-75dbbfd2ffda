From 58fdb85525d4605cdcd788d790f9546d36d9eadf Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 13 Sep 2019 14:41:31 +0200
Subject: [PATCH 24/49] lib: media: fix media not being preparsed after a
 failure

After a failure (like when browsing a smb folder with credential), it was
impossible to preparse the same media again.
---
 lib/media.c | 5 ++++-
 1 file changed, 4 insertions(+), 1 deletion(-)

diff --git a/lib/media.c b/lib/media.c
index a74d57bdbd..b46a4c7277 100644
--- a/lib/media.c
+++ b/lib/media.c
@@ -296,7 +296,7 @@ static void send_parsed_changed( libvlc_media_t *p_md,
     }
 
     p_md->parsed_status = new_status;
-    if( p_md->parsed_status == libvlc_media_parsed_status_skipped )
+    if( p_md->parsed_status != libvlc_media_parsed_status_done )
         p_md->has_asked_preparse = false;
 
     vlc_mutex_unlock( &p_md->parsed_lock );
@@ -818,7 +818,10 @@ static int media_parse(libvlc_media_t *media, bool b_async,
     needed = !media->has_asked_preparse;
     media->has_asked_preparse = true;
     if (needed)
+    {
         media->is_parsed = false;
+        media->parsed_status = 0;
+    }
     vlc_mutex_unlock(&media->parsed_lock);
 
     if (needed)
-- 
2.39.3 (Apple Git-146)

