From 8c6eb1f0ff8d52bd33e594d05fa01c85c811fd53 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 18 Dec 2020 11:16:13 +0100
Subject: [PATCH 39/49] keystore: use the system keystore from the first try

Currently, all access modules are first requesting credentials without
triggering a dialog. This caused the system keystore to never be fetched
on the first try. Therefore some access modules could fallback to a
guest/anonymous account when a valid credential was saved on the system
keystore.

Now the system keystore will be fetched from the first try. Access
modules will still fallback to a guest/anonymous account if the system
keystore doesn't have any credentials (before prompting credentials via
a dialog).
---
 src/misc/keystore.c | 3 ---
 1 file changed, 3 deletions(-)

diff --git a/src/misc/keystore.c b/src/misc/keystore.c
index a002810174..5f417aa972 100644
--- a/src/misc/keystore.c
+++ b/src/misc/keystore.c
@@ -442,9 +442,6 @@ vlc_credential_get(vlc_credential *p_credential, vlc_object_t *p_parent,
         }
 
         case GET_FROM_KEYSTORE:
-            if (!psz_dialog_title || !psz_dialog_fmt)
-                return false;
-
             if (p_credential->p_keystore == NULL)
                 p_credential->p_keystore = vlc_keystore_create(p_parent);
             if (p_credential->p_keystore != NULL)
-- 
2.39.3 (Apple Git-146)

