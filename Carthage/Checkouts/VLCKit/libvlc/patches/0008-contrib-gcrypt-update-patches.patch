From bd65b69f0f633afffcf2f358573e75a4f757cd6e Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Mon, 12 Sep 2016 17:03:37 +0200
Subject: [PATCH 08/49] contrib/gcrypt: update patches

---
 .../src/gcrypt/fix-sha1-ssse3-for-clang.patch | 308 +++++++++++++++++-
 .../work-around-libtool-limitation.patch      |  16 +-
 2 files changed, 310 insertions(+), 14 deletions(-)

diff --git a/contrib/src/gcrypt/fix-sha1-ssse3-for-clang.patch b/contrib/src/gcrypt/fix-sha1-ssse3-for-clang.patch
index f1d3ccc71f..21d7a30c8d 100644
--- a/contrib/src/gcrypt/fix-sha1-ssse3-for-clang.patch
+++ b/contrib/src/gcrypt/fix-sha1-ssse3-for-clang.patch
@@ -1,16 +1,312 @@
+diff -ru libgcrypt/cipher/arcfour-amd64.S libgcrypt/cipher/arcfour-amd64.S
+--- libgcrypt/cipher/arcfour-amd64.S	2016-03-23 12:59:34.000000000 +0100
++++ libgcrypt/cipher/arcfour-amd64.S	2016-09-13 10:41:44.000000000 +0200
+@@ -19,16 +19,20 @@
+     defined(HAVE_COMPATIBLE_GCC_WIN64_PLATFORM_AS))
+ 
+ #ifdef HAVE_COMPATIBLE_GCC_AMD64_PLATFORM_AS
++#ifdef __APPLE__
++# define ELF(...) /*_*/
++#else
+ # define ELF(...) __VA_ARGS__
++#endif
+ #else
+ # define ELF(...) /*_*/
+ #endif
+ 
+ .text
+ .align 16
+-.globl _gcry_arcfour_amd64
++.globl __gcry_arcfour_amd64
+ ELF(.type _gcry_arcfour_amd64,@function)
+-_gcry_arcfour_amd64:
++__gcry_arcfour_amd64:
+ 	push	%rbp
+ 	push	%rbx
+ 	mov	%rdi,		%rbp	# key = ARG(key)
+diff -ru libgcrypt/cipher/des-amd64.S libgcrypt/cipher/des-amd64.S
+--- libgcrypt/cipher/des-amd64.S	2016-03-23 12:59:34.000000000 +0100
++++ libgcrypt/cipher/des-amd64.S	2016-09-13 10:44:32.000000000 +0200
+@@ -30,7 +30,11 @@
+ #endif
+ 
+ #ifdef HAVE_COMPATIBLE_GCC_AMD64_PLATFORM_AS
++#ifdef __APPLE__
++# define ELF(...) /*_*/
++#else
+ # define ELF(...) __VA_ARGS__
++#endif
+ #else
+ # define ELF(...) /*_*/
+ #endif
+@@ -191,10 +195,10 @@
+ 	movl   right##d, 4(io);
+ 
+ .align 8
+-.globl _gcry_3des_amd64_crypt_block
++.globl __gcry_3des_amd64_crypt_block
+ ELF(.type  _gcry_3des_amd64_crypt_block,@function;)
+ 
+-_gcry_3des_amd64_crypt_block:
++__gcry_3des_amd64_crypt_block:
+ 	/* input:
+ 	 *	%rdi: round keys, CTX
+ 	 *	%rsi: dst
+@@ -466,7 +470,7 @@
+ 
+ .align 8
+ ELF(.type  _gcry_3des_amd64_crypt_blk3,@function;)
+-_gcry_3des_amd64_crypt_blk3:
++__gcry_3des_amd64_crypt_blk3:
+ 	/* input:
+ 	 *  %rdi: round keys, CTX
+ 	 *  RL0d, RR0d, RL1d, RR1d, RL2d, RR2d: 3 input blocks
+@@ -538,9 +542,9 @@
+ ELF(.size _gcry_3des_amd64_crypt_blk3,.-_gcry_3des_amd64_crypt_blk3;)
+ 
+ .align 8
+-.globl  _gcry_3des_amd64_cbc_dec
++.globl  __gcry_3des_amd64_cbc_dec
+ ELF(.type   _gcry_3des_amd64_cbc_dec,@function;)
+-_gcry_3des_amd64_cbc_dec:
++__gcry_3des_amd64_cbc_dec:
+ 	/* input:
+ 	 *	%rdi: ctx, CTX
+ 	 *	%rsi: dst (3 blocks)
+@@ -574,7 +578,7 @@
+ 	bswapl RL2d;
+ 	bswapl RR2d;
+ 
+-	call _gcry_3des_amd64_crypt_blk3;
++	call __gcry_3des_amd64_crypt_blk3;
+ 
+ 	popq %rcx; /*iv*/
+ 	popq %rdx; /*src*/
+@@ -614,9 +618,9 @@
+ ELF(.size _gcry_3des_amd64_cbc_dec,.-_gcry_3des_amd64_cbc_dec;)
+ 
+ .align 8
+-.globl  _gcry_3des_amd64_ctr_enc
++.globl  __gcry_3des_amd64_ctr_enc
+ ELF(.type   _gcry_3des_amd64_ctr_enc,@function;)
+-_gcry_3des_amd64_ctr_enc:
++__gcry_3des_amd64_ctr_enc:
+ 	/* input:
+ 	 *	%rdi: ctx, CTX
+ 	 *	%rsi: dst (3 blocks)
+@@ -655,7 +659,7 @@
+ 	/* store new IV */
+ 	movq RT0, (RW2);
+ 
+-	call _gcry_3des_amd64_crypt_blk3;
++	call __gcry_3des_amd64_crypt_blk3;
+ 
+ 	popq %rdx; /*src*/
+ 	popq %rsi; /*dst*/
+@@ -692,9 +696,9 @@
+ ELF(.size _gcry_3des_amd64_cbc_dec,.-_gcry_3des_amd64_cbc_dec;)
+ 
+ .align 8
+-.globl  _gcry_3des_amd64_cfb_dec
++.globl  __gcry_3des_amd64_cfb_dec
+ ELF(.type   _gcry_3des_amd64_cfb_dec,@function;)
+-_gcry_3des_amd64_cfb_dec:
++__gcry_3des_amd64_cfb_dec:
+ 	/* input:
+ 	 *	%rdi: ctx, CTX
+ 	 *	%rsi: dst (3 blocks)
+@@ -731,7 +735,7 @@
+ 	movq 4 * 4(%rdx), RW0;
+ 	movq RW0, (RW2);
+ 
+-	call _gcry_3des_amd64_crypt_blk3;
++	call __gcry_3des_amd64_crypt_blk3;
+ 
+ 	popq %rdx; /*src*/
+ 	popq %rsi; /*dst*/
+diff -ru libgcrypt/cipher/poly1305-avx2-amd64.S libgcrypt/cipher/poly1305-avx2-amd64.S
+--- libgcrypt/cipher/poly1305-avx2-amd64.S	2016-03-23 12:59:34.000000000 +0100
++++ libgcrypt/cipher/poly1305-avx2-amd64.S	2016-09-13 10:43:05.000000000 +0200
+@@ -30,7 +30,11 @@
+     defined(ENABLE_AVX2_SUPPORT)
+ 
+ #ifdef HAVE_COMPATIBLE_GCC_AMD64_PLATFORM_AS
++#ifdef __APPLE__
++# define ELF(...) /*_*/
++#else
+ # define ELF(...) __VA_ARGS__
++#endif
+ #else
+ # define ELF(...) /*_*/
+ #endif
+@@ -40,9 +44,9 @@
+ 
+ 
+ .align 8
+-.globl _gcry_poly1305_amd64_avx2_init_ext
++.globl __gcry_poly1305_amd64_avx2_init_ext
+ ELF(.type  _gcry_poly1305_amd64_avx2_init_ext,@function;)
+-_gcry_poly1305_amd64_avx2_init_ext:
++__gcry_poly1305_amd64_avx2_init_ext:
+ .Lpoly1305_init_ext_avx2_local:
+ 	xor %edx, %edx
+ 	vzeroupper
+@@ -403,9 +407,9 @@
+ 
+ 
+ .align 8
+-.globl _gcry_poly1305_amd64_avx2_blocks
++.globl __gcry_poly1305_amd64_avx2_blocks
+ ELF(.type  _gcry_poly1305_amd64_avx2_blocks,@function;)
+-_gcry_poly1305_amd64_avx2_blocks:
++__gcry_poly1305_amd64_avx2_blocks:
+ .Lpoly1305_blocks_avx2_local:
+ 	vzeroupper
+ 	pushq %rbp
+@@ -729,9 +733,9 @@
+ 
+ 
+ .align 8
+-.globl _gcry_poly1305_amd64_avx2_finish_ext
++.globl __gcry_poly1305_amd64_avx2_finish_ext
+ ELF(.type  _gcry_poly1305_amd64_avx2_finish_ext,@function;)
+-_gcry_poly1305_amd64_avx2_finish_ext:
++__gcry_poly1305_amd64_avx2_finish_ext:
+ .Lpoly1305_finish_ext_avx2_local:
+ 	vzeroupper
+ 	pushq %rbp
+diff -ru libgcrypt/cipher/poly1305-sse2-amd64.S libgcrypt/cipher/poly1305-sse2-amd64.S
+--- libgcrypt/cipher/poly1305-sse2-amd64.S	2016-03-23 12:59:34.000000000 +0100
++++ libgcrypt/cipher/poly1305-sse2-amd64.S	2016-09-13 10:42:49.000000000 +0200
+@@ -29,7 +29,11 @@
+     defined(HAVE_COMPATIBLE_GCC_WIN64_PLATFORM_AS))
+ 
+ #ifdef HAVE_COMPATIBLE_GCC_AMD64_PLATFORM_AS
++#ifdef __APPLE__
++# define ELF(...) /*_*/
++#else
+ # define ELF(...) __VA_ARGS__
++#endif
+ #else
+ # define ELF(...) /*_*/
+ #endif
+@@ -39,9 +43,9 @@
+ 
+ 
+ .align 8
+-.globl _gcry_poly1305_amd64_sse2_init_ext
++.globl __gcry_poly1305_amd64_sse2_init_ext
+ ELF(.type  _gcry_poly1305_amd64_sse2_init_ext,@function;)
+-_gcry_poly1305_amd64_sse2_init_ext:
++__gcry_poly1305_amd64_sse2_init_ext:
+ .Lpoly1305_init_ext_x86_local:
+ 	xor %edx, %edx
+ 	pushq %r12
+@@ -285,9 +289,9 @@
+ 
+ 
+ .align 8
+-.globl _gcry_poly1305_amd64_sse2_finish_ext
++.globl __gcry_poly1305_amd64_sse2_finish_ext
+ ELF(.type  _gcry_poly1305_amd64_sse2_finish_ext,@function;)
+-_gcry_poly1305_amd64_sse2_finish_ext:
++__gcry_poly1305_amd64_sse2_finish_ext:
+ .Lpoly1305_finish_ext_x86_local:
+ 	pushq %rbp
+ 	movq %rsp, %rbp
+@@ -436,9 +440,9 @@
+ 
+ 
+ .align 8
+-.globl _gcry_poly1305_amd64_sse2_blocks
++.globl __gcry_poly1305_amd64_sse2_blocks
+ ELF(.type  _gcry_poly1305_amd64_sse2_blocks,@function;)
+-_gcry_poly1305_amd64_sse2_blocks:
++__gcry_poly1305_amd64_sse2_blocks:
+ .Lpoly1305_blocks_x86_local:
+ 	pushq %rbp
+ 	movq %rsp, %rbp
+diff -ru libgcrypt/cipher/sha1-avx-amd64.S libgcrypt/cipher/sha1-avx-amd64.S
+--- libgcrypt/cipher/sha1-avx-amd64.S	2016-07-14 11:19:17.000000000 +0200
++++ libgcrypt/cipher/sha1-avx-amd64.S	2016-09-13 10:41:55.000000000 +0200
+@@ -41,7 +41,11 @@
+ 
+ 
+ #ifdef HAVE_COMPATIBLE_GCC_AMD64_PLATFORM_AS
++#ifdef __APPLE__
++# define ELF(...) /*_*/
++#else
+ # define ELF(...) __VA_ARGS__
++#endif
+ #else
+ # define ELF(...) /*_*/
+ #endif
+@@ -215,10 +219,10 @@
+  *                                  size_t nblks)
+  */
+ .text
+-.globl _gcry_sha1_transform_amd64_avx
++.globl __gcry_sha1_transform_amd64_avx
+ ELF(.type _gcry_sha1_transform_amd64_avx,@function)
+ .align 16
+-_gcry_sha1_transform_amd64_avx:
++__gcry_sha1_transform_amd64_avx:
+   /* input:
+    *	%rdi: ctx, CTX
+    *	%rsi: data (64*nblks bytes)
+diff -ru libgcrypt/cipher/sha1-avx-bmi2-amd64.S libgcrypt/cipher/sha1-avx-bmi2-amd64.S
+--- libgcrypt/cipher/sha1-avx-bmi2-amd64.S	2016-03-23 12:59:34.000000000 +0100
++++ libgcrypt/cipher/sha1-avx-bmi2-amd64.S	2016-09-13 10:40:10.000000000 +0200
+@@ -42,7 +42,11 @@
+ 
+ 
+ #ifdef HAVE_COMPATIBLE_GCC_AMD64_PLATFORM_AS
++#ifdef __APPLE__
++# define ELF(...) /*_*/
++#else
+ # define ELF(...) __VA_ARGS__
++#endif
+ #else
+ # define ELF(...) /*_*/
+ #endif
+@@ -213,10 +217,10 @@
+  *                                      size_t nblks)
+  */
+ .text
+-.globl _gcry_sha1_transform_amd64_avx_bmi2
++.globl __gcry_sha1_transform_amd64_avx_bmi2
+ ELF(.type _gcry_sha1_transform_amd64_avx_bmi2,@function)
+ .align 16
+-_gcry_sha1_transform_amd64_avx_bmi2:
++__gcry_sha1_transform_amd64_avx_bmi2:
+   /* input:
+    *	%rdi: ctx, CTX
+    *	%rsi: data (64*nblks bytes)
 diff -ru libgcrypt/cipher/sha1-ssse3-amd64.S libgcrypt/cipher/sha1-ssse3-amd64.S
---- libgcrypt/cipher/sha1-ssse3-amd64.S	2014-08-21 14:50:39.000000000 +0200
-+++ libgcrypt/cipher/sha1-ssse3-amd64.S	2015-10-03 22:40:21.000000000 +0200
-@@ -217,10 +217,9 @@
-  * _gcry_sha1_transform_amd64_ssse3 (void *ctx, const unsigned char *data)
+--- libgcrypt/cipher/sha1-ssse3-amd64.S	2016-03-23 12:59:34.000000000 +0100
++++ libgcrypt/cipher/sha1-ssse3-amd64.S	2016-09-13 10:43:14.000000000 +0200
+@@ -41,7 +41,11 @@
+ 
+ 
+ #ifdef HAVE_COMPATIBLE_GCC_AMD64_PLATFORM_AS
++#ifdef __APPLE__
++# define ELF(...) /*_*/
++#else
+ # define ELF(...) __VA_ARGS__
++#endif
+ #else
+ # define ELF(...) /*_*/
+ #endif
+@@ -227,10 +231,10 @@
+  *                                   size_t nblks)
   */
  .text
 -.globl _gcry_sha1_transform_amd64_ssse3
--.type _gcry_sha1_transform_amd64_ssse3,@function
 +.globl __gcry_sha1_transform_amd64_ssse3
+ ELF(.type _gcry_sha1_transform_amd64_ssse3,@function)
  .align 16
 -_gcry_sha1_transform_amd64_ssse3:
 +__gcry_sha1_transform_amd64_ssse3:
    /* input:
     *	%rdi: ctx, CTX
-    *	%rsi: data (64 bytes)
+    *	%rsi: data (64*nblks bytes)
diff --git a/contrib/src/gcrypt/work-around-libtool-limitation.patch b/contrib/src/gcrypt/work-around-libtool-limitation.patch
index df97ffb488..957f268ad2 100644
--- a/contrib/src/gcrypt/work-around-libtool-limitation.patch
+++ b/contrib/src/gcrypt/work-around-libtool-limitation.patch
@@ -1,18 +1,18 @@
-diff -ru libgcrypt-broken/cipher/Makefile.am libgcrypt/cipher/Makefile.am
---- libgcrypt-broken/cipher/Makefile.am	2013-11-06 23:05:24.000000000 +0100
-+++ libgcrypt/cipher/Makefile.am	2013-11-06 23:21:25.000000000 +0100
+diff -ru libgcrypt/cipher/Makefile.am libgcrypt/cipher/Makefile.am
+--- libgcrypt/cipher/Makefile.am	2016-08-17 13:21:22.000000000 +0200
++++ libgcrypt/cipher/Makefile.am	2016-09-12 15:13:54.000000000 +0200
 @@ -19,6 +19,8 @@
  
  # Process this file with automake to produce Makefile.in
  
 +LIBTOOL=@LIBTOOL@ --tag=CC
 +
- EXTRA_DIST = Manifest
- 
  # Need to include ../src in addition to top_srcdir because gcrypt.h is
-diff -ru libgcrypt-broken/mpi/Makefile.am libgcrypt/mpi/Makefile.am
---- libgcrypt-broken/mpi/Makefile.am	2013-11-06 23:05:24.000000000 +0100
-+++ libgcrypt/mpi/Makefile.am	2013-11-06 23:22:04.000000000 +0100
+ # a built header.
+ AM_CPPFLAGS = -I../src -I$(top_srcdir)/src
+diff -ru libgcrypt/mpi/Makefile.am libgcrypt-fixed/mpi/Makefile.am
+--- libgcrypt/mpi/Makefile.am	2016-03-23 12:59:34.000000000 +0100
++++ libgcrypt/mpi/Makefile.am	2016-09-12 15:13:28.000000000 +0200
 @@ -23,6 +23,9 @@
  
  # Need to include ../src in addition to top_srcdir because gcrypt.h is
-- 
2.39.3 (Apple Git-146)

