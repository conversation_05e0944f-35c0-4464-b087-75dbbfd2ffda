From d45468a8ba68fa639ada072057b00015f2339230 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 30 Apr 2018 14:33:08 +0100
Subject: [PATCH 13/49] add auto deinterlacer-mode which is also valid

---
 lib/video.c | 3 ++-
 1 file changed, 2 insertions(+), 1 deletion(-)

diff --git a/lib/video.c b/lib/video.c
index 035cc0ebf1..a3e5b248e9 100644
--- a/lib/video.c
+++ b/lib/video.c
@@ -675,7 +675,8 @@ void libvlc_video_set_deinterlace( libvlc_media_player_t *p_mi,
      && strcmp (psz_mode, "discard")  && strcmp (psz_mode, "linear")
      && strcmp (psz_mode, "mean")     && strcmp (psz_mode, "x")
      && strcmp (psz_mode, "yadif")    && strcmp (psz_mode, "yadif2x")
-     && strcmp (psz_mode, "phosphor") && strcmp (psz_mode, "ivtc"))
+     && strcmp (psz_mode, "phosphor") && strcmp (psz_mode, "ivtc")
+     && strcmp (psz_mode, "auto"))
         return;
 
     if (*psz_mode)
-- 
2.39.3 (Apple Git-146)

