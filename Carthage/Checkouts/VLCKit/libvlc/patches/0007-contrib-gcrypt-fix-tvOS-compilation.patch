From 0c9a660d8d71480b8ac100d618852c9ca4f59faf Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Sat, 3 Oct 2015 22:45:14 +0200
Subject: [PATCH 07/49] contrib/gcrypt: fix tvOS compilation

---
 .../src/gcrypt/fix-sha1-ssse3-for-clang.patch    | 16 ++++++++++++++++
 contrib/src/gcrypt/rules.mak                     |  6 ++++++
 2 files changed, 22 insertions(+)
 create mode 100644 contrib/src/gcrypt/fix-sha1-ssse3-for-clang.patch

diff --git a/contrib/src/gcrypt/fix-sha1-ssse3-for-clang.patch b/contrib/src/gcrypt/fix-sha1-ssse3-for-clang.patch
new file mode 100644
index 0000000000..f1d3ccc71f
--- /dev/null
+++ b/contrib/src/gcrypt/fix-sha1-ssse3-for-clang.patch
@@ -0,0 +1,16 @@
+diff -ru libgcrypt/cipher/sha1-ssse3-amd64.S libgcrypt/cipher/sha1-ssse3-amd64.S
+--- libgcrypt/cipher/sha1-ssse3-amd64.S	2014-08-21 14:50:39.000000000 +0200
++++ libgcrypt/cipher/sha1-ssse3-amd64.S	2015-10-03 22:40:21.000000000 +0200
+@@ -217,10 +217,9 @@
+  * _gcry_sha1_transform_amd64_ssse3 (void *ctx, const unsigned char *data)
+  */
+ .text
+-.globl _gcry_sha1_transform_amd64_ssse3
+-.type _gcry_sha1_transform_amd64_ssse3,@function
++.globl __gcry_sha1_transform_amd64_ssse3
+ .align 16
+-_gcry_sha1_transform_amd64_ssse3:
++__gcry_sha1_transform_amd64_ssse3:
+   /* input:
+    *	%rdi: ctx, CTX
+    *	%rsi: data (64 bytes)
diff --git a/contrib/src/gcrypt/rules.mak b/contrib/src/gcrypt/rules.mak
index b90816f425..e7cc790295 100644
--- a/contrib/src/gcrypt/rules.mak
+++ b/contrib/src/gcrypt/rules.mak
@@ -16,6 +16,7 @@ gcrypt: libgcrypt-$(GCRYPT_VERSION).tar.bz2 .sum-gcrypt
 	$(UNPACK)
 	$(APPLY) $(SRC)/gcrypt/disable-tests-compilation.patch
 	$(APPLY) $(SRC)/gcrypt/work-around-libtool-limitation.patch
+	$(APPLY) $(SRC)/gcrypt/fix-sha1-ssse3-for-clang.patch
 	$(APPLY) $(SRC)/gcrypt/fix-pthread-detection.patch
 	$(APPLY) $(SRC)/gcrypt/0001-random-Don-t-assume-that-_WIN64-implies-x86_64.patch
 	$(APPLY) $(SRC)/gcrypt/0002-aarch64-mpi-Fix-building-the-mpi-aarch64-assembly-fo.patch
@@ -50,6 +51,11 @@ GCRYPT_EXTRA_CFLAGS = -fheinous-gnu-extensions
 else
 GCRYPT_EXTRA_CFLAGS =
 endif
+ifdef HAVE_TVOS
+ifeq ($(ARCH), x86_64)
+GCRYPT_CONF += --disable-asm --enable-ciphers=des,rfc2268,arcfour --enable-digests=md5,sha1,rmd160
+endif
+endif
 ifdef HAVE_MACOSX
 GCRYPT_CONF += --disable-aesni-support
 ifeq ($(ARCH),aarch64)
-- 
2.39.3 (Apple Git-146)

