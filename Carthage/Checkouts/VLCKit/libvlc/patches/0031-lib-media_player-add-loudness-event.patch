From 3fd2b2c8d9372497e39453c03bf17c33d1ebf26f Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 14 Aug 2020 13:22:10 +0200
Subject: [PATCH 31/49] lib: media_player: add loudness event

---
 include/vlc/libvlc_events.h |  6 ++++++
 lib/media_player.c          | 42 +++++++++++++++++++++++++++++++++++++
 src/audio_output/output.c   |  2 +-
 3 files changed, 49 insertions(+), 1 deletion(-)

diff --git a/include/vlc/libvlc_events.h b/include/vlc/libvlc_events.h
index a15389e795..fef21ba4fc 100644
--- a/include/vlc/libvlc_events.h
+++ b/include/vlc/libvlc_events.h
@@ -95,6 +95,7 @@ enum libvlc_event_e {
     libvlc_MediaPlayerAudioDevice,
     libvlc_MediaPlayerChapterChanged,
     libvlc_MediaPlayerRecordChanged,
+    libvlc_MediaPlayerLoudnessChanged,
 
     libvlc_MediaListItemAdded=0x200,
     libvlc_MediaListWillAddItem,
@@ -219,6 +220,11 @@ typedef struct libvlc_event_t
             int new_count;
         } media_player_vout;
 
+        struct
+        {
+            double momentary_loudness;
+        } media_player_loudness_changed;
+
         /* media list */
         struct
         {
diff --git a/lib/media_player.c b/lib/media_player.c
index 33dc76b971..d8d1a1b9d6 100644
--- a/lib/media_player.c
+++ b/lib/media_player.c
@@ -156,6 +156,24 @@ static void media_detach_preparsed_event( libvlc_media_t *p_md )
                       p_md );
 }
 
+static int
+loudness_meter_changed( vlc_object_t * p_this, char const * psz_cmd,
+                        vlc_value_t oldval, vlc_value_t newval,
+                        void * p_userdata )
+{
+    libvlc_media_player_t * p_mi = p_userdata;
+
+    struct vlc_audio_loudness_meter *meter = newval.p_address;
+    libvlc_event_t event = {
+        .type = libvlc_MediaPlayerLoudnessChanged,
+    };
+    event.u.media_player_loudness_changed.momentary_loudness = meter->loudness_momentary;
+    libvlc_event_send( &p_mi->event_manager, &event );
+
+    return VLC_SUCCESS;
+    (void) p_this; (void) psz_cmd; (void) oldval;
+}
+
 /*
  * Release the associated input thread.
  *
@@ -172,6 +190,13 @@ stop_input_thread( libvlc_media_player_t *p_mi )
         return NULL;
     p_mi->input.p_thread = NULL;
 
+    audio_output_t *aout = input_resource_HoldAout(p_mi->input.p_resource);
+    if (aout != NULL)
+    {
+        var_DelCallback(aout, "loudness-meter", loudness_meter_changed, p_mi);
+        vlc_object_release(aout);
+    }
+
     media_detach_preparsed_event( p_mi->p_md );
 
     var_DelCallback( p_input_thread, "can-seek",
@@ -774,6 +799,9 @@ libvlc_media_player_new( libvlc_instance_t *instance )
     var_Create (mp, "equalizer-vlcfreqs", VLC_VAR_BOOL);
     var_Create (mp, "equalizer-bands", VLC_VAR_STRING);
 
+    var_Create (mp, "audio-meter", VLC_VAR_STRING);
+    var_SetString (mp, "audio-meter", "ebur128");
+
     /* Initialize the shared HTTP cookie jar */
     vlc_value_t cookies;
     cookies.p_address = vlc_http_cookies_new();
@@ -1060,6 +1088,14 @@ create_input_thread( libvlc_media_player_t *p_mi )
     var_AddCallback( p_input_thread, "intf-event", input_event_changed, p_mi );
     add_es_callbacks( p_input_thread, p_mi );
 
+    audio_output_t *aout = input_resource_GetAout(p_mi->input.p_resource);
+    if( aout != NULL )
+    {
+        vlc_object_hold(aout);
+        var_AddCallback(aout, "loudness-meter", loudness_meter_changed, p_mi);
+        input_resource_PutAout(p_mi->input.p_resource, aout);
+    }
+
     if( input_Start( p_input_thread ) )
     {
         del_es_callbacks( p_input_thread, p_mi );
@@ -1070,8 +1106,14 @@ create_input_thread( libvlc_media_player_t *p_mi )
         input_Close( p_input_thread );
         media_detach_preparsed_event( p_mi->p_md );
         libvlc_printerr( "Input initialization failure" );
+        if (aout != NULL)
+        {
+            var_DelCallback(aout, "loudness-meter", loudness_meter_changed, p_mi);
+            vlc_object_release(aout);
+        }
         return NULL;
     }
+    vlc_object_release(aout);
 
     return p_input_thread;
 }
diff --git a/src/audio_output/output.c b/src/audio_output/output.c
index ee835ed9a2..d0ef9a5345 100644
--- a/src/audio_output/output.c
+++ b/src/audio_output/output.c
@@ -334,7 +334,7 @@ audio_output_t *aout_New (vlc_object_t *parent)
     text.psz_string = _("Audio visualizations");
     var_Change (aout, "audio-visual", VLC_VAR_SETTEXT, &text, NULL);
 
-    var_Create (aout, "audio-meter", VLC_VAR_STRING);
+    var_Create (aout, "audio-meter", VLC_VAR_STRING | VLC_VAR_DOINHERIT);
     var_AddCallback (aout, "audio-meter", MeterCallback, NULL);
     var_Create (aout, "ebur128-fullmeter", VLC_VAR_BOOL);
 
-- 
2.39.3 (Apple Git-146)

