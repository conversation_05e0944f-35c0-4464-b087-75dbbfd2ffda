From f0f435f3d47b111ef99688e7970fe5c3552e9e6b Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 16 Sep 2016 15:51:10 +0200
Subject: [PATCH 04/49] http: add vlc_http_cookies_clear

Clear all cookies without deleting the jar.
---
 include/vlc_http.h     | 1 +
 src/libvlccore.sym     | 1 +
 src/misc/httpcookies.c | 7 +++++--
 3 files changed, 7 insertions(+), 2 deletions(-)

diff --git a/include/vlc_http.h b/include/vlc_http.h
index a48ea79496..f6b0251779 100644
--- a/include/vlc_http.h
+++ b/include/vlc_http.h
@@ -71,6 +71,7 @@ VLC_API char *vlc_http_auth_FormatAuthorizationHeader
 typedef struct vlc_http_cookie_jar_t vlc_http_cookie_jar_t;
 
 VLC_API vlc_http_cookie_jar_t * vlc_http_cookies_new( void ) VLC_USED;
+VLC_API void vlc_http_cookies_clear( vlc_http_cookie_jar_t * p_jar );
 VLC_API void vlc_http_cookies_destroy( vlc_http_cookie_jar_t * p_jar );
 
 /**
diff --git a/src/libvlccore.sym b/src/libvlccore.sym
index a15ba0d923..db7f4030b9 100644
--- a/src/libvlccore.sym
+++ b/src/libvlccore.sym
@@ -128,6 +128,7 @@ vlc_http_auth_ParseAuthenticationInfoHeader
 vlc_http_auth_FormatAuthorizationHeader
 vlc_http_cookies_new
 vlc_http_cookies_destroy
+vlc_http_cookies_clear
 vlc_http_cookies_store
 vlc_http_cookies_fetch
 httpd_ClientIP
diff --git a/src/misc/httpcookies.c b/src/misc/httpcookies.c
index 77ab852b15..f229b85dea 100644
--- a/src/misc/httpcookies.c
+++ b/src/misc/httpcookies.c
@@ -281,7 +281,7 @@ vlc_http_cookie_jar_t * vlc_http_cookies_new(void)
     return jar;
 }
 
-void vlc_http_cookies_destroy( vlc_http_cookie_jar_t * p_jar )
+void vlc_http_cookies_clear( vlc_http_cookie_jar_t * p_jar )
 {
     if ( !p_jar )
         return;
@@ -290,8 +290,11 @@ void vlc_http_cookies_destroy( vlc_http_cookie_jar_t * p_jar )
         cookie_destroy( vlc_array_item_at_index( &p_jar->cookies, i ) );
 
     vlc_array_clear( &p_jar->cookies );
-    vlc_mutex_destroy( &p_jar->lock );
+}
 
+void vlc_http_cookies_destroy( vlc_http_cookie_jar_t * p_jar )
+{
+    vlc_http_cookies_clear( p_jar );
     free( p_jar );
 }
 
-- 
2.39.3 (Apple Git-146)

