From 0224d4d790235ecbe191747ab609f3c8af97bfa9 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Mon, 28 Jun 2021 13:11:15 +0200
Subject: [PATCH 36/49] http/cookie: fix double-free

---
 lib/media_player.c | 1 -
 1 file changed, 1 deletion(-)

diff --git a/lib/media_player.c b/lib/media_player.c
index b80cf4645b..31f340447e 100644
--- a/lib/media_player.c
+++ b/lib/media_player.c
@@ -928,7 +928,6 @@ static void libvlc_media_player_destroy( libvlc_media_player_t *p_mi )
     if ( cookies )
     {
         var_Destroy( p_mi, "http-cookies" );
-        vlc_http_cookies_destroy( cookies );
     }
 
     libvlc_instance_t *instance = p_mi->p_libvlc_instance;
-- 
2.39.3 (Apple Git-146)

