From f7a135506064c93a94c4fc9331954689fa549995 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 29 Jun 2023 13:37:29 +0200
Subject: [PATCH 48/49] contrib: upnp: add patch to fix potential busyloop

Not tested.
---
 .../upnp/0001-miniserver-fix-busy-loop.patch  | 118 ++++++++++++++++++
 contrib/src/upnp/rules.mak                    |   1 +
 2 files changed, 119 insertions(+)
 create mode 100644 contrib/src/upnp/0001-miniserver-fix-busy-loop.patch

diff --git a/contrib/src/upnp/0001-miniserver-fix-busy-loop.patch b/contrib/src/upnp/0001-miniserver-fix-busy-loop.patch
new file mode 100644
index 0000000000..9e81d17ece
--- /dev/null
+++ b/contrib/src/upnp/0001-miniserver-fix-busy-loop.patch
@@ -0,0 +1,118 @@
+From d3a50751e1513b912fa968eef1c7f4b290c22937 Mon Sep 17 00:00:00 2001
+From: <PERSON> Guillem <<EMAIL>>
+Date: Thu, 29 Jun 2023 13:34:13 +0200
+Subject: [PATCH] miniserver: fix busy loop
+
+---
+ upnp/src/genlib/miniserver/miniserver.c | 35 +++++++++++++++++++------
+ upnp/src/inc/ssdplib.h                  |  4 ++-
+ upnp/src/ssdp/ssdp_server.c             |  7 +++--
+ 3 files changed, 35 insertions(+), 11 deletions(-)
+
+diff --git a/upnp/src/genlib/miniserver/miniserver.c b/upnp/src/genlib/miniserver/miniserver.c
+index cffe7984..f41b03d2 100644
+--- a/upnp/src/genlib/miniserver/miniserver.c
++++ b/upnp/src/genlib/miniserver/miniserver.c
+@@ -537,10 +537,20 @@ static void web_server_accept(SOCKET lsock, fd_set *set)
+ 	#endif /* INTERNAL_WEB_SERVER */
+ }
+ 
+-static void ssdp_read(SOCKET rsock, fd_set *set)
++static void ssdp_read(SOCKET *rsock, fd_set *set)
+ {
+-	if (rsock != INVALID_SOCKET && FD_ISSET(rsock, set)) {
+-		readFromSSDPSocket(rsock);
++	if (*rsock != INVALID_SOCKET && FD_ISSET(*rsock, set)) {
++		int ret = readFromSSDPSocket(*rsock);
++		if (ret != 0) {
++			UpnpPrintf(UPNP_INFO,
++				MSERV,
++				__FILE__,
++				__LINE__,
++				"miniserver: Error in readFromSSDPSocket(%d): closing socket\n",
++                *rsock);
++			sock_close(*rsock);
++			*rsock = INVALID_SOCKET;
++		}
+ 	}
+ }
+ 
+@@ -584,6 +594,15 @@ static int receive_from_stopSock(SOCKET ssock, fd_set *set)
+ 				return 1;
+ 			}
+ 		}
++		else
++		{
++			UpnpPrintf(UPNP_INFO,
++				MSERV,
++				__FILE__,
++				__LINE__,
++				"miniserver: stopSock Error, aborting...\n");
++			return 1;
++		}
+ 	}
+ 
+ 	return 0;
+@@ -658,12 +677,12 @@ static void RunMiniServer(
+ 			web_server_accept(
+ 				miniSock->miniServerSock6UlaGua, &rdSet);
+ 	#ifdef INCLUDE_CLIENT_APIS
+-			ssdp_read(miniSock->ssdpReqSock4, &rdSet);
+-			ssdp_read(miniSock->ssdpReqSock6, &rdSet);
++			ssdp_read(&miniSock->ssdpReqSock4, &rdSet);
++			ssdp_read(&miniSock->ssdpReqSock6, &rdSet);
+ 	#endif /* INCLUDE_CLIENT_APIS */
+-			ssdp_read(miniSock->ssdpSock4, &rdSet);
+-			ssdp_read(miniSock->ssdpSock6, &rdSet);
+-			ssdp_read(miniSock->ssdpSock6UlaGua, &rdSet);
++			ssdp_read(&miniSock->ssdpSock4, &rdSet);
++			ssdp_read(&miniSock->ssdpSock6, &rdSet);
++			ssdp_read(&miniSock->ssdpSock6UlaGua, &rdSet);
+ 			stopSock = receive_from_stopSock(
+ 				miniSock->miniServerStopSock, &rdSet);
+ 		}
+diff --git a/upnp/src/inc/ssdplib.h b/upnp/src/inc/ssdplib.h
+index 64f500c8..3578a245 100644
+--- a/upnp/src/inc/ssdplib.h
++++ b/upnp/src/inc/ssdplib.h
+@@ -239,8 +239,10 @@ int ssdp_request_type(
+ 
+ /*!
+  * \brief This function reads the data from the ssdp socket.
++ *
++ * \return 0 on success; -1 on error.
+  */
+-void readFromSSDPSocket(
++int readFromSSDPSocket(
+ 	/* [in] SSDP socket. */
+ 	SOCKET socket);
+ 
+diff --git a/upnp/src/ssdp/ssdp_server.c b/upnp/src/ssdp/ssdp_server.c
+index 728f543d..c4f23f40 100644
+--- a/upnp/src/ssdp/ssdp_server.c
++++ b/upnp/src/ssdp/ssdp_server.c
+@@ -805,7 +805,7 @@ static void ssdp_event_handler_thread(
+ 	free_ssdp_event_handler_data(data);
+ }
+ 
+-void readFromSSDPSocket(SOCKET socket)
++int readFromSSDPSocket(SOCKET socket)
+ {
+ 	char *requestBuf = NULL;
+ 	char staticBuf[BUFSIZE];
+@@ -896,8 +896,11 @@ void readFromSSDPSocket(SOCKET socket)
+ 			if (ThreadPoolAdd(&gRecvThreadPool, &job, NULL) != 0)
+ 				free_ssdp_event_handler_data(data);
+ 		}
+-	} else
++		return 0;
++	} else {
+ 		free_ssdp_event_handler_data(data);
++		return -1;
++    }
+ }
+ 
+ /*!
+-- 
+2.39.2
+
diff --git a/contrib/src/upnp/rules.mak b/contrib/src/upnp/rules.mak
index 54202e106f..8167611fd7 100644
--- a/contrib/src/upnp/rules.mak
+++ b/contrib/src/upnp/rules.mak
@@ -56,6 +56,7 @@ endif
 ifdef HAVE_IOS
 	$(APPLY) $(SRC)/upnp/fix-reuseaddr-option.patch
 endif
+	$(APPLY) $(SRC)/upnp/0001-miniserver-fix-busy-loop.patch
 	$(UPDATE_AUTOCONFIG)
 	$(MOVE)
 
-- 
2.39.3 (Apple Git-146)

