From e36aba7dc8aec813ba1e33a7fe02039890b8c421 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 27 Mar 2018 16:52:35 +0200
Subject: [PATCH 16/49] chromecast: use vt encoder from avcodec

---
 modules/stream_out/chromecast/cast.cpp | 13 +++++++++++++
 1 file changed, 13 insertions(+)

diff --git a/modules/stream_out/chromecast/cast.cpp b/modules/stream_out/chromecast/cast.cpp
index d7afc90236..ce1f5d8a3e 100644
--- a/modules/stream_out/chromecast/cast.cpp
+++ b/modules/stream_out/chromecast/cast.cpp
@@ -950,12 +950,25 @@ static std::string GetVencX264Option( sout_stream_t * /* p_stream */,
     return ssout.str();
 }
 
+#ifdef __APPLE__
+static std::string GetVencAvcodecVTOption( sout_stream_t * /* p_stream */,
+                                           const video_format_t * p_vid,
+                                           int i_quality )
+{
+    std::stringstream ssout;
+    ssout << "venc=avcodec{codec=h264_videotoolbox,options{realtime=1}}";
+    return ssout.str();
+}
+#endif
 
 static struct
 {
     vlc_fourcc_t fcc;
     std::string (*get_opt)( sout_stream_t *, const video_format_t *, int);
 } venc_opt_list[] = {
+#ifdef __APPLE__
+    { .fcc = VLC_CODEC_H264, .get_opt = GetVencAvcodecVTOption },
+#endif
     { .fcc = VLC_CODEC_H264, .get_opt = GetVencX264Option },
     { .fcc = VLC_CODEC_VP8,  .get_opt = GetVencVPXOption },
     { .fcc = VLC_CODEC_H264, .get_opt = NULL },
-- 
2.39.3 (Apple Git-146)

