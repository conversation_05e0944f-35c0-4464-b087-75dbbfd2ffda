From 44cdb5dbc3f35921308f7f9dbd3e146fff26d4b0 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Mon, 1 Oct 2018 15:37:57 +0200
Subject: [PATCH 22/49] access_output: file: Add error dialog for write/open

---
 modules/access_output/file.c | 8 ++++++++
 1 file changed, 8 insertions(+)

diff --git a/modules/access_output/file.c b/modules/access_output/file.c
index ef4f2d18d1..55e4822b90 100644
--- a/modules/access_output/file.c
+++ b/modules/access_output/file.c
@@ -87,6 +87,9 @@ static ssize_t Write( sout_access_out_t *p_access, block_t *p_buffer )
         {
             if (errno == EINTR)
                 continue;
+            if (errno == ENOSPC)
+                vlc_dialog_display_error(p_access, "record",
+                                         "An error occurred during recording. Error: %s", vlc_strerror_c(errno));
             block_ChainRelease (p_buffer);
             msg_Err( p_access, "cannot write: %s", vlc_strerror_c(errno) );
             return -1;
@@ -304,8 +307,13 @@ static int Open( vlc_object_t *p_this )
             if (fd != -1)
                 break;
             if (fd == -1)
+            {
                 msg_Err (p_access, "cannot create %s: %s", path,
                          vlc_strerror_c(errno));
+
+                vlc_dialog_display_error(p_access, "record",
+                                         "An error occurred during recording. Error: %s", vlc_strerror_c(errno));
+            }
             if (overwrite || errno != EEXIST)
                 break;
             flags &= ~O_EXCL;
-- 
2.39.3 (Apple Git-146)

