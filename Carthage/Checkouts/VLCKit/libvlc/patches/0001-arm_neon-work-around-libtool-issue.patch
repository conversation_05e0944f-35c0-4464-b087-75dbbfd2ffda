From 8ecae1f604d4722bd1b4b9bd37c72f85888be102 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Thu, 18 Dec 2014 22:14:55 +0100
Subject: [PATCH 01/49] arm_neon: work-around libtool issue

---
 modules/arm_neon/Makefile.am | 2 ++
 1 file changed, 2 insertions(+)

diff --git a/modules/arm_neon/Makefile.am b/modules/arm_neon/Makefile.am
index 10f5c15d7c..0406ded7eb 100644
--- a/modules/arm_neon/Makefile.am
+++ b/modules/arm_neon/Makefile.am
@@ -1,3 +1,5 @@
+LIBTOOL=@LIBTOOL@ --tag=CC
+
 neondir = $(pluginsdir)/arm_neon
 
 libchroma_yuv_neon_plugin_la_SOURCES = \
-- 
2.39.3 (Apple Git-146)

