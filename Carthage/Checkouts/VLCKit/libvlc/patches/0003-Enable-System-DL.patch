From 8238620cc5e93b5edca647ec9df5ca9a459a21c9 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Fri, 22 Jul 2016 11:11:44 +0200
Subject: [PATCH 03/49] Enable System DL

---
 contrib/src/ffmpeg/patch-as-patch-can.patch | 20 ++++++++++++++++++++
 contrib/src/ffmpeg/rules.mak                |  4 ++++
 2 files changed, 24 insertions(+)
 create mode 100644 contrib/src/ffmpeg/patch-as-patch-can.patch

diff --git a/contrib/src/ffmpeg/patch-as-patch-can.patch b/contrib/src/ffmpeg/patch-as-patch-can.patch
new file mode 100644
index 0000000000..2d60cf7140
--- /dev/null
+++ b/contrib/src/ffmpeg/patch-as-patch-can.patch
@@ -0,0 +1,20 @@
+Only in ffmpeg: .DS_Store
+diff -ru ffmpeg/libavcodec/audiotoolboxdec.c ffmpeg-fix/libavcodec/audiotoolboxdec.c
+--- ffmpeg/libavcodec/audiotoolboxdec.c	2016-05-09 11:44:29.000000000 +0200
++++ ffmpeg-fix/libavcodec/audiotoolboxdec.c	2016-05-09 14:01:04.000000000 +0200
+@@ -345,7 +345,6 @@
+         avctx->bit_rate = bit_rate;
+         in_format.mSampleRate = avctx->sample_rate;
+ #endif
+-#if CONFIG_AC3_AT_DECODER || CONFIG_EAC3_AT_DECODER
+     } else if (pkt && pkt->size >= 7 &&
+                (avctx->codec_id == AV_CODEC_ID_AC3 ||
+                 avctx->codec_id == AV_CODEC_ID_EAC3)) {
+@@ -358,7 +357,6 @@
+         in_format.mChannelsPerFrame = hdr.channels;
+         avctx->frame_size = hdr.num_blocks * 256;
+         avctx->bit_rate = hdr.bit_rate;
+-#endif
+     } else {
+         in_format.mSampleRate = avctx->sample_rate ? avctx->sample_rate : 44100;
+         in_format.mChannelsPerFrame = avctx->channels ? avctx->channels : 1;
diff --git a/contrib/src/ffmpeg/rules.mak b/contrib/src/ffmpeg/rules.mak
index d0b72a5e4e..710c29369e 100644
--- a/contrib/src/ffmpeg/rules.mak
+++ b/contrib/src/ffmpeg/rules.mak
@@ -33,6 +33,9 @@ FFMPEGCONF = \
 	--disable-encoder=vorbis \
 	--disable-decoder=opus \
 	--enable-libgsm \
+	--disable-decoder=mlp \
+	--disable-demuxer=mlp \
+	--disable-parser=mlp \
 	--disable-debug \
 	--disable-avdevice \
 	--disable-devices \
@@ -259,6 +262,7 @@ endif
 ifdef USE_LIBAV
 	$(APPLY) $(SRC)/ffmpeg/libav_gsm.patch
 endif
+	$(APPLY) $(SRC)/ffmpeg/patch-as-patch-can.patch
 	$(MOVE)
 
 .ffmpeg: ffmpeg
-- 
2.39.3 (Apple Git-146)

