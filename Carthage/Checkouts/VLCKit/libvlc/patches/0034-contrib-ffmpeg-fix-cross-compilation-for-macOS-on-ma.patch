From f19d552c408b512c8e19a573ce0e217ef795152f Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Sat, 20 Feb 2021 13:19:24 +0100
Subject: [PATCH 34/49] contrib/ffmpeg: fix cross-compilation for macOS on
 macOS

---
 contrib/src/ffmpeg/rules.mak | 7 +++++--
 1 file changed, 5 insertions(+), 2 deletions(-)

diff --git a/contrib/src/ffmpeg/rules.mak b/contrib/src/ffmpeg/rules.mak
index 189428f29e..99fa0aa317 100644
--- a/contrib/src/ffmpeg/rules.mak
+++ b/contrib/src/ffmpeg/rules.mak
@@ -149,7 +149,7 @@ endif
 
 # Darwin
 ifdef HAVE_DARWIN_OS
-FFMPEGCONF += --arch=$(ARCH) --target-os=darwin --extra-cflags="$(CFLAGS)"
+FFMPEGCONF += --arch=$(ARCH) --target-os=darwin --extra-cflags="$(CFLAGS)" --enable-pic
 ifdef USE_FFMPEG
 FFMPEGCONF += --disable-lzma
 endif
@@ -157,11 +157,14 @@ ifeq ($(ARCH),x86_64)
 FFMPEGCONF += --cpu=core2
 endif
 ifdef HAVE_IOS
-FFMPEGCONF += --enable-pic --extra-ldflags="$(EXTRA_CFLAGS) -isysroot $(IOS_SDK)"
+FFMPEGCONF += --extra-ldflags="$(EXTRA_CFLAGS) -isysroot $(IOS_SDK)"
 ifdef HAVE_NEON
 FFMPEGCONF += --as="$(AS)"
 endif
 endif
+ifdef HAVE_MACOSX
+FFMPEGCONF += --extra-ldflags="$(EXTRA_LDFLAGS) -isysroot $(MACOSX_SDK)"
+endif
 endif
 
 # Linux
-- 
2.39.3 (Apple Git-146)

