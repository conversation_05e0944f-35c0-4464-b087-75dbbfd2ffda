From 1d8e291f917f0e54a38d99cdbdfe6489ab06533d Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Hugo=20Beauz=C3=A9<PERSON>-<PERSON><PERSON><PERSON>?= <<EMAIL>>
Date: Fri, 29 Nov 2019 15:41:08 +0100
Subject: [PATCH 09/49] input: Extract attachment also when preparsing

---
 include/vlc/libvlc_events.h                  |  10 +
 include/vlc/libvlc_picture.h                 | 149 +++++++++++
 include/vlc/vlc.h                            |   1 +
 include/vlc_demux.h                          |   8 +-
 include/vlc_events.h                         |   8 +-
 include/vlc_input.h                          |  49 +---
 lib/Makefile.am                              |   5 +-
 lib/libvlc.sym                               |  12 +
 lib/libvlc_internal.h                        |   1 +
 lib/media.c                                  |  37 +++
 lib/media_discoverer.c                       |   1 +
 lib/media_list.c                             |   1 +
 lib/media_list_player.c                      |   1 +
 lib/media_player.c                           |   1 +
 lib/picture.c                                | 245 +++++++++++++++++++
 lib/picture_internal.h                       |  48 ++++
 modules/access/attachment.c                  |   2 +-
 modules/access/bluray.c                      |   6 +-
 modules/codec/libass.c                       |   2 +-
 modules/codec/subsusf.c                      |   2 +-
 modules/demux/avformat/demux.c               |   6 +-
 modules/demux/avi/avi.c                      |   4 +-
 modules/demux/flac.c                         |   4 +-
 modules/demux/mkv/demux.cpp                  |   2 -
 modules/demux/mkv/demux.hpp                  |   5 +-
 modules/demux/mkv/matroska_segment_parse.cpp |  38 ++-
 modules/demux/mkv/mkv.cpp                    |   9 +-
 modules/demux/mkv/mkv.hpp                    |  30 ---
 modules/demux/mp4/mp4.c                      | 192 ++++++++-------
 modules/demux/mpeg/ts.c                      |   4 +-
 modules/demux/ogg.c                          |   4 +-
 modules/text_renderer/freetype/freetype.c    |   8 +-
 src/Makefile.am                              |   1 +
 src/input/attachment.c                       |  96 ++++++++
 src/input/control.c                          |   4 +-
 src/input/input.c                            | 107 ++++----
 src/input/input_internal.h                   |   1 -
 src/input/meta.c                             |   4 +-
 src/libvlccore.sym                           |   3 +
 39 files changed, 833 insertions(+), 278 deletions(-)
 create mode 100644 include/vlc/libvlc_picture.h
 create mode 100644 lib/picture.c
 create mode 100644 lib/picture_internal.h
 create mode 100644 src/input/attachment.c

diff --git a/include/vlc/libvlc_events.h b/include/vlc/libvlc_events.h
index f8b0e9b5b2..2de48565c6 100644
--- a/include/vlc/libvlc_events.h
+++ b/include/vlc/libvlc_events.h
@@ -55,6 +55,12 @@ enum libvlc_event_e {
     libvlc_MediaFreed,
     libvlc_MediaStateChanged,
     libvlc_MediaSubItemTreeAdded,
+    /**
+     * One or more embedded thumbnails were found during the media preparsing
+     * The user can hold these picture(s) using libvlc_picture_retain if they
+     * wish to use them
+     */
+    libvlc_MediaAttachedThumbnailsFound,
 
     libvlc_MediaPlayerMediaChanged=0x100,
     libvlc_MediaPlayerNothingSpecial,
@@ -167,6 +173,10 @@ typedef struct libvlc_event_t
         {
             libvlc_media_t * item;
         } media_subitemtree_added;
+        struct
+        {
+            libvlc_picture_list_t* thumbnails;
+        } media_attached_thumbnails_found;
 
         /* media instance */
         struct
diff --git a/include/vlc/libvlc_picture.h b/include/vlc/libvlc_picture.h
new file mode 100644
index **********..770a13cc40
--- /dev/null
+++ b/include/vlc/libvlc_picture.h
@@ -0,0 +1,149 @@
+/*****************************************************************************
+ * libvlc_picture.h:  libvlc external API
+ *****************************************************************************
+ * Copyright (C) 2018 VLC authors and VideoLAN
+ *
+ * Authors: <AUTHORS>
+ *
+ * This program is free software; you can redistribute it and/or modify it
+ * under the terms of the GNU Lesser General Public License as published by
+ * the Free Software Foundation; either version 2.1 of the License, or
+ * (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU Lesser General Public License for more details.
+ *
+ * You should have received a copy of the GNU Lesser General Public License
+ * along with this program; if not, write to the Free Software Foundation,
+ * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
+ *****************************************************************************/
+
+#ifndef VLC_LIBVLC_PICTURE_H
+#define VLC_LIBVLC_PICTURE_H 1
+
+# ifdef __cplusplus
+extern "C" {
+# endif
+
+typedef struct libvlc_picture_t libvlc_picture_t;
+typedef struct libvlc_picture_list_t libvlc_picture_list_t;
+
+typedef enum libvlc_picture_type_t
+{
+    libvlc_picture_Argb,
+    libvlc_picture_Png,
+    libvlc_picture_Jpg,
+} libvlc_picture_type_t;
+
+/**
+ * Increment the reference count of this picture.
+ *
+ * \see libvlc_picture_release()
+ * \param pic A picture object
+ */
+LIBVLC_API void
+libvlc_picture_retain( libvlc_picture_t* pic );
+
+/**
+ * Decrement the reference count of this picture.
+ * When the reference count reaches 0, the picture will be released.
+ * The picture must not be accessed after calling this function.
+ *
+ * \see libvlc_picture_retain
+ * \param pic A picture object
+ */
+LIBVLC_API void
+libvlc_picture_release( libvlc_picture_t* pic );
+
+/**
+ * Saves this picture to a file. The image format is the same as the one
+ * returned by \link libvlc_picture_type \endlink
+ *
+ * \param pic A picture object
+ * \param path The path to the generated file
+ * \return 0 in case of success, -1 otherwise
+ */
+LIBVLC_API int
+libvlc_picture_save( const libvlc_picture_t* pic, const char* path );
+
+/**
+ * Returns the image internal buffer, including potential padding.
+ * The libvlc_picture_t owns the returned buffer, which must not be modified nor
+ * freed.
+ *
+ * \param pic A picture object
+ * \param size A pointer to a size_t that will hold the size of the buffer [required]
+ * \return A pointer to the internal buffer.
+ */
+LIBVLC_API const unsigned char*
+libvlc_picture_get_buffer( const libvlc_picture_t* pic, size_t *size );
+
+/**
+ * Returns the picture type
+ *
+ * \param pic A picture object
+ * \see libvlc_picture_type_t
+ */
+LIBVLC_API libvlc_picture_type_t
+libvlc_picture_type( const libvlc_picture_t* pic );
+
+/**
+ * Returns the image stride, ie. the number of bytes per line.
+ * This can only be called on images of type libvlc_picture_Argb
+ *
+ * \param pic A picture object
+ */
+LIBVLC_API unsigned int
+libvlc_picture_get_stride( const libvlc_picture_t* pic );
+
+/**
+ * Returns the width of the image in pixels
+ *
+ * \param pic A picture object
+ */
+LIBVLC_API unsigned int
+libvlc_picture_get_width( const libvlc_picture_t* pic );
+
+/**
+ * Returns the height of the image in pixels
+ *
+ * \param pic A picture object
+ */
+LIBVLC_API unsigned int
+libvlc_picture_get_height( const libvlc_picture_t* pic );
+
+/**
+ * Returns the time at which this picture was generated, in milliseconds
+ * \param pic A picture object
+ */
+LIBVLC_API libvlc_time_t
+libvlc_picture_get_time( const libvlc_picture_t* pic );
+
+/**
+ * Returns the number of pictures in the list
+ */
+LIBVLC_API size_t libvlc_picture_list_count( const libvlc_picture_list_t* list );
+
+/**
+ * Returns the picture at the provided index.
+ *
+ * If the index is out of bound, the result is undefined.
+ */
+LIBVLC_API libvlc_picture_t* libvlc_picture_list_at( const libvlc_picture_list_t* list,
+                                                     size_t index );
+
+/**
+ * Destroys a picture list and releases the pictures it contains
+ * \param list The list to destroy
+ *
+ * Calling this function with a NULL list is safe and will return immediatly
+ */
+LIBVLC_API void libvlc_picture_list_destroy( libvlc_picture_list_t* list );
+
+# ifdef __cplusplus
+}
+# endif
+
+#endif // VLC_LIBVLC_PICTURE_H
diff --git a/include/vlc/vlc.h b/include/vlc/vlc.h
index 6d25cd507c..87f7795c89 100644
--- a/include/vlc/vlc.h
+++ b/include/vlc/vlc.h
@@ -39,6 +39,7 @@ extern "C" {
 
 #include <vlc/libvlc.h>
 #include <vlc/libvlc_renderer_discoverer.h>
+#include <vlc/libvlc_picture.h>
 #include <vlc/libvlc_media.h>
 #include <vlc/libvlc_media_player.h>
 #include <vlc/libvlc_media_list.h>
diff --git a/include/vlc_demux.h b/include/vlc_demux.h
index f359f547fa..152ad66c0d 100644
--- a/include/vlc_demux.h
+++ b/include/vlc_demux.h
@@ -270,8 +270,12 @@ enum demux_query_e
     /* Meta data */
     DEMUX_HAS_UNSUPPORTED_META, /* arg1= bool *   res can fail    */
 
-    /* Attachments */
-    DEMUX_GET_ATTACHMENTS,      /* arg1=input_attachment_t***, int* res=can fail */
+    /*
+     * Fetches attachment from the demux.
+     * The returned attachments are owned by the demuxer and must not be modified
+     * arg1=input_attachment_t***, int* res=can fail
+     */
+    DEMUX_GET_ATTACHMENTS,
 
     /* RECORD you are ensured that it is never called twice with the same state
      * you should accept it only if the stream can be recorded without
diff --git a/include/vlc_events.h b/include/vlc_events.h
index 4cca73e784..8f4802aaec 100644
--- a/include/vlc_events.h
+++ b/include/vlc_events.h
@@ -105,6 +105,7 @@ typedef enum vlc_event_type_t {
     vlc_InputItemInfoChanged,
     vlc_InputItemErrorWhenReadingChanged,
     vlc_InputItemPreparseEnded,
+    vlc_InputItemAttachmentsFound,
 } vlc_event_type_t;
 
 typedef struct vlc_event_listeners_group_t
@@ -117,7 +118,7 @@ typedef struct vlc_event_manager_t
 {
     void * p_obj;
     vlc_mutex_t lock;
-    vlc_event_listeners_group_t events[vlc_InputItemPreparseEnded + 1];
+    vlc_event_listeners_group_t events[vlc_InputItemAttachmentsFound + 1];
 } vlc_event_manager_t;
 
 /* Event definition */
@@ -164,6 +165,11 @@ typedef struct vlc_event_t
         {
             int new_status;
         } input_item_preparse_ended;
+        struct input_item_attachments_found
+        {
+            input_attachment_t** attachments;
+            size_t count;
+        } input_item_attachments_found;
     } u;
 } vlc_event_t;
 
diff --git a/include/vlc_input.h b/include/vlc_input.h
index 0687836b1c..5f143d7e9a 100644
--- a/include/vlc_input.h
+++ b/include/vlc_input.h
@@ -164,50 +164,15 @@ struct input_attachment_t
     void *p_data;
 };
 
-static inline void vlc_input_attachment_Delete( input_attachment_t *a )
-{
-    if( !a )
-        return;
-
-    free( a->p_data );
-    free( a->psz_description );
-    free( a->psz_mime );
-    free( a->psz_name );
-    free( a );
-}
-
-static inline input_attachment_t *vlc_input_attachment_New( const char *psz_name,
-                                                            const char *psz_mime,
-                                                            const char *psz_description,
-                                                            const void *p_data,
-                                                            size_t i_data )
-{
-    input_attachment_t *a = (input_attachment_t *)malloc( sizeof (*a) );
-    if( unlikely(a == NULL) )
-        return NULL;
+VLC_API void vlc_input_attachment_Release( input_attachment_t *a );
 
-    a->psz_name = strdup( psz_name ? psz_name : "" );
-    a->psz_mime = strdup( psz_mime ? psz_mime : "" );
-    a->psz_description = strdup( psz_description ? psz_description : "" );
-    a->i_data = i_data;
-    a->p_data = malloc( i_data );
-    if( i_data > 0 && likely(a->p_data != NULL) )
-        memcpy( a->p_data, p_data, i_data );
+VLC_API input_attachment_t *vlc_input_attachment_New( const char *psz_name,
+                                                      const char *psz_mime,
+                                                      const char *psz_description,
+                                                      const void *p_data,
+                                                      size_t i_data );
 
-    if( unlikely(a->psz_name == NULL || a->psz_mime == NULL
-              || a->psz_description == NULL || (i_data > 0 && a->p_data == NULL)) )
-    {
-        vlc_input_attachment_Delete( a );
-        a = NULL;
-    }
-    return a;
-}
-
-static inline input_attachment_t *vlc_input_attachment_Duplicate( const input_attachment_t *a )
-{
-    return vlc_input_attachment_New( a->psz_name, a->psz_mime, a->psz_description,
-                                     a->p_data, a->i_data );
-}
+VLC_API input_attachment_t *vlc_input_attachment_Hold( input_attachment_t *a );
 
 /*****************************************************************************
  * input defines/constants.
diff --git a/lib/Makefile.am b/lib/Makefile.am
index e342c977c3..6c82fbda94 100644
--- a/lib/Makefile.am
+++ b/lib/Makefile.am
@@ -20,6 +20,7 @@ pkginclude_HEADERS = \
 	../include/vlc/libvlc_media_player.h \
 	../include/vlc/libvlc_vlm.h \
 	../include/vlc/libvlc_renderer_discoverer.h \
+	../include/vlc/libvlc_picture.h \
 	../include/vlc/vlc.h
 
 nodist_pkginclude_HEADERS = ../include/vlc/libvlc_version.h
@@ -35,6 +36,7 @@ libvlc_la_SOURCES = \
 	media_internal.h \
 	media_list_internal.h \
 	media_player_internal.h \
+	picture_internal.h \
 	renderer_discoverer_internal.h \
 	core.c \
 	dialog.c \
@@ -52,7 +54,8 @@ libvlc_la_SOURCES = \
 	media_list_path.h \
 	media_list_player.c \
 	media_library.c \
-	media_discoverer.c
+	media_discoverer.c \
+	picture.c
 EXTRA_DIST = libvlc.pc.in libvlc.sym ../include/vlc/libvlc_version.h.in
 
 libvlc_la_LIBADD = \
diff --git a/lib/libvlc.sym b/lib/libvlc.sym
index 8ae5da8d0f..fc68e55f64 100644
--- a/lib/libvlc.sym
+++ b/lib/libvlc.sym
@@ -316,3 +316,15 @@ libvlc_wait
 libvlc_audio_filter_list_get
 libvlc_video_filter_list_get
 libvlc_module_description_list_release
+libvlc_picture_retain
+libvlc_picture_release
+libvlc_picture_save
+libvlc_picture_get_buffer
+libvlc_picture_type
+libvlc_picture_get_stride
+libvlc_picture_get_width
+libvlc_picture_get_height
+libvlc_picture_get_time
+libvlc_picture_list_at
+libvlc_picture_list_count
+libvlc_picture_list_destroy
diff --git a/lib/libvlc_internal.h b/lib/libvlc_internal.h
index a6ba458d54..b36321b75e 100644
--- a/lib/libvlc_internal.h
+++ b/lib/libvlc_internal.h
@@ -31,6 +31,7 @@
 
 #include <vlc/libvlc.h>
 #include <vlc/libvlc_dialog.h>
+#include <vlc/libvlc_picture.h>
 #include <vlc/libvlc_media.h>
 #include <vlc/libvlc_events.h>
 
diff --git a/lib/media.c b/lib/media.c
index f44a3ea3c3..bf73044d61 100644
--- a/lib/media.c
+++ b/lib/media.c
@@ -31,6 +31,7 @@
 #include <vlc/libvlc.h>
 #include <vlc/libvlc_media.h>
 #include <vlc/libvlc_media_list.h> // For the subitems, here for convenience
+#include <vlc/libvlc_picture.h>
 #include <vlc/libvlc_events.h>
 
 #include <vlc_common.h>
@@ -44,6 +45,7 @@
 #include "libvlc_internal.h"
 #include "media_internal.h"
 #include "media_list_internal.h"
+#include "picture_internal.h"
 
 static const vlc_meta_type_t libvlc_to_vlc_meta[] =
 {
@@ -247,6 +249,33 @@ static void input_item_duration_changed( const vlc_event_t *p_event,
     libvlc_event_send( &p_md->event_manager, &event );
 }
 
+static void input_item_attachments_found( const vlc_event_t *p_event,
+                                                 void * user_data )
+{
+    libvlc_media_t * p_md = user_data;
+    libvlc_event_t event;
+
+    libvlc_picture_list_t* list = libvlc_picture_list_from_attachments(
+                p_event->u.input_item_attachments_found.attachments,
+                p_event->u.input_item_attachments_found.count );
+    if( !list )
+        return;
+    if( !libvlc_picture_list_count(list) )
+    {
+        libvlc_picture_list_destroy( list );
+        return;
+    }
+
+    /* Construct the event */
+    event.type = libvlc_MediaAttachedThumbnailsFound;
+    event.u.media_attached_thumbnails_found.thumbnails = list;
+
+    /* Send the event */
+    libvlc_event_send( &p_md->event_manager, &event );
+
+    libvlc_picture_list_destroy( list );
+}
+
 static void send_parsed_changed( libvlc_media_t *p_md,
                                  libvlc_media_parsed_status_t new_status )
 {
@@ -339,6 +368,10 @@ static void install_input_item_observer( libvlc_media_t *p_md )
                       vlc_InputItemPreparseEnded,
                       input_item_preparse_ended,
                       p_md );
+    vlc_event_attach( &p_md->p_input_item->event_manager,
+                      vlc_InputItemAttachmentsFound,
+                      input_item_attachments_found,
+                      p_md );
 }
 
 /**************************************************************************
@@ -362,6 +395,10 @@ static void uninstall_input_item_observer( libvlc_media_t *p_md )
                       vlc_InputItemPreparseEnded,
                       input_item_preparse_ended,
                       p_md );
+    vlc_event_detach( &p_md->p_input_item->event_manager,
+                      vlc_InputItemAttachmentsFound,
+                      input_item_attachments_found,
+                      p_md );
 }
 
 /**************************************************************************
diff --git a/lib/media_discoverer.c b/lib/media_discoverer.c
index 367d7ea8e8..687709ce1c 100644
--- a/lib/media_discoverer.c
+++ b/lib/media_discoverer.c
@@ -31,6 +31,7 @@
 #include <vlc/libvlc_media.h>
 #include <vlc/libvlc_media_list.h>
 #include <vlc/libvlc_media_discoverer.h>
+#include <vlc/libvlc_picture.h>
 #include <vlc/libvlc_events.h>
 
 #include <vlc_services_discovery.h>
diff --git a/lib/media_list.c b/lib/media_list.c
index 7c93e41d5f..ef763fb1f0 100644
--- a/lib/media_list.c
+++ b/lib/media_list.c
@@ -30,6 +30,7 @@
 #include <vlc/libvlc.h>
 #include <vlc/libvlc_media.h>
 #include <vlc/libvlc_media_list.h>
+#include <vlc/libvlc_picture.h>
 #include <vlc/libvlc_events.h>
 
 #include <vlc_common.h>
diff --git a/lib/media_list_player.c b/lib/media_list_player.c
index c76cd6934e..64b5c0964e 100644
--- a/lib/media_list_player.c
+++ b/lib/media_list_player.c
@@ -33,6 +33,7 @@
 #include <vlc/libvlc_media_list.h>
 #include <vlc/libvlc_media_player.h>
 #include <vlc/libvlc_media_list_player.h>
+#include <vlc/libvlc_picture.h>
 #include <vlc/libvlc_events.h>
 #include <assert.h>
 
diff --git a/lib/media_player.c b/lib/media_player.c
index 0b324cc796..0a4b3e0a14 100644
--- a/lib/media_player.c
+++ b/lib/media_player.c
@@ -29,6 +29,7 @@
 #include <vlc/libvlc.h>
 #include <vlc/libvlc_renderer_discoverer.h>
 #include <vlc/libvlc_media.h>
+#include <vlc/libvlc_picture.h>
 #include <vlc/libvlc_events.h>
 
 #include <vlc_demux.h>
diff --git a/lib/picture.c b/lib/picture.c
new file mode 100644
index **********..ff32932453
--- /dev/null
+++ b/lib/picture.c
@@ -0,0 +1,245 @@
+/*****************************************************************************
+ * picture.c:  libvlc API picture management
+ *****************************************************************************
+ * Copyright (C) 2018 VLC authors and VideoLAN
+ *
+ * Authors: <AUTHORS>
+ *
+ * This program is free software; you can redistribute it and/or modify it
+ * under the terms of the GNU Lesser General Public License as published by
+ * the Free Software Foundation; either version 2.1 of the License, or
+ * (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU Lesser General Public License for more details.
+ *
+ * You should have received a copy of the GNU Lesser General Public License
+ * along with this program; if not, write to the Free Software Foundation,
+ * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
+ *****************************************************************************/
+
+#ifdef HAVE_CONFIG_H
+# include "config.h"
+#endif
+
+#include <vlc/libvlc.h>
+#include <vlc/libvlc_picture.h>
+
+#include <vlc_atomic.h>
+#include <vlc_picture.h>
+#include <vlc_block.h>
+#include <vlc_image.h>
+#include <vlc_input.h>
+#include <vlc_fs.h>
+#include <assert.h>
+
+#include "picture_internal.h"
+
+struct libvlc_picture_t
+{
+    atomic_uintptr_t refs;
+    libvlc_picture_type_t type;
+    block_t* converted;
+    video_format_t fmt;
+    libvlc_time_t time;
+    input_attachment_t* attachment;
+};
+
+struct libvlc_picture_list_t
+{
+    size_t count;
+    libvlc_picture_t* pictures[];
+};
+
+libvlc_picture_t* libvlc_picture_new( vlc_object_t* p_obj, picture_t* input,
+                                      libvlc_picture_type_t type,
+                                      unsigned int width, unsigned int height )
+{
+    libvlc_picture_t *pic = malloc( sizeof( *pic ) );
+    if ( unlikely( pic == NULL ) )
+        return NULL;
+    atomic_init(&pic->refs, 1);
+    pic->type = type;
+    pic->time = input->date / 1000;
+    pic->attachment = NULL;
+    vlc_fourcc_t format;
+    switch ( type )
+    {
+        case libvlc_picture_Argb:
+            format = VLC_CODEC_ARGB;
+            break;
+        case libvlc_picture_Jpg:
+            format = VLC_CODEC_JPEG;
+            break;
+        case libvlc_picture_Png:
+            format = VLC_CODEC_PNG;
+            break;
+        default:
+            vlc_assert_unreachable();
+    }
+    if ( picture_Export( p_obj, &pic->converted, &pic->fmt,
+                         input, format, width, height ) != VLC_SUCCESS )
+    {
+        free( pic );
+        return NULL;
+    }
+
+    return pic;
+}
+
+static void libvlc_picture_block_release( block_t* block )
+{
+    free( block );
+}
+
+static libvlc_picture_t* libvlc_picture_from_attachment( input_attachment_t* attachment )
+{
+    vlc_fourcc_t fcc = image_Mime2Fourcc( attachment->psz_mime );
+    if ( fcc != VLC_CODEC_PNG && fcc != VLC_CODEC_JPEG )
+        return NULL;
+    libvlc_picture_t *pic = malloc( sizeof( *pic ) );
+    if ( unlikely( pic == NULL ) )
+        return NULL;
+    pic->converted = malloc( sizeof( *pic->converted ) );
+    if ( unlikely( pic->converted == NULL ) )
+    {
+        free(pic);
+        return NULL;
+    }
+    atomic_init(&pic->refs, 1);
+    pic->attachment = vlc_input_attachment_Hold( attachment );
+    pic->time = VLC_TS_INVALID;
+    block_Init( pic->converted, attachment->p_data, attachment->i_data);
+    pic->converted->pf_release = libvlc_picture_block_release;
+    video_format_Init( &pic->fmt, fcc );
+    switch ( fcc )
+    {
+    case VLC_CODEC_PNG:
+        pic->type = libvlc_picture_Png;
+        break;
+    case VLC_CODEC_JPEG:
+        pic->type = libvlc_picture_Jpg;
+        break;
+    default:
+        vlc_assert_unreachable();
+    }
+
+    return pic;
+}
+
+void libvlc_picture_retain( libvlc_picture_t* pic )
+{
+    uintptr_t prev = atomic_fetch_add_explicit(&pic->refs, 1, memory_order_relaxed);
+    assert(prev);
+    VLC_UNUSED(prev);
+}
+
+void libvlc_picture_release( libvlc_picture_t* pic )
+{
+    uintptr_t prev = atomic_fetch_sub_explicit(&pic->refs, 1, memory_order_acq_rel);
+    assert(prev);
+
+    if (prev != 1)
+        return;
+    video_format_Clean( &pic->fmt );
+    if ( pic->converted )
+        block_Release( pic->converted );
+    if ( pic->attachment )
+        vlc_input_attachment_Release( pic->attachment );
+    free( pic );
+}
+
+int libvlc_picture_save( const libvlc_picture_t* pic, const char* path )
+{
+    FILE* file = vlc_fopen( path, "wb" );
+    if ( !file )
+        return -1;
+    size_t res = fwrite( pic->converted->p_buffer,
+                         pic->converted->i_buffer, 1, file );
+    fclose( file );
+    return res == 1 ? 0 : -1;
+}
+
+const unsigned char* libvlc_picture_get_buffer( const libvlc_picture_t* pic,
+                                                size_t *size )
+{
+    assert( size != NULL );
+    *size = pic->converted->i_buffer;
+    return pic->converted->p_buffer;
+}
+
+libvlc_picture_type_t libvlc_picture_type( const libvlc_picture_t* pic )
+{
+    return pic->type;
+}
+
+unsigned int libvlc_picture_get_stride( const libvlc_picture_t *pic )
+{
+    assert( pic->type == libvlc_picture_Argb );
+    return pic->fmt.i_width * pic->fmt.i_bits_per_pixel / 8;
+}
+
+unsigned int libvlc_picture_get_width( const libvlc_picture_t* pic )
+{
+    return pic->fmt.i_visible_width;
+}
+
+unsigned int libvlc_picture_get_height( const libvlc_picture_t* pic )
+{
+    return pic->fmt.i_visible_height;
+}
+
+libvlc_time_t libvlc_picture_get_time( const libvlc_picture_t* pic )
+{
+    return pic->time;
+}
+
+libvlc_picture_list_t* libvlc_picture_list_from_attachments( input_attachment_t** attachments,
+                                                             size_t nb_attachments )
+{
+    size_t size = 0;
+    libvlc_picture_list_t* list;
+    if ( mul_overflow( nb_attachments, sizeof( libvlc_picture_t* ), &size ) )
+        return NULL;
+    if ( add_overflow( size, sizeof( *list ), &size ) )
+        return NULL;
+
+    list = malloc( size );
+    if ( !list )
+        return NULL;
+    list->count = 0;
+    for ( size_t i = 0; i < nb_attachments; ++i )
+    {
+        input_attachment_t* a = attachments[i];
+        libvlc_picture_t *pic = libvlc_picture_from_attachment( a );
+        if( !pic )
+            continue;
+        list->pictures[list->count] = pic;
+        list->count++;
+    }
+    return list;
+}
+
+size_t libvlc_picture_list_count( const libvlc_picture_list_t* list )
+{
+    assert( list );
+    return list->count;
+}
+
+libvlc_picture_t* libvlc_picture_list_at( const libvlc_picture_list_t* list,
+                                          size_t index )
+{
+    assert( list );
+    return list->pictures[index];
+}
+
+void libvlc_picture_list_destroy( libvlc_picture_list_t* list )
+{
+    if ( !list )
+        return;
+    for ( size_t i = 0; i < list->count; ++i )
+        libvlc_picture_release( list->pictures[i] );
+    free( list );
+}
diff --git a/lib/picture_internal.h b/lib/picture_internal.h
new file mode 100644
index **********..1724a8791d
--- /dev/null
+++ b/lib/picture_internal.h
@@ -0,0 +1,48 @@
+/*****************************************************************************
+ * picture_internal.h:  libvlc API picture management
+ *****************************************************************************
+ * Copyright (C) 1998-2018 VLC authors and VideoLAN
+ *
+ * Authors: <AUTHORS>
+ *
+ * This program is free software; you can redistribute it and/or modify it
+ * under the terms of the GNU Lesser General Public License as published by
+ * the Free Software Foundation; either version 2.1 of the License, or
+ * (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU Lesser General Public License for more details.
+ *
+ * You should have received a copy of the GNU Lesser General Public License
+ * along with this program; if not, write to the Free Software Foundation,
+ * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
+ *****************************************************************************/
+
+#ifndef PICTURE_INTERNAL_H
+#define PICTURE_INTERNAL_H
+
+#include <vlc_picture.h>
+
+/**
+ * \brief libvlc_picture_new Wraps a libvlccore's picture_t to a libvlc_picture_t
+ * \param p_obj A vlc object
+ * \param p_input Input picture
+ * \param i_type Desired converted picture type
+ * \param i_width Converted picture width
+ * \param i_height Converted picture height
+ * \return An opaque libvlc_picture_t
+ *
+ * The picture refcount is left untouched by this function, but is converted to
+ * the required format and stored as a block_t
+ * The returned picture must be released through libvlc_picture_release
+ */
+libvlc_picture_t* libvlc_picture_new( vlc_object_t* p_obj, picture_t* p_pic,
+                                      libvlc_picture_type_t i_format,
+                                      unsigned int i_width, unsigned int i_height );
+
+libvlc_picture_list_t* libvlc_picture_list_from_attachments( input_attachment_t** attachments,
+                                                             size_t nb_attachments );
+
+#endif /* PICTURE_INTERNAL_H */
diff --git a/modules/access/attachment.c b/modules/access/attachment.c
index 9623f82369..b4eddfaeba 100644
--- a/modules/access/attachment.c
+++ b/modules/access/attachment.c
@@ -104,7 +104,7 @@ static void Close(vlc_object_t *object)
     stream_t     *access = (stream_t *)object;
     access_sys_t *sys = access->p_sys;
 
-    vlc_input_attachment_Delete(sys->attachment);
+    vlc_input_attachment_Release(sys->attachment);
 }
 
 /* */
diff --git a/modules/access/bluray.c b/modules/access/bluray.c
index 8dcfeee3b4..b92c000c2a 100644
--- a/modules/access/bluray.c
+++ b/modules/access/bluray.c
@@ -1044,7 +1044,7 @@ static void blurayClose(vlc_object_t *object)
     TAB_CLEAN(p_sys->i_title, p_sys->pp_title);
 
     for (int i = 0; i < p_sys->i_attachments; i++)
-      vlc_input_attachment_Delete(p_sys->attachments[i]);
+      vlc_input_attachment_Release(p_sys->attachments[i]);
     TAB_CLEAN(p_sys->i_attachments, p_sys->attachments);
 
     ARRAY_RESET(p_sys->events_delayed);
@@ -2463,9 +2463,7 @@ static int blurayControl(demux_t *p_demux, int query, va_list args)
             return VLC_EGENERIC;
         for (int i = 0; i < p_sys->i_attachments; i++)
         {
-            input_attachment_t *p_dup = vlc_input_attachment_Duplicate(p_sys->attachments[i]);
-            if(p_dup)
-                (*ppp_attach)[(*pi_int)++] = p_dup;
+            (*ppp_attach)[(*pi_int)++] = vlc_input_attachment_Hold(p_sys->attachments[i]);
         }
         return VLC_SUCCESS;
     }
diff --git a/modules/codec/libass.c b/modules/codec/libass.c
index e537bed2a0..c70b195f2c 100644
--- a/modules/codec/libass.c
+++ b/modules/codec/libass.c
@@ -198,7 +198,7 @@ static int Create( vlc_object_t *p_this )
 
             ass_add_font( p_sys->p_library, p_attach->psz_name, p_attach->p_data, p_attach->i_data );
         }
-        vlc_input_attachment_Delete( p_attach );
+        vlc_input_attachment_Release( p_attach );
     }
     free( pp_attachments );
 
diff --git a/modules/codec/subsusf.c b/modules/codec/subsusf.c
index 147f45f264..35fb6d763f 100644
--- a/modules/codec/subsusf.c
+++ b/modules/codec/subsusf.c
@@ -548,7 +548,7 @@ static int ParseImageAttachments( decoder_t *p_dec )
                 }
             }
         }
-        vlc_input_attachment_Delete( pp_attachments[ k ] );
+        vlc_input_attachment_Release( pp_attachments[ k ] );
     }
     free( pp_attachments );
 
diff --git a/modules/demux/avformat/demux.c b/modules/demux/avformat/demux.c
index 3b355bb3fa..f4c2280f3a 100644
--- a/modules/demux/avformat/demux.c
+++ b/modules/demux/avformat/demux.c
@@ -739,7 +739,7 @@ void avformat_CloseDemux( vlc_object_t *p_this )
     }
 
     for( int i = 0; i < p_sys->i_attachments; i++ )
-        vlc_input_attachment_Delete( p_sys->attachments[i] );
+        vlc_input_attachment_Release( p_sys->attachments[i] );
     TAB_CLEAN( p_sys->i_attachments, p_sys->attachments);
 
     if( p_sys->p_title )
@@ -1132,9 +1132,7 @@ static int Control( demux_t *p_demux, int i_query, va_list args )
 
             for( i = 0; i < p_sys->i_attachments; i++ )
             {
-                (*ppp_attach)[i] = vlc_input_attachment_Duplicate( p_sys->attachments[i] );
-                if((*ppp_attach)[i] == NULL)
-                    break;
+                (*ppp_attach)[i] = vlc_input_attachment_Hold( p_sys->attachments[i] );
             }
             *pi_int = i;
             return VLC_SUCCESS;
diff --git a/modules/demux/avi/avi.c b/modules/demux/avi/avi.c
index 4a8296d8ff..01f6947ab7 100644
--- a/modules/demux/avi/avi.c
+++ b/modules/demux/avi/avi.c
@@ -294,7 +294,7 @@ static void Close ( vlc_object_t * p_this )
         vlc_meta_Delete( p_sys->meta );
 
     for( unsigned i = 0; i < p_sys->i_attachment; i++)
-        vlc_input_attachment_Delete(p_sys->attachment[i]);
+        vlc_input_attachment_Release(p_sys->attachment[i]);
     free(p_sys->attachment);
 
     free( p_sys );
@@ -1898,7 +1898,7 @@ static int Control( demux_t *p_demux, int i_query, va_list args )
             {
                 *pi_int = p_sys->i_attachment;
                 for( unsigned i = 0; i < p_sys->i_attachment; i++ )
-                    (*ppp_attach)[i] = vlc_input_attachment_Duplicate( p_sys->attachment[i] );
+                    (*ppp_attach)[i] = vlc_input_attachment_Hold( p_sys->attachment[i] );
                 return VLC_SUCCESS;
             }
             return VLC_EGENERIC;
diff --git a/modules/demux/flac.c b/modules/demux/flac.c
index cb6c0e6e0c..5be19372e7 100644
--- a/modules/demux/flac.c
+++ b/modules/demux/flac.c
@@ -202,7 +202,7 @@ static void Close( vlc_object_t * p_this )
     TAB_CLEAN( p_sys->i_seekpoint, p_sys->seekpoint );
 
     for( int i = 0; i < p_sys->i_attachments; i++ )
-        vlc_input_attachment_Delete( p_sys->attachments[i] );
+        vlc_input_attachment_Release( p_sys->attachments[i] );
     TAB_CLEAN( p_sys->i_attachments, p_sys->attachments);
 
     for( int i = 0; i < p_sys->i_title_seekpoints; i++ )
@@ -584,7 +584,7 @@ static int Control( demux_t *p_demux, int i_query, va_list args )
             return VLC_EGENERIC;
         *pi_int = p_sys->i_attachments;
         for( int i = 0; i < p_sys->i_attachments; i++ )
-            (*ppp_attach)[i] = vlc_input_attachment_Duplicate( p_sys->attachments[i] );
+            (*ppp_attach)[i] = vlc_input_attachment_Hold( p_sys->attachments[i] );
         return VLC_SUCCESS;
     }
     else if( i_query == DEMUX_GET_TITLE_INFO )
diff --git a/modules/demux/mkv/demux.cpp b/modules/demux/mkv/demux.cpp
index 154deb0052..5d9a009e54 100644
--- a/modules/demux/mkv/demux.cpp
+++ b/modules/demux/mkv/demux.cpp
@@ -439,8 +439,6 @@ demux_sys_t::~demux_sys_t()
         delete opened_segments[i];
     for ( i=0; i<used_vsegments.size(); i++ )
         delete used_vsegments[i];
-    for ( i=0; i<stored_attachments.size(); i++ )
-        delete stored_attachments[i];
     if( meta ) vlc_meta_Delete( meta );
 
     while( titles.size() )
diff --git a/modules/demux/mkv/demux.hpp b/modules/demux/mkv/demux.hpp
index dc67c613fd..282b095614 100644
--- a/modules/demux/mkv/demux.hpp
+++ b/modules/demux/mkv/demux.hpp
@@ -290,6 +290,8 @@ typedef struct {
 #endif
 ////////////////////////////////////////
 
+#include <memory>
+
 class virtual_segment_c;
 class chapter_item_c;
 
@@ -362,7 +364,8 @@ public:
     size_t                           i_current_title;
 
     std::vector<matroska_stream_c*>  streams;
-    std::vector<attachment_c*>       stored_attachments;
+    std::vector<std::unique_ptr<input_attachment_t,
+                    void(*)(input_attachment_t*)>> stored_attachments;
     std::vector<matroska_segment_c*> opened_segments;
     std::vector<virtual_segment_c*>  used_vsegments;
     virtual_segment_c                *p_current_vsegment;
diff --git a/modules/demux/mkv/matroska_segment_parse.cpp b/modules/demux/mkv/matroska_segment_parse.cpp
index 37a650d539..2c1190ec5a 100644
--- a/modules/demux/mkv/matroska_segment_parse.cpp
+++ b/modules/demux/mkv/matroska_segment_parse.cpp
@@ -1333,33 +1333,31 @@ void matroska_segment_c::ParseAttachments( KaxAttachments *attachments )
         char *psz_tmp_utf8 =  ToUTF8( UTFstring( GetChild<KaxFileName>( *attachedFile ) ) );
         std::string attached_filename(psz_tmp_utf8);
         free(psz_tmp_utf8);
-        attachment_c *new_attachment = new attachment_c( attached_filename,
-                                                         GetChild<KaxMimeType>( *attachedFile ),
-                                                         img_data.GetSize() );
-
-        msg_Dbg( &sys.demuxer, "|   |   - %s (%s)", new_attachment->fileName(), new_attachment->mimeType() );
-
-        if( new_attachment->init() )
-        {
-            memcpy( new_attachment->p_data, img_data.GetBuffer(), img_data.GetSize() );
-            sys.stored_attachments.push_back( new_attachment );
-            if( !strncmp( new_attachment->mimeType(), "image/", 6 ) )
+        auto new_attachment = vlc_input_attachment_New( attached_filename.c_str(),
+                                                        GetChild<KaxMimeType>( *attachedFile ).GetValue().c_str(),
+                                                        nullptr,
+                                                        img_data.GetBuffer(),
+                                                        img_data.GetSize() );
+        if (!new_attachment)
+            continue;
+        msg_Dbg( &sys.demuxer, "|   |   - %s (%s)", new_attachment->psz_name,
+                 new_attachment->psz_mime );
+
+        if( !strncmp( new_attachment->psz_mime, "image/", 6 ) )
+        {
+            char *psz_url;
+            if( asprintf( &psz_url, "attachment://%s",
+                          new_attachment->psz_name ) >= 0 )
             {
-                char *psz_url;
-                if( asprintf( &psz_url, "attachment://%s",
-                              new_attachment->fileName() ) == -1 )
-                    continue;
                 if( !sys.meta )
                     sys.meta = vlc_meta_New();
                 vlc_meta_SetArtURL( sys.meta, psz_url );
                 free( psz_url );
             }
         }
-        else
-        {
-            delete new_attachment;
-        }
-
+        sys.stored_attachments.push_back(
+                    std::unique_ptr<input_attachment_t, decltype(&vlc_input_attachment_Release)>(
+                        new_attachment, &vlc_input_attachment_Release ) );
         attachedFile = &GetNextChild<KaxAttached>( *attachments, *attachedFile );
     }
 }
diff --git a/modules/demux/mkv/mkv.cpp b/modules/demux/mkv/mkv.cpp
index cce9e34002..e7ab81721a 100644
--- a/modules/demux/mkv/mkv.cpp
+++ b/modules/demux/mkv/mkv.cpp
@@ -313,14 +313,7 @@ static int Control( demux_t *p_demux, int i_query, va_list args )
                 return VLC_ENOMEM;
             for( size_t i = 0; i < p_sys->stored_attachments.size(); i++ )
             {
-                attachment_c *a = p_sys->stored_attachments[i];
-                (*ppp_attach)[i] = vlc_input_attachment_New( a->fileName(), a->mimeType(), NULL,
-                                                             a->p_data, a->size() );
-                if( !(*ppp_attach)[i] )
-                {
-                    free(*ppp_attach);
-                    return VLC_ENOMEM;
-                }
+                (*ppp_attach)[i] = vlc_input_attachment_Hold( p_sys->stored_attachments[i].get() );
             }
             return VLC_SUCCESS;
 
diff --git a/modules/demux/mkv/mkv.hpp b/modules/demux/mkv/mkv.hpp
index 27a1566c43..a78eecd532 100644
--- a/modules/demux/mkv/mkv.hpp
+++ b/modules/demux/mkv/mkv.hpp
@@ -125,36 +125,6 @@ void BlockDecode( demux_t *p_demux, KaxBlock *block, KaxSimpleBlock *simpleblock
                   vlc_tick_t i_pts, vlc_tick_t i_duration, bool b_key_picture,
                   bool b_discardable_picture );
 
-class attachment_c
-{
-public:
-    attachment_c( const std::string& _psz_file_name, const std::string& _psz_mime_type, int _i_size )
-        :i_size(_i_size)
-        ,psz_file_name( _psz_file_name)
-        ,psz_mime_type( _psz_mime_type)
-    {
-        p_data = NULL;
-    }
-    ~attachment_c() { free( p_data ); }
-
-    /* Allocs the data space. Returns true if allocation went ok */
-    bool init()
-    {
-        p_data = malloc( i_size );
-        return (p_data != NULL);
-    }
-
-    const char* fileName() const { return psz_file_name.c_str(); }
-    const char* mimeType() const { return psz_mime_type.c_str(); }
-    int         size() const    { return i_size; }
-
-    void          *p_data;
-private:
-    int            i_size;
-    std::string    psz_file_name;
-    std::string    psz_mime_type;
-};
-
 class matroska_segment_c;
 struct matroska_stream_c
 {
diff --git a/modules/demux/mp4/mp4.c b/modules/demux/mp4/mp4.c
index bcdb244807..5c9f068b7a 100644
--- a/modules/demux/mp4/mp4.c
+++ b/modules/demux/mp4/mp4.c
@@ -122,6 +122,8 @@ struct demux_sys_t
     } hacks;
 
     mp4_fragments_index_t *p_fragsindex;
+    ssize_t i_attachments;
+    input_attachment_t **pp_attachments;
 };
 
 #define DEMUX_INCREMENT (CLOCK_FREQ / 4) /* How far the pcr will go, each round */
@@ -798,6 +800,7 @@ static int Open( vlc_object_t * p_this )
     p_demux->pf_control = Control;
 
     p_sys->context.i_lastseqnumber = UINT32_MAX;
+    p_sys->i_attachments = -1;
 
     p_demux->p_sys = p_sys;
 
@@ -2053,112 +2056,123 @@ static int Control( demux_t *p_demux, int i_query, va_list args )
             input_attachment_t ***ppp_attach = va_arg( args, input_attachment_t*** );
             int *pi_int = va_arg( args, int * );
 
-            MP4_Box_t *p_udta = NULL;
-            size_t i_count = 0;
-            int i_index = 0;
-
-            /* Count number of total attachments */
-            for( ; psz_meta_roots[i_index] && !p_udta; i_index++ )
+            if( p_sys->i_attachments == -1 )
             {
-                p_udta = MP4_BoxGet( p_sys->p_root, psz_meta_roots[i_index] );
-                if ( p_udta )
-                    i_count += MP4_BoxCount( p_udta, "covr/data" );
-            }
+                MP4_Box_t *p_udta = NULL;
+                size_t i_count = 0;
+                int i_index = 0;
 
-            for ( size_t i=0; i< ARRAY_SIZE(rgi_pict_atoms); i++ )
-            {
-                char rgsz_path[5];
-                snprintf( rgsz_path, 5, "%4.4s", (char*)&rgi_pict_atoms[i] );
-                i_count += MP4_BoxCount( p_sys->p_root, rgsz_path );
-            }
+                /* Count number of total attachments */
+                for( ; psz_meta_roots[i_index] && !p_udta; i_index++ )
+                {
+                    p_udta = MP4_BoxGet( p_sys->p_root, psz_meta_roots[i_index] );
+                    if ( p_udta )
+                        i_count += MP4_BoxCount( p_udta, "covr/data" );
+                }
 
-            if ( i_count == 0 )
-                return VLC_EGENERIC;
+                for ( size_t i=0; i< ARRAY_SIZE(rgi_pict_atoms); i++ )
+                {
+                    char rgsz_path[5];
+                    snprintf( rgsz_path, 5, "%4.4s", (char*)&rgi_pict_atoms[i] );
+                    i_count += MP4_BoxCount( p_sys->p_root, rgsz_path );
+                }
 
-            *ppp_attach = (input_attachment_t**)
-                    vlc_alloc( i_count, sizeof(input_attachment_t*) );
-            if( !(*ppp_attach) ) return VLC_ENOMEM;
+                if ( i_count == 0 )
+                    return VLC_EGENERIC;
 
-            /* First add cover attachments */
-            i_count = 0;
-            size_t i_box_count = 0;
-            if ( p_udta )
-            {
-                const MP4_Box_t *p_data = MP4_BoxGet( p_udta, "covr/data" );
-                for( ; p_data; p_data = p_data->p_next )
+                p_sys->pp_attachments = (input_attachment_t**)
+                        vlc_alloc( i_count, sizeof(input_attachment_t*) );
+                if( !p_sys->pp_attachments ) return VLC_ENOMEM;
+
+                /* First add cover attachments */
+                i_count = 0;
+                size_t i_box_count = 0;
+                if ( p_udta )
                 {
-                    char *psz_mime;
-                    char *psz_filename;
-                    i_box_count++;
+                    const MP4_Box_t *p_data = MP4_BoxGet( p_udta, "covr/data" );
+                    for( ; p_data; p_data = p_data->p_next )
+                    {
+                        char *psz_mime;
+                        char *psz_filename;
+                        i_box_count++;
 
-                    if ( p_data->i_type != ATOM_data || !imageTypeCompatible( BOXDATA(p_data) ) )
-                        continue;
+                        if ( p_data->i_type != ATOM_data || !imageTypeCompatible( BOXDATA(p_data) ) )
+                            continue;
 
-                    switch( BOXDATA(p_data)->e_wellknowntype )
-                    {
-                    case DATA_WKT_PNG:
-                        psz_mime = strdup( "image/png" );
-                        break;
-                    case DATA_WKT_JPEG:
-                        psz_mime = strdup( "image/jpeg" );
-                        break;
-                    case DATA_WKT_BMP:
-                        psz_mime = strdup( "image/bmp" );
-                        break;
-                    default:
-                        continue;
+                        switch( BOXDATA(p_data)->e_wellknowntype )
+                        {
+                        case DATA_WKT_PNG:
+                            psz_mime = strdup( "image/png" );
+                            break;
+                        case DATA_WKT_JPEG:
+                            psz_mime = strdup( "image/jpeg" );
+                            break;
+                        case DATA_WKT_BMP:
+                            psz_mime = strdup( "image/bmp" );
+                            break;
+                        default:
+                            continue;
+                        }
+
+                        if ( asprintf( &psz_filename, "%s/covr/data[%"PRIu64"]", psz_meta_roots[i_index - 1],
+                                       (uint64_t) i_box_count - 1 ) >= 0 )
+                        {
+                            p_sys->pp_attachments[i_count++] =
+                                vlc_input_attachment_New( psz_filename, psz_mime, "Cover picture",
+                                    BOXDATA(p_data)->p_blob, BOXDATA(p_data)->i_blob );
+                            msg_Dbg( p_demux, "adding attachment %s", psz_filename );
+                            free( psz_filename );
+                        }
+
+                        free( psz_mime );
                     }
+                }
 
-                    if ( asprintf( &psz_filename, "%s/covr/data[%"PRIu64"]", psz_meta_roots[i_index - 1],
-                                   (uint64_t) i_box_count - 1 ) >= 0 )
+                /* Then quickdraw pict ones */
+                for ( size_t i=0; i< ARRAY_SIZE(rgi_pict_atoms); i++ )
+                {
+                    char rgsz_path[5];
+                    snprintf( rgsz_path, 5, "%4.4s", (char*)&rgi_pict_atoms[i] );
+                    const MP4_Box_t *p_pict = MP4_BoxGet( p_sys->p_root, rgsz_path );
+                    i_box_count = 0;
+                    for( ; p_pict; p_pict = p_pict->p_next )
                     {
-                        (*ppp_attach)[i_count++] =
-                            vlc_input_attachment_New( psz_filename, psz_mime, "Cover picture",
-                                BOXDATA(p_data)->p_blob, BOXDATA(p_data)->i_blob );
-                        msg_Dbg( p_demux, "adding attachment %s", psz_filename );
-                        free( psz_filename );
+                        if ( i_box_count++ == UINT16_MAX ) /* pnot only handles 2^16 */
+                            break;
+                        if ( p_pict->i_type != rgi_pict_atoms[i] )
+                            continue;
+                        char rgsz_location[12];
+                        snprintf( rgsz_location, 12, "%4.4s[%"PRIu16"]", (char*)&rgi_pict_atoms[i],
+                                  (uint16_t) i_box_count - 1 );
+                        p_sys->pp_attachments[i_count] = vlc_input_attachment_New( rgsz_location, "image/x-pict",
+                            "Quickdraw image", p_pict->data.p_binary->p_blob, p_pict->data.p_binary->i_blob );
+                        if ( !p_sys->pp_attachments[i_count] )
+                        {
+                            i_count = 0;
+                            break;
+                        }
+                        i_count++;
+                        msg_Dbg( p_demux, "adding attachment %s", rgsz_location );
                     }
-
-                    free( psz_mime );
                 }
-            }
 
-            /* Then quickdraw pict ones */
-            for ( size_t i=0; i< ARRAY_SIZE(rgi_pict_atoms); i++ )
-            {
-                char rgsz_path[5];
-                snprintf( rgsz_path, 5, "%4.4s", (char*)&rgi_pict_atoms[i] );
-                const MP4_Box_t *p_pict = MP4_BoxGet( p_sys->p_root, rgsz_path );
-                i_box_count = 0;
-                for( ; p_pict; p_pict = p_pict->p_next )
+                if ( i_count == 0 )
                 {
-                    if ( i_box_count++ == UINT16_MAX ) /* pnot only handles 2^16 */
-                        break;
-                    if ( p_pict->i_type != rgi_pict_atoms[i] )
-                        continue;
-                    char rgsz_location[12];
-                    snprintf( rgsz_location, 12, "%4.4s[%"PRIu16"]", (char*)&rgi_pict_atoms[i],
-                              (uint16_t) i_box_count - 1 );
-                    (*ppp_attach)[i_count] = vlc_input_attachment_New( rgsz_location, "image/x-pict",
-                        "Quickdraw image", p_pict->data.p_binary->p_blob, p_pict->data.p_binary->i_blob );
-                    if ( !(*ppp_attach)[i_count] )
-                    {
-                        i_count = 0;
-                        break;
-                    }
-                    i_count++;
-                    msg_Dbg( p_demux, "adding attachment %s", rgsz_location );
+                    free( *ppp_attach );
+                    return VLC_EGENERIC;
                 }
+                p_sys->i_attachments = i_count;
             }
-
-            if ( i_count == 0 )
+            *pi_int = p_sys->i_attachments;
+            *ppp_attach = calloc( p_sys->i_attachments, sizeof(**ppp_attach ) );
+            if( !*ppp_attach )
+                return VLC_ENOMEM;
+            for ( int i = 0; i < p_sys->i_attachments; ++i )
             {
-                free( *ppp_attach );
-                return VLC_EGENERIC;
+                (*ppp_attach)[i] = vlc_input_attachment_Hold( p_sys->pp_attachments[i] );
+                msg_Dbg( p_demux, "adding attachment %s", (*ppp_attach)[i]->psz_name );
             }
-
-            *pi_int = i_count;
-
+            *pi_int = p_sys->i_attachments;
             return VLC_SUCCESS;
         }
 
@@ -2257,6 +2271,10 @@ static void Close ( vlc_object_t * p_this )
         MP4_TrackClean( p_demux->out, &p_sys->track[i_track] );
     free( p_sys->track );
 
+    for ( int i = 0; i < p_sys->i_attachments; ++i )
+        vlc_input_attachment_Release( p_sys->pp_attachments[i] );
+    free( p_sys->pp_attachments );
+
     free( p_sys );
 }
 
diff --git a/modules/demux/mpeg/ts.c b/modules/demux/mpeg/ts.c
index e42346cc18..a6504ac8ff 100644
--- a/modules/demux/mpeg/ts.c
+++ b/modules/demux/mpeg/ts.c
@@ -552,7 +552,7 @@ static int Open( vlc_object_t *p_this )
 static void FreeDictAttachment( void *p_value, void *p_obj )
 {
     VLC_UNUSED(p_obj);
-    vlc_input_attachment_Delete( (input_attachment_t *) p_value );
+    vlc_input_attachment_Release( (input_attachment_t *) p_value );
 }
 
 static void Close( vlc_object_t *p_this )
@@ -1177,7 +1177,7 @@ static int Control( demux_t *p_demux, int i_query, va_list args )
                                          p_entry; p_entry = p_entry->p_next )
             {
                 msg_Err(p_demux, "GET ATTACHMENT %s", p_entry->psz_key);
-                (*ppp_attach)[*pi_int] = vlc_input_attachment_Duplicate(
+                (*ppp_attach)[*pi_int] = vlc_input_attachment_Hold(
                                                 (input_attachment_t *) p_entry->p_value );
                 if( (*ppp_attach)[*pi_int] )
                     (*pi_int)++;
diff --git a/modules/demux/ogg.c b/modules/demux/ogg.c
index f978ac639f..4e9cc11c81 100644
--- a/modules/demux/ogg.c
+++ b/modules/demux/ogg.c
@@ -835,7 +835,7 @@ static int Control( demux_t *p_demux, int i_query, va_list args )
                 return VLC_ENOMEM;
             *pi_int = p_sys->i_attachments;
             for( int i = 0; i < p_sys->i_attachments; i++ )
-                (*ppp_attach)[i] = vlc_input_attachment_Duplicate( p_sys->attachments[i] );
+                (*ppp_attach)[i] = vlc_input_attachment_Hold( p_sys->attachments[i] );
             return VLC_SUCCESS;
         }
 
@@ -2295,7 +2295,7 @@ static void Ogg_EndOfStream( demux_t *p_demux )
     p_ogg->p_meta = NULL;
 
     for(int i=0; i<p_ogg->i_attachments; i++)
-        vlc_input_attachment_Delete( p_ogg->attachments[i] );
+        vlc_input_attachment_Release( p_ogg->attachments[i] );
     TAB_CLEAN(p_ogg->i_attachments, p_ogg->attachments);
 
     for ( int i=0; i < p_ogg->i_seekpoints; i++ )
diff --git a/modules/text_renderer/freetype/freetype.c b/modules/text_renderer/freetype/freetype.c
index c30749ce29..4c99697a40 100644
--- a/modules/text_renderer/freetype/freetype.c
+++ b/modules/text_renderer/freetype/freetype.c
@@ -333,7 +333,7 @@ static int LoadFontsFromAttachments( filter_t *p_filter )
     if( !p_sys->pp_font_attachments )
     {
         for( int i = 0; i < i_attachments_cnt; ++i )
-            vlc_input_attachment_Delete( pp_attachments[ i ] );
+            vlc_input_attachment_Release( pp_attachments[ i ] );
         free( pp_attachments );
         return VLC_ENOMEM;
     }
@@ -399,7 +399,7 @@ static int LoadFontsFromAttachments( filter_t *p_filter )
         }
         else
         {
-            vlc_input_attachment_Delete( p_attach );
+            vlc_input_attachment_Release( p_attach );
         }
     }
 
@@ -436,7 +436,7 @@ error:
         free( psz_lc );
 
     for( int i = k + 1; i < i_attachments_cnt; ++i )
-        vlc_input_attachment_Delete( pp_attachments[ i ] );
+        vlc_input_attachment_Release( pp_attachments[ i ] );
 
     free( pp_attachments );
     return VLC_ENOMEM;
@@ -1536,7 +1536,7 @@ static void Destroy( vlc_object_t *p_this )
     if( p_sys->pp_font_attachments )
     {
         for( int k = 0; k < p_sys->i_font_attachments; k++ )
-            vlc_input_attachment_Delete( p_sys->pp_font_attachments[k] );
+            vlc_input_attachment_Release( p_sys->pp_font_attachments[k] );
 
         free( p_sys->pp_font_attachments );
     }
diff --git a/src/Makefile.am b/src/Makefile.am
index 03f68947b2..db7f93a6bb 100644
--- a/src/Makefile.am
+++ b/src/Makefile.am
@@ -240,6 +240,7 @@ libvlccore_la_SOURCES = \
 	input/info.h \
 	input/meta.c \
 	input/clock.h \
+	input/attachment.c \
 	input/decoder.h \
 	input/demux.h \
 	input/es_out.h \
diff --git a/src/input/attachment.c b/src/input/attachment.c
new file mode 100644
index **********..554f217a01
--- /dev/null
+++ b/src/input/attachment.c
@@ -0,0 +1,96 @@
+/*****************************************************************************
+ * attachment.c: Input attachments
+ *****************************************************************************
+ * Copyright (C) 1999-2020 VLC authors and VideoLAN
+ *
+ * This program is free software; you can redistribute it and/or modify it
+ * under the terms of the GNU Lesser General Public License as published by
+ * the Free Software Foundation; either version 2.1 of the License, or
+ * (at your option) any later version.
+ *
+ * This program is distributed in the hope that it will be useful,
+ * but WITHOUT ANY WARRANTY; without even the implied warranty of
+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
+ * GNU Lesser General Public License for more details.
+ *
+ * You should have received a copy of the GNU Lesser General Public License
+ * along with this program; if not, write to the Free Software Foundation,
+ * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
+ *****************************************************************************/
+
+#ifdef HAVE_CONFIG_H
+# include "config.h"
+#endif
+
+#include <vlc_common.h>
+#include <vlc_input.h>
+#include <vlc_atomic.h>
+#include <assert.h>
+
+struct input_attachment_priv
+{
+    input_attachment_t a;
+    atomic_uintptr_t refs;
+};
+
+static struct input_attachment_priv* input_attachment_priv( input_attachment_t* a )
+{
+    return container_of( a, struct input_attachment_priv, a );
+}
+
+void vlc_input_attachment_Release( input_attachment_t *a )
+{
+    if( !a )
+        return;
+
+    struct input_attachment_priv* p = input_attachment_priv( a );
+
+    uintptr_t prev = atomic_fetch_sub_explicit(&p->refs, 1, memory_order_acq_rel);
+    assert(prev);
+
+    if (prev != 1)
+        return;
+
+    free( a->p_data );
+    free( a->psz_description );
+    free( a->psz_mime );
+    free( a->psz_name );
+    free( p );
+}
+
+input_attachment_t *vlc_input_attachment_New( const char *psz_name,
+                                              const char *psz_mime,
+                                              const char *psz_description,
+                                              const void *p_data,
+                                              size_t i_data )
+{
+    struct input_attachment_priv *a = (struct input_attachment_priv *)malloc( sizeof (*a) );
+    if( unlikely(a == NULL) )
+        return NULL;
+
+    atomic_init(&a->refs, 1);
+    a->a.psz_name = strdup( psz_name ? psz_name : "" );
+    a->a.psz_mime = strdup( psz_mime ? psz_mime : "" );
+    a->a.psz_description = strdup( psz_description ? psz_description : "" );
+    a->a.i_data = i_data;
+    a->a.p_data = malloc( i_data );
+    if( i_data > 0 && likely(a->a.p_data != NULL) )
+        memcpy( a->a.p_data, p_data, i_data );
+
+    if( unlikely(a->a.psz_name == NULL || a->a.psz_mime == NULL
+              || a->a.psz_description == NULL || (i_data > 0 && a->a.p_data == NULL)) )
+    {
+        vlc_input_attachment_Release( &a->a );
+        return NULL;
+    }
+    return &a->a;
+}
+
+input_attachment_t *vlc_input_attachment_Hold( input_attachment_t *a )
+{
+    struct input_attachment_priv* p = input_attachment_priv( a );
+    uintptr_t prev = atomic_fetch_add_explicit(&p->refs, 1, memory_order_relaxed);
+    assert(prev);
+    VLC_UNUSED(prev);
+    return a;
+}
diff --git a/src/input/control.c b/src/input/control.c
index 0b37545a00..37e5155d67 100644
--- a/src/input/control.c
+++ b/src/input/control.c
@@ -467,7 +467,7 @@ int input_vaControl( input_thread_t *p_input, int i_query, va_list args )
             *pi_attachment = priv->i_attachment;
             *ppp_attachment = vlc_alloc( priv->i_attachment, sizeof(input_attachment_t*));
             for( int i = 0; i < priv->i_attachment; i++ )
-                (*ppp_attachment)[i] = vlc_input_attachment_Duplicate( priv->attachment[i] );
+                (*ppp_attachment)[i] = vlc_input_attachment_Hold( priv->attachment[i] );
 
             vlc_mutex_unlock( &priv->p_item->lock );
             return VLC_SUCCESS;
@@ -483,7 +483,7 @@ int input_vaControl( input_thread_t *p_input, int i_query, va_list args )
             {
                 if( !strcmp( priv->attachment[i]->psz_name, psz_name ) )
                 {
-                    *pp_attachment = vlc_input_attachment_Duplicate(priv->attachment[i] );
+                    *pp_attachment = vlc_input_attachment_Hold(priv->attachment[i] );
                     vlc_mutex_unlock( &priv->p_item->lock );
                     return VLC_SUCCESS;
                 }
diff --git a/src/input/input.c b/src/input/input.c
index fcd3ecbf55..ccfdf2b730 100644
--- a/src/input/input.c
+++ b/src/input/input.c
@@ -98,9 +98,8 @@ static void InputGetExtraFiles( input_thread_t *p_input,
                                 int *pi_list, char ***pppsz_list,
                                 const char **psz_access, const char *psz_path );
 
-static void AppendAttachment( int *pi_attachment, input_attachment_t ***ppp_attachment,
-                              const demux_t ***ppp_attachment_demux,
-                              int i_new, input_attachment_t **pp_new, const demux_t *p_demux );
+static void AppendAttachment(input_thread_t* p_input,
+                              int i_new, input_attachment_t **pp_new);
 
 #define SLAVE_ADD_NOFLAG    0
 #define SLAVE_ADD_FORCED    (1<<0)
@@ -324,7 +323,6 @@ static input_thread_t *Create( vlc_object_t *p_parent, input_item_t *p_item,
     memset( &priv->bookmark, 0, sizeof(priv->bookmark) );
     TAB_INIT( priv->i_bookmark, priv->pp_bookmark );
     TAB_INIT( priv->i_attachment, priv->attachment );
-    priv->attachment_demux = NULL;
     priv->p_sout   = NULL;
     priv->b_out_pace_control = false;
     priv->p_renderer = p_renderer && b_preparsing == false ?
@@ -1249,7 +1247,7 @@ static void LoadSlaves( input_thread_t *p_input )
             free( psz_mrl );
             /* Don't update item slaves for attachements */
         }
-        vlc_input_attachment_Delete( a );
+        vlc_input_attachment_Release( a );
     }
     free( pp_attachment );
     if( i_attachment > 0 )
@@ -1568,10 +1566,8 @@ do { \
     if( priv->i_attachment > 0 )
     {
         for( int i = 0; i < priv->i_attachment; i++ )
-            vlc_input_attachment_Delete( priv->attachment[i] );
+            vlc_input_attachment_Release( priv->attachment[i] );
         TAB_CLEAN( priv->i_attachment, priv->attachment );
-        free( priv->attachment_demux);
-        priv->attachment_demux = NULL;
     }
 
     /* clean bookmarks */
@@ -2851,17 +2847,6 @@ static input_source_t *InputSourceNew( input_thread_t *p_input,
             in->b_title_demux = true;
         }
 
-        int i_attachment;
-        input_attachment_t **attachment;
-        if( !demux_Control( in->p_demux, DEMUX_GET_ATTACHMENTS,
-                             &attachment, &i_attachment ) )
-        {
-            vlc_mutex_lock( &input_priv(p_input)->p_item->lock );
-            AppendAttachment( &input_priv(p_input)->i_attachment, &input_priv(p_input)->attachment, &input_priv(p_input)->attachment_demux,
-                              i_attachment, attachment, in->p_demux );
-            vlc_mutex_unlock( &input_priv(p_input)->p_item->lock );
-        }
-
         demux_Control( in->p_demux, DEMUX_GET_PTS_DELAY, &in->i_pts_delay );
         if( in->i_pts_delay > INPUT_PTS_DELAY_MAX )
             in->i_pts_delay = INPUT_PTS_DELAY_MAX;
@@ -2869,6 +2854,16 @@ static input_source_t *InputSourceNew( input_thread_t *p_input,
             in->i_pts_delay = 0;
     }
 
+    int i_attachment;
+    input_attachment_t **attachment;
+    if( !demux_Control( in->p_demux, DEMUX_GET_ATTACHMENTS,
+                         &attachment, &i_attachment ) )
+    {
+        vlc_mutex_lock( &input_priv(p_input)->p_item->lock );
+        AppendAttachment( p_input, i_attachment, attachment );
+        vlc_mutex_unlock( &input_priv(p_input)->p_item->lock );
+    }
+
     if( demux_Control( in->p_demux, DEMUX_GET_FPS, &in->f_fps ) )
         in->f_fps = 0.f;
 
@@ -2942,8 +2937,8 @@ static void InputSourceMeta( input_thread_t *p_input,
         if( p_demux_meta->i_attachments > 0 )
         {
             vlc_mutex_lock( &input_priv(p_input)->p_item->lock );
-            AppendAttachment( &input_priv(p_input)->i_attachment, &input_priv(p_input)->attachment, &input_priv(p_input)->attachment_demux,
-                              p_demux_meta->i_attachments, p_demux_meta->attachments, p_demux);
+            AppendAttachment( p_input, p_demux_meta->i_attachments,
+                              p_demux_meta->attachments );
             vlc_mutex_unlock( &input_priv(p_input)->p_item->lock );
         }
         module_unneed( p_demux, p_id3 );
@@ -3065,39 +3060,39 @@ static void InputMetaUser( input_thread_t *p_input, vlc_meta_t *p_meta )
     }
 }
 
-static void AppendAttachment( int *pi_attachment, input_attachment_t ***ppp_attachment,
-                              const demux_t ***ppp_attachment_demux,
-                              int i_new, input_attachment_t **pp_new, const demux_t *p_demux )
+static void AppendAttachment( input_thread_t *p_input, int i_new,
+                              input_attachment_t **pp_new )
 {
-    int i_attachment = *pi_attachment;
+    input_thread_private_t *priv = input_priv( p_input );
+    int i_attachment = priv->i_attachment;
     int i;
 
-    input_attachment_t **pp_att = realloc( *ppp_attachment,
+    if ( i_attachment + i_new == 0 )
+        /* nothing to do */
+        return;
+
+    input_attachment_t **pp_att = realloc( priv->attachment,
                     sizeof(*pp_att) * ( i_attachment + i_new ) );
     if( likely(pp_att) )
     {
-        *ppp_attachment = pp_att;
-        const demux_t **pp_attdmx = realloc( *ppp_attachment_demux,
-                        sizeof(*pp_attdmx) * ( i_attachment + i_new ) );
-        if( likely(pp_attdmx) )
+        priv->attachment = pp_att;
+        for( i = 0; i < i_new; i++ )
         {
-            *ppp_attachment_demux = pp_attdmx;
-
-            for( i = 0; i < i_new; i++ )
-            {
-                pp_att[i_attachment] = pp_new[i];
-                pp_attdmx[i_attachment++] = p_demux;
-            }
-            /* */
-            *pi_attachment = i_attachment;
-            free( pp_new );
-            return;
+            pp_att[i_attachment++] = pp_new[i];
         }
+        /* */
+        priv->i_attachment = i_attachment;
+        vlc_event_send( &priv->p_item->event_manager, &(vlc_event_t) {
+            .type = vlc_InputItemAttachmentsFound,
+            .u.input_item_attachments_found.attachments = pp_new,
+            .u.input_item_attachments_found.count = i_new } );
+        free( pp_new );
+        return;
     }
 
     /* on alloc errors */
     for( i = 0; i < i_new; i++ )
-        vlc_input_attachment_Delete( pp_new[i] );
+        vlc_input_attachment_Release( pp_new[i] );
     free( pp_new );
 }
 
@@ -3121,26 +3116,26 @@ static void InputUpdateMeta( input_thread_t *p_input, demux_t *p_demux )
     if( !demux_Control( p_demux, DEMUX_GET_ATTACHMENTS,
                         &attachment, &i_attachment ) )
     {
-        vlc_mutex_lock( &input_priv(p_input)->p_item->lock );
-        if( input_priv(p_input)->i_attachment > 0 )
+        input_thread_private_t *priv = input_priv(p_input);
+        vlc_mutex_lock( &priv->p_item->lock );
+        int nb_new = 0;
+        for ( int i = 0; i < i_attachment; ++i )
         {
-            int j = 0;
-            for( int i = 0; i < input_priv(p_input)->i_attachment; i++ )
+            bool is_new = true;
+            for( int j = 0; j < priv->i_attachment; ++j )
             {
-                if( input_priv(p_input)->attachment_demux[i] == p_demux )
-                    vlc_input_attachment_Delete( input_priv(p_input)->attachment[i] );
-                else
+                if( priv->attachment[j] == attachment[i] )
                 {
-                    input_priv(p_input)->attachment[j] = input_priv(p_input)->attachment[i];
-                    input_priv(p_input)->attachment_demux[j] = input_priv(p_input)->attachment_demux[i];
-                    j++;
+                    vlc_input_attachment_Release( attachment[i] );
+                    is_new = false;
+                    break;
                 }
             }
-            input_priv(p_input)->i_attachment = j;
+            if( is_new )
+                attachment[nb_new++] = attachment[i];
         }
-        AppendAttachment( &input_priv(p_input)->i_attachment, &input_priv(p_input)->attachment, &input_priv(p_input)->attachment_demux,
-                          i_attachment, attachment, p_demux );
-        vlc_mutex_unlock( &input_priv(p_input)->p_item->lock );
+        AppendAttachment( p_input, nb_new, attachment );
+        vlc_mutex_unlock( &priv->p_item->lock );
     }
 
     es_out_ControlSetMeta( input_priv(p_input)->p_es_out, p_meta );
diff --git a/src/input/input_internal.h b/src/input/input_internal.h
index ea02f39f5a..3ce907e2de 100644
--- a/src/input/input_internal.h
+++ b/src/input/input_internal.h
@@ -130,7 +130,6 @@ typedef struct input_thread_private_t
     /* Input attachment */
     int i_attachment;
     input_attachment_t **attachment;
-    const demux_t **attachment_demux;
 
     /* Main input properties */
 
diff --git a/src/input/meta.c b/src/input/meta.c
index 241b926079..16ad4edb2d 100644
--- a/src/input/meta.c
+++ b/src/input/meta.c
@@ -226,7 +226,7 @@ void input_ExtractAttachmentAndCacheArt( input_thread_t *p_input,
 
         if( !strcmp( a->psz_name, name ) )
         {
-            p_attachment = vlc_input_attachment_Duplicate( a );
+            p_attachment = vlc_input_attachment_Hold( a );
             break;
         }
     }
@@ -250,7 +250,7 @@ void input_ExtractAttachmentAndCacheArt( input_thread_t *p_input,
 
     playlist_SaveArt( VLC_OBJECT(p_input), p_item,
                       p_attachment->p_data, p_attachment->i_data, psz_type );
-    vlc_input_attachment_Delete( p_attachment );
+    vlc_input_attachment_Release( p_attachment );
 }
 
 int input_item_WriteMeta( vlc_object_t *obj, input_item_t *p_item )
diff --git a/src/libvlccore.sym b/src/libvlccore.sym
index db7f4030b9..8454600451 100644
--- a/src/libvlccore.sym
+++ b/src/libvlccore.sym
@@ -763,3 +763,6 @@ vlc_rd_get_names
 vlc_rd_new
 vlc_rd_release
 vlc_rd_probe_add
+vlc_input_attachment_New
+vlc_input_attachment_Hold
+vlc_input_attachment_Release
-- 
2.39.3 (Apple Git-146)

