From 2ebd90dc19dfc7c10deb5190f8c73b2c9af013cc Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Thu, 27 Sep 2018 18:40:39 +0200
Subject: [PATCH 21/49] libvlc: events: Add callbacks for record

---
 include/vlc/libvlc_events.h |  9 +++++++++
 lib/media_player.c          | 16 ++++++++++++++++
 2 files changed, 25 insertions(+)

diff --git a/include/vlc/libvlc_events.h b/include/vlc/libvlc_events.h
index 2de48565c6..a15389e795 100644
--- a/include/vlc/libvlc_events.h
+++ b/include/vlc/libvlc_events.h
@@ -32,6 +32,8 @@
 
 # ifdef __cplusplus
 extern "C" {
+# else
+#  include <stdbool.h>
 # endif
 
 typedef struct libvlc_renderer_item_t libvlc_renderer_item_t;
@@ -92,6 +94,7 @@ enum libvlc_event_e {
     libvlc_MediaPlayerAudioVolume,
     libvlc_MediaPlayerAudioDevice,
     libvlc_MediaPlayerChapterChanged,
+    libvlc_MediaPlayerRecordChanged,
 
     libvlc_MediaListItemAdded=0x200,
     libvlc_MediaListWillAddItem,
@@ -285,6 +288,12 @@ typedef struct libvlc_event_t
             const char *device;
         } media_player_audio_device;
 
+        struct
+        {
+            const char *file_path;
+            bool recording;
+        } media_player_record_changed;
+
         struct
         {
             libvlc_renderer_item_t *item;
diff --git a/lib/media_player.c b/lib/media_player.c
index c6250f1e4a..5e530182c7 100644
--- a/lib/media_player.c
+++ b/lib/media_player.c
@@ -448,6 +448,22 @@ input_event_changed( vlc_object_t * p_this, char const * psz_cmd,
             }
         }
     }
+    else if ( newval.i_int == INPUT_EVENT_RECORD )
+    {
+        bool recording = var_GetBool( p_input, "record" );
+        char *file_path = NULL;
+
+        if ( !recording )
+            file_path = var_GetString( p_mi->obj.libvlc, "record-file" );
+
+        event.type = libvlc_MediaPlayerRecordChanged;
+        event.u.media_player_record_changed.file_path = file_path;
+        event.u.media_player_record_changed.recording = recording;
+
+        libvlc_event_send( &p_mi->event_manager, &event );
+
+        free( file_path );
+    }
 
     return VLC_SUCCESS;
 }
-- 
2.39.3 (Apple Git-146)

