From db5d1853b07342275ba5dd3243d2ba02df8ac1b7 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Wed, 10 Dec 2014 22:14:55 +0100
Subject: [PATCH 02/49] disable neon volume plugin

---
 modules/arm_neon/Makefile.am | 1 -
 1 file changed, 1 deletion(-)

diff --git a/modules/arm_neon/Makefile.am b/modules/arm_neon/Makefile.am
index 0406ded7eb..48aa33332b 100644
--- a/modules/arm_neon/Makefile.am
+++ b/modules/arm_neon/Makefile.am
@@ -27,7 +27,6 @@ libyuv_rgb_neon_plugin_LIBTOOLFLAGS = --tag=CC
 if HAVE_NEON
 neon_LTLIBRARIES = \
 	libchroma_yuv_neon_plugin.la \
-	libvolume_neon_plugin.la \
 	libyuv_rgb_neon_plugin.la
 endif
 
-- 
2.39.3 (Apple Git-146)

