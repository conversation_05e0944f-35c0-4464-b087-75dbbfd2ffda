From 9c25e8d550d79130906aeba0bbb87f7ed3afbd1f Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Sun, 7 Dec 2014 20:02:18 +0100
Subject: [PATCH 06/49] contrib/gcrypt: work-around a libtool limitation

---
 contrib/src/gcrypt/rules.mak                  |  1 +
 .../work-around-libtool-limitation.patch      | 25 +++++++++++++++++++
 2 files changed, 26 insertions(+)
 create mode 100644 contrib/src/gcrypt/work-around-libtool-limitation.patch

diff --git a/contrib/src/gcrypt/rules.mak b/contrib/src/gcrypt/rules.mak
index 7319fc046c..b90816f425 100644
--- a/contrib/src/gcrypt/rules.mak
+++ b/contrib/src/gcrypt/rules.mak
@@ -15,6 +15,7 @@ $(TARBALLS)/libgcrypt-$(GCRYPT_VERSION).tar.bz2:
 gcrypt: libgcrypt-$(GCRYPT_VERSION).tar.bz2 .sum-gcrypt
 	$(UNPACK)
 	$(APPLY) $(SRC)/gcrypt/disable-tests-compilation.patch
+	$(APPLY) $(SRC)/gcrypt/work-around-libtool-limitation.patch
 	$(APPLY) $(SRC)/gcrypt/fix-pthread-detection.patch
 	$(APPLY) $(SRC)/gcrypt/0001-random-Don-t-assume-that-_WIN64-implies-x86_64.patch
 	$(APPLY) $(SRC)/gcrypt/0002-aarch64-mpi-Fix-building-the-mpi-aarch64-assembly-fo.patch
diff --git a/contrib/src/gcrypt/work-around-libtool-limitation.patch b/contrib/src/gcrypt/work-around-libtool-limitation.patch
new file mode 100644
index 0000000000..df97ffb488
--- /dev/null
+++ b/contrib/src/gcrypt/work-around-libtool-limitation.patch
@@ -0,0 +1,25 @@
+diff -ru libgcrypt-broken/cipher/Makefile.am libgcrypt/cipher/Makefile.am
+--- libgcrypt-broken/cipher/Makefile.am	2013-11-06 23:05:24.000000000 +0100
++++ libgcrypt/cipher/Makefile.am	2013-11-06 23:21:25.000000000 +0100
+@@ -19,6 +19,8 @@
+ 
+ # Process this file with automake to produce Makefile.in
+ 
++LIBTOOL=@LIBTOOL@ --tag=CC
++
+ EXTRA_DIST = Manifest
+ 
+ # Need to include ../src in addition to top_srcdir because gcrypt.h is
+diff -ru libgcrypt-broken/mpi/Makefile.am libgcrypt/mpi/Makefile.am
+--- libgcrypt-broken/mpi/Makefile.am	2013-11-06 23:05:24.000000000 +0100
++++ libgcrypt/mpi/Makefile.am	2013-11-06 23:22:04.000000000 +0100
+@@ -23,6 +23,9 @@
+ 
+ # Need to include ../src in addition to top_srcdir because gcrypt.h is
+ # a built header.
++
++LIBTOOL=@LIBTOOL@ --tag=CC
++
+ AM_CPPFLAGS = -I../src -I$(top_srcdir)/src
+ AM_CFLAGS = $(GPG_ERROR_CFLAGS)
+ 
-- 
2.39.3 (Apple Git-146)

