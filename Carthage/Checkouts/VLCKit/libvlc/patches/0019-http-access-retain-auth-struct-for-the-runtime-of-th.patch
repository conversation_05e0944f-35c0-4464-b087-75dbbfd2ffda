From 5a39dccf112e0baefaca7cb32f83cdc74608f4e0 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Mon, 10 Sep 2018 20:55:11 +0200
Subject: [PATCH 19/49] http access: retain auth struct for the runtime of the
 module

Previously, it was retained for a given connection only, so all information needed for a digest login was lost as soon as the module was reconnecting due to the expected 401 on attempted basic login
---
 modules/access/http.c   | 12 +++++++-----
 src/network/http_auth.c | 17 ++++++++++++++++-
 2 files changed, 23 insertions(+), 6 deletions(-)

diff --git a/modules/access/http.c b/modules/access/http.c
index 99d386c98e..9a0b54680d 100644
--- a/modules/access/http.c
+++ b/modules/access/http.c
@@ -269,6 +269,8 @@ static int Open( vlc_object_t *p_this )
         p_sys->url.psz_username = (char *) credential.psz_username;
         p_sys->url.psz_password = (char *) credential.psz_password;
     }
+    vlc_http_auth_Init( &p_sys->auth );
+    vlc_http_auth_Init( &p_sys->proxy_auth );
 
 connect:
     /* Connect */
@@ -356,6 +358,8 @@ error:
     free( p_sys->psz_referrer );
     free( p_sys->psz_username );
     free( p_sys->psz_password );
+    vlc_http_auth_Deinit( &p_sys->auth );
+    vlc_http_auth_Deinit( &p_sys->proxy_auth );
 
     return ret;
 }
@@ -384,6 +388,9 @@ static void Close( vlc_object_t *p_this )
     free( p_sys->psz_username );
     free( p_sys->psz_password );
 
+    vlc_http_auth_Deinit( &p_sys->auth );
+    vlc_http_auth_Deinit( &p_sys->proxy_auth );
+
     Disconnect( p_access );
 }
 
@@ -628,8 +635,6 @@ static int Connect( stream_t *p_access )
     free( p_sys->psz_icy_name );
     free( p_sys->psz_icy_title );
 
-    vlc_http_auth_Init( &p_sys->auth );
-    vlc_http_auth_Init( &p_sys->proxy_auth );
     p_sys->psz_location = NULL;
     p_sys->psz_mime = NULL;
     p_sys->i_icy_meta = 0;
@@ -977,9 +982,6 @@ static void Disconnect( stream_t *p_access )
     if( p_sys->fd != -1)
         net_Close(p_sys->fd);
     p_sys->fd = -1;
-
-    vlc_http_auth_Deinit( &p_sys->auth );
-    vlc_http_auth_Deinit( &p_sys->proxy_auth );
 }
 
 /*****************************************************************************
diff --git a/src/network/http_auth.c b/src/network/http_auth.c
index 5c3d165cf8..7270330f9b 100644
--- a/src/network/http_auth.c
+++ b/src/network/http_auth.c
@@ -243,7 +243,10 @@ void vlc_http_auth_ParseWwwAuthenticateHeader(
         /* 2 Basic Authentication Scheme */
         msg_Dbg( p_this, "Using Basic Authentication" );
         psz_header += sizeof( psz_basic_prefix ) - 1;
+
+        free( p_auth->psz_realm );
         p_auth->psz_realm = AuthGetParam( psz_header, "realm" );
+
         if ( p_auth->psz_realm == NULL )
             msg_Warn( p_this, "Basic Authentication: "
                       "Mandatory 'realm' parameter is missing" );
@@ -259,12 +262,24 @@ void vlc_http_auth_ParseWwwAuthenticateHeader(
             return;
 
         psz_header += sizeof( psz_digest_prefix ) - 1;
+        p_auth->psz_nonce = AuthGetParam( psz_header, "nonce" );
+
+        free( p_auth->psz_realm );
         p_auth->psz_realm = AuthGetParam( psz_header, "realm" );
+
+        free( p_auth->psz_domain );
         p_auth->psz_domain = AuthGetParam( psz_header, "domain" );
-        p_auth->psz_nonce = AuthGetParam( psz_header, "nonce" );
+
+        free( p_auth->psz_opaque );
         p_auth->psz_opaque = AuthGetParam( psz_header, "opaque" );
+
+        free( p_auth->psz_stale );
         p_auth->psz_stale = AuthGetParamNoQuotes( psz_header, "stale" );
+
+        free( p_auth->psz_algorithm );
         p_auth->psz_algorithm = AuthGetParamNoQuotes( psz_header, "algorithm" );
+
+        free( p_auth->psz_qop );
         p_auth->psz_qop = AuthGetParam( psz_header, "qop" );
         p_auth->i_nonce = 0;
 
-- 
2.39.3 (Apple Git-146)

