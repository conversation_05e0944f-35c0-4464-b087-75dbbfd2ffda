From 21e843b236febbba779ad28d8795d8b17be97c2e Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Thu, 6 Jan 2022 01:08:11 +0100
Subject: [PATCH 38/49] adaptive: NULL http-token

The token property is exclusive to VLCKit to allow passing bearer tokens
 to the http stack. However, it is not used for adaptive streaming, but
needs to be nullified when using libvlchttp to prevent a use-after-free.
---
 modules/demux/adaptive/http/HTTPConnection.cpp | 2 ++
 1 file changed, 2 insertions(+)

diff --git a/modules/demux/adaptive/http/HTTPConnection.cpp b/modules/demux/adaptive/http/HTTPConnection.cpp
index e296897567..8e5f838758 100644
--- a/modules/demux/adaptive/http/HTTPConnection.cpp
+++ b/modules/demux/adaptive/http/HTTPConnection.cpp
@@ -306,6 +306,8 @@ RequestStatus LibVLCHTTPConnection::request(const std::string &path,
     if(source->create(params.getUrl().c_str(), useragent,referer, range))
         return RequestStatus::GenericError;
 
+    source->http_res->token = NULL;
+
     struct vlc_credential crd;
     struct vlc_url_t crd_url;
     vlc_UrlParse(&crd_url, params.getUrl().c_str());
-- 
2.39.3 (Apple Git-146)

