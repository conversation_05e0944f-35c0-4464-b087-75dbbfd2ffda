From badb35a1fbd276eb5bbe4813d363c0bbee329290 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Mon, 21 Jun 2021 14:03:09 +0200
Subject: [PATCH 35/49] contrib/live555: fix hostname lookup on iOS devices

This adds a hack to fix the hostname lookup on iOS devices matching our
approach used within the iOS app. macOS or the iOS simulator are not
affected.

Fixes videolan/VLCKit#502
---
 ...groupsock-fix-hostname-lookup-on-iOS.patch | 54 +++++++++++++++++++
 contrib/src/live555/rules.mak                 |  1 +
 2 files changed, 55 insertions(+)
 create mode 100644 contrib/src/live555/groupsock-fix-hostname-lookup-on-iOS.patch

diff --git a/contrib/src/live555/groupsock-fix-hostname-lookup-on-iOS.patch b/contrib/src/live555/groupsock-fix-hostname-lookup-on-iOS.patch
new file mode 100644
index 0000000000..c28b0ab337
--- /dev/null
+++ b/contrib/src/live555/groupsock-fix-hostname-lookup-on-iOS.patch
@@ -0,0 +1,54 @@
+From 1587ce420bbf7b4fbec2abdf36509ec35cc87da7 Mon Sep 17 00:00:00 2001
+From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
+Date: Mon, 21 Jun 2021 17:16:09 +0200
+Subject: [PATCH] fix hostname lookup on iOS devices
+
+This adds a hack to fix the hostname lookup on iOS devices matching our
+approach used within the iOS app. macOS or the iOS simulator are not
+affected.
+
+Fixes videolan/VLCKit#502
+---
+ groupsock/GroupsockHelper.cpp | 13 ++++++++++++-
+ 1 file changed, 12 insertions(+), 1 deletion(-)
+
+diff --git a/groupsock/GroupsockHelper.cpp b/groupsock/GroupsockHelper.cpp
+index 4576236..54e11dd 100644
+--- a/groupsock/GroupsockHelper.cpp
++++ b/groupsock/GroupsockHelper.cpp
+@@ -41,6 +41,10 @@ extern "C" int initializeWinsockIfNecessary();
+ # define MSG_NOSIGNAL 0
+ #endif
+ 
++#ifdef __APPLE__
++# include <TargetConditionals.h>
++#endif
++
+ // By default, use INADDR_ANY for the sending and receiving interfaces:
+ netAddressBits SendingInterfaceAddr = INADDR_ANY;
+ netAddressBits ReceivingInterfaceAddr = INADDR_ANY;
+@@ -656,7 +660,7 @@ netAddressBits ourIPAddress(UsageEnvironment& env) {
+     if (!loopbackWorks) do {
+       // We couldn't find our address using multicast loopback,
+       // so try instead to look it up directly - by first getting our host name, and then resolving this host name
+-      char hostname[100];
++      char hostname[262];
+       hostname[0] = '\0';
+       int result = gethostname(hostname, sizeof hostname);
+       if (result != 0 || hostname[0] == '\0') {
+@@ -664,6 +668,13 @@ netAddressBits ourIPAddress(UsageEnvironment& env) {
+ 	break;
+       }
+ 
++      // iOS devices return a hostname that requires a .local suffix
++#if TARGET_OS_IPHONE
++#if !TARGET_IPHONE_SIMULATOR
++      strncat(hostname, ".local", sizeof(hostname) - strlen(hostname) - 1);
++#endif
++#endif
++
+       // Try to resolve "hostname" to an IP address:
+       NetAddressList addresses(hostname);
+       NetAddressList::Iterator iter(addresses);
+-- 
*****.1 (Apple Git-130)
diff --git a/contrib/src/live555/rules.mak b/contrib/src/live555/rules.mak
index 6bf42d4b42..d475a13fcc 100644
--- a/contrib/src/live555/rules.mak
+++ b/contrib/src/live555/rules.mak
@@ -92,6 +92,7 @@ endif
 endif
 	# Fix creating static libs on mingw
 	$(APPLY) $(SRC)/live555/mingw-static-libs.patch
+	$(APPLY) $(SRC)/live555/groupsock-fix-hostname-lookup-on-iOS.patch
 
 	mv live.$(LIVE555_VERSION) $@ && touch $@
 
-- 
2.39.3 (Apple Git-146)

