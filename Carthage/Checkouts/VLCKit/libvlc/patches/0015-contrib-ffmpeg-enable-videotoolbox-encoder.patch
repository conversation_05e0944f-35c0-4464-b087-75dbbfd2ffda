From f57b859819647b8e4a728cd0377d74def359772d Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 27 Mar 2018 16:49:34 +0200
Subject: [PATCH 15/49] contrib: ffmpeg: enable videotoolbox encoder
MIME-Version: 1.0
Content-Type: text/plain; charset=UTF-8
Content-Transfer-Encoding: 8bit

No problem for decoders since it won't be used.

(cherry picked from commit 1003d6aa220d18088d2d6d2949dc117b622f85e3)
Signed-off-by: <PERSON> <<EMAIL>>
---
 contrib/src/ffmpeg/rules.mak | 1 -
 1 file changed, 1 deletion(-)

diff --git a/contrib/src/ffmpeg/rules.mak b/contrib/src/ffmpeg/rules.mak
index 710c29369e..189428f29e 100644
--- a/contrib/src/ffmpeg/rules.mak
+++ b/contrib/src/ffmpeg/rules.mak
@@ -57,7 +57,6 @@ FFMPEGCONF += \
 	--disable-linux-perf
 ifdef HAVE_DARWIN_OS
 FFMPEGCONF += \
-	--disable-videotoolbox \
 	--disable-securetransport
 endif
 endif
-- 
2.39.3 (Apple Git-146)

