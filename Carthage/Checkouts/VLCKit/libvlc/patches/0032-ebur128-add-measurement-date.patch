From a358008f7aac12e669abb0964c0d6ef31593ecfd Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 18 Aug 2020 14:53:43 +0200
Subject: [PATCH 32/49] ebur128: add measurement date

---
 include/vlc/libvlc_events.h       |  7 +++++++
 include/vlc_aout.h                |  6 ++++++
 lib/media_player.c                |  1 +
 modules/audio_filter/libebur128.c | 20 ++++++++++----------
 4 <USER> <GROUP>, 24 insertions(+), 10 deletions(-)

diff --git a/include/vlc/libvlc_events.h b/include/vlc/libvlc_events.h
index fef21ba4fc..dfef931136 100644
--- a/include/vlc/libvlc_events.h
+++ b/include/vlc/libvlc_events.h
@@ -222,6 +222,13 @@ typedef struct libvlc_event_t
 
         struct
         {
+            /**
+             * Absolute date of the measurement
+             *
+             * It is most likely in the future (0 to 2seconds) depending on the audio
+             * output buffer size. */
+            int64_t date;
+            /** Momentary loudness (last 400ms), in LUFS */
             double momentary_loudness;
         } media_player_loudness_changed;
 
diff --git a/include/vlc_aout.h b/include/vlc_aout.h
index a958c1b6d4..4e2e5a251f 100644
--- a/include/vlc_aout.h
+++ b/include/vlc_aout.h
@@ -114,6 +114,12 @@
 /** Audio output object */
 struct vlc_audio_loudness_meter
 {
+    /**
+     * Absolute date of the measurement
+     *
+     * It is most likely in the future (0 to 2seconds) depending on the audio
+     * output buffer size. */
+    mtime_t date;
     /** Momentary loudness, in LUFS */
     double loudness_momentary;
     /** Short term loudness, in LUFS (VLC_PLAYER_AOUT_LOUDNESS_METER_FULL) */
diff --git a/lib/media_player.c b/lib/media_player.c
index d8d1a1b9d6..b80cf4645b 100644
--- a/lib/media_player.c
+++ b/lib/media_player.c
@@ -167,6 +167,7 @@ loudness_meter_changed( vlc_object_t * p_this, char const * psz_cmd,
     libvlc_event_t event = {
         .type = libvlc_MediaPlayerLoudnessChanged,
     };
+    event.u.media_player_loudness_changed.date = meter->date;
     event.u.media_player_loudness_changed.momentary_loudness = meter->loudness_momentary;
     libvlc_event_send( &p_mi->event_manager, &event );
 
diff --git a/modules/audio_filter/libebur128.c b/modules/audio_filter/libebur128.c
index b6e3964f1b..621c28fef0 100644
--- a/modules/audio_filter/libebur128.c
+++ b/modules/audio_filter/libebur128.c
@@ -36,7 +36,7 @@ struct filter_sys
 {
     ebur128_state *state;
     mtime_t last_update;
-    bool new_frames;
+    mtime_t last_block_date;
 };
 
 static ebur128_state *
@@ -83,12 +83,12 @@ error:
 }
 
 static int
-SendLoudnessMeter(filter_t *filter)
+SendLoudnessMeter(filter_t *filter, mtime_t play_date)
 {
     struct filter_sys *sys = (void *) filter->p_sys;
 
     int error;
-    struct vlc_audio_loudness_meter meter = { 0, 0, 0, 0, 0 };
+    struct vlc_audio_loudness_meter meter = { play_date, 0, 0, 0, 0, 0 };
 
     error = ebur128_loudness_momentary(sys->state, &meter.loudness_momentary);
     if (error != EBUR128_SUCCESS)
@@ -198,15 +198,15 @@ Process(filter_t *filter, block_t *block)
 
     if (out->i_pts + out->i_length - sys->last_update >= UPDATE_INTERVAL)
     {
-        error = SendLoudnessMeter(filter);
+        error = SendLoudnessMeter(filter, out->i_pts + out->i_length);
         if (error == EBUR128_SUCCESS)
         {
             sys->last_update = out->i_pts + out->i_length;
-            sys->new_frames = false;
+            sys->last_block_date = VLC_TS_INVALID;
         }
     }
     else
-        sys->new_frames = true;
+        sys->last_block_date = out->i_pts + out->i_length;
 
     return out;
 }
@@ -218,10 +218,10 @@ Flush(filter_t *filter)
 
     if (sys->state != NULL)
     {
-        if (sys->new_frames)
+        if (sys->last_block_date != VLC_TS_INVALID)
         {
-            SendLoudnessMeter(filter);
-            sys->new_frames = false;
+            SendLoudnessMeter(filter, sys->last_block_date);
+            sys->last_block_date = VLC_TS_INVALID;
         }
         sys->last_update = VLC_TS_INVALID;
 
@@ -253,7 +253,7 @@ static int Open(vlc_object_t *this)
         return VLC_ENOMEM;
 
     sys->last_update = VLC_TS_INVALID;
-    sys->new_frames = false;
+    sys->last_block_date = VLC_TS_INVALID;
     sys->state = CreateEbuR128State(filter);
     if (sys->state == NULL)
     {
-- 
2.39.3 (Apple Git-146)

