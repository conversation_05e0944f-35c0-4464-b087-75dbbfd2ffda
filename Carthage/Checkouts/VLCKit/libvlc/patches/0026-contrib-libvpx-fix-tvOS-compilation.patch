From e2580cde72d4c69c2c6f2a8b321b66b6f394f642 Mon Sep 17 00:00:00 2001
From: =?UTF-8?q?Felix=20Paul=20K=C3=BChne?= <<EMAIL>>
Date: Sat, 19 Sep 2020 13:51:08 +0200
Subject: [PATCH 26/49] contrib/libvpx: fix tvOS compilation

---
 contrib/src/vpx/libvpx-tvos.patch | 21 +++++++++++++++++++++
 contrib/src/vpx/rules.mak         |  3 +++
 2 files changed, 24 insertions(+)
 create mode 100644 contrib/src/vpx/libvpx-tvos.patch

diff --git a/contrib/src/vpx/libvpx-tvos.patch b/contrib/src/vpx/libvpx-tvos.patch
new file mode 100644
index 0000000000..f4a22f56ec
--- /dev/null
+++ b/contrib/src/vpx/libvpx-tvos.patch
@@ -0,0 +1,21 @@
+diff -ru libvpx/build/make/configure.sh libvpx/build/make/configure.sh
+--- libvpx/build/make/configure.sh	2020-09-19 13:43:58.000000000 +0200
++++ libvpx/build/make/configure.sh	2020-09-19 13:43:26.000000000 +0200
+@@ -996,7 +996,7 @@
+ 
+         darwin*)
+           if ! enabled external_build; then
+-            XCRUN_FIND="xcrun --sdk iphoneos --find"
++            XCRUN_FIND="xcrun --sdk appletvos --find"
+             CXX="$(${XCRUN_FIND} clang++)"
+             CC="$(${XCRUN_FIND} clang)"
+             AR="$(${XCRUN_FIND} ar)"
+@@ -1015,7 +1015,7 @@
+             add_cflags -arch ${tgt_isa}
+             add_ldflags -arch ${tgt_isa}
+ 
+-            alt_libc="$(show_darwin_sdk_path iphoneos)"
++            alt_libc="$(show_darwin_sdk_path appletvos)"
+             if [ -d "${alt_libc}" ]; then
+               add_cflags -isysroot ${alt_libc}
+             fi
diff --git a/contrib/src/vpx/rules.mak b/contrib/src/vpx/rules.mak
index 263ce7d1f0..5805042b66 100644
--- a/contrib/src/vpx/rules.mak
+++ b/contrib/src/vpx/rules.mak
@@ -26,6 +26,9 @@ ifdef HAVE_MACOSX
 ifeq ($(ARCH),aarch64)
 	$(APPLY) $(SRC)/vpx/libvpx-darwin-aarch64.patch
 endif
+endif
+ifdef HAVE_TVOS
+	$(APPLY) $(SRC)/vpx/libvpx-tvos.patch
 endif
 	# Disable automatic addition of -fembed-bitcode for iOS
 	# as it is enabled through --extra-cflags if necessary.
-- 
2.39.3 (Apple Git-146)

