# VLCKit

VLCKit is a generic library for any audio or video playback needs on OS X, iOS and tvOS. It also supports active streaming and media to file conversations on the Mac. It is open-source software licensed under LGPLv2.1 or later, available in source code and binary form from the [VideoLAN website]. You can also integrate MobileVLCKit easily via [CocoaPods].

## How to contribute

We are happy to accept additions and improvements to VLCKit by you! However, we don't support merge requests at this point.

So, you have code you want to upstream - what to do? Join the [VLC Development mailing-list] and send us your patches!

git includes powerful tools to create patch files and even includes an email client to automatically send those. For detailed information, [see our wiki].

## How to get in touch

VLC is an open source project with a long history (we started publicly in 2001!) - communication still works mainly as in the old days. On the aforementioned mailing-list, but mostly on IRC.
You will find us on the #videolan channel on the freenode network.

Don't have an IRC client installed? You can use a [web interface] - alternatively, consider looking at [Colloquy], [LimeChat] or [Adium].

[VLC Development mailing-list]: <https://mailman.videolan.org/listinfo/vlc-devel>
[see our wiki]: <https://wiki.videolan.org/Sending_Patches_VLC/>
[web interface]: <http://webchat.freenode.net>
[Colloquy]: <http://colloquy.info>
[LimeChat]: <http://limechat.net/mac/>
[Adium]: <https://adium.im>