<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Headers/Public/DynamicTVVLCKit.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html">Headers</a></li><li class="navelem"><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html">Public</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">DynamicTVVLCKit.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/*****************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * DynamicTVVLCKit.h: dynamic library umbrella header</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *****************************************************************************</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Copyright (C) 2016 VideoLabs SAS</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * $Id$</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> * Authors: <AUTHORS>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> * This program is free software; you can redistribute it and/or modify it</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> * under the terms of the GNU Lesser General Public License as published by</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> * the Free Software Foundation; either version 2.1 of the License, or</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> * (at your option) any later version.</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> * This program is distributed in the hope that it will be useful,</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * but WITHOUT ANY WARRANTY; without even the implied warranty of</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * GNU Lesser General Public License for more details.</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * You should have received a copy of the GNU Lesser General Public License</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * along with this program; if not, write to the Free Software Foundation,</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> *****************************************************************************/</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; </div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#import &lt;UIKit/UIKit.h&gt;</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160; </div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;FOUNDATION_EXPORT <span class="keywordtype">double</span> DynamicTVVLCKitVersionNumber;</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160; </div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;FOUNDATION_EXPORT <span class="keyword">const</span> <span class="keywordtype">unsigned</span> <span class="keywordtype">char</span> DynamicTVVLCKitVersionString[];</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160; </div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="comment">// In this header, you should import all the public headers of your framework using statements like #import &lt;DynamicTVVLCKit/PublicHeader.h&gt;</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160; </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#import &lt;DynamicTVVLCKit/VLCAudio.h&gt;</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#import &lt;DynamicTVVLCKit/VLCLibrary.h&gt;</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#import &lt;DynamicTVVLCKit/VLCMedia.h&gt;</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#import &lt;DynamicTVVLCKit/VLCMediaDiscoverer.h&gt;</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#import &lt;DynamicTVVLCKit/VLCMediaList.h&gt;</span></div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#import &lt;DynamicTVVLCKit/VLCMediaPlayer.h&gt;</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#import &lt;DynamicTVVLCKit/VLCMediaListPlayer.h&gt;</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#import &lt;DynamicTVVLCKit/VLCMediaThumbnailer.h&gt;</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#import &lt;DynamicTVVLCKit/VLCTime.h&gt;</span></div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#import &lt;DynamicTVVLCKit/VLCDialogProvider.h&gt;</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media.html">VLCMedia</a>;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media_library.html">VLCMediaLibrary</a>;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media_list.html">VLCMediaList</a>;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_time.html">VLCTime</a>;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_video_view.html">VLCVideoView</a>;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_audio.html">VLCAudio</a>;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a>;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a>;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a>;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_dialog_provider.html">VLCDialogProvider</a>;</div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="ainterface_v_l_c_media_list_html"><div class="ttname"><a href="interface_v_l_c_media_list.html">VLCMediaList</a></div><div class="ttdef"><b>Definition:</b> VLCMediaList.h:68</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html"><div class="ttname"><a href="interface_v_l_c_media.html">VLCMedia</a></div><div class="ttdef"><b>Definition:</b> VLCMedia.h:113</div></div>
<div class="ttc" id="ainterface_v_l_c_media_player_html"><div class="ttname"><a href="interface_v_l_c_media_player.html">VLCMediaPlayer</a></div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:186</div></div>
<div class="ttc" id="ainterface_v_l_c_media_library_html"><div class="ttname"><a href="interface_v_l_c_media_library.html">VLCMediaLibrary</a></div><div class="ttdef"><b>Definition:</b> VLCMediaLibrary.h:33</div></div>
<div class="ttc" id="ainterface_v_l_c_media_thumbnailer_html"><div class="ttname"><a href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a></div><div class="ttdef"><b>Definition:</b> VLCMediaThumbnailer.h:36</div></div>
<div class="ttc" id="ainterface_v_l_c_dialog_provider_html"><div class="ttname"><a href="interface_v_l_c_dialog_provider.html">VLCDialogProvider</a></div><div class="ttdef"><b>Definition:</b> VLCDialogProvider.h:116</div></div>
<div class="ttc" id="ainterface_v_l_c_audio_html"><div class="ttname"><a href="interface_v_l_c_audio.html">VLCAudio</a></div><div class="ttdef"><b>Definition:</b> VLCAudio.h:37</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_player_html"><div class="ttname"><a href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></div><div class="ttdef"><b>Definition:</b> VLCMediaListPlayer.h:66</div></div>
<div class="ttc" id="ainterface_v_l_c_video_view_html"><div class="ttname"><a href="interface_v_l_c_video_view.html">VLCVideoView</a></div><div class="ttdef"><b>Definition:</b> VLCVideoView.h:32</div></div>
<div class="ttc" id="ainterface_v_l_c_time_html"><div class="ttname"><a href="interface_v_l_c_time.html">VLCTime</a></div><div class="ttdef"><b>Definition:</b> VLCTime.h:31</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
