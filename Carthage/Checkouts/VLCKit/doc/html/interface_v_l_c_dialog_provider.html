<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCDialogProvider Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_dialog_provider-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCDialogProvider Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_dialog_provider_8h_source.html">VLCDialogProvider.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCDialogProvider:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_dialog_provider.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a51bf3d3c491d976761a78ac431f9e986"><td class="memItemLeft" align="right" valign="top">(instancetype _Nullable)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_dialog_provider.html#a51bf3d3c491d976761a78ac431f9e986">initWithLibrary:customUI:</a></td></tr>
<tr class="separator:a51bf3d3c491d976761a78ac431f9e986"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a75004021734803d0acbd1043dfcb59de"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_dialog_provider.html#a75004021734803d0acbd1043dfcb59de">postUsername:andPassword:forDialogReference:store:</a></td></tr>
<tr class="separator:a75004021734803d0acbd1043dfcb59de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a0eefde74fa37648c5362bc864a3492"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_dialog_provider.html#a8a0eefde74fa37648c5362bc864a3492">postAction:forDialogReference:</a></td></tr>
<tr class="separator:a8a0eefde74fa37648c5362bc864a3492"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a067cfa85cd47a387ccccba06b6a14b3e"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_dialog_provider.html#a067cfa85cd47a387ccccba06b6a14b3e">dismissDialogWithReference:</a></td></tr>
<tr class="separator:a067cfa85cd47a387ccccba06b6a14b3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a15847411703d3b120abb0a9b4b7e8ed2"><td class="memItemLeft" align="right" valign="top">id&lt; <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html">VLCCustomDialogRendererProtocol</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_dialog_provider.html#a15847411703d3b120abb0a9b4b7e8ed2">customRenderer</a></td></tr>
<tr class="separator:a15847411703d3b120abb0a9b4b7e8ed2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>dialog provider base class </p><dl class="section note"><dt>Note</dt><dd>For iOS and tvOS, there are useable implementations available which don't require the use of a custom renderer </dd></dl>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="a067cfa85cd47a387ccccba06b6a14b3e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a067cfa85cd47a387ccccba06b6a14b3e">&#9670;&nbsp;</a></span>dismissDialogWithReference:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) dismissDialogWithReference: </td>
          <td></td>
          <td class="paramtype">(NSValue *_Nonnull)&#160;</td>
          <td class="paramname"><em>dialogReference</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>if you requested custom UI mode for dialogs, use this method to cancel a progress dialog </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">dialogReference</td><td>reference to the dialog you want to cancel </td></tr>
  </table>
  </dd>
</dl>
<dl class="section note"><dt>Note</dt><dd>This method does not have any effect if you don't use custom UI mode </dd></dl>

</div>
</div>
<a id="a51bf3d3c491d976761a78ac431f9e986"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a51bf3d3c491d976761a78ac431f9e986">&#9670;&nbsp;</a></span>initWithLibrary:customUI:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype _Nullable) initWithLibrary: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> *_Nullable)&#160;</td>
          <td class="paramname"><em>library</em></td>
        </tr>
        <tr>
          <td class="paramkey">customUI:</td>
          <td></td>
          <td class="paramtype">(BOOL)&#160;</td>
          <td class="paramname"><em>customUI</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>initializer method to run the dialog provider instance on a specific library instance</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">library</td><td>the <a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> instance </td></tr>
    <tr><td class="paramname">customUI</td><td>enable custom UI mode </td></tr>
  </table>
  </dd>
</dl>
<dl class="section note"><dt>Note</dt><dd>if library param is NULL, [<a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> sharedLibrary] will be used </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>the dialog provider instance, can be NULL on malloc failures </dd></dl>

</div>
</div>
<a id="a8a0eefde74fa37648c5362bc864a3492"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8a0eefde74fa37648c5362bc864a3492">&#9670;&nbsp;</a></span>postAction:forDialogReference:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) postAction: </td>
          <td></td>
          <td class="paramtype">(int)&#160;</td>
          <td class="paramname"><em>buttonNumber</em></td>
        </tr>
        <tr>
          <td class="paramkey">forDialogReference:</td>
          <td></td>
          <td class="paramtype">(NSValue *_Nonnull)&#160;</td>
          <td class="paramname"><em>dialogReference</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>if you requested custom UI mode for dialogs, use this method respond to a question dialog </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">buttonNumber</td><td>the button number the user pressed, use 3 if s/he cancelled, otherwise respectively 1 or 2 depending on the selected action </td></tr>
    <tr><td class="paramname">dialogReference</td><td>reference to the dialog you respond to </td></tr>
  </table>
  </dd>
</dl>
<dl class="section note"><dt>Note</dt><dd>This method does not have any effect if you don't use custom UI mode </dd></dl>

</div>
</div>
<a id="a75004021734803d0acbd1043dfcb59de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a75004021734803d0acbd1043dfcb59de">&#9670;&nbsp;</a></span>postUsername:andPassword:forDialogReference:store:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) postUsername: </td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>username</em></td>
        </tr>
        <tr>
          <td class="paramkey">andPassword:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>password</em></td>
        </tr>
        <tr>
          <td class="paramkey">forDialogReference:</td>
          <td></td>
          <td class="paramtype">(NSValue *_Nonnull)&#160;</td>
          <td class="paramname"><em>dialogReference</em></td>
        </tr>
        <tr>
          <td class="paramkey">store:</td>
          <td></td>
          <td class="paramtype">(BOOL)&#160;</td>
          <td class="paramname"><em>store</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>if you requested custom UI mode for dialogs, use this method respond to a login dialog </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">username</td><td>or NULL if cancelled </td></tr>
    <tr><td class="paramname">password</td><td>or NULL if cancelled </td></tr>
    <tr><td class="paramname">dialogReference</td><td>reference to the dialog you respond to </td></tr>
    <tr><td class="paramname">store</td><td>shall VLC store the login securely? </td></tr>
  </table>
  </dd>
</dl>
<dl class="section note"><dt>Note</dt><dd>This method does not have any effect if you don't use custom UI mode </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a15847411703d3b120abb0a9b4b7e8ed2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a15847411703d3b120abb0a9b4b7e8ed2">&#9670;&nbsp;</a></span>customRenderer</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (id&lt;<a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html">VLCCustomDialogRendererProtocol</a>&gt;) customRenderer</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">weak</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>initializer method to run the dialog provider instance on a specific library instance</p>
<dl class="section return"><dt>Returns</dt><dd>the object set </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_dialog_provider_8h_source.html">VLCDialogProvider.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
