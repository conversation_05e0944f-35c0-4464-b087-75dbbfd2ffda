<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: &lt;VLCMediaPlayerDelegate&gt; Protocol Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="protocol_v_l_c_media_player_delegate-p-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">&lt;VLCMediaPlayerDelegate&gt; Protocol Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_media_player_8h_source.html">VLCMediaPlayer.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for &lt;VLCMediaPlayerDelegate&gt;:</div>
<div class="dyncontent">
 <div class="center">
  <img src="protocol_v_l_c_media_player_delegate-p.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:afd15a9281d2a0d4c03b1b67ff0041ba8"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#afd15a9281d2a0d4c03b1b67ff0041ba8">mediaPlayerStateChanged:</a></td></tr>
<tr class="separator:afd15a9281d2a0d4c03b1b67ff0041ba8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c1d64150f362598808f444b66046f7a"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#a5c1d64150f362598808f444b66046f7a">mediaPlayerTimeChanged:</a></td></tr>
<tr class="separator:a5c1d64150f362598808f444b66046f7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a857838e3a322f96cf60fa94dc905e8d5"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#a857838e3a322f96cf60fa94dc905e8d5">mediaPlayerTitleChanged:</a></td></tr>
<tr class="separator:a857838e3a322f96cf60fa94dc905e8d5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa0cba7a31646e3b6dbb7b994776eaf4b"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#aa0cba7a31646e3b6dbb7b994776eaf4b">mediaPlayerChapterChanged:</a></td></tr>
<tr class="separator:aa0cba7a31646e3b6dbb7b994776eaf4b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a20baffdd00732f78f8b1ddf4689983cc"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#a20baffdd00732f78f8b1ddf4689983cc">mediaPlayerLoudnessChanged:</a></td></tr>
<tr class="separator:a20baffdd00732f78f8b1ddf4689983cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac975d4b4100985c55cf5765fef978c29"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#ac975d4b4100985c55cf5765fef978c29">mediaPlayerSnapshot:</a></td></tr>
<tr class="separator:ac975d4b4100985c55cf5765fef978c29"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3c1f8b9adb12e923abc5686e0dd7f5f8"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#a3c1f8b9adb12e923abc5686e0dd7f5f8">mediaPlayerStartedRecording:</a></td></tr>
<tr class="separator:a3c1f8b9adb12e923abc5686e0dd7f5f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae9712ac043c045a0d7eaee79281d6eef"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#ae9712ac043c045a0d7eaee79281d6eef">mediaPlayer:recordingStoppedAtPath:</a></td></tr>
<tr class="separator:ae9712ac043c045a0d7eaee79281d6eef"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Formal protocol declaration for playback delegates. Allows playback messages to be trapped by delegated objects. </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="ae9712ac043c045a0d7eaee79281d6eef"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae9712ac043c045a0d7eaee79281d6eef">&#9670;&nbsp;</a></span>mediaPlayer:recordingStoppedAtPath:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaPlayer: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> *)&#160;</td>
          <td class="paramname"><em>player</em></td>
        </tr>
        <tr>
          <td class="paramkey">recordingStoppedAtPath:</td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>path</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sent by the default notification center whenever the player stopped recording. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">player</td><td>the player who stopped recording </td></tr>
    <tr><td class="paramname">path</td><td>the path to the file that the player recorded to </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aa0cba7a31646e3b6dbb7b994776eaf4b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa0cba7a31646e3b6dbb7b994776eaf4b">&#9670;&nbsp;</a></span>mediaPlayerChapterChanged:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaPlayerChapterChanged: </td>
          <td></td>
          <td class="paramtype">(NSNotification *)&#160;</td>
          <td class="paramname"><em>aNotification</em></td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sent by the default notification center whenever the player's chapter has changed (if any).</p>
<p>Discussion The value of aNotification is always an VLCMediaPlayerChapterChanged notification. You can retrieve the <a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> object in question by sending object to aNotification. </p>

</div>
</div>
<a id="a20baffdd00732f78f8b1ddf4689983cc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a20baffdd00732f78f8b1ddf4689983cc">&#9670;&nbsp;</a></span>mediaPlayerLoudnessChanged:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaPlayerLoudnessChanged: </td>
          <td></td>
          <td class="paramtype">(NSNotification *)&#160;</td>
          <td class="paramname"><em>aNotification</em></td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sent by the default notification center whenever the player's loundess has changed (if any).</p>
<p>Discussion The value of aNotification is always an VLCMediaPlayerLoudnessChanged notification. You can retrieve the <a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> object in question by sending object to aNotification. </p>

</div>
</div>
<a id="ac975d4b4100985c55cf5765fef978c29"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac975d4b4100985c55cf5765fef978c29">&#9670;&nbsp;</a></span>mediaPlayerSnapshot:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaPlayerSnapshot: </td>
          <td></td>
          <td class="paramtype">(NSNotification *)&#160;</td>
          <td class="paramname"><em>aNotification</em></td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sent by the default notification center whenever a new snapshot is taken.</p>
<p>Discussion The value of aNotification is always an VLCMediaPlayerSnapshotTaken notification. You can retrieve the <a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> object in question by sending object to aNotification. </p>

</div>
</div>
<a id="a3c1f8b9adb12e923abc5686e0dd7f5f8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3c1f8b9adb12e923abc5686e0dd7f5f8">&#9670;&nbsp;</a></span>mediaPlayerStartedRecording:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaPlayerStartedRecording: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> *)&#160;</td>
          <td class="paramname"><em>player</em></td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sent by the default notification center whenever the player started recording. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">player</td><td>the player who started recording </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="afd15a9281d2a0d4c03b1b67ff0041ba8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afd15a9281d2a0d4c03b1b67ff0041ba8">&#9670;&nbsp;</a></span>mediaPlayerStateChanged:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaPlayerStateChanged: </td>
          <td></td>
          <td class="paramtype">(NSNotification *)&#160;</td>
          <td class="paramname"><em>aNotification</em></td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sent by the default notification center whenever the player's state has changed.</p>
<p>Discussion The value of aNotification is always an VLCMediaPlayerStateChanged notification. You can retrieve the <a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> object in question by sending object to aNotification. </p>

</div>
</div>
<a id="a5c1d64150f362598808f444b66046f7a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5c1d64150f362598808f444b66046f7a">&#9670;&nbsp;</a></span>mediaPlayerTimeChanged:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaPlayerTimeChanged: </td>
          <td></td>
          <td class="paramtype">(NSNotification *)&#160;</td>
          <td class="paramname"><em>aNotification</em></td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sent by the default notification center whenever the player's time has changed.</p>
<p>Discussion The value of aNotification is always an VLCMediaPlayerTimeChanged notification. You can retrieve the <a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> object in question by sending object to aNotification. </p>

</div>
</div>
<a id="a857838e3a322f96cf60fa94dc905e8d5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a857838e3a322f96cf60fa94dc905e8d5">&#9670;&nbsp;</a></span>mediaPlayerTitleChanged:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaPlayerTitleChanged: </td>
          <td></td>
          <td class="paramtype">(NSNotification *)&#160;</td>
          <td class="paramname"><em>aNotification</em></td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sent by the default notification center whenever the player's title has changed (if any).</p>
<p>Discussion The value of aNotification is always an VLCMediaPlayerTitleChanged notification. You can retrieve the <a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> object in question by sending object to aNotification. </p><dl class="section note"><dt>Note</dt><dd>this is about a title in the navigation sense, not about metadata </dd></dl>

</div>
</div>
<hr/>The documentation for this protocol was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_media_player_8h_source.html">VLCMediaPlayer.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
