<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>VLCKit: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Related&#160;Pages</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCLibrary Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="interface_v_l_c_library.html">VLCLibrary</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#a9201c5f4b1ee745e31e3875835914124">changeset</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#a91485a448eb7b18c7b5c36bba4c56184">compiler</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#a30523c86de4b6a2fbc86f1d6a8274b59">debugLogging</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#a0b0d7e806f3c1c49243873f99c3608fa">debugLoggingLevel</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_library.html#ab029c774c0b133dc9c15b9f4446de8c5">initWithOptions:</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#abb375a92ef38f3cacab3d1d501b747d3">instance</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_library.html#afe1e5231b6bc4b9b12e0fd2c44862dbb">setApplicationIdentifier:withVersion:andApplicationIconName:</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_library.html#ae91040ee78413a160918871e1e9475ff">setHumanReadableName:withHTTPUserAgent:</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">+&#160;</td><td><a class="el" href="interface_v_l_c_library.html#a7eddfd69ce66ffc5603bc68a5777187e">sharedLibrary</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#a99403afc6373f5f0ca5b89b6c64d3f32">version</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
