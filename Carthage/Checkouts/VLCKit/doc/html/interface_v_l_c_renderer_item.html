<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCRendererItem Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_renderer_item-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCRendererItem Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_renderer_item_8h_source.html">VLCRendererItem.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCRendererItem:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_renderer_item.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a7d4e309821a947227bdf33e2f9eb668e"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_renderer_item.html#a7d4e309821a947227bdf33e2f9eb668e">NS_UNAVAILABLE</a></td></tr>
<tr class="separator:a7d4e309821a947227bdf33e2f9eb668e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a5dcd15900cdcd7901c2e9a9cc6820831"><td class="memItemLeft" align="right" valign="top">NSString *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_renderer_item.html#a5dcd15900cdcd7901c2e9a9cc6820831">name</a></td></tr>
<tr class="separator:a5dcd15900cdcd7901c2e9a9cc6820831"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaac905fef90a323b9a56d116aba7c840"><td class="memItemLeft" align="right" valign="top">NSString *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_renderer_item.html#aaac905fef90a323b9a56d116aba7c840">type</a></td></tr>
<tr class="separator:aaac905fef90a323b9a56d116aba7c840"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2fd800be1553921e947962d62a767601"><td class="memItemLeft" align="right" valign="top">NSString *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_renderer_item.html#a2fd800be1553921e947962d62a767601">iconURI</a></td></tr>
<tr class="separator:a2fd800be1553921e947962d62a767601"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9f68e1e3618c4e521a3e52478672a36d"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_renderer_item.html#a9f68e1e3618c4e521a3e52478672a36d">flags</a></td></tr>
<tr class="separator:a9f68e1e3618c4e521a3e52478672a36d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Renderer Item </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="a7d4e309821a947227bdf33e2f9eb668e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7d4e309821a947227bdf33e2f9eb668e">&#9670;&nbsp;</a></span>NS_UNAVAILABLE</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) NS_UNAVAILABLE </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section note"><dt>Note</dt><dd>Unavailable, handled by <code><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></code> </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a> </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a9f68e1e3618c4e521a3e52478672a36d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9f68e1e3618c4e521a3e52478672a36d">&#9670;&nbsp;</a></span>flags</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) flags</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Flags of the renderer item </p>

</div>
</div>
<a id="a2fd800be1553921e947962d62a767601"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2fd800be1553921e947962d62a767601">&#9670;&nbsp;</a></span>iconURI</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString*) iconURI</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>IconURI of the renderer item </p>

</div>
</div>
<a id="a5dcd15900cdcd7901c2e9a9cc6820831"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5dcd15900cdcd7901c2e9a9cc6820831">&#9670;&nbsp;</a></span>name</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString*) name</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Name of the renderer item </p>

</div>
</div>
<a id="aaac905fef90a323b9a56d116aba7c840"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaac905fef90a323b9a56d116aba7c840">&#9670;&nbsp;</a></span>type</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString*) type</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>For now, the type can only be "chromecast" ("upnp", "airplay" may come later) </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_renderer_item_8h_source.html">VLCRendererItem.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
