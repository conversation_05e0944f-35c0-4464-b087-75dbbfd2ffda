<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>VLCKit: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Related&#160;Pages</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCMediaListPlayer Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media_list_player.html#a39e3151a069ead5d9bdc4bd1d8b0e815">initWithDrawable:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media_list_player.html#a2026b15a0a74d3be733346074f44ba18">initWithOptions:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media_list_player.html#a9229793245755c2bf82cedf7d4032a90">initWithOptions:andDrawable:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html#a9a58ef35e80015f0441ae1861856a2bf">mediaList</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html#a77a18025616fa5fa2d06a539f6eea9f3">mediaPlayer</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html#a9443120446a79cd785b1e2b71882f199">next</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media_list_player.html#ac0e7b5153919a1108723be359773f7f9">pause</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media_list_player.html#a2f22768be90786ad490b4c3ee07900c9">play</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media_list_player.html#a0b70c60e898b6ac244486fe11f8ec6b3">playItemAtIndex:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media_list_player.html#a08850682934ccc36da966054281b34d3">playItemAtNumber:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media_list_player.html#a001f13e8971cb4073dd4c148192a23a9">playMedia:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html#afae8b92f265eeec5b87bd55bd0fd104c">previous</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html#a7f868c26a93d999852c3448d28913af4">repeatMode</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html#ac1a1b1d304efb4e1c62026f9ed321e9d">rootMedia</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media_list_player.html#ab4473d33d43e75b4c73fd9ed1ec5cc2c">stop</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
