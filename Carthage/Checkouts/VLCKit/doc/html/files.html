<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: File List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">File List</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">Here is a list of all documented files with brief descriptions:</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span><span onclick="javascript:toggleLevel(3);">3</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_0_" class="arrow" onclick="toggleFolder('0_')">&#9660;</span><span id="img_0_" class="iconfopen" onclick="toggleFolder('0_')">&#160;</span><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html" target="_self">Headers</a></td><td class="desc"></td></tr>
<tr id="row_0_0_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_0_0_" class="arrow" onclick="toggleFolder('0_0_')">&#9660;</span><span id="img_0_0_" class="iconfopen" onclick="toggleFolder('0_0_')">&#160;</span><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html" target="_self">Public</a></td><td class="desc"></td></tr>
<tr id="row_0_0_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_dynamic_mobile_v_l_c_kit_8h_source.html"><span class="icondoc"></span></a><b>DynamicMobileVLCKit.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_1_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_dynamic_t_v_v_l_c_kit_8h_source.html"><span class="icondoc"></span></a><b>DynamicTVVLCKit.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_2_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_mobile_v_l_c_kit_8h_source.html"><span class="icondoc"></span></a><b>MobileVLCKit.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_3_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_t_v_v_l_c_kit_8h_source.html"><span class="icondoc"></span></a><b>TVVLCKit.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_4_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_audio_8h_source.html"><span class="icondoc"></span></a><b>VLCAudio.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_5_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_dialog_provider_8h_source.html"><span class="icondoc"></span></a><b>VLCDialogProvider.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_6_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_kit_8h_source.html"><span class="icondoc"></span></a><b>VLCKit.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_7_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_library_8h_source.html"><span class="icondoc"></span></a><b>VLCLibrary.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_8_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_media_8h_source.html"><span class="icondoc"></span></a><b>VLCMedia.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_9_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_media_discoverer_8h_source.html"><span class="icondoc"></span></a><b>VLCMediaDiscoverer.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_10_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_media_library_8h_source.html"><span class="icondoc"></span></a><b>VLCMediaLibrary.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_11_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_media_list_8h_source.html"><span class="icondoc"></span></a><b>VLCMediaList.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_12_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_media_list_player_8h_source.html"><span class="icondoc"></span></a><b>VLCMediaListPlayer.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_13_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_media_player_8h_source.html"><span class="icondoc"></span></a><b>VLCMediaPlayer.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_14_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_media_thumbnailer_8h_source.html"><span class="icondoc"></span></a><b>VLCMediaThumbnailer.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_15_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_renderer_discoverer_8h_source.html"><span class="icondoc"></span></a><b>VLCRendererDiscoverer.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_16_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_renderer_item_8h_source.html"><span class="icondoc"></span></a><b>VLCRendererItem.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_17_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_stream_output_8h_source.html"><span class="icondoc"></span></a><b>VLCStreamOutput.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_18_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_stream_session_8h_source.html"><span class="icondoc"></span></a><b>VLCStreamSession.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_19_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_time_8h_source.html"><span class="icondoc"></span></a><b>VLCTime.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_20_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_transcoder_8h_source.html"><span class="icondoc"></span></a><b>VLCTranscoder.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_21_"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_video_layer_8h_source.html"><span class="icondoc"></span></a><b>VLCVideoLayer.h</b></td><td class="desc"></td></tr>
<tr id="row_0_0_22_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><a href="_v_l_c_video_view_8h_source.html"><span class="icondoc"></span></a><b>VLCVideoView.h</b></td><td class="desc"></td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
