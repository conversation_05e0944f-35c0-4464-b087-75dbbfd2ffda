<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.14"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: &lt;VLCMediaDelegate &gt; Protocol Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.14 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="protocol_v_l_c_media_delegate_01-p-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">&lt;VLCMediaDelegate &gt; Protocol Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_media_8h_source.html">VLCMedia.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for &lt;VLCMediaDelegate &gt;:</div>
<div class="dyncontent">
 <div class="center">
  <img src="protocol_v_l_c_media_delegate_01-p.png" usemap="#_3CVLCMediaDelegate_20_3E_map" alt=""/>
  <map id="_3CVLCMediaDelegate_20_3E_map" name="_3CVLCMediaDelegate_20_3E_map">
</map>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:aed1d7f2ff7d8f89ba0cbbf780e09427c"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_delegate_01-p.html#aed1d7f2ff7d8f89ba0cbbf780e09427c">mediaMetaDataDidChange:</a></td></tr>
<tr class="separator:aed1d7f2ff7d8f89ba0cbbf780e09427c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a60bcf0b83c3c0a1bc876ce818feba300"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_delegate_01-p.html#a60bcf0b83c3c0a1bc876ce818feba300">mediaDidFinishParsing:</a></td></tr>
<tr class="separator:a60bcf0b83c3c0a1bc876ce818feba300"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Informal protocol declaration for <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> delegates. Allows data changes to be trapped. </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="a60bcf0b83c3c0a1bc876ce818feba300"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a60bcf0b83c3c0a1bc876ce818feba300">&#9670;&nbsp;</a></span>mediaDidFinishParsing:()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void VLCMediaDelegate) mediaDidFinishParsing: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *)&#160;</td>
          <td class="paramname"><em>aMedia</em></td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Delegate method called whenever the media was parsed. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aMedia</td><td>The media resource whose meta data has been changed. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aed1d7f2ff7d8f89ba0cbbf780e09427c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aed1d7f2ff7d8f89ba0cbbf780e09427c">&#9670;&nbsp;</a></span>mediaMetaDataDidChange:()</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void VLCMediaDelegate) mediaMetaDataDidChange: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *)&#160;</td>
          <td class="paramname"><em>aMedia</em></td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Delegate method called whenever the media's meta data was changed for whatever reason </p><dl class="section note"><dt>Note</dt><dd>this is called more often than mediaDidFinishParsing, so it may be less efficient </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aMedia</td><td>The media resource whose meta data has been changed. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this protocol was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_media_8h_source.html">VLCMedia.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.14
</small></address>
</body>
</html>
