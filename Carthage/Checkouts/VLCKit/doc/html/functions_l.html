<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_l"></a>- l -</h3><ul>
<li>lastSnapshot
: <a class="el" href="interface_v_l_c_media_player.html#a0ce7b35bcd4b876e071f73fb558304df">VLCMediaPlayer</a>
</li>
<li>length
: <a class="el" href="interface_v_l_c_media.html#afd47b541ffd9e93a5864ced1f127101d">VLCMedia</a>
</li>
<li>lengthWaitUntilDate:
: <a class="el" href="interface_v_l_c_media.html#a82e93da5f18bf8584beff1b714d496d4">VLCMedia</a>
</li>
<li>libraryInstance
: <a class="el" href="interface_v_l_c_media_discoverer.html#a7c84bb252d3183d9314b447a16a68b06">VLCMediaDiscoverer</a>
, <a class="el" href="interface_v_l_c_media_player.html#a32b5af405c337b3704957d7c15cbdd61">VLCMediaPlayer</a>
</li>
<li>libVLCinstance
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#a14051a6f88fd60fb723c6de3aa5a7321">VLCMediaThumbnailer</a>
</li>
<li>list
: <a class="el" href="interface_v_l_c_renderer_discoverer.html#aa6ffdcd169141a032021df59fdfe3e14">VLCRendererDiscoverer</a>
</li>
<li>lock
: <a class="el" href="interface_v_l_c_media_list.html#aa852de8d98be00568958006826b24661">VLCMediaList</a>
</li>
<li>longJumpBackward
: <a class="el" href="interface_v_l_c_media_player.html#a78d5c9af63086443c9539c06b63bff4d">VLCMediaPlayer</a>
</li>
<li>longJumpForward
: <a class="el" href="interface_v_l_c_media_player.html#ad04f7a86349209e50615afc1a513a768">VLCMediaPlayer</a>
</li>
<li>longName
: <a class="el" href="interface_v_l_c_renderer_discoverer_description.html#a164296731eab83b21084576ded002861">VLCRendererDiscovererDescription</a>
</li>
<li>loudnessValue
: <a class="el" href="interface_v_l_c_media_loudness.html#a70a3e717400017a173498a9c802cc70f">VLCMediaLoudness</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
