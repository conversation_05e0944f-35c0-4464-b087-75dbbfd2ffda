<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCVideoView Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_video_view-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCVideoView Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_video_view_8h_source.html">VLCVideoView.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCVideoView:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_video_view.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a3a12c7c5dc64b90fc469e435e86c2ef4"><td class="memItemLeft" align="right" valign="top">NSColor *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_video_view.html#a3a12c7c5dc64b90fc469e435e86c2ef4">backColor</a></td></tr>
<tr class="separator:a3a12c7c5dc64b90fc469e435e86c2ef4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83cc53903dedd9770bb0a1fb68235db2"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_video_view.html#a83cc53903dedd9770bb0a1fb68235db2">hasVideo</a></td></tr>
<tr class="separator:a83cc53903dedd9770bb0a1fb68235db2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac43fc86fcef23d9e87bcb84c477d68da"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_video_view.html#ac43fc86fcef23d9e87bcb84c477d68da">fillScreen</a></td></tr>
<tr class="separator:ac43fc86fcef23d9e87bcb84c477d68da"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>a custom view suitable for video rendering in AppKit environments </p>
</div><h2 class="groupheader">Property Documentation</h2>
<a id="a3a12c7c5dc64b90fc469e435e86c2ef4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3a12c7c5dc64b90fc469e435e86c2ef4">&#9670;&nbsp;</a></span>backColor</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSColor*) backColor</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>NSColor to set as the view background if no video is being rendered </p>

</div>
</div>
<a id="ac43fc86fcef23d9e87bcb84c477d68da"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac43fc86fcef23d9e87bcb84c477d68da">&#9670;&nbsp;</a></span>fillScreen</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) fillScreen</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Should the video fill the screen by adding letterboxing or stretching? </p><dl class="section return"><dt>Returns</dt><dd>the BOOL value </dd></dl>

</div>
</div>
<a id="a83cc53903dedd9770bb0a1fb68235db2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a83cc53903dedd9770bb0a1fb68235db2">&#9670;&nbsp;</a></span>hasVideo</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) hasVideo</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Is a video being rendered in this layer? </p><dl class="section return"><dt>Returns</dt><dd>the BOOL value </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_video_view_8h_source.html">VLCVideoView.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
