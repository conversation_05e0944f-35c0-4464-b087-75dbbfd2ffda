<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCTranscoder Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_transcoder-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCTranscoder Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_transcoder_8h_source.html">VLCTranscoder.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCTranscoder:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_transcoder.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a0ec64622da15a7b9826a99b1d242778f"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_transcoder.html#a0ec64622da15a7b9826a99b1d242778f">reencodeAndMuxSRTFile:toMP4File:outputPath:</a></td></tr>
<tr class="separator:a0ec64622da15a7b9826a99b1d242778f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a66e9bdba467fe831193fc8633b11dfe2"><td class="memItemLeft" align="right" valign="top">id&lt; <a class="el" href="protocol_v_l_c_transcoder_delegate-p.html">VLCTranscoderDelegate</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_transcoder.html#a66e9bdba467fe831193fc8633b11dfe2">delegate</a></td></tr>
<tr class="separator:a66e9bdba467fe831193fc8633b11dfe2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Provides an object to convert a subtitle file and moviefile into one. </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="a0ec64622da15a7b9826a99b1d242778f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0ec64622da15a7b9826a99b1d242778f">&#9670;&nbsp;</a></span>reencodeAndMuxSRTFile:toMP4File:outputPath:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) reencodeAndMuxSRTFile: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>srtPath</em></td>
        </tr>
        <tr>
          <td class="paramkey">toMP4File:</td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>mp4Path</em></td>
        </tr>
        <tr>
          <td class="paramkey">outputPath:</td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>outPath</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Reencode and remuxes an srt and mp4 file to an mkv file with embedded subtitles either with VideoToolbox-based H264 encoding or VP80 is Videotoolbox is not available </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">srtPath</td><td>path to srt file </td></tr>
    <tr><td class="paramname">mp4Path</td><td>path to mp4 file </td></tr>
    <tr><td class="paramname">outPath</td><td>path where the new file should be written to </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>an BOOL with the success status, returns NO if the subtitle file is not an srt or mp4File is not an mp4 file or the files don't exist at that path or transcoding failed for other reasons </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a66e9bdba467fe831193fc8633b11dfe2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a66e9bdba467fe831193fc8633b11dfe2">&#9670;&nbsp;</a></span>delegate</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (id&lt;<a class="el" href="protocol_v_l_c_transcoder_delegate-p.html">VLCTranscoderDelegate</a>&gt;) delegate</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">weak</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>the delegate object implementing the optional protocol </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_transcoder_8h_source.html">VLCTranscoder.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
