<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCMedia Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="interface_v_l_c_media.html">VLCMedia</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#ac32a90c64851638af38108040b37e454">__attribute__</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a28c23c5d427727732476f86c6d0645ee">__attribute__</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a28c23c5d427727732476f86c6d0645ee">__attribute__</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#ab09b1de8ddcfae6c2ecc331777d54119">addOption:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry">-&#160;</td><td class="entry"><b>addOptions:</b> (defined in <a class="el" href="interface_v_l_c_media.html">VLCMedia</a>)</td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a51bdb09726b9f4d72072e144ea7314cc">clearStoredCookies</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">+&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a98f23cb39854168dc79df832cecb7ff2">codecNameForFourCC:trackType:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#ac0b967f4529a5c2a23972cca9fd2a800">compare:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a2cb849f8dceb22cebbac149921c785a5">delegate</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a1e060d1cb138c0e0ecffe53d985b2dd3">demuxBitrate</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#af9a048525b9aeb4919c47e1148962638">initAsNodeWithName:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a4215c08e40a19e60bf10a9ea15b8fb85">initWithPath:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a0f4a6c10ac143fdcd19bc12fdb2bb71a">initWithStream:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a1a980dff03ccacf966e754c0a60bac49">initWithURL:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a2eb646a3d37eaec7de62ba174b9682f7">inputBitrate</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#afd47b541ffd9e93a5864ced1f127101d">length</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a82e93da5f18bf8584beff1b714d496d4">lengthWaitUntilDate:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry">+&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a3aaff98fc9546ceaf4c1871577c00a17">mediaAsNodeWithName:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a2018328bfcb5934f725b285026fe4e98">mediaSizeSuitableForDevice</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a8f6e78f8cc5d52384047ddaea9e01dcf">mediaType</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">+&#160;</td><td><a class="el" href="interface_v_l_c_media.html#ae65d970c9c066ab28ec0c8bdcc076101">mediaWithPath:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry">+&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a38e5fb8f18d50b6de684a7e56c1611fa">mediaWithURL:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#af495ec3452fdcdcd89aa2a13695ba6dd">metadataForKey:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#ac61f729efe7481e86d26e7e92fff0dd2">metaDictionary</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#af4439eb3074ae064da27365b68ddbfc8">NS_ENUM</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a3ae7faaa9307383b5651794c141b3e5e">NS_ENUM</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a353de8b3f3676f9f422630ed595fdfcc">NS_ENUM</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a4ad7dacc361919932777b2bf5a141023">NS_ENUM</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"><span class="mlabel">inline</span></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a289dc0ff117c7013b6b5363d9f35fd01">numberOfCorruptedDataPackets</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#ae5f6aa8f4cfd924c9f31cea1292739de">numberOfDecodedAudioBlocks</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a0d65e705f516777543cb6ac2df310779">numberOfDecodedVideoBlocks</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a148f7e15ef691ed0c6cf631eb3bc34d8">numberOfDiscontinuties</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a42f0be6a3830572833122e758ddaafb1">numberOfDisplayedPictures</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a13d927d07a8bc2cebab7363317c0a932">numberOfLostAudioBuffers</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#ab7456ceac9f4ac4b395bcc50064d58dd">numberOfLostPictures</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a958ceff6c2c01085c9c11963fc00e9ab">numberOfPlayedAudioBuffers</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a936f14e9dbdb6355604040bb963cf1b2">numberOfReadBytesOnDemux</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#aadc4d2257ae507913c39611e9c935665">numberOfReadBytesOnInput</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a4f663bcbd8cfea3c1fa23035a5e2e119">numberOfSentBytes</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a65060dbc9eefe3518c4aa81daba05320">numberOfSentPackets</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#adc94b1c776ed671be57746c79e04f187">parsedStatus</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#a0213a3ea482353bce0d7bb59355d497a">parseStop</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#aecfb52ec0989cd489fdc2966cd431586">parseWithOptions:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#ac00685e5d9a33652413b298c43423b5a">parseWithOptions:timeout:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a2332173d72093469abebf56f4c70ae80">saveMetadata</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#ab1f738fbdeaa9efaf918223c0ed187e4">setMetadata:forKey:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#af247fea93ce48e219ddef15bdaf256de">state</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a9c2d85f9c2dba700d7b2ca18cf12049a">stats</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media.html#ac04b45047fa221e26ac0c589e28fc5ff">storeCookie:forHost:path:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#afd944ae42af805d532f4ab36d5b0fe7d">streamOutputBitrate</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a08f3d51d9b8199fd20143d178b368b2f">subitems</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a7b098bacc67ab0ff8fa9d316bef987d6">tracksInformation</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#aef3995dbdd704cc5c8ed4fc2e383e0a6">url</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba0184ba9260e22d0182cf418b2d73c20c">VLCMediaDoInteract</a> enum value</td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba802f388c99aa52b9bd26a11f55ffd4f0">VLCMediaFetchLocal</a> enum value</td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cbaf9a01d0578ab528242b3de74d5e216dc">VLCMediaFetchNetwork</a> enum value</td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba96d692455552df3b6332ce02b5c955a1">VLCMediaParseLocal</a> enum value</td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cbadac6d0a10ac08b36376db311ce6a91a5">VLCMediaParseNetwork</a> enum value</td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">VLCMediaParsingOptions</a> typedef</td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#aabf94e7de92ae328dba46d6c53e5d869">VLCMediaTracksInformationAudioChannelsNumber</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a5f42247ad4cefc2cfa4a96bd95f53356">VLCMediaTracksInformationAudioRate</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#add3bac6827f60b1cbe44544c106b39c0">VLCMediaTracksInformationBitrate</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a0e44431952021460c5f59f600236630b">VLCMediaTracksInformationCodec</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#ae380aafa86ebd25ad38ab630a6dc86dd">VLCMediaTracksInformationCodecLevel</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a076812e00bd51440c4d47da823011f86">VLCMediaTracksInformationCodecProfile</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a480f68a87f30c723f9364f00620de519">VLCMediaTracksInformationDescription</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#afd5e8623f3246506f21576ca006df47e">VLCMediaTracksInformationFrameRate</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a635234c93bcb43393868435ab98ad0a8">VLCMediaTracksInformationFrameRateDenominator</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a523d5f9351c2fcac0d9b600773734c81">VLCMediaTracksInformationId</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#ac6879d1635a7c5c306bafc23cbed755a">VLCMediaTracksInformationLanguage</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#ada9d9ba5acf71414913ecc83cb975bf6">VLCMediaTracksInformationSourceAspectRatio</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#aa3b1ead249368c4b73a544f07c84bcdc">VLCMediaTracksInformationSourceAspectRatioDenominator</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#ad173cd33fb9d51175e676b62838cd980">VLCMediaTracksInformationTextEncoding</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#ac5ccaa4e433a8bc847e54739d69827b7">VLCMediaTracksInformationType</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#ac6eb47ffe2b3a79f562e0164e83416b1">VLCMediaTracksInformationTypeAudio</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a3d8f7f156478e43c45dfedf7459c9939">VLCMediaTracksInformationTypeText</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a2b91c4e456c6ce07682477d41772adc2">VLCMediaTracksInformationTypeUnknown</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a2ac631a7a8c7416ac9a13b914efeb22e">VLCMediaTracksInformationTypeVideo</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a52414aa5aff9e0e929d6b3dad0461dd2">VLCMediaTracksInformationVideoHeight</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a32e842b07314f6b7a965fd8d5770bf8d">VLCMediaTracksInformationVideoOrientation</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#ac28eab6679f8761ce13ea02d61562d21">VLCMediaTracksInformationVideoProjection</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media.html#a29ca0a5036cfa556f5d7098c44030123">VLCMediaTracksInformationVideoWidth</a></td><td class="entry"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
