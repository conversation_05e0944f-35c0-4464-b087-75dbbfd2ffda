<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCAudio Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_audio-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCAudio Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_audio_8h_source.html">VLCAudio.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCAudio:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_audio.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:ab5c770cd553794ec294c77fd4e56668d"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_audio.html#ab5c770cd553794ec294c77fd4e56668d">setMute:</a></td></tr>
<tr class="separator:ab5c770cd553794ec294c77fd4e56668d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a35b0ee4fbc502b52f7ae89970b27a246"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_audio.html#a35b0ee4fbc502b52f7ae89970b27a246">volumeDown</a></td></tr>
<tr class="separator:a35b0ee4fbc502b52f7ae89970b27a246"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abce4117645f4fc354f457dca9a991aa3"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_audio.html#abce4117645f4fc354f457dca9a991aa3">volumeUp</a></td></tr>
<tr class="separator:abce4117645f4fc354f457dca9a991aa3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:aa2739a3d1ec35d69a9920e4a9588ef8c"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_audio.html#aa2739a3d1ec35d69a9920e4a9588ef8c">muted</a></td></tr>
<tr class="separator:aa2739a3d1ec35d69a9920e4a9588ef8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a553194cab2c141b89804db92e1a43f87"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_audio.html#a553194cab2c141b89804db92e1a43f87">volume</a></td></tr>
<tr class="separator:a553194cab2c141b89804db92e1a43f87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac37223907edb0cafcee6f609edc28782"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_audio.html#ac37223907edb0cafcee6f609edc28782">passthrough</a></td></tr>
<tr class="separator:ac37223907edb0cafcee6f609edc28782"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>basic class to control audio output </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="ab5c770cd553794ec294c77fd4e56668d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab5c770cd553794ec294c77fd4e56668d">&#9670;&nbsp;</a></span>setMute:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) setMute: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Mute the current audio output. </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000001">Deprecated:</a></b></dt><dd>This selector will be removed in the next release </dd></dl>

</div>
</div>
<a id="a35b0ee4fbc502b52f7ae89970b27a246"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a35b0ee4fbc502b52f7ae89970b27a246">&#9670;&nbsp;</a></span>volumeDown</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) volumeDown </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>lower the current audio output volume </p>

</div>
</div>
<a id="abce4117645f4fc354f457dca9a991aa3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abce4117645f4fc354f457dca9a991aa3">&#9670;&nbsp;</a></span>volumeUp</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) volumeUp </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>higher the current audio output volume </p>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="aa2739a3d1ec35d69a9920e4a9588ef8c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa2739a3d1ec35d69a9920e4a9588ef8c">&#9670;&nbsp;</a></span>muted</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) muted</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Property to mute the current audio output </p><dl class="section note"><dt>Note</dt><dd>decoding continues when muted, so consider disabling the audio track if you don't want audio for a long time </dd></dl>

</div>
</div>
<a id="ac37223907edb0cafcee6f609edc28782"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac37223907edb0cafcee6f609edc28782">&#9670;&nbsp;</a></span>passthrough</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) passthrough</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>enable passthrough mode for the current audio device </p><dl class="section note"><dt>Note</dt><dd>There is no warrenty that it succeeds as it depends on the capabilities of the hardware audio decoder / receiver attached by the user </dd></dl>

</div>
</div>
<a id="a553194cab2c141b89804db92e1a43f87"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a553194cab2c141b89804db92e1a43f87">&#9670;&nbsp;</a></span>volume</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) volume</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>control the current audio output volume </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_audio_8h_source.html">VLCAudio.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
