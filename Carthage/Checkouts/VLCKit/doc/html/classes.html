<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Index</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Class Index</div>  </div>
</div><!--header-->
<div class="contents">
<div class="qindex"><a class="qindex" href="#letter_v">v</a></div>
<table class="classindex">
<tr><td rowspan="2" valign="bottom"><a name="letter_v"></a><table border="0" cellspacing="0" cellpadding="0"><tr><td><div class="ah">&#160;&#160;v&#160;&#160;</div></td></tr></table>
</td>
<td valign="top"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html">VLCMediaListPlayerDelegate</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="protocol_v_l_c_renderer_discoverer_delegate-p.html">VLCRendererDiscovererDelegate</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="protocol_v_l_c_transcoder_delegate-p.html">VLCTranscoderDelegate</a>&#160;&#160;&#160;</td>
</tr>
<tr><td valign="top"><a class="el" href="protocol_v_l_c_media_delegate-p.html">VLCMediaDelegate</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_media_loudness.html">VLCMediaLoudness</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_renderer_discoverer_description.html">VLCRendererDiscovererDescription</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_video_layer.html">VLCVideoLayer</a>&#160;&#160;&#160;</td>
</tr>
<tr><td valign="top"><a class="el" href="interface_v_l_c_audio.html">VLCAudio</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_media_discoverer.html">VLCMediaDiscoverer</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_video_view.html">VLCVideoView</a>&#160;&#160;&#160;</td>
</tr>
<tr><td valign="top"><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html">VLCCustomDialogRendererProtocol</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_media_library.html">VLCMediaLibrary</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="protocol_v_l_c_media_player_delegate-p.html">VLCMediaPlayerDelegate</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_stream_output.html">VLCStreamOutput</a>&#160;&#160;&#160;</td>
<td></td></tr>
<tr><td valign="top"><a class="el" href="interface_v_l_c_dialog_provider.html">VLCDialogProvider</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_stream_session.html">VLCStreamSession</a>&#160;&#160;&#160;</td>
<td></td></tr>
<tr><td valign="top"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="protocol_v_l_c_media_list_delegate-p.html">VLCMediaListDelegate</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="protocol_v_l_c_media_thumbnailer_delegate-p.html">VLCMediaThumbnailerDelegate</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_time.html">VLCTime</a>&#160;&#160;&#160;</td>
<td></td></tr>
<tr><td valign="top"><a class="el" href="protocol_v_l_c_library_log_receiver_protocol-p.html">VLCLibraryLogReceiverProtocol</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a>&#160;&#160;&#160;</td>
<td valign="top"><a class="el" href="interface_v_l_c_transcoder.html">VLCTranscoder</a>&#160;&#160;&#160;</td>
<td></td></tr>
<tr><td></td><td></td><td></td><td></td><td></td></tr>
</table>
<div class="qindex"><a class="qindex" href="#letter_v">v</a></div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
