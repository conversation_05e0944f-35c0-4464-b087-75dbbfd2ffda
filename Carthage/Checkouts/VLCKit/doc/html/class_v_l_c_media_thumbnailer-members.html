<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>VLCKit: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Related&#160;Pages</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCMediaThumbnailer Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html#a48ad5cbc29377c223ed35eb92097d4ac">delegate</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_media_thumbnailer.html#a8c0bce3c2eef22eae4d8b046ce37761b">fetchThumbnail</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html#a14051a6f88fd60fb723c6de3aa5a7321">libVLCinstance</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html#aecc4d4aeea24bd960190a0d1a47a5a56">media</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html#ab6c36054a654a28cc678082d0a1e02fa">snapshotPosition</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html#a2cc0a6c7836be7d967858c67e51a5f50">thumbnail</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">+&#160;</td><td><a class="el" href="interface_v_l_c_media_thumbnailer.html#a364d20f11c696b300c42fd5eb404c3f9">thumbnailerWithMedia:andDelegate:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry">+&#160;</td><td><a class="el" href="interface_v_l_c_media_thumbnailer.html#a8b4b7e2a8aa3b3c51146916ef33dc043">thumbnailerWithMedia:delegate:andVLCLibrary:</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html#ad183c5b8990465157f049c61c2e188a0">thumbnailHeight</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html#a8c71e4fc706ae741252eecb95d33a055">thumbnailWidth</a></td><td class="entry"><a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
