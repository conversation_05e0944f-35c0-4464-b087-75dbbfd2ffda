<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCMediaList Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_media_list-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCMediaList Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_media_list_8h_source.html">VLCMediaList.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCMediaList:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_media_list.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a29976e5d1b3eae05b6b052d2765f5090"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list.html#a29976e5d1b3eae05b6b052d2765f5090">initWithArray:</a></td></tr>
<tr class="separator:a29976e5d1b3eae05b6b052d2765f5090"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa852de8d98be00568958006826b24661"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list.html#aa852de8d98be00568958006826b24661">lock</a></td></tr>
<tr class="separator:aa852de8d98be00568958006826b24661"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a697c6eebd811e4a9db798b9ee4044f"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list.html#a1a697c6eebd811e4a9db798b9ee4044f">unlock</a></td></tr>
<tr class="separator:a1a697c6eebd811e4a9db798b9ee4044f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42eb32becfd0b41b61094a8f37dd2ed0"><td class="memItemLeft" align="right" valign="top">(NSUInteger)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list.html#a42eb32becfd0b41b61094a8f37dd2ed0">addMedia:</a></td></tr>
<tr class="separator:a42eb32becfd0b41b61094a8f37dd2ed0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6855e74eaf93ec3e512d683423ecc2e3"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list.html#a6855e74eaf93ec3e512d683423ecc2e3">insertMedia:atIndex:</a></td></tr>
<tr class="separator:a6855e74eaf93ec3e512d683423ecc2e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16aeab5bf78f68472369386d7a2eaae7"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list.html#a16aeab5bf78f68472369386d7a2eaae7">removeMediaAtIndex:</a></td></tr>
<tr class="separator:a16aeab5bf78f68472369386d7a2eaae7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af07ba2e377bd890b7b12c61ac34a9e5c"><td class="memItemLeft" align="right" valign="top">(<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list.html#af07ba2e377bd890b7b12c61ac34a9e5c">mediaAtIndex:</a></td></tr>
<tr class="separator:af07ba2e377bd890b7b12c61ac34a9e5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a38eeb1fa92b98b7b4823acf39e8e8865"><td class="memItemLeft" align="right" valign="top">(NSUInteger)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list.html#a38eeb1fa92b98b7b4823acf39e8e8865">indexOfMedia:</a></td></tr>
<tr class="separator:a38eeb1fa92b98b7b4823acf39e8e8865"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:aa91841c1b03e5a4640ef1bed6c621b44"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_list.html#aa91841c1b03e5a4640ef1bed6c621b44">count</a></td></tr>
<tr class="separator:aa91841c1b03e5a4640ef1bed6c621b44"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa5d8f85f2a5e5492177de14912028f63"><td class="memItemLeft" align="right" valign="top">id&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_list.html#aa5d8f85f2a5e5492177de14912028f63">delegate</a></td></tr>
<tr class="separator:aa5d8f85f2a5e5492177de14912028f63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9f5b01b188f801ba9a2d675d6d94962"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_list.html#ad9f5b01b188f801ba9a2d675d6d94962">isReadOnly</a></td></tr>
<tr class="separator:ad9f5b01b188f801ba9a2d675d6d94962"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a> </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="a42eb32becfd0b41b61094a8f37dd2ed0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a42eb32becfd0b41b61094a8f37dd2ed0">&#9670;&nbsp;</a></span>addMedia:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSUInteger) addMedia: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *)&#160;</td>
          <td class="paramname"><em>media</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>add a media to a read-write list</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">media</td><td>the media object to add </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the index of the newly added media </dd></dl>
<dl class="section note"><dt>Note</dt><dd>this function silently fails if the list is read-only </dd></dl>

</div>
</div>
<a id="a38eeb1fa92b98b7b4823acf39e8e8865"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a38eeb1fa92b98b7b4823acf39e8e8865">&#9670;&nbsp;</a></span>indexOfMedia:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSUInteger) indexOfMedia: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *)&#160;</td>
          <td class="paramname"><em>media</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>retrieve the position of a media item</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">media</td><td>the media object to search for </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The lowest index of the provided media in the list If media does not exist in the list, returns NSNotFound. </dd></dl>

</div>
</div>
<a id="a29976e5d1b3eae05b6b052d2765f5090"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a29976e5d1b3eae05b6b052d2765f5090">&#9670;&nbsp;</a></span>initWithArray:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithArray: </td>
          <td></td>
          <td class="paramtype">(NSArray *)&#160;</td>
          <td class="paramname"><em>array</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>initializer with a set of <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> instances </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">array</td><td>the NSArray of <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> instances </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>instance of <a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a> equipped with the <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> instances </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="interface_v_l_c_media.html">VLCMedia</a> </dd></dl>

</div>
</div>
<a id="a6855e74eaf93ec3e512d683423ecc2e3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6855e74eaf93ec3e512d683423ecc2e3">&#9670;&nbsp;</a></span>insertMedia:atIndex:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) insertMedia: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *)&#160;</td>
          <td class="paramname"><em>media</em></td>
        </tr>
        <tr>
          <td class="paramkey">atIndex:</td>
          <td></td>
          <td class="paramtype">(NSUInteger)&#160;</td>
          <td class="paramname"><em>index</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>add a media to a read-write list at a given position</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">media</td><td>the media object to add </td></tr>
    <tr><td class="paramname">index</td><td>the index where to add the given media </td></tr>
  </table>
  </dd>
</dl>
<dl class="section note"><dt>Note</dt><dd>this function silently fails if the list is read-only </dd></dl>

</div>
</div>
<a id="aa852de8d98be00568958006826b24661"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa852de8d98be00568958006826b24661">&#9670;&nbsp;</a></span>lock</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) lock </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>lock the media list from being edited by another thread </p>

</div>
</div>
<a id="af07ba2e377bd890b7b12c61ac34a9e5c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af07ba2e377bd890b7b12c61ac34a9e5c">&#9670;&nbsp;</a></span>mediaAtIndex:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *) mediaAtIndex: </td>
          <td></td>
          <td class="paramtype">(NSUInteger)&#160;</td>
          <td class="paramname"><em>index</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>retrieve a media from a given position</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">index</td><td>the index of the media you want </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the media object </dd></dl>

</div>
</div>
<a id="a16aeab5bf78f68472369386d7a2eaae7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a16aeab5bf78f68472369386d7a2eaae7">&#9670;&nbsp;</a></span>removeMediaAtIndex:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) removeMediaAtIndex: </td>
          <td></td>
          <td class="paramtype">(NSUInteger)&#160;</td>
          <td class="paramname"><em>index</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>remove media at position index and return true if the operation was successful. An unsuccessful operation occurs when the index is greater than the medialists count</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">index</td><td>the index of the media to remove </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>boolean result of the removal operation </dd></dl>
<dl class="section note"><dt>Note</dt><dd>this function silently fails if the list is read-only </dd></dl>

</div>
</div>
<a id="a1a697c6eebd811e4a9db798b9ee4044f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a697c6eebd811e4a9db798b9ee4044f">&#9670;&nbsp;</a></span>unlock</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) unlock </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>unlock the media list from being edited by another thread </p>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="aa91841c1b03e5a4640ef1bed6c621b44"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa91841c1b03e5a4640ef1bed6c621b44">&#9670;&nbsp;</a></span>count</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) count</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>count number of media items in the list </p><dl class="section return"><dt>Returns</dt><dd>the number of media objects </dd></dl>

</div>
</div>
<a id="aa5d8f85f2a5e5492177de14912028f63"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa5d8f85f2a5e5492177de14912028f63">&#9670;&nbsp;</a></span>delegate</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (id) delegate</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">weak</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>delegate property to listen to addition/removal events </p>

</div>
</div>
<a id="ad9f5b01b188f801ba9a2d675d6d94962"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad9f5b01b188f801ba9a2d675d6d94962">&#9670;&nbsp;</a></span>isReadOnly</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) isReadOnly</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>read-only property to check if the media list is writable or not </p><dl class="section return"><dt>Returns</dt><dd>boolean value if the list is read-only </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_media_list_8h_source.html">VLCMediaList.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
