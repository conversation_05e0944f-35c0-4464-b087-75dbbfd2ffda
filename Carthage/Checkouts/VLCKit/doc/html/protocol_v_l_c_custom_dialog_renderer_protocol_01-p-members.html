<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.14"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.14 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">&lt;VLCCustomDialogRendererProtocol &gt; Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html">&lt;VLCCustomDialogRendererProtocol &gt;</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html#a288ed9f961ae4bb468db61e35b631da2">cancelDialogWithReference:</a></td><td class="entry"><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html">&lt;VLCCustomDialogRendererProtocol &gt;</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html#ab4308c597c3b339bb50085e3c5750075">showErrorWithTitle:message:</a></td><td class="entry"><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html">&lt;VLCCustomDialogRendererProtocol &gt;</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html#af369ee55629e2172323081fb4e85dba4">showLoginWithTitle:message:defaultUsername:askingForStorage:withReference:</a></td><td class="entry"><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html">&lt;VLCCustomDialogRendererProtocol &gt;</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html#a008624d653baaff6b763bc40852ce065">showProgressWithTitle:message:isIndeterminate:position:cancelString:withReference:</a></td><td class="entry"><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html">&lt;VLCCustomDialogRendererProtocol &gt;</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html#aaf89ec0239b9d1533e007c7f4af38dfc">showQuestionWithTitle:message:type:cancelString:action1String:action2String:withReference:</a></td><td class="entry"><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html">&lt;VLCCustomDialogRendererProtocol &gt;</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html#a7b5d3cd75a42166e5c5eb3e9e08229b2">updateProgressWithReference:message:postion:</a></td><td class="entry"><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html">&lt;VLCCustomDialogRendererProtocol &gt;</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.14
</small></address>
</body>
</html>
