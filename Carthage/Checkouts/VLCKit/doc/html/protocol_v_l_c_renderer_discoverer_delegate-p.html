<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: &lt;VLCRendererDiscovererDelegate&gt; Protocol Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="protocol_v_l_c_renderer_discoverer_delegate-p-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">&lt;VLCRendererDiscovererDelegate&gt; Protocol Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_renderer_discoverer_8h_source.html">VLCRendererDiscoverer.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for &lt;VLCRendererDiscovererDelegate&gt;:</div>
<div class="dyncontent">
 <div class="center">
  <img src="protocol_v_l_c_renderer_discoverer_delegate-p.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:ace1824f67c69b1d102aa1c232df85589"><td class="memItemLeft" align="right" valign="top"><a id="ace1824f67c69b1d102aa1c232df85589"></a>
(void)&#160;</td><td class="memItemRight" valign="bottom">- <b>rendererDiscovererItemAdded:item:</b></td></tr>
<tr class="separator:ace1824f67c69b1d102aa1c232df85589"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae81b499f745979ab59e10c8a2e2d8e21"><td class="memItemLeft" align="right" valign="top"><a id="ae81b499f745979ab59e10c8a2e2d8e21"></a>
(void)&#160;</td><td class="memItemRight" valign="bottom">- <b>rendererDiscovererItemDeleted:item:</b></td></tr>
<tr class="separator:ae81b499f745979ab59e10c8a2e2d8e21"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Renderer Discoverer delegate protocol Allows to be notified upon discovery/changes of an renderer item </p>
</div><hr/>The documentation for this protocol was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_renderer_discoverer_8h_source.html">VLCRendererDiscoverer.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
