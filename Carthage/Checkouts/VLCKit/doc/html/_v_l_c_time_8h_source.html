<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Headers/Public/VLCTime.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html">Headers</a></li><li class="navelem"><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html">Public</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCTime.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno"><a class="line" href="interface_v_l_c_time.html#af9b7c469a7cb064a3d9c2c5bcaa769bb">    1</a></span>&#160;<span class="comment">/*****************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * VLCTime.h: VLCKit.framework VLCTime header</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *****************************************************************************</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Copyright (C) 2007 Pierre d&#39;Herbemont</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * Copyright (C) 2007-2016 VLC authors and VideoLAN</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> * $Id$</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> * Authors: <AUTHORS>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> * This program is free software; you can redistribute it and/or modify it</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> * under the terms of the GNU Lesser General Public License as published by</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> * the Free Software Foundation; either version 2.1 of the License, or</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> * (at your option) any later version.</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * This program is distributed in the hope that it will be useful,</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * but WITHOUT ANY WARRANTY; without even the implied warranty of</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * GNU Lesser General Public License for more details.</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * You should have received a copy of the GNU Lesser General Public License</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * along with this program; if not, write to the Free Software Foundation,</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *****************************************************************************/</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160; </div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#import &lt;Foundation/Foundation.h&gt;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160; </div>
<div class="line"><a name="l00030"></a><span class="lineno"><a class="line" href="interface_v_l_c_time.html">   30</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_time.html">VLCTime</a> : NSObject</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;+ (<a class="code" href="interface_v_l_c_time.html">VLCTime</a> *)<a class="code" href="interface_v_l_c_time.html#ae4b7157f2152d3eddafe14e126a4b96c">nullTime</a>;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;+ (<a class="code" href="interface_v_l_c_time.html">VLCTime</a> *)timeWithNumber:(NSNumber *)aNumber;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;+ (<a class="code" href="interface_v_l_c_time.html">VLCTime</a> *)timeWithInt:(<span class="keywordtype">int</span>)aInt;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160; </div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;- (instancetype)initWithNumber:(NSNumber *)aNumber;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;- (instancetype)initWithInt:(<span class="keywordtype">int</span>)aInt;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160; </div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="comment">/* Properties */</span></div>
<div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="interface_v_l_c_time.html#a66fccafd32f7627fc119318a6c811394">   68</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly) NSNumber * <a class="code" href="interface_v_l_c_time.html#a66fccafd32f7627fc119318a6c811394">value</a>;    </div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160; </div>
<div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="interface_v_l_c_time.html#a4614e337e6304712ddd9182861e347ed">   74</a></span>&#160;<span class="keyword">@property</span> (readonly) NSNumber * numberValue <a class="code" href="interface_v_l_c_time.html#a4614e337e6304712ddd9182861e347ed">__attribute__</a>((deprecated));        <span class="comment">// here for backwards compatibility</span></div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160; </div>
<div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="interface_v_l_c_time.html#a7aa23f16fef10097af66ac4d9754d0dc">   80</a></span>&#160;<span class="keyword">@property</span> (readonly) NSString * <a class="code" href="interface_v_l_c_time.html#a7aa23f16fef10097af66ac4d9754d0dc">stringValue</a>;</div>
<div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="interface_v_l_c_time.html#acde55e3e8f75f7242f4fbf5c11e64743">   85</a></span>&#160;<span class="keyword">@property</span> (readonly) NSString * <a class="code" href="interface_v_l_c_time.html#acde55e3e8f75f7242f4fbf5c11e64743">verboseStringValue</a>;</div>
<div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="interface_v_l_c_time.html#aa6d4bce56be3df94a0896763f1769921">   90</a></span>&#160;<span class="keyword">@property</span> (readonly) NSString * <a class="code" href="interface_v_l_c_time.html#aa6d4bce56be3df94a0896763f1769921">minuteStringValue</a>;</div>
<div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="interface_v_l_c_time.html#a02ff06b1610b86d9bb1770a93837be42">   95</a></span>&#160;<span class="keyword">@property</span> (readonly) <span class="keywordtype">int</span> <a class="code" href="interface_v_l_c_time.html#aff49484cae12d9fbf65ce6b4a7da2cfd">intValue</a>;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160; </div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;<span class="comment">/* Comparators */</span></div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;- (NSComparisonResult)compare:(<a class="code" href="interface_v_l_c_time.html">VLCTime</a> *)aTime;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;- (BOOL)isEqual:(<span class="keywordtype">id</span>)object;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;- (NSUInteger)<a class="code" href="interface_v_l_c_time.html#a5f24e1e825a0676c3da2f6a3d5bc0a13">hash</a>;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160; </div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;<span class="keyword">@end</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="ainterface_v_l_c_time_html_aff49484cae12d9fbf65ce6b4a7da2cfd"><div class="ttname"><a href="interface_v_l_c_time.html#aff49484cae12d9fbf65ce6b4a7da2cfd">VLCTime::intValue</a></div><div class="ttdeci">int intValue</div><div class="ttdef"><b>Definition:</b> VLCTime.h:95</div></div>
<div class="ttc" id="ainterface_v_l_c_time_html_a66fccafd32f7627fc119318a6c811394"><div class="ttname"><a href="interface_v_l_c_time.html#a66fccafd32f7627fc119318a6c811394">VLCTime::value</a></div><div class="ttdeci">NSNumber * value</div><div class="ttdoc">Holds, in milliseconds, the VLCTime value.</div><div class="ttdef"><b>Definition:</b> VLCTime.h:68</div></div>
<div class="ttc" id="ainterface_v_l_c_time_html_ae4b7157f2152d3eddafe14e126a4b96c"><div class="ttname"><a href="interface_v_l_c_time.html#ae4b7157f2152d3eddafe14e126a4b96c">+[VLCTime nullTime]</a></div><div class="ttdeci">VLCTime * nullTime()</div></div>
<div class="ttc" id="ainterface_v_l_c_time_html_acde55e3e8f75f7242f4fbf5c11e64743"><div class="ttname"><a href="interface_v_l_c_time.html#acde55e3e8f75f7242f4fbf5c11e64743">VLCTime::verboseStringValue</a></div><div class="ttdeci">NSString * verboseStringValue</div><div class="ttdef"><b>Definition:</b> VLCTime.h:85</div></div>
<div class="ttc" id="ainterface_v_l_c_time_html_aa6d4bce56be3df94a0896763f1769921"><div class="ttname"><a href="interface_v_l_c_time.html#aa6d4bce56be3df94a0896763f1769921">VLCTime::minuteStringValue</a></div><div class="ttdeci">NSString * minuteStringValue</div><div class="ttdef"><b>Definition:</b> VLCTime.h:90</div></div>
<div class="ttc" id="ainterface_v_l_c_time_html_a5f24e1e825a0676c3da2f6a3d5bc0a13"><div class="ttname"><a href="interface_v_l_c_time.html#a5f24e1e825a0676c3da2f6a3d5bc0a13">-[VLCTime hash]</a></div><div class="ttdeci">NSUInteger hash()</div></div>
<div class="ttc" id="ainterface_v_l_c_time_html_a7aa23f16fef10097af66ac4d9754d0dc"><div class="ttname"><a href="interface_v_l_c_time.html#a7aa23f16fef10097af66ac4d9754d0dc">VLCTime::stringValue</a></div><div class="ttdeci">NSString * stringValue</div><div class="ttdef"><b>Definition:</b> VLCTime.h:80</div></div>
<div class="ttc" id="ainterface_v_l_c_time_html_a4614e337e6304712ddd9182861e347ed"><div class="ttname"><a href="interface_v_l_c_time.html#a4614e337e6304712ddd9182861e347ed">-[VLCTime __attribute__]</a></div><div class="ttdeci">NSNumber *numberValue __attribute__((deprecated))</div></div>
<div class="ttc" id="ainterface_v_l_c_time_html"><div class="ttname"><a href="interface_v_l_c_time.html">VLCTime</a></div><div class="ttdef"><b>Definition:</b> VLCTime.h:31</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
