<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: &lt;VLCMediaListPlayerDelegate&gt; Protocol Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="protocol_v_l_c_media_list_player_delegate-p-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">&lt;VLCMediaListPlayerDelegate&gt; Protocol Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="dynheader">
Inheritance diagram for &lt;VLCMediaListPlayerDelegate&gt;:</div>
<div class="dyncontent">
 <div class="center">
  <img src="protocol_v_l_c_media_list_player_delegate-p.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:afe3046f44ecb7cdde5d5659d543365a8"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html#afe3046f44ecb7cdde5d5659d543365a8">mediaListPlayerFinishedPlayback:</a></td></tr>
<tr class="separator:afe3046f44ecb7cdde5d5659d543365a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab86eca2f5040184a50cb8f74f1ca3845"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html#ab86eca2f5040184a50cb8f74f1ca3845">mediaListPlayer:nextMedia:</a></td></tr>
<tr class="separator:ab86eca2f5040184a50cb8f74f1ca3845"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a22d8350c9779fb7bcdf30cc1076a7073"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html#a22d8350c9779fb7bcdf30cc1076a7073">mediaListPlayerStopped:</a></td></tr>
<tr class="separator:a22d8350c9779fb7bcdf30cc1076a7073"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Method Documentation</h2>
<a id="ab86eca2f5040184a50cb8f74f1ca3845"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab86eca2f5040184a50cb8f74f1ca3845">&#9670;&nbsp;</a></span>mediaListPlayer:nextMedia:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaListPlayer: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a> *)&#160;</td>
          <td class="paramname"><em>player</em></td>
        </tr>
        <tr>
          <td class="paramkey">nextMedia:</td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *)&#160;</td>
          <td class="paramname"><em>media</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sent when <a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a> going to play next media </p>

</div>
</div>
<a id="afe3046f44ecb7cdde5d5659d543365a8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afe3046f44ecb7cdde5d5659d543365a8">&#9670;&nbsp;</a></span>mediaListPlayerFinishedPlayback:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaListPlayerFinishedPlayback: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a> *)&#160;</td>
          <td class="paramname"><em>player</em></td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sent when <a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a> has finished playing. </p>

</div>
</div>
<a id="a22d8350c9779fb7bcdf30cc1076a7073"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a22d8350c9779fb7bcdf30cc1076a7073">&#9670;&nbsp;</a></span>mediaListPlayerStopped:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaListPlayerStopped: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a> *)&#160;</td>
          <td class="paramname"><em>player</em></td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sent when <a class="el" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a> is stopped. Internally or by using the stop() </p><dl class="section see"><dt>See also</dt><dd>stop </dd></dl>

</div>
</div>
<hr/>The documentation for this protocol was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_media_list_player_8h_source.html">VLCMediaListPlayer.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
