<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Headers/Public/VLCMediaPlayer.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html">Headers</a></li><li class="navelem"><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html">Public</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCMediaPlayer.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/*****************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * VLCMediaPlayer.h: VLCKit.framework VLCMediaPlayer header</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *****************************************************************************</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Copyright (C) 2007-2009 Pierre d&#39;Herbemont</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * Copyright (C) 2007-2015 VLC authors and VideoLAN</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> * Copyright (C) 2009-2015 Felix Paul Kühne</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> * $Id$</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> * Authors: <AUTHORS>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *          Felix Paul Kühne &lt;fkuehne # videolan.org&gt;</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *          Soomin Lee &lt;TheHungryBu # gmail.com&gt;</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> * This program is free software; you can redistribute it and/or modify it</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> * under the terms of the GNU Lesser General Public License as published by</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * the Free Software Foundation; either version 2.1 of the License, or</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * (at your option) any later version.</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * This program is distributed in the hope that it will be useful,</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * but WITHOUT ANY WARRANTY; without even the implied warranty of</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * GNU Lesser General Public License for more details.</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> * You should have received a copy of the GNU Lesser General Public License</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> * along with this program; if not, write to the Free Software Foundation,</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="comment"> * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="comment"> *****************************************************************************/</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160; </div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#import &lt;Foundation/Foundation.h&gt;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#if TARGET_OS_IPHONE</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor"># import &lt;CoreGraphics/CoreGraphics.h&gt;</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor"># import &lt;UIKit/UIKit.h&gt;</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#import &quot;VLCMedia.h&quot;</span></div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#import &quot;VLCTime.h&quot;</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#import &quot;VLCAudio.h&quot;</span></div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#if !TARGET_OS_IPHONE</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_video_view.html">VLCVideoView</a>;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_video_layer.html">VLCVideoLayer</a>;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160; </div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_library.html">VLCLibrary</a>;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a>;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a>;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160; </div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="comment">/* Notification Messages */</span></div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMediaPlayerTimeChanged;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMediaPlayerStateChanged;</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMediaPlayerTitleChanged;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMediaPlayerChapterChanged;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMediaPlayerLoudnessChanged;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160; </div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="keyword">typedef</span> NS_ENUM(NSInteger, VLCMediaPlayerState)</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;{</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;    VLCMediaPlayerStateStopped,        </div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;    VLCMediaPlayerStateOpening,        </div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;    VLCMediaPlayerStateBuffering,      </div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;    VLCMediaPlayerStateEnded,          </div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;    VLCMediaPlayerStateError,          </div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;    VLCMediaPlayerStatePlaying,        </div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;    VLCMediaPlayerStatePaused,         </div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;    VLCMediaPlayerStateESAdded         </div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;};</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160; </div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;<span class="keyword">typedef</span> NS_ENUM(<span class="keywordtype">unsigned</span>, VLCMediaPlaybackNavigationAction)</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;{</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;    VLCMediaPlaybackNavigationActionActivate = 0,</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;    VLCMediaPlaybackNavigationActionUp,</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;    VLCMediaPlaybackNavigationActionDown,</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;    VLCMediaPlaybackNavigationActionLeft,</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;    VLCMediaPlaybackNavigationActionRight</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;};</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160; </div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="keyword">typedef</span> NS_ENUM(NSInteger, VLCDeinterlace)</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;{</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    VLCDeinterlaceAuto = -1,</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    VLCDeinterlaceOn = 1,</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    VLCDeinterlaceOff = 0</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;};</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160; </div>
<div class="line"><a name="l00093"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_loudness.html">   93</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_media_loudness.html">VLCMediaLoudness</a> : NSObject</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160; </div>
<div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_loudness.html#a70a3e717400017a173498a9c802cc70f">   99</a></span>&#160;<span class="keyword">@property</span> (readonly) <span class="keywordtype">double</span> <a class="code" href="interface_v_l_c_media_loudness.html#a70a3e717400017a173498a9c802cc70f">loudnessValue</a>;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160; </div>
<div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="protocol_v_l_c_media_player_delegate-p.html#a857838e3a322f96cf60fa94dc905e8d5">  105</a></span>&#160;<span class="keyword">@property</span> (readonly) int64_t <a class="code" href="interface_v_l_c_media_loudness.html#af2b5b96b54bc4b5f802ba1f8fbe2de43">date</a>;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160; </div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160; </div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="keyword">extern</span> NSString * VLCMediaPlayerStateToString(VLCMediaPlayerState state);</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160; </div>
<div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="protocol_v_l_c_media_player_delegate-p.html">  120</a></span>&#160;<span class="keyword">@protocol </span><a class="code" href="protocol_v_l_c_media_player_delegate-p.html">VLCMediaPlayerDelegate</a> &lt;NSObject&gt;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160; </div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="keyword">@optional</span></div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;- (void)mediaPlayerStateChanged:(NSNotification *)aNotification;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160; </div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;- (void)mediaPlayerTimeChanged:(NSNotification *)aNotification;</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160; </div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;- (void)mediaPlayerTitleChanged:(NSNotification *)aNotification;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160; </div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;- (void)mediaPlayerChapterChanged:(NSNotification *)aNotification;</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160; </div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;- (void)mediaPlayerLoudnessChanged:(NSNotification *)aNotification;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160; </div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;- (void)mediaPlayerSnapshot:(NSNotification *)aNotification;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160; </div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;- (void)mediaPlayerStartedRecording:(<a class="code" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> *)player;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160; </div>
<div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;- (void)mediaPlayer:(<a class="code" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> *)player recordingStoppedAtPath:(NSString *)path;</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160; </div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160; </div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160; </div>
<div class="line"><a name="l00185"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html">  185</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> : NSObject</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160; </div>
<div class="line"><a name="l00190"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a32b5af405c337b3704957d7c15cbdd61">  190</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly) <a class="code" href="interface_v_l_c_library.html">VLCLibrary</a> *libraryInstance;</div>
<div class="line"><a name="l00194"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a1eb2229ede2d006bec1650e4d4b0fa02">  194</a></span>&#160;<span class="keyword">@property</span> (weak, nonatomic) id&lt;VLCMediaPlayerDelegate&gt; delegate;</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160; </div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;<span class="preprocessor">#if !TARGET_OS_IPHONE</span></div>
<div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;<span class="comment">/* Initializers */</span></div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;- (instancetype)initWithVideoView:(<a class="code" href="interface_v_l_c_video_view.html">VLCVideoView</a> *)aVideoView;</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;- (instancetype)initWithVideoLayer:(<a class="code" href="interface_v_l_c_video_layer.html">VLCVideoLayer</a> *)aVideoLayer;</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160; </div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;- (instancetype)initWithOptions:(NSArray *)options;</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;- (instancetype)initWithLibVLCInstance:(<span class="keywordtype">void</span> *)playerInstance andLibrary:(<a class="code" href="interface_v_l_c_library.html">VLCLibrary</a> *)library;</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160; </div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;<span class="comment">/* Video View Options */</span></div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;<span class="comment">// TODO: Should be it&#39;s own object?</span></div>
<div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160; </div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;<span class="preprocessor">#pragma mark -</span></div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;<span class="preprocessor">#pragma mark video functionality</span></div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160; </div>
<div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;<span class="preprocessor">#if !TARGET_OS_IPHONE</span></div>
<div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160; </div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;- (void)setVideoView:(<a class="code" href="interface_v_l_c_video_view.html">VLCVideoView</a> *)aVideoView;</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;- (void)setVideoLayer:(<a class="code" href="interface_v_l_c_video_layer.html">VLCVideoLayer</a> *)aVideoLayer;</div>
<div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160; </div>
<div class="line"><a name="l00250"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#af6fa6a9a81e9db3aa58596a1b5b48196">  250</a></span>&#160;<span class="keyword">@property</span> (strong) <span class="keywordtype">id</span> drawable; <span class="comment">/* The videoView or videoLayer */</span></div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160; </div>
<div class="line"><a name="l00260"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a3ee849792344fed560e4308ebe8e4a76">  260</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY) <span class="keywordtype">char</span> *videoAspectRatio;</div>
<div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160; </div>
<div class="line"><a name="l00268"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a3f0fd895e58be570f115ab6f09501ffe">  268</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY) <span class="keywordtype">char</span> *videoCropGeometry;</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160; </div>
<div class="line"><a name="l00279"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a95abbbed4ddab2adb3fb5c4a8b8ae076">  279</a></span>&#160;<span class="keyword">@property</span> (nonatomic) <span class="keywordtype">float</span> scaleFactor;</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160; </div>
<div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;- (void)saveVideoSnapshotAt:(NSString *)path withWidth:(<span class="keywordtype">int</span>)width andHeight:(<span class="keywordtype">int</span>)height;</div>
<div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160; </div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;- (void)setDeinterlaceFilter: (NSString *)name;</div>
<div class="line"><a name="l00299"></a><span class="lineno">  299</span>&#160; </div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;- (void)setDeinterlace:(VLCDeinterlace)deinterlace withFilter:(NSString *)name;</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160; </div>
<div class="line"><a name="l00313"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#af7a158c0c906970b991154efab0300bd">  313</a></span>&#160;<span class="keyword">@property</span> (nonatomic) BOOL adjustFilterEnabled;</div>
<div class="line"><a name="l00319"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#aee995a8531e197917dbad6516594a777">  319</a></span>&#160;<span class="keyword">@property</span> (nonatomic) <span class="keywordtype">float</span> contrast;</div>
<div class="line"><a name="l00325"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#aec3ba8ab1cc0e096e27e26718904d8a7">  325</a></span>&#160;<span class="keyword">@property</span> (nonatomic) <span class="keywordtype">float</span> brightness;</div>
<div class="line"><a name="l00331"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#ab276a7f5adfa522cfe25d0a3b637f646">  331</a></span>&#160;<span class="keyword">@property</span> (nonatomic) <span class="keywordtype">float</span> hue;</div>
<div class="line"><a name="l00337"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a6b7d12cf171b798406f128a3f5b54908">  337</a></span>&#160;<span class="keyword">@property</span> (nonatomic) <span class="keywordtype">float</span> saturation;</div>
<div class="line"><a name="l00343"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#acab90fc3b5eef2c26e044df40fd84a61">  343</a></span>&#160;<span class="keyword">@property</span> (nonatomic) <span class="keywordtype">float</span> gamma;</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160; </div>
<div class="line"><a name="l00353"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#adcfbd421109bce67c3950a8c45b0bbea">  353</a></span>&#160;<span class="keyword">@property</span> (nonatomic) <span class="keywordtype">float</span> rate;</div>
<div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160; </div>
<div class="line"><a name="l00359"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a68098fd773aeae1824545f7490079f3c">  359</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly, weak) <a class="code" href="interface_v_l_c_audio.html">VLCAudio</a> * audio;</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160; </div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;<span class="comment">/* Video Information */</span></div>
<div class="line"><a name="l00366"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a0734e2b2d4edebeaf3ca9e1cce85f361">  366</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) CGSize videoSize;</div>
<div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160; </div>
<div class="line"><a name="l00374"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#aa39d7bc28b9d74bca2e18e27357576eb">  374</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) BOOL hasVideoOut;</div>
<div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160; </div>
<div class="line"><a name="l00381"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#ab59f33aa946850a6387d87630c2cca16">  381</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) <span class="keywordtype">float</span> framesPerSecond <a class="code" href="interface_v_l_c_media_player.html#ab59f33aa946850a6387d87630c2cca16">__attribute__</a>((deprecated));</div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160; </div>
<div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;<span class="preprocessor">#pragma mark -</span></div>
<div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;<span class="preprocessor">#pragma mark time</span></div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160; </div>
<div class="line"><a name="l00395"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a5915273012b273885dd9570d56777ccf">  395</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, strong) <a class="code" href="interface_v_l_c_time.html">VLCTime</a> *time;</div>
<div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160; </div>
<div class="line"><a name="l00402"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a994615b429c023db77a00d1efec06fd3">  402</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly, weak) <a class="code" href="interface_v_l_c_time.html">VLCTime</a> *remainingTime;</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160; </div>
<div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;<span class="preprocessor">#pragma mark -</span></div>
<div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;<span class="preprocessor">#pragma mark ES track handling</span></div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160; </div>
<div class="line"><a name="l00414"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a8715f7c45a4389fd57dc6730312ca43e">  414</a></span>&#160;<span class="keyword">@property</span> (readwrite) <span class="keywordtype">int</span> currentVideoTrackIndex;</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160; </div>
<div class="line"><a name="l00420"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#aaa31a3ee365dd1721064f19319bb8026">  420</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly, copy) NSArray *videoTrackNames;</div>
<div class="line"><a name="l00421"></a><span class="lineno">  421</span>&#160; </div>
<div class="line"><a name="l00426"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a4f1abde67436f198f0d07b885bd5ac59">  426</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly, copy) NSArray *videoTrackIndexes;</div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160; </div>
<div class="line"><a name="l00432"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#af142280306f73367c1a3aa748f7233f9">  432</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) <span class="keywordtype">int</span> numberOfVideoTracks;</div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160; </div>
<div class="line"><a name="l00441"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a23063281cc095d506b38f421d974bfe3">  441</a></span>&#160;<span class="keyword">@property</span> (readwrite) <span class="keywordtype">int</span> currentVideoSubTitleIndex;</div>
<div class="line"><a name="l00442"></a><span class="lineno">  442</span>&#160; </div>
<div class="line"><a name="l00447"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#aaaf3e36c370456bbf30058eedaf7844e">  447</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly, copy) NSArray *videoSubTitlesNames;</div>
<div class="line"><a name="l00448"></a><span class="lineno">  448</span>&#160; </div>
<div class="line"><a name="l00453"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a1c8a4af83a85f3e8606049aad6f75169">  453</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly, copy) NSArray *videoSubTitlesIndexes;</div>
<div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160; </div>
<div class="line"><a name="l00459"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a18546cd8ca1b827eb5ddb9384e172166">  459</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) <span class="keywordtype">int</span> numberOfSubtitlesTracks;</div>
<div class="line"><a name="l00460"></a><span class="lineno">  460</span>&#160; </div>
<div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;- (BOOL)openVideoSubTitlesFromFile:(NSString *)path __attribute__((deprecated));</div>
<div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160; </div>
<div class="line"><a name="l00471"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a7eb2b3aeb06985b3b655c1c76609924f">  471</a></span>&#160;<span class="keyword">typedef</span> <a class="code" href="interface_v_l_c_media_player.html#a7eb2b3aeb06985b3b655c1c76609924f">NS_ENUM</a>(<span class="keywordtype">unsigned</span>, VLCMediaPlaybackSlaveType)</div>
<div class="line"><a name="l00472"></a><span class="lineno">  472</span>&#160;{</div>
<div class="line"><a name="l00473"></a><span class="lineno">  473</span>&#160;    VLCMediaPlaybackSlaveTypeSubtitle = 0,</div>
<div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;    VLCMediaPlaybackSlaveTypeAudio</div>
<div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;};</div>
<div class="line"><a name="l00476"></a><span class="lineno">  476</span>&#160; </div>
<div class="line"><a name="l00485"></a><span class="lineno">  485</span>&#160;- (int)addPlaybackSlave:(NSURL *)slaveURL type:(VLCMediaPlaybackSlaveType)slaveType enforce:(BOOL)enforceSelection;</div>
<div class="line"><a name="l00486"></a><span class="lineno">  486</span>&#160; </div>
<div class="line"><a name="l00493"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a8fe537bb090fd54c8f223a8e682b76c8">  493</a></span>&#160;<span class="keyword">@property</span> (readwrite) NSInteger currentVideoSubTitleDelay;</div>
<div class="line"><a name="l00494"></a><span class="lineno">  494</span>&#160; </div>
<div class="line"><a name="l00504"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#ae2a2398ebf77aaa0dd5218d6e17a61f4">  504</a></span>&#160;<span class="keyword">@property</span> (readwrite) <span class="keywordtype">int</span> currentChapterIndex;</div>
<div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;- (void)previousChapter;</div>
<div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;- (void)nextChapter;</div>
<div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;- (int)numberOfChaptersForTitle:(<span class="keywordtype">int</span>)titleIndex;</div>
<div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160; </div>
<div class="line"><a name="l00523"></a><span class="lineno">  523</span>&#160;- (NSArray *)chaptersForTitleIndex:(<span class="keywordtype">int</span>)titleIndex __attribute__((deprecated));</div>
<div class="line"><a name="l00524"></a><span class="lineno">  524</span>&#160; </div>
<div class="line"><a name="l00528"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a20081eb2719b21bd55b4d2ce185f86f2">  528</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media_player.html#a20081eb2719b21bd55b4d2ce185f86f2">VLCChapterDescriptionName</a>;</div>
<div class="line"><a name="l00532"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#ab5d7190ce89c08ae1b14e6c2a827d104">  532</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media_player.html#ab5d7190ce89c08ae1b14e6c2a827d104">VLCChapterDescriptionTimeOffset</a>;</div>
<div class="line"><a name="l00536"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a5d87f02211d47497f35bf616f7a0374e">  536</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media_player.html#a5d87f02211d47497f35bf616f7a0374e">VLCChapterDescriptionDuration</a>;</div>
<div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160; </div>
<div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;- (NSArray *)chapterDescriptionsOfTitle:(<span class="keywordtype">int</span>)titleIndex;</div>
<div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160; </div>
<div class="line"><a name="l00554"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#aefa58a904c759776773a8287225138b3">  554</a></span>&#160;<span class="keyword">@property</span> (readwrite) <span class="keywordtype">int</span> currentTitleIndex;</div>
<div class="line"><a name="l00559"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a73e07e681449f1122a4fa1f66d9fc52d">  559</a></span>&#160;<span class="keyword">@property</span> (readonly) <span class="keywordtype">int</span> numberOfTitles;</div>
<div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160; </div>
<div class="line"><a name="l00565"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#aae0b500274a748ac5c035c2a3c46c366">  565</a></span>&#160;<span class="keyword">@property</span> (readonly) NSUInteger countOfTitles <a class="code" href="interface_v_l_c_media_player.html#aae0b500274a748ac5c035c2a3c46c366">__attribute__</a>((deprecated));</div>
<div class="line"><a name="l00570"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a4e26f7c5c05ef844fc1d8a3ae8e99ad4">  570</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly, copy) NSArray *titles <a class="code" href="interface_v_l_c_media_player.html#a4e26f7c5c05ef844fc1d8a3ae8e99ad4">__attribute__</a>((deprecated));</div>
<div class="line"><a name="l00571"></a><span class="lineno">  571</span>&#160; </div>
<div class="line"><a name="l00575"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a2907bb09c29757c5c0f89e5bbe7e7394">  575</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media_player.html#a2907bb09c29757c5c0f89e5bbe7e7394">VLCTitleDescriptionName</a>;</div>
<div class="line"><a name="l00579"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#ac4bd07aad0599f2f61cbac7281981df7">  579</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media_player.html#ac4bd07aad0599f2f61cbac7281981df7">VLCTitleDescriptionDuration</a>;</div>
<div class="line"><a name="l00583"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a1cfbed633aa7783841c153d48088ba70">  583</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media_player.html#a1cfbed633aa7783841c153d48088ba70">VLCTitleDescriptionIsMenu</a>;</div>
<div class="line"><a name="l00584"></a><span class="lineno">  584</span>&#160; </div>
<div class="line"><a name="l00594"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a937a50fb274ec99b146d999fd8c02a1b">  594</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly, copy) NSArray *titleDescriptions;</div>
<div class="line"><a name="l00595"></a><span class="lineno">  595</span>&#160; </div>
<div class="line"><a name="l00600"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a93ba313f95351de59e84cdeeea720822">  600</a></span>&#160;<span class="keyword">@property</span> (readonly) <span class="keywordtype">int</span> indexOfLongestTitle;</div>
<div class="line"><a name="l00601"></a><span class="lineno">  601</span>&#160; </div>
<div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;<span class="comment">/* Audio Options */</span></div>
<div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160; </div>
<div class="line"><a name="l00611"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a4aedc95307034b1d3a0f8ec51802e7f4">  611</a></span>&#160;<span class="keyword">@property</span> (readwrite) <span class="keywordtype">int</span> currentAudioTrackIndex;</div>
<div class="line"><a name="l00612"></a><span class="lineno">  612</span>&#160; </div>
<div class="line"><a name="l00617"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#adde3f17fc7a88a00221a58bd564120c8">  617</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly, copy) NSArray *audioTrackNames;</div>
<div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160; </div>
<div class="line"><a name="l00623"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#ae67fa3af66466f93f46bd14c07c60780">  623</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly, copy) NSArray *audioTrackIndexes;</div>
<div class="line"><a name="l00624"></a><span class="lineno">  624</span>&#160; </div>
<div class="line"><a name="l00629"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a932d093bc73aea01f953a1b96023f401">  629</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) <span class="keywordtype">int</span> numberOfAudioTracks;</div>
<div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160; </div>
<div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;<span class="preprocessor">#pragma mark -</span></div>
<div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;<span class="preprocessor">#pragma mark audio functionality</span></div>
<div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160; </div>
<div class="line"><a name="l00638"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#ad29d5abf2c543de5d7f911a8a216480e">  638</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY) <span class="keywordtype">int</span> audioChannel;</div>
<div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160; </div>
<div class="line"><a name="l00646"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a4e32c4423cb48d9491591ba55df6cbd6">  646</a></span>&#160;<span class="keyword">@property</span> (readwrite) NSInteger currentAudioPlaybackDelay;</div>
<div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160; </div>
<div class="line"><a name="l00651"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a5f5763e66e58c4b44045ef098bdb818a">  651</a></span>&#160;<span class="keyword">@property</span> (readonly) <a class="code" href="interface_v_l_c_media_loudness.html">VLCMediaLoudness</a> *momentaryLoudness;</div>
<div class="line"><a name="l00652"></a><span class="lineno">  652</span>&#160; </div>
<div class="line"><a name="l00653"></a><span class="lineno">  653</span>&#160;<span class="preprocessor">#pragma mark -</span></div>
<div class="line"><a name="l00654"></a><span class="lineno">  654</span>&#160;<span class="preprocessor">#pragma mark equalizer</span></div>
<div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160; </div>
<div class="line"><a name="l00663"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#abb2864039496be0bde196467971dd873">  663</a></span>&#160;<span class="keyword">@property</span> (weak, readonly) NSArray *equalizerProfiles;</div>
<div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160; </div>
<div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;- (void)resetEqualizerFromProfile:(<span class="keywordtype">unsigned</span>)profile;</div>
<div class="line"><a name="l00670"></a><span class="lineno">  670</span>&#160; </div>
<div class="line"><a name="l00676"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a1750c810467d11cb0cf4e835ea9163f3">  676</a></span>&#160;<span class="keyword">@property</span> (readwrite) BOOL equalizerEnabled;</div>
<div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160; </div>
<div class="line"><a name="l00683"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a1dd4611ad95d596a0d086092ca0c571a">  683</a></span>&#160;<span class="keyword">@property</span> (readwrite) CGFloat preAmplification;</div>
<div class="line"><a name="l00684"></a><span class="lineno">  684</span>&#160; </div>
<div class="line"><a name="l00688"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#ae61e529ff86b246ebe54cc29b0a96c0e">  688</a></span>&#160;<span class="keyword">@property</span> (readonly) <span class="keywordtype">unsigned</span> numberOfBands;</div>
<div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160; </div>
<div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;- (CGFloat)frequencyOfBandAtIndex:(<span class="keywordtype">unsigned</span>)index;</div>
<div class="line"><a name="l00695"></a><span class="lineno">  695</span>&#160; </div>
<div class="line"><a name="l00700"></a><span class="lineno">  700</span>&#160;- (void)setAmplification:(CGFloat)amplification forBand:(<span class="keywordtype">unsigned</span>)index;</div>
<div class="line"><a name="l00701"></a><span class="lineno">  701</span>&#160; </div>
<div class="line"><a name="l00706"></a><span class="lineno">  706</span>&#160;- (CGFloat)amplificationOfBand:(<span class="keywordtype">unsigned</span>)index;</div>
<div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160; </div>
<div class="line"><a name="l00708"></a><span class="lineno">  708</span>&#160;<span class="preprocessor">#pragma mark -</span></div>
<div class="line"><a name="l00709"></a><span class="lineno">  709</span>&#160;<span class="preprocessor">#pragma mark media handling</span></div>
<div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160; </div>
<div class="line"><a name="l00711"></a><span class="lineno">  711</span>&#160;<span class="comment">/* Media Options */</span></div>
<div class="line"><a name="l00715"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#abe9c128cdaa533d51c5209d4c17d5b09">  715</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, strong) <a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *media;</div>
<div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160; </div>
<div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160;<span class="preprocessor">#pragma mark -</span></div>
<div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;<span class="preprocessor">#pragma mark playback operations</span></div>
<div class="line"><a name="l00719"></a><span class="lineno">  719</span>&#160; </div>
<div class="line"><a name="l00724"></a><span class="lineno">  724</span>&#160;- (void)play;</div>
<div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160; </div>
<div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;- (void)pause;</div>
<div class="line"><a name="l00730"></a><span class="lineno">  730</span>&#160; </div>
<div class="line"><a name="l00734"></a><span class="lineno">  734</span>&#160;- (void)stop;</div>
<div class="line"><a name="l00735"></a><span class="lineno">  735</span>&#160; </div>
<div class="line"><a name="l00739"></a><span class="lineno">  739</span>&#160;- (void)gotoNextFrame;</div>
<div class="line"><a name="l00740"></a><span class="lineno">  740</span>&#160; </div>
<div class="line"><a name="l00744"></a><span class="lineno">  744</span>&#160;- (void)fastForward;</div>
<div class="line"><a name="l00745"></a><span class="lineno">  745</span>&#160; </div>
<div class="line"><a name="l00750"></a><span class="lineno">  750</span>&#160;- (void)fastForwardAtRate:(<span class="keywordtype">float</span>)rate;</div>
<div class="line"><a name="l00751"></a><span class="lineno">  751</span>&#160; </div>
<div class="line"><a name="l00755"></a><span class="lineno">  755</span>&#160;- (void)rewind;</div>
<div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160; </div>
<div class="line"><a name="l00761"></a><span class="lineno">  761</span>&#160;- (void)rewindAtRate:(<span class="keywordtype">float</span>)rate;</div>
<div class="line"><a name="l00762"></a><span class="lineno">  762</span>&#160; </div>
<div class="line"><a name="l00767"></a><span class="lineno">  767</span>&#160;- (void)jumpBackward:(<span class="keywordtype">int</span>)interval;</div>
<div class="line"><a name="l00768"></a><span class="lineno">  768</span>&#160; </div>
<div class="line"><a name="l00773"></a><span class="lineno">  773</span>&#160;- (void)jumpForward:(<span class="keywordtype">int</span>)interval;</div>
<div class="line"><a name="l00774"></a><span class="lineno">  774</span>&#160; </div>
<div class="line"><a name="l00778"></a><span class="lineno">  778</span>&#160;- (void)extraShortJumpBackward;</div>
<div class="line"><a name="l00779"></a><span class="lineno">  779</span>&#160; </div>
<div class="line"><a name="l00783"></a><span class="lineno">  783</span>&#160;- (void)extraShortJumpForward;</div>
<div class="line"><a name="l00784"></a><span class="lineno">  784</span>&#160; </div>
<div class="line"><a name="l00788"></a><span class="lineno">  788</span>&#160;- (void)shortJumpBackward;</div>
<div class="line"><a name="l00789"></a><span class="lineno">  789</span>&#160; </div>
<div class="line"><a name="l00793"></a><span class="lineno">  793</span>&#160;- (void)shortJumpForward;</div>
<div class="line"><a name="l00794"></a><span class="lineno">  794</span>&#160; </div>
<div class="line"><a name="l00798"></a><span class="lineno">  798</span>&#160;- (void)mediumJumpBackward;</div>
<div class="line"><a name="l00799"></a><span class="lineno">  799</span>&#160; </div>
<div class="line"><a name="l00803"></a><span class="lineno">  803</span>&#160;- (void)mediumJumpForward;</div>
<div class="line"><a name="l00804"></a><span class="lineno">  804</span>&#160; </div>
<div class="line"><a name="l00808"></a><span class="lineno">  808</span>&#160;- (void)longJumpBackward;</div>
<div class="line"><a name="l00809"></a><span class="lineno">  809</span>&#160; </div>
<div class="line"><a name="l00813"></a><span class="lineno">  813</span>&#160;- (void)longJumpForward;</div>
<div class="line"><a name="l00814"></a><span class="lineno">  814</span>&#160; </div>
<div class="line"><a name="l00818"></a><span class="lineno">  818</span>&#160;- (void)performNavigationAction:(VLCMediaPlaybackNavigationAction)action;</div>
<div class="line"><a name="l00819"></a><span class="lineno">  819</span>&#160; </div>
<div class="line"><a name="l00831"></a><span class="lineno">  831</span>&#160;- (BOOL)updateViewpoint:(<span class="keywordtype">float</span>)yaw pitch:(<span class="keywordtype">float</span>)pitch roll:(<span class="keywordtype">float</span>)roll fov:(<span class="keywordtype">float</span>)fov absolute:(BOOL)absolute;</div>
<div class="line"><a name="l00832"></a><span class="lineno">  832</span>&#160; </div>
<div class="line"><a name="l00838"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a5f8af3ae371f616810320e1fb447f6dc">  838</a></span>&#160;<span class="keyword">@property</span> (nonatomic) <span class="keywordtype">float</span> yaw;</div>
<div class="line"><a name="l00839"></a><span class="lineno">  839</span>&#160; </div>
<div class="line"><a name="l00845"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#ae7af67ee4b28da45c957bafca617840f">  845</a></span>&#160;<span class="keyword">@property</span> (nonatomic) <span class="keywordtype">float</span> pitch;</div>
<div class="line"><a name="l00846"></a><span class="lineno">  846</span>&#160; </div>
<div class="line"><a name="l00852"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a17e2d158c437a5bffd8da88673b99efc">  852</a></span>&#160;<span class="keyword">@property</span> (nonatomic) <span class="keywordtype">float</span> roll;</div>
<div class="line"><a name="l00853"></a><span class="lineno">  853</span>&#160; </div>
<div class="line"><a name="l00859"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a43185c858bc8767f33a19a9971d34fc4">  859</a></span>&#160;<span class="keyword">@property</span> (nonatomic) <span class="keywordtype">float</span> fov;</div>
<div class="line"><a name="l00860"></a><span class="lineno">  860</span>&#160; </div>
<div class="line"><a name="l00861"></a><span class="lineno">  861</span>&#160;<span class="preprocessor">#pragma mark -</span></div>
<div class="line"><a name="l00862"></a><span class="lineno">  862</span>&#160;<span class="preprocessor">#pragma mark playback information</span></div>
<div class="line"><a name="l00863"></a><span class="lineno">  863</span>&#160; </div>
<div class="line"><a name="l00867"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a6fa5e39a09fd25c262c9a2ea20e5b9df">  867</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, getter=isPlaying, readonly) BOOL playing;</div>
<div class="line"><a name="l00868"></a><span class="lineno">  868</span>&#160; </div>
<div class="line"><a name="l00873"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#aa55f09ffe39e021920248ff142ae0f75">  873</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) BOOL willPlay;</div>
<div class="line"><a name="l00874"></a><span class="lineno">  874</span>&#160; </div>
<div class="line"><a name="l00879"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#aa800575a8facf5db251df3cc88bd44ea">  879</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) VLCMediaPlayerState state;</div>
<div class="line"><a name="l00880"></a><span class="lineno">  880</span>&#160; </div>
<div class="line"><a name="l00885"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#af10549bcee345334f42548cfda9ce51c">  885</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY) <span class="keywordtype">float</span> position;</div>
<div class="line"><a name="l00886"></a><span class="lineno">  886</span>&#160; </div>
<div class="line"><a name="l00892"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#ae3f8bc09a07c8b58935d4cd1cf58e69e">  892</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, getter=isSeekable, readonly) BOOL seekable;</div>
<div class="line"><a name="l00893"></a><span class="lineno">  893</span>&#160; </div>
<div class="line"><a name="l00898"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a1e2ece165a5fb056a2f8737ac1ff2367">  898</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) BOOL canPause;</div>
<div class="line"><a name="l00899"></a><span class="lineno">  899</span>&#160; </div>
<div class="line"><a name="l00905"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#ada4fadb2ae81bd34fce217a34571872a">  905</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly, copy) NSArray *snapshots;</div>
<div class="line"><a name="l00906"></a><span class="lineno">  906</span>&#160; </div>
<div class="line"><a name="l00907"></a><span class="lineno">  907</span>&#160;<span class="preprocessor">#if TARGET_OS_IPHONE</span></div>
<div class="line"><a name="l00908"></a><span class="lineno">  908</span>&#160; </div>
<div class="line"><a name="l00914"></a><span class="lineno">  914</span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) UIImage *lastSnapshot;</div>
<div class="line"><a name="l00915"></a><span class="lineno">  915</span>&#160;<span class="preprocessor">#else</span></div>
<div class="line"><a name="l00916"></a><span class="lineno">  916</span>&#160; </div>
<div class="line"><a name="l00922"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_player.html#a41917b1e63701715a0e66f16e22c6f63">  922</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSImage *lastSnapshot;</div>
<div class="line"><a name="l00923"></a><span class="lineno">  923</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00924"></a><span class="lineno">  924</span>&#160; </div>
<div class="line"><a name="l00930"></a><span class="lineno">  930</span>&#160;- (BOOL)startRecordingAtPath:(NSString *)path;</div>
<div class="line"><a name="l00931"></a><span class="lineno">  931</span>&#160; </div>
<div class="line"><a name="l00936"></a><span class="lineno">  936</span>&#160;- (BOOL)stopRecording;</div>
<div class="line"><a name="l00937"></a><span class="lineno">  937</span>&#160; </div>
<div class="line"><a name="l00938"></a><span class="lineno">  938</span>&#160;<span class="preprocessor">#pragma mark -</span></div>
<div class="line"><a name="l00939"></a><span class="lineno">  939</span>&#160;<span class="preprocessor">#pragma mark Renderer</span></div>
<div class="line"><a name="l00940"></a><span class="lineno">  940</span>&#160; </div>
<div class="line"><a name="l00949"></a><span class="lineno">  949</span>&#160;- (BOOL)setRendererItem:(<a class="code" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a> *)item;</div>
<div class="line"><a name="l00950"></a><span class="lineno">  950</span>&#160; </div>
<div class="line"><a name="l00951"></a><span class="lineno">  951</span>&#160;<span class="keyword">@end</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="ainterface_v_l_c_media_player_html_a20081eb2719b21bd55b4d2ce185f86f2"><div class="ttname"><a href="interface_v_l_c_media_player.html#a20081eb2719b21bd55b4d2ce185f86f2">VLCMediaPlayer::VLCChapterDescriptionName</a></div><div class="ttdeci">NSString *const VLCChapterDescriptionName</div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:528</div></div>
<div class="ttc" id="ainterface_v_l_c_media_loudness_html_af2b5b96b54bc4b5f802ba1f8fbe2de43"><div class="ttname"><a href="interface_v_l_c_media_loudness.html#af2b5b96b54bc4b5f802ba1f8fbe2de43">VLCMediaLoudness::date</a></div><div class="ttdeci">int64_t date</div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:105</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html"><div class="ttname"><a href="interface_v_l_c_media.html">VLCMedia</a></div><div class="ttdef"><b>Definition:</b> VLCMedia.h:113</div></div>
<div class="ttc" id="ainterface_v_l_c_media_player_html"><div class="ttname"><a href="interface_v_l_c_media_player.html">VLCMediaPlayer</a></div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:186</div></div>
<div class="ttc" id="aprotocol_v_l_c_media_player_delegate-p_html"><div class="ttname"><a href="protocol_v_l_c_media_player_delegate-p.html">VLCMediaPlayerDelegate-p</a></div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:120</div></div>
<div class="ttc" id="ainterface_v_l_c_renderer_item_html"><div class="ttname"><a href="interface_v_l_c_renderer_item.html">VLCRendererItem</a></div><div class="ttdef"><b>Definition:</b> VLCRendererItem.h:37</div></div>
<div class="ttc" id="ainterface_v_l_c_media_player_html_a1cfbed633aa7783841c153d48088ba70"><div class="ttname"><a href="interface_v_l_c_media_player.html#a1cfbed633aa7783841c153d48088ba70">VLCMediaPlayer::VLCTitleDescriptionIsMenu</a></div><div class="ttdeci">NSString *const VLCTitleDescriptionIsMenu</div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:583</div></div>
<div class="ttc" id="ainterface_v_l_c_media_player_html_a2907bb09c29757c5c0f89e5bbe7e7394"><div class="ttname"><a href="interface_v_l_c_media_player.html#a2907bb09c29757c5c0f89e5bbe7e7394">VLCMediaPlayer::VLCTitleDescriptionName</a></div><div class="ttdeci">NSString *const VLCTitleDescriptionName</div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:575</div></div>
<div class="ttc" id="ainterface_v_l_c_media_player_html_a7eb2b3aeb06985b3b655c1c76609924f"><div class="ttname"><a href="interface_v_l_c_media_player.html#a7eb2b3aeb06985b3b655c1c76609924f">-[VLCMediaPlayer NS_ENUM]</a></div><div class="ttdeci">typedef NS_ENUM(unsigned, VLCMediaPlaybackSlaveType)</div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:471</div></div>
<div class="ttc" id="ainterface_v_l_c_audio_html"><div class="ttname"><a href="interface_v_l_c_audio.html">VLCAudio</a></div><div class="ttdef"><b>Definition:</b> VLCAudio.h:37</div></div>
<div class="ttc" id="ainterface_v_l_c_library_html"><div class="ttname"><a href="interface_v_l_c_library.html">VLCLibrary</a></div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:47</div></div>
<div class="ttc" id="ainterface_v_l_c_media_player_html_a5d87f02211d47497f35bf616f7a0374e"><div class="ttname"><a href="interface_v_l_c_media_player.html#a5d87f02211d47497f35bf616f7a0374e">VLCMediaPlayer::VLCChapterDescriptionDuration</a></div><div class="ttdeci">NSString *const VLCChapterDescriptionDuration</div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:536</div></div>
<div class="ttc" id="ainterface_v_l_c_media_player_html_ac4bd07aad0599f2f61cbac7281981df7"><div class="ttname"><a href="interface_v_l_c_media_player.html#ac4bd07aad0599f2f61cbac7281981df7">VLCMediaPlayer::VLCTitleDescriptionDuration</a></div><div class="ttdeci">NSString *const VLCTitleDescriptionDuration</div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:579</div></div>
<div class="ttc" id="ainterface_v_l_c_video_layer_html"><div class="ttname"><a href="interface_v_l_c_video_layer.html">VLCVideoLayer</a></div><div class="ttdef"><b>Definition:</b> VLCVideoLayer.h:31</div></div>
<div class="ttc" id="ainterface_v_l_c_media_player_html_a4e26f7c5c05ef844fc1d8a3ae8e99ad4"><div class="ttname"><a href="interface_v_l_c_media_player.html#a4e26f7c5c05ef844fc1d8a3ae8e99ad4">-[VLCMediaPlayer __attribute__]</a></div><div class="ttdeci">NSArray *titles __attribute__((deprecated))</div></div>
<div class="ttc" id="ainterface_v_l_c_media_loudness_html_a70a3e717400017a173498a9c802cc70f"><div class="ttname"><a href="interface_v_l_c_media_loudness.html#a70a3e717400017a173498a9c802cc70f">VLCMediaLoudness::loudnessValue</a></div><div class="ttdeci">double loudnessValue</div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:99</div></div>
<div class="ttc" id="ainterface_v_l_c_media_player_html_ab59f33aa946850a6387d87630c2cca16"><div class="ttname"><a href="interface_v_l_c_media_player.html#ab59f33aa946850a6387d87630c2cca16">-[VLCMediaPlayer __attribute__]</a></div><div class="ttdeci">float framesPerSecond __attribute__((deprecated))</div></div>
<div class="ttc" id="ainterface_v_l_c_media_loudness_html"><div class="ttname"><a href="interface_v_l_c_media_loudness.html">VLCMediaLoudness</a></div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:94</div></div>
<div class="ttc" id="ainterface_v_l_c_media_player_html_aae0b500274a748ac5c035c2a3c46c366"><div class="ttname"><a href="interface_v_l_c_media_player.html#aae0b500274a748ac5c035c2a3c46c366">-[VLCMediaPlayer __attribute__]</a></div><div class="ttdeci">NSUInteger countOfTitles __attribute__((deprecated))</div></div>
<div class="ttc" id="ainterface_v_l_c_media_player_html_ab5d7190ce89c08ae1b14e6c2a827d104"><div class="ttname"><a href="interface_v_l_c_media_player.html#ab5d7190ce89c08ae1b14e6c2a827d104">VLCMediaPlayer::VLCChapterDescriptionTimeOffset</a></div><div class="ttdeci">NSString *const VLCChapterDescriptionTimeOffset</div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:532</div></div>
<div class="ttc" id="ainterface_v_l_c_video_view_html"><div class="ttname"><a href="interface_v_l_c_video_view.html">VLCVideoView</a></div><div class="ttdef"><b>Definition:</b> VLCVideoView.h:32</div></div>
<div class="ttc" id="ainterface_v_l_c_time_html"><div class="ttname"><a href="interface_v_l_c_time.html">VLCTime</a></div><div class="ttdef"><b>Definition:</b> VLCTime.h:31</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
