<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Headers/Public/VLCRendererDiscoverer.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html">Headers</a></li><li class="navelem"><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html">Public</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCRendererDiscoverer.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/*****************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * VLCRendererDiscoverer.h</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *****************************************************************************</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Copyright © 2018 VLC authors, VideoLAN</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * Copyright © 2018 Videolabs</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> * Authors: <AUTHORS>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> * This program is free software; you can redistribute it and/or modify it</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> * under the terms of the GNU Lesser General Public License as published by</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> * the Free Software Foundation; either version 2.1 of the License, or</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> * (at your option) any later version.</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> * This program is distributed in the hope that it will be useful,</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * but WITHOUT ANY WARRANTY; without even the implied warranty of</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * GNU Lesser General Public License for more details.</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * You should have received a copy of the GNU Lesser General Public License</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * along with this program; if not, write to the Free Software Foundation,</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> *****************************************************************************/</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; </div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a>;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a>;</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160; </div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;NS_ASSUME_NONNULL_BEGIN</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160; </div>
<div class="line"><a name="l00033"></a><span class="lineno"><a class="line" href="protocol_v_l_c_renderer_discoverer_delegate-p.html">   33</a></span>&#160;<span class="keyword">@protocol </span><a class="code" href="protocol_v_l_c_renderer_discoverer_delegate-p.html">VLCRendererDiscovererDelegate</a> &lt;NSObject&gt;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160; </div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;- (void)rendererDiscovererItemAdded:(<a class="code" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a> *)rendererDiscoverer</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;                               item:(<a class="code" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a> *)item;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;- (void)rendererDiscovererItemDeleted:(<a class="code" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a> *)rendererDiscoverer</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;                               item:(<a class="code" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a> *)item;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160; </div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160; </div>
<div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="interface_v_l_c_renderer_discoverer_description.html">   45</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_renderer_discoverer_description.html">VLCRendererDiscovererDescription</a> : NSObject</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160; </div>
<div class="line"><a name="l00050"></a><span class="lineno"><a class="line" href="interface_v_l_c_renderer_discoverer_description.html#af6af459a1c5ed50d53695149a2b49a6c">   50</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly, copy) NSString *<a class="code" href="interface_v_l_c_renderer_discoverer_description.html#af6af459a1c5ed50d53695149a2b49a6c">name</a>;</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160; </div>
<div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="interface_v_l_c_renderer_discoverer_description.html#a164296731eab83b21084576ded002861">   55</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly, copy) NSString *<a class="code" href="interface_v_l_c_renderer_discoverer_description.html#a164296731eab83b21084576ded002861">longName</a>;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160; </div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;- (instancetype)initWithName:(NSString *)name longName:(NSString *)longName;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160; </div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160; </div>
<div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="interface_v_l_c_renderer_discoverer.html">   70</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a> : NSObject</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160; </div>
<div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="interface_v_l_c_renderer_discoverer.html#a831fc60a7a5ea8bc1114bc36cb051f66">   75</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly, copy) NSString *<a class="code" href="interface_v_l_c_renderer_discoverer_description.html#af6af459a1c5ed50d53695149a2b49a6c">name</a>;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160; </div>
<div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="interface_v_l_c_renderer_discoverer.html#ad1782fa86584819376e5686797b765ee">   80</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly, copy) NSArray&lt;VLCRendererItem *&gt; *renderers;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160; </div>
<div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="interface_v_l_c_renderer_discoverer.html#ab7ca6afa57b5f53f6284791fde3f8839">   85</a></span>&#160;<span class="keyword">@property</span> (nonatomic, weak) id &lt;VLCRendererDiscovererDelegate&gt; _Nullable delegate;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160; </div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;- (instancetype)init NS_UNAVAILABLE;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160; </div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;- (instancetype _Nullable)initWithName:(NSString *)name;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160; </div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160; </div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;- (NSArray&lt;<a class="code" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a> *&gt; *)renderers;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160; </div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;- (BOOL)start;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160; </div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;- (void)stop;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160; </div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;+ (NSArray&lt;<a class="code" href="interface_v_l_c_renderer_discoverer_description.html">VLCRendererDiscovererDescription</a> *&gt; * _Nullable)list;</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160; </div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160; </div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;NS_ASSUME_NONNULL_END</div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="ainterface_v_l_c_renderer_discoverer_description_html_af6af459a1c5ed50d53695149a2b49a6c"><div class="ttname"><a href="interface_v_l_c_renderer_discoverer_description.html#af6af459a1c5ed50d53695149a2b49a6c">VLCRendererDiscovererDescription::name</a></div><div class="ttdeci">NSString * name</div><div class="ttdef"><b>Definition:</b> VLCRendererDiscoverer.h:50</div></div>
<div class="ttc" id="ainterface_v_l_c_renderer_item_html"><div class="ttname"><a href="interface_v_l_c_renderer_item.html">VLCRendererItem</a></div><div class="ttdef"><b>Definition:</b> VLCRendererItem.h:37</div></div>
<div class="ttc" id="ainterface_v_l_c_renderer_discoverer_html"><div class="ttname"><a href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></div><div class="ttdef"><b>Definition:</b> VLCRendererDiscoverer.h:71</div></div>
<div class="ttc" id="aprotocol_v_l_c_renderer_discoverer_delegate-p_html"><div class="ttname"><a href="protocol_v_l_c_renderer_discoverer_delegate-p.html">VLCRendererDiscovererDelegate-p</a></div><div class="ttdef"><b>Definition:</b> VLCRendererDiscoverer.h:33</div></div>
<div class="ttc" id="ainterface_v_l_c_renderer_discoverer_description_html"><div class="ttname"><a href="interface_v_l_c_renderer_discoverer_description.html">VLCRendererDiscovererDescription</a></div><div class="ttdef"><b>Definition:</b> VLCRendererDiscoverer.h:46</div></div>
<div class="ttc" id="ainterface_v_l_c_renderer_discoverer_description_html_a164296731eab83b21084576ded002861"><div class="ttname"><a href="interface_v_l_c_renderer_discoverer_description.html#a164296731eab83b21084576ded002861">VLCRendererDiscovererDescription::longName</a></div><div class="ttdeci">NSString * longName</div><div class="ttdef"><b>Definition:</b> VLCRendererDiscoverer.h:55</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
