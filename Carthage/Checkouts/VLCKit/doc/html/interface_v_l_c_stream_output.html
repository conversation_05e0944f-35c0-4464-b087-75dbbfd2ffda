<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCStreamOutput Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#pub-static-methods">Class Methods</a> &#124;
<a href="interface_v_l_c_stream_output-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCStreamOutput Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_stream_output_8h_source.html">VLCStreamOutput.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCStreamOutput:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_stream_output.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a11c02ad55e225c5afa473aeb15db13e0"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_stream_output.html#a11c02ad55e225c5afa473aeb15db13e0">initWithOptionDictionary:</a></td></tr>
<tr class="separator:a11c02ad55e225c5afa473aeb15db13e0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Class Methods</h2></td></tr>
<tr class="memitem:ae31bb946515ad904af62b14ad4c0174c"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_stream_output.html#ae31bb946515ad904af62b14ad4c0174c">streamOutputWithOptionDictionary:</a></td></tr>
<tr class="separator:ae31bb946515ad904af62b14ad4c0174c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a37d21584a752b9ddaecd248d171bdf59"><td class="memItemLeft" align="right" valign="top">(id)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_stream_output.html#a37d21584a752b9ddaecd248d171bdf59">rtpBroadcastStreamOutputWithSAPAnnounce:</a></td></tr>
<tr class="separator:a37d21584a752b9ddaecd248d171bdf59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa573b7c97a017789e58ab392812e3032"><td class="memItemLeft" align="right" valign="top">((deprecated)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_stream_output.html#aa573b7c97a017789e58ab392812e3032">__attribute__</a></td></tr>
<tr class="separator:aa573b7c97a017789e58ab392812e3032"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a518d4dd8d9b9733acd58dce332f8d004"><td class="memItemLeft" align="right" valign="top">(id)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_stream_output.html#a518d4dd8d9b9733acd58dce332f8d004">ipodStreamOutputWithFilePath:</a></td></tr>
<tr class="separator:a518d4dd8d9b9733acd58dce332f8d004"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abacda1a7636077fc336abde914fb4877"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_stream_output.html#abacda1a7636077fc336abde914fb4877">streamOutputWithFilePath:</a></td></tr>
<tr class="separator:abacda1a7636077fc336abde914fb4877"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c1586457352350d81adbe686729eb92"><td class="memItemLeft" align="right" valign="top">(id)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_stream_output.html#a1c1586457352350d81adbe686729eb92">mpeg2StreamOutputWithFilePath:</a></td></tr>
<tr class="separator:a1c1586457352350d81adbe686729eb92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5c5ec3624e9cc31e1de8630e42430c40"><td class="memItemLeft" align="right" valign="top">(id)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_stream_output.html#a5c5ec3624e9cc31e1de8630e42430c40">mpeg4StreamOutputWithFilePath:</a></td></tr>
<tr class="separator:a5c5ec3624e9cc31e1de8630e42430c40"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>a class allowing you to stream media based on predefined definitions </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="aa573b7c97a017789e58ab392812e3032"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa573b7c97a017789e58ab392812e3032">&#9670;&nbsp;</a></span>__attribute__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ ((deprecated) __attribute__ </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000021">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a id="a11c02ad55e225c5afa473aeb15db13e0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a11c02ad55e225c5afa473aeb15db13e0">&#9670;&nbsp;</a></span>initWithOptionDictionary:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithOptionDictionary: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000018">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a id="a518d4dd8d9b9733acd58dce332f8d004"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a518d4dd8d9b9733acd58dce332f8d004">&#9670;&nbsp;</a></span>ipodStreamOutputWithFilePath:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (id) ipodStreamOutputWithFilePath: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000022">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a id="a1c1586457352350d81adbe686729eb92"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c1586457352350d81adbe686729eb92">&#9670;&nbsp;</a></span>mpeg2StreamOutputWithFilePath:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (id) mpeg2StreamOutputWithFilePath: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000024">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a id="a5c5ec3624e9cc31e1de8630e42430c40"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5c5ec3624e9cc31e1de8630e42430c40">&#9670;&nbsp;</a></span>mpeg4StreamOutputWithFilePath:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (id) mpeg4StreamOutputWithFilePath: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000025">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a id="a37d21584a752b9ddaecd248d171bdf59"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a37d21584a752b9ddaecd248d171bdf59">&#9670;&nbsp;</a></span>rtpBroadcastStreamOutputWithSAPAnnounce:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (id) rtpBroadcastStreamOutputWithSAPAnnounce: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000020">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a id="abacda1a7636077fc336abde914fb4877"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abacda1a7636077fc336abde914fb4877">&#9670;&nbsp;</a></span>streamOutputWithFilePath:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (instancetype) streamOutputWithFilePath: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000023">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a id="ae31bb946515ad904af62b14ad4c0174c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae31bb946515ad904af62b14ad4c0174c">&#9670;&nbsp;</a></span>streamOutputWithOptionDictionary:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (instancetype) streamOutputWithOptionDictionary: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000019">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_stream_output_8h_source.html">VLCStreamOutput.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
