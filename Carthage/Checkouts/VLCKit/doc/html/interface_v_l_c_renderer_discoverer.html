<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCRendererDiscoverer Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#pub-static-methods">Class Methods</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_renderer_discoverer-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCRendererDiscoverer Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_renderer_discoverer_8h_source.html">VLCRendererDiscoverer.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCRendererDiscoverer:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_renderer_discoverer.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a6168c53319b51261d2e7a59b5ea89fff"><td class="memItemLeft" align="right" valign="top"><a id="a6168c53319b51261d2e7a59b5ea89fff"></a>
(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <b>NS_UNAVAILABLE</b></td></tr>
<tr class="separator:a6168c53319b51261d2e7a59b5ea89fff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a955ed444b32f6fd9dedbecc880142567"><td class="memItemLeft" align="right" valign="top">(instancetype _Nullable)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_renderer_discoverer.html#a955ed444b32f6fd9dedbecc880142567">initWithName:</a></td></tr>
<tr class="separator:a955ed444b32f6fd9dedbecc880142567"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acb4c6a5e9321cdd8432bd0f6e6ebee1d"><td class="memItemLeft" align="right" valign="top">(NSArray&lt; <a class="el" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a> * &gt; *)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_renderer_discoverer.html#acb4c6a5e9321cdd8432bd0f6e6ebee1d">renderers</a></td></tr>
<tr class="separator:acb4c6a5e9321cdd8432bd0f6e6ebee1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f344593f78573d3cba3bc9b89ccea14"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_renderer_discoverer.html#a5f344593f78573d3cba3bc9b89ccea14">start</a></td></tr>
<tr class="separator:a5f344593f78573d3cba3bc9b89ccea14"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7ca6afa57b5f53f6284791fde3f8839"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_renderer_discoverer.html#ab7ca6afa57b5f53f6284791fde3f8839">stop</a></td></tr>
<tr class="separator:ab7ca6afa57b5f53f6284791fde3f8839"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Class Methods</h2></td></tr>
<tr class="memitem:aa6ffdcd169141a032021df59fdfe3e14"><td class="memItemLeft" align="right" valign="top">(NSArray&lt; <a class="el" href="interface_v_l_c_renderer_discoverer_description.html">VLCRendererDiscovererDescription</a> * &gt; *_Nullable)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_renderer_discoverer.html#aa6ffdcd169141a032021df59fdfe3e14">list</a></td></tr>
<tr class="separator:aa6ffdcd169141a032021df59fdfe3e14"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a831fc60a7a5ea8bc1114bc36cb051f66"><td class="memItemLeft" align="right" valign="top">NSString *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_renderer_discoverer.html#a831fc60a7a5ea8bc1114bc36cb051f66">name</a></td></tr>
<tr class="separator:a831fc60a7a5ea8bc1114bc36cb051f66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad1782fa86584819376e5686797b765ee"><td class="memItemLeft" align="right" valign="top">NSArray&lt; <a class="el" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a> * &gt; *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_renderer_discoverer.html#ad1782fa86584819376e5686797b765ee">renderers</a></td></tr>
<tr class="separator:ad1782fa86584819376e5686797b765ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a06b6ab494062dd31592e2bff75132fe1"><td class="memItemLeft" align="right" valign="top">id&lt; <a class="el" href="protocol_v_l_c_renderer_discoverer_delegate-p.html">VLCRendererDiscovererDelegate</a> &gt; _Nullable&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_renderer_discoverer.html#a06b6ab494062dd31592e2bff75132fe1">delegate</a></td></tr>
<tr class="separator:a06b6ab494062dd31592e2bff75132fe1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Renderer Discoverer </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="a955ed444b32f6fd9dedbecc880142567"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a955ed444b32f6fd9dedbecc880142567">&#9670;&nbsp;</a></span>initWithName:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype _Nullable) initWithName: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>name</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Instanciates a <code><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></code> </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">name</td><td>Name of the renderer discoverer </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A new <code><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></code> object, only if there were no errors </dd></dl>

</div>
</div>
<a id="aa6ffdcd169141a032021df59fdfe3e14"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa6ffdcd169141a032021df59fdfe3e14">&#9670;&nbsp;</a></span>list</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (NSArray&lt;<a class="el" href="interface_v_l_c_renderer_discoverer_description.html">VLCRendererDiscovererDescription</a> *&gt; * _Nullable) list </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns an <code>NSArray</code> of <code><a class="el" href="interface_v_l_c_renderer_discoverer_description.html">VLCRendererDiscovererDescription</a></code> </p><dl class="section note"><dt>Note</dt><dd>Call this method to retreive information in order to instanciate a <code> </code><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a><code> \return An</code>NSArray<code>of</code>VLCRendererDiscovererDescription` </dd></dl>

</div>
</div>
<a id="acb4c6a5e9321cdd8432bd0f6e6ebee1d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acb4c6a5e9321cdd8432bd0f6e6ebee1d">&#9670;&nbsp;</a></span>renderers</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray&lt;<a class="el" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a> *&gt; *) renderers </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns discovered renderers </p><dl class="section return"><dt>Returns</dt><dd>discovered renderers </dd></dl>

</div>
</div>
<a id="a5f344593f78573d3cba3bc9b89ccea14"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f344593f78573d3cba3bc9b89ccea14">&#9670;&nbsp;</a></span>start</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) start </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Start the renderer discoverer </p><dl class="section return"><dt>Returns</dt><dd><code>YES</code> if successful, <code>NO</code> otherwise </dd></dl>

</div>
</div>
<a id="ab7ca6afa57b5f53f6284791fde3f8839"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab7ca6afa57b5f53f6284791fde3f8839">&#9670;&nbsp;</a></span>stop</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) stop </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Stops the renderer discoverer </p><dl class="section note"><dt>Note</dt><dd>This cannot fail </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a06b6ab494062dd31592e2bff75132fe1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a06b6ab494062dd31592e2bff75132fe1">&#9670;&nbsp;</a></span>delegate</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (id&lt;<a class="el" href="protocol_v_l_c_renderer_discoverer_delegate-p.html">VLCRendererDiscovererDelegate</a>&gt; _Nullable) delegate</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">weak</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Receiver's delegate </p>

</div>
</div>
<a id="a831fc60a7a5ea8bc1114bc36cb051f66"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a831fc60a7a5ea8bc1114bc36cb051f66">&#9670;&nbsp;</a></span>name</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString*) name</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Name of the renderer discoverer </p>

</div>
</div>
<a id="ad1782fa86584819376e5686797b765ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad1782fa86584819376e5686797b765ee">&#9670;&nbsp;</a></span>renderers</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray&lt;<a class="el" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a> *&gt;*) renderers</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Renderers of the discoverer </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_renderer_discoverer_8h_source.html">VLCRendererDiscoverer.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
