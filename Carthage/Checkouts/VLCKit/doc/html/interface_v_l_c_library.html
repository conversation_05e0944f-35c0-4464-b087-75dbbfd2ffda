<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCLibrary Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#pub-static-methods">Class Methods</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_library-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCLibrary Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_library_8h_source.html">VLCLibrary.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCLibrary:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_library.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:ab029c774c0b133dc9c15b9f4446de8c5"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_library.html#ab029c774c0b133dc9c15b9f4446de8c5">initWithOptions:</a></td></tr>
<tr class="separator:ab029c774c0b133dc9c15b9f4446de8c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a86c8d6b68e6852dea7045a40a9110dde"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_library.html#a86c8d6b68e6852dea7045a40a9110dde">setDebugLoggingToFile:</a></td></tr>
<tr class="separator:a86c8d6b68e6852dea7045a40a9110dde"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae91040ee78413a160918871e1e9475ff"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_library.html#ae91040ee78413a160918871e1e9475ff">setHumanReadableName:withHTTPUserAgent:</a></td></tr>
<tr class="separator:ae91040ee78413a160918871e1e9475ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afe1e5231b6bc4b9b12e0fd2c44862dbb"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_library.html#afe1e5231b6bc4b9b12e0fd2c44862dbb">setApplicationIdentifier:withVersion:andApplicationIconName:</a></td></tr>
<tr class="separator:afe1e5231b6bc4b9b12e0fd2c44862dbb"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Class Methods</h2></td></tr>
<tr class="memitem:a7eddfd69ce66ffc5603bc68a5777187e"><td class="memItemLeft" align="right" valign="top">(<a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> *)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_library.html#a7eddfd69ce66ffc5603bc68a5777187e">sharedLibrary</a></td></tr>
<tr class="separator:a7eddfd69ce66ffc5603bc68a5777187e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a30523c86de4b6a2fbc86f1d6a8274b59"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_library.html#a30523c86de4b6a2fbc86f1d6a8274b59">debugLogging</a></td></tr>
<tr class="separator:a30523c86de4b6a2fbc86f1d6a8274b59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b0d7e806f3c1c49243873f99c3608fa"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_library.html#a0b0d7e806f3c1c49243873f99c3608fa">debugLoggingLevel</a></td></tr>
<tr class="separator:a0b0d7e806f3c1c49243873f99c3608fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7194d31353ddeef7273dd9d9c394841a"><td class="memItemLeft" align="right" valign="top">id&lt; <a class="el" href="protocol_v_l_c_library_log_receiver_protocol-p.html">VLCLibraryLogReceiverProtocol</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_library.html#a7194d31353ddeef7273dd9d9c394841a">debugLoggingTarget</a></td></tr>
<tr class="separator:a7194d31353ddeef7273dd9d9c394841a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a99403afc6373f5f0ca5b89b6c64d3f32"><td class="memItemLeft" align="right" valign="top">NSString *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_library.html#a99403afc6373f5f0ca5b89b6c64d3f32">version</a></td></tr>
<tr class="separator:a99403afc6373f5f0ca5b89b6c64d3f32"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91485a448eb7b18c7b5c36bba4c56184"><td class="memItemLeft" align="right" valign="top">NSString *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_library.html#a91485a448eb7b18c7b5c36bba4c56184">compiler</a></td></tr>
<tr class="separator:a91485a448eb7b18c7b5c36bba4c56184"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9201c5f4b1ee745e31e3875835914124"><td class="memItemLeft" align="right" valign="top">NSString *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_library.html#a9201c5f4b1ee745e31e3875835914124">changeset</a></td></tr>
<tr class="separator:a9201c5f4b1ee745e31e3875835914124"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb375a92ef38f3cacab3d1d501b747d3"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_library.html#abb375a92ef38f3cacab3d1d501b747d3">instance</a></td></tr>
<tr class="separator:abb375a92ef38f3cacab3d1d501b747d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The <a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> is the base library of VLCKit.framework. This object provides a shared instance that exposes the internal functionalities of libvlc and libvlc-control. The <a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> object is instantiated automatically when VLCKit.framework is loaded into memory. Also, it is automatically destroyed when VLCKit.framework is unloaded from memory.</p>
<p>Currently, the framework does not support multiple instances of <a class="el" href="interface_v_l_c_library.html">VLCLibrary</a>. Furthermore, you cannot destroy any instance of <a class="el" href="interface_v_l_c_library.html">VLCLibrary</a>; this is done automatically by the dynamic link loader. </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="ab029c774c0b133dc9c15b9f4446de8c5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab029c774c0b133dc9c15b9f4446de8c5">&#9670;&nbsp;</a></span>initWithOptions:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithOptions: </td>
          <td></td>
          <td class="paramtype">(NSArray *)&#160;</td>
          <td class="paramname"><em>options</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns an individual instance which can be customized with options </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">options</td><td>NSArray with NSString instance containing the options </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the individual library instance </dd></dl>

</div>
</div>
<a id="afe1e5231b6bc4b9b12e0fd2c44862dbb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afe1e5231b6bc4b9b12e0fd2c44862dbb">&#9670;&nbsp;</a></span>setApplicationIdentifier:withVersion:andApplicationIconName:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) setApplicationIdentifier: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>identifier</em></td>
        </tr>
        <tr>
          <td class="paramkey">withVersion:</td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>version</em></td>
        </tr>
        <tr>
          <td class="paramkey">andApplicationIconName:</td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>icon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets meta-information about the application </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">identifier</td><td>Java-style application identifier, e.g. "com.acme.foobar" </td></tr>
    <tr><td class="paramname">version</td><td>Application version numbers, e.g. "1.2.3" </td></tr>
    <tr><td class="paramname">icon</td><td>Application icon name, e.g. "foobar" </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a86c8d6b68e6852dea7045a40a9110dde"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a86c8d6b68e6852dea7045a40a9110dde">&#9670;&nbsp;</a></span>setDebugLoggingToFile:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) setDebugLoggingToFile: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>filePath</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Activates debug logging to a file stream If the file already exists, the log will be appended by the end. If it does not exist, will be created. The file will continously updated with new messages from this library instance. </p><dl class="section note"><dt>Note</dt><dd>It is the client app's obligation to ensure that the target file path is writable and all subfolders exist </dd></dl>
<dl class="section warning"><dt>Warning</dt><dd>when enabling this feature, logging to the console or an object target will be stopped automatically </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>Returns NO on failure </dd></dl>

</div>
</div>
<a id="ae91040ee78413a160918871e1e9475ff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae91040ee78413a160918871e1e9475ff">&#9670;&nbsp;</a></span>setHumanReadableName:withHTTPUserAgent:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) setHumanReadableName: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>readableName</em></td>
        </tr>
        <tr>
          <td class="paramkey">withHTTPUserAgent:</td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>userAgent</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets the application name and HTTP User Agent libvlc will pass it to servers when required by protocol </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">readableName</td><td>Human-readable application name, e.g. "FooBar player 1.2.3" </td></tr>
    <tr><td class="paramname">userAgent</td><td>HTTP User Agent, e.g. "FooBar/1.2.3 Python/2.6.0" </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a7eddfd69ce66ffc5603bc68a5777187e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7eddfd69ce66ffc5603bc68a5777187e">&#9670;&nbsp;</a></span>sharedLibrary</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (<a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> *) sharedLibrary </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns the library's shared instance </p><dl class="section return"><dt>Returns</dt><dd>The library's shared instance </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a9201c5f4b1ee745e31e3875835914124"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9201c5f4b1ee745e31e3875835914124">&#9670;&nbsp;</a></span>changeset</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString*) changeset</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the library's changeset </p><dl class="section return"><dt>Returns</dt><dd>The library version example "adfee99" </dd></dl>

</div>
</div>
<a id="a91485a448eb7b18c7b5c36bba4c56184"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a91485a448eb7b18c7b5c36bba4c56184">&#9670;&nbsp;</a></span>compiler</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString*) compiler</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the compiler used to build the libvlc binary </p><dl class="section return"><dt>Returns</dt><dd>The compiler version string. </dd></dl>

</div>
</div>
<a id="a30523c86de4b6a2fbc86f1d6a8274b59"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a30523c86de4b6a2fbc86f1d6a8274b59">&#9670;&nbsp;</a></span>debugLogging</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) debugLogging</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Enables/disables debug logging to console </p><dl class="section note"><dt>Note</dt><dd>NSLog is used to log messages </dd></dl>

</div>
</div>
<a id="a0b0d7e806f3c1c49243873f99c3608fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0b0d7e806f3c1c49243873f99c3608fa">&#9670;&nbsp;</a></span>debugLoggingLevel</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) debugLoggingLevel</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Gets/sets the debug logging level </p><dl class="section note"><dt>Note</dt><dd>Logging level 0: info/notice 1: error 2: warning 3-4: debug </dd>
<dd>
values set here will be consired only when logging to console </dd></dl>
<dl class="section warning"><dt>Warning</dt><dd>If an invalid level is provided, level defaults to 0 </dd></dl>

</div>
</div>
<a id="a7194d31353ddeef7273dd9d9c394841a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7194d31353ddeef7273dd9d9c394841a">&#9670;&nbsp;</a></span>debugLoggingTarget</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (id&lt;<a class="el" href="protocol_v_l_c_library_log_receiver_protocol-p.html">VLCLibraryLogReceiverProtocol</a>&gt;) debugLoggingTarget</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Activates debug logging to an object target following the <a class="el" href="protocol_v_l_c_library_log_receiver_protocol-p.html">VLCLibraryLogReceiverProtocol</a> protocol The target will be continously called as new messages arrive from this library instance. </p><dl class="section warning"><dt>Warning</dt><dd>when enabling this feature, logging to the console or a file will be stopped automatically </dd></dl>

</div>
</div>
<a id="abb375a92ef38f3cacab3d1d501b747d3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abb375a92ef38f3cacab3d1d501b747d3">&#9670;&nbsp;</a></span>instance</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void*) instance</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>libvlc instance wrapped by the <a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> instance </p><dl class="section note"><dt>Note</dt><dd>If you want to use it, you are most likely wrong (or want to add a proper ObjC API) </dd></dl>

</div>
</div>
<a id="a99403afc6373f5f0ca5b89b6c64d3f32"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a99403afc6373f5f0ca5b89b6c64d3f32">&#9670;&nbsp;</a></span>version</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString*) version</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the library's version </p><dl class="section return"><dt>Returns</dt><dd>The library version example "0.9.0-git Grishenko" </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_library_8h_source.html">VLCLibrary.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
