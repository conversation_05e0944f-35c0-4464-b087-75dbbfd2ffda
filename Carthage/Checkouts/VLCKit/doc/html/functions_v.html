<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_v"></a>- v -</h3><ul>
<li>value
: <a class="el" href="interface_v_l_c_time.html#a66fccafd32f7627fc119318a6c811394">VLCTime</a>
</li>
<li>verboseStringValue
: <a class="el" href="interface_v_l_c_time.html#acde55e3e8f75f7242f4fbf5c11e64743">VLCTime</a>
</li>
<li>version
: <a class="el" href="interface_v_l_c_library.html#a99403afc6373f5f0ca5b89b6c64d3f32">VLCLibrary</a>
</li>
<li>videoAspectRatio
: <a class="el" href="interface_v_l_c_media_player.html#a3ee849792344fed560e4308ebe8e4a76">VLCMediaPlayer</a>
</li>
<li>videoCropGeometry
: <a class="el" href="interface_v_l_c_media_player.html#a3f0fd895e58be570f115ab6f09501ffe">VLCMediaPlayer</a>
</li>
<li>videoSize
: <a class="el" href="interface_v_l_c_media_player.html#a0734e2b2d4edebeaf3ca9e1cce85f361">VLCMediaPlayer</a>
</li>
<li>videoSubTitlesIndexes
: <a class="el" href="interface_v_l_c_media_player.html#a1c8a4af83a85f3e8606049aad6f75169">VLCMediaPlayer</a>
</li>
<li>videoSubTitlesNames
: <a class="el" href="interface_v_l_c_media_player.html#aaaf3e36c370456bbf30058eedaf7844e">VLCMediaPlayer</a>
</li>
<li>videoTrackIndexes
: <a class="el" href="interface_v_l_c_media_player.html#a4f1abde67436f198f0d07b885bd5ac59">VLCMediaPlayer</a>
</li>
<li>videoTrackNames
: <a class="el" href="interface_v_l_c_media_player.html#aaa31a3ee365dd1721064f19319bb8026">VLCMediaPlayer</a>
</li>
<li>VLCChapterDescriptionDuration
: <a class="el" href="interface_v_l_c_media_player.html#a5d87f02211d47497f35bf616f7a0374e">VLCMediaPlayer</a>
</li>
<li>VLCChapterDescriptionName
: <a class="el" href="interface_v_l_c_media_player.html#a20081eb2719b21bd55b4d2ce185f86f2">VLCMediaPlayer</a>
</li>
<li>VLCChapterDescriptionTimeOffset
: <a class="el" href="interface_v_l_c_media_player.html#ab5d7190ce89c08ae1b14e6c2a827d104">VLCMediaPlayer</a>
</li>
<li>VLCMediaDoInteract
: <a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba0184ba9260e22d0182cf418b2d73c20c">VLCMedia</a>
</li>
<li>VLCMediaFetchLocal
: <a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba802f388c99aa52b9bd26a11f55ffd4f0">VLCMedia</a>
</li>
<li>VLCMediaFetchNetwork
: <a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cbaf9a01d0578ab528242b3de74d5e216dc">VLCMedia</a>
</li>
<li>VLCMediaParseLocal
: <a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba96d692455552df3b6332ce02b5c955a1">VLCMedia</a>
</li>
<li>VLCMediaParseNetwork
: <a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cbadac6d0a10ac08b36376db311ce6a91a5">VLCMedia</a>
</li>
<li>VLCMediaParsingOptions
: <a class="el" href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationAudioChannelsNumber
: <a class="el" href="interface_v_l_c_media.html#aabf94e7de92ae328dba46d6c53e5d869">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationAudioRate
: <a class="el" href="interface_v_l_c_media.html#a5f42247ad4cefc2cfa4a96bd95f53356">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationBitrate
: <a class="el" href="interface_v_l_c_media.html#add3bac6827f60b1cbe44544c106b39c0">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationCodec
: <a class="el" href="interface_v_l_c_media.html#a0e44431952021460c5f59f600236630b">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationCodecLevel
: <a class="el" href="interface_v_l_c_media.html#ae380aafa86ebd25ad38ab630a6dc86dd">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationCodecProfile
: <a class="el" href="interface_v_l_c_media.html#a076812e00bd51440c4d47da823011f86">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationDescription
: <a class="el" href="interface_v_l_c_media.html#a480f68a87f30c723f9364f00620de519">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationFrameRate
: <a class="el" href="interface_v_l_c_media.html#afd5e8623f3246506f21576ca006df47e">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationFrameRateDenominator
: <a class="el" href="interface_v_l_c_media.html#a635234c93bcb43393868435ab98ad0a8">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationId
: <a class="el" href="interface_v_l_c_media.html#a523d5f9351c2fcac0d9b600773734c81">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationLanguage
: <a class="el" href="interface_v_l_c_media.html#ac6879d1635a7c5c306bafc23cbed755a">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationSourceAspectRatio
: <a class="el" href="interface_v_l_c_media.html#ada9d9ba5acf71414913ecc83cb975bf6">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationSourceAspectRatioDenominator
: <a class="el" href="interface_v_l_c_media.html#aa3b1ead249368c4b73a544f07c84bcdc">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationTextEncoding
: <a class="el" href="interface_v_l_c_media.html#ad173cd33fb9d51175e676b62838cd980">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationType
: <a class="el" href="interface_v_l_c_media.html#ac5ccaa4e433a8bc847e54739d69827b7">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationTypeAudio
: <a class="el" href="interface_v_l_c_media.html#ac6eb47ffe2b3a79f562e0164e83416b1">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationTypeText
: <a class="el" href="interface_v_l_c_media.html#a3d8f7f156478e43c45dfedf7459c9939">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationTypeUnknown
: <a class="el" href="interface_v_l_c_media.html#a2b91c4e456c6ce07682477d41772adc2">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationTypeVideo
: <a class="el" href="interface_v_l_c_media.html#a2ac631a7a8c7416ac9a13b914efeb22e">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationVideoHeight
: <a class="el" href="interface_v_l_c_media.html#a52414aa5aff9e0e929d6b3dad0461dd2">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationVideoOrientation
: <a class="el" href="interface_v_l_c_media.html#a32e842b07314f6b7a965fd8d5770bf8d">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationVideoProjection
: <a class="el" href="interface_v_l_c_media.html#ac28eab6679f8761ce13ea02d61562d21">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationVideoWidth
: <a class="el" href="interface_v_l_c_media.html#a29ca0a5036cfa556f5d7098c44030123">VLCMedia</a>
</li>
<li>VLCTitleDescriptionDuration
: <a class="el" href="interface_v_l_c_media_player.html#ac4bd07aad0599f2f61cbac7281981df7">VLCMediaPlayer</a>
</li>
<li>VLCTitleDescriptionIsMenu
: <a class="el" href="interface_v_l_c_media_player.html#a1cfbed633aa7783841c153d48088ba70">VLCMediaPlayer</a>
</li>
<li>VLCTitleDescriptionName
: <a class="el" href="interface_v_l_c_media_player.html#a2907bb09c29757c5c0f89e5bbe7e7394">VLCMediaPlayer</a>
</li>
<li>volume
: <a class="el" href="interface_v_l_c_audio.html#a553194cab2c141b89804db92e1a43f87">VLCAudio</a>
</li>
<li>volumeDown
: <a class="el" href="interface_v_l_c_audio.html#a35b0ee4fbc502b52f7ae89970b27a246">VLCAudio</a>
</li>
<li>volumeUp
: <a class="el" href="interface_v_l_c_audio.html#abce4117645f4fc354f457dca9a991aa3">VLCAudio</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
