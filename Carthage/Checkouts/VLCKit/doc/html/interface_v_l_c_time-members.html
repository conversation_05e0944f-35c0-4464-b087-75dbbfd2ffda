<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCTime Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="interface_v_l_c_time.html">VLCTime</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_time.html#a4614e337e6304712ddd9182861e347ed">__attribute__</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_time.html#a4bde16fb3441c6e7ff40a75e2f206148">compare:</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_time.html#a5f24e1e825a0676c3da2f6a3d5bc0a13">hash</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_time.html#ac9717d5978ab18fa6547cde44904cfad">initWithInt:</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_time.html#a4681ebad8d27e7243ebf6d83679e8a24">initWithNumber:</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_time.html#aff49484cae12d9fbf65ce6b4a7da2cfd">intValue</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_time.html#a02ff06b1610b86d9bb1770a93837be42">isEqual:</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_time.html#aa6d4bce56be3df94a0896763f1769921">minuteStringValue</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">+&#160;</td><td><a class="el" href="interface_v_l_c_time.html#ae4b7157f2152d3eddafe14e126a4b96c">nullTime</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_time.html#a7aa23f16fef10097af66ac4d9754d0dc">stringValue</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">+&#160;</td><td><a class="el" href="interface_v_l_c_time.html#a936e42eed02e03b404f14bdbce13d06f">timeWithInt:</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry">+&#160;</td><td><a class="el" href="interface_v_l_c_time.html#af9b7c469a7cb064a3d9c2c5bcaa769bb">timeWithNumber:</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_time.html#a66fccafd32f7627fc119318a6c811394">value</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_time.html#acde55e3e8f75f7242f4fbf5c11e64743">verboseStringValue</a></td><td class="entry"><a class="el" href="interface_v_l_c_time.html">VLCTime</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
