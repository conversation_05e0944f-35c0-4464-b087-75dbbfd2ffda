<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCRendererDiscoverer Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_renderer_discoverer.html#a06b6ab494062dd31592e2bff75132fe1">delegate</a></td><td class="entry"><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_renderer_discoverer.html#a955ed444b32f6fd9dedbecc880142567">initWithName:</a></td><td class="entry"><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">+&#160;</td><td><a class="el" href="interface_v_l_c_renderer_discoverer.html#aa6ffdcd169141a032021df59fdfe3e14">list</a></td><td class="entry"><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_renderer_discoverer.html#a831fc60a7a5ea8bc1114bc36cb051f66">name</a></td><td class="entry"><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></td><td class="entry"></td></tr>
  <tr bgcolor="#f0f0f0" class="even"><td class="entry">-&#160;</td><td class="entry"><b>NS_UNAVAILABLE</b> (defined in <a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a>)</td><td class="entry"><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_renderer_discoverer.html#ad1782fa86584819376e5686797b765ee">renderers</a></td><td class="entry"><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_renderer_discoverer.html#acb4c6a5e9321cdd8432bd0f6e6ebee1d">renderers</a></td><td class="entry"><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_renderer_discoverer.html#a5f344593f78573d3cba3bc9b89ccea14">start</a></td><td class="entry"><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_renderer_discoverer.html#ab7ca6afa57b5f53f6284791fde3f8839">stop</a></td><td class="entry"><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
