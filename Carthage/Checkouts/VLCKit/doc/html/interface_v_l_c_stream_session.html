<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCStreamSession Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#pub-static-methods">Class Methods</a> &#124;
<a href="interface_v_l_c_stream_session-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCStreamSession Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_stream_session_8h_source.html">VLCStreamSession.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCStreamSession:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_stream_session.png" usemap="#VLCStreamSession_map" alt=""/>
  <map id="VLCStreamSession_map" name="VLCStreamSession_map">
<area href="interface_v_l_c_media_player.html" alt="VLCMediaPlayer" shape="rect" coords="0,56,120,80"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:afd34666df70b52b43ea64394796f4d52"><td class="memItemLeft" align="right" valign="top">(<a class="el" href="interface_v_l_c_stream_output.html">VLCStreamOutput</a> *streamOutput)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_stream_session.html#afd34666df70b52b43ea64394796f4d52">__attribute__</a></td></tr>
<tr class="separator:afd34666df70b52b43ea64394796f4d52"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e5017ee1863eb61a649f7d28311fb30"><td class="memItemLeft" align="right" valign="top">(BOOL isComplete)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_stream_session.html#a8e5017ee1863eb61a649f7d28311fb30">__attribute__</a></td></tr>
<tr class="separator:a8e5017ee1863eb61a649f7d28311fb30"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a69590b40a62d896b8a9ba88ca2f69ecb"><td class="memItemLeft" align="right" valign="top">(NSUInteger reattemptedConnections)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_stream_session.html#a69590b40a62d896b8a9ba88ca2f69ecb">__attribute__</a></td></tr>
<tr class="separator:a69590b40a62d896b8a9ba88ca2f69ecb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a462740188cf07ef740c1669dcfd34c2b"><td class="memItemLeft" align="right" valign="top">((deprecated)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_stream_session.html#a462740188cf07ef740c1669dcfd34c2b">__attribute__</a></td></tr>
<tr class="separator:a462740188cf07ef740c1669dcfd34c2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a462740188cf07ef740c1669dcfd34c2b"><td class="memItemLeft" align="right" valign="top">((deprecated)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_stream_session.html#a462740188cf07ef740c1669dcfd34c2b">__attribute__</a></td></tr>
<tr class="separator:a462740188cf07ef740c1669dcfd34c2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header pub_methods_interface_v_l_c_media_player"><td colspan="2" onclick="javascript:toggleInherit('pub_methods_interface_v_l_c_media_player')"><img src="closed.png" alt="-"/>&#160;Instance Methods inherited from <a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a></td></tr>
<tr class="memitem:a432545540f6be27394824275fa6e3d10 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a432545540f6be27394824275fa6e3d10">initWithVideoView:</a></td></tr>
<tr class="separator:a432545540f6be27394824275fa6e3d10 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad00097d1bab674773134188770396d1d inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ad00097d1bab674773134188770396d1d">initWithVideoLayer:</a></td></tr>
<tr class="separator:ad00097d1bab674773134188770396d1d inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80fb8e4483b8675f8752ad006fd0f361 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a80fb8e4483b8675f8752ad006fd0f361">initWithOptions:</a></td></tr>
<tr class="separator:a80fb8e4483b8675f8752ad006fd0f361 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91e1bad6eaedb58cbfe097f633c50ebd inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a91e1bad6eaedb58cbfe097f633c50ebd">initWithLibVLCInstance:andLibrary:</a></td></tr>
<tr class="separator:a91e1bad6eaedb58cbfe097f633c50ebd inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1eb2229ede2d006bec1650e4d4b0fa02 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a1eb2229ede2d006bec1650e4d4b0fa02">setVideoView:</a></td></tr>
<tr class="separator:a1eb2229ede2d006bec1650e4d4b0fa02 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76dc478bf25fae8e0f671e39d006ce25 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a76dc478bf25fae8e0f671e39d006ce25">setVideoLayer:</a></td></tr>
<tr class="separator:a76dc478bf25fae8e0f671e39d006ce25 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac0e16c49e1746e74a35fd50f870c5e31 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ac0e16c49e1746e74a35fd50f870c5e31">saveVideoSnapshotAt:withWidth:andHeight:</a></td></tr>
<tr class="separator:ac0e16c49e1746e74a35fd50f870c5e31 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95abbbed4ddab2adb3fb5c4a8b8ae076 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a95abbbed4ddab2adb3fb5c4a8b8ae076">setDeinterlaceFilter:</a></td></tr>
<tr class="separator:a95abbbed4ddab2adb3fb5c4a8b8ae076 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a236ed2450c49f8acd0846499bf26fbb5 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a236ed2450c49f8acd0846499bf26fbb5">setDeinterlace:withFilter:</a></td></tr>
<tr class="separator:a236ed2450c49f8acd0846499bf26fbb5 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a18546cd8ca1b827eb5ddb9384e172166 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a18546cd8ca1b827eb5ddb9384e172166">openVideoSubTitlesFromFile:</a></td></tr>
<tr class="separator:a18546cd8ca1b827eb5ddb9384e172166 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7eb2b3aeb06985b3b655c1c76609924f inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(typedef)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a7eb2b3aeb06985b3b655c1c76609924f">NS_ENUM</a></td></tr>
<tr class="separator:a7eb2b3aeb06985b3b655c1c76609924f inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9477125c5c85a4c598e4088c031331e2 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(int)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a9477125c5c85a4c598e4088c031331e2">addPlaybackSlave:type:enforce:</a></td></tr>
<tr class="separator:a9477125c5c85a4c598e4088c031331e2 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae2a2398ebf77aaa0dd5218d6e17a61f4 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ae2a2398ebf77aaa0dd5218d6e17a61f4">previousChapter</a></td></tr>
<tr class="separator:ae2a2398ebf77aaa0dd5218d6e17a61f4 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acfaac1c1cf8ae35bd40d80afa42bf1f0 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#acfaac1c1cf8ae35bd40d80afa42bf1f0">nextChapter</a></td></tr>
<tr class="separator:acfaac1c1cf8ae35bd40d80afa42bf1f0 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42a283dd2c12d510f247d10510deffff inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(int)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a42a283dd2c12d510f247d10510deffff">numberOfChaptersForTitle:</a></td></tr>
<tr class="separator:a42a283dd2c12d510f247d10510deffff inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59fbfd5a29004a32b9f64328ad6b57a4 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(NSArray *)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a59fbfd5a29004a32b9f64328ad6b57a4">chaptersForTitleIndex:</a></td></tr>
<tr class="separator:a59fbfd5a29004a32b9f64328ad6b57a4 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a637f12081f1748693c9234b33c752640 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(NSArray *)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a637f12081f1748693c9234b33c752640">chapterDescriptionsOfTitle:</a></td></tr>
<tr class="separator:a637f12081f1748693c9234b33c752640 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb2864039496be0bde196467971dd873 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#abb2864039496be0bde196467971dd873">resetEqualizerFromProfile:</a></td></tr>
<tr class="separator:abb2864039496be0bde196467971dd873 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa46d4dfad32097dc1cac62f795e06f39 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(CGFloat)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#aa46d4dfad32097dc1cac62f795e06f39">frequencyOfBandAtIndex:</a></td></tr>
<tr class="separator:aa46d4dfad32097dc1cac62f795e06f39 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae61e529ff86b246ebe54cc29b0a96c0e inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ae61e529ff86b246ebe54cc29b0a96c0e">setAmplification:forBand:</a></td></tr>
<tr class="separator:ae61e529ff86b246ebe54cc29b0a96c0e inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab60ddfe3c2f9d1943e094ce169ac1dbf inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(CGFloat)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ab60ddfe3c2f9d1943e094ce169ac1dbf">amplificationOfBand:</a></td></tr>
<tr class="separator:ab60ddfe3c2f9d1943e094ce169ac1dbf inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7da1ef4be33931daadf5937cd2365924 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a7da1ef4be33931daadf5937cd2365924">play</a></td></tr>
<tr class="separator:a7da1ef4be33931daadf5937cd2365924 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaef26685e063e62599a5b0248a072a0f inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#aaef26685e063e62599a5b0248a072a0f">pause</a></td></tr>
<tr class="separator:aaef26685e063e62599a5b0248a072a0f inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7184261982c10d5d1307e37ed16fb52 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ad7184261982c10d5d1307e37ed16fb52">stop</a></td></tr>
<tr class="separator:ad7184261982c10d5d1307e37ed16fb52 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0efca8c5d59212429f7aa715f61b7637 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a0efca8c5d59212429f7aa715f61b7637">gotoNextFrame</a></td></tr>
<tr class="separator:a0efca8c5d59212429f7aa715f61b7637 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ad6cf29b157d2042f125b03a739c6ea inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a1ad6cf29b157d2042f125b03a739c6ea">fastForward</a></td></tr>
<tr class="separator:a1ad6cf29b157d2042f125b03a739c6ea inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0790b3fdd424f5c59472f416b8ee5f4 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ae0790b3fdd424f5c59472f416b8ee5f4">fastForwardAtRate:</a></td></tr>
<tr class="separator:ae0790b3fdd424f5c59472f416b8ee5f4 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac03553439681974c0da7014c44b104d6 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ac03553439681974c0da7014c44b104d6">rewind</a></td></tr>
<tr class="separator:ac03553439681974c0da7014c44b104d6 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade646072ef74f61b84e4afdc260a0f36 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ade646072ef74f61b84e4afdc260a0f36">rewindAtRate:</a></td></tr>
<tr class="separator:ade646072ef74f61b84e4afdc260a0f36 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a86d420386d6dde28818ae2a7e56f6198 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a86d420386d6dde28818ae2a7e56f6198">jumpBackward:</a></td></tr>
<tr class="separator:a86d420386d6dde28818ae2a7e56f6198 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51e8067be7f55f64d3cfa3e4acccfafc inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a51e8067be7f55f64d3cfa3e4acccfafc">jumpForward:</a></td></tr>
<tr class="separator:a51e8067be7f55f64d3cfa3e4acccfafc inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefc367fa665de839effe00bccb4b7261 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#aefc367fa665de839effe00bccb4b7261">extraShortJumpBackward</a></td></tr>
<tr class="separator:aefc367fa665de839effe00bccb4b7261 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac77c500c9dbfb4bf3f3228db0f970e50 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ac77c500c9dbfb4bf3f3228db0f970e50">extraShortJumpForward</a></td></tr>
<tr class="separator:ac77c500c9dbfb4bf3f3228db0f970e50 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a8548e2888bbcf9bf8ed5a35fc30cf4 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a8a8548e2888bbcf9bf8ed5a35fc30cf4">shortJumpBackward</a></td></tr>
<tr class="separator:a8a8548e2888bbcf9bf8ed5a35fc30cf4 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab09028de82e23ba963e7b645949aa212 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ab09028de82e23ba963e7b645949aa212">shortJumpForward</a></td></tr>
<tr class="separator:ab09028de82e23ba963e7b645949aa212 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae010feb668404c839ca683ecef6ee7a2 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ae010feb668404c839ca683ecef6ee7a2">mediumJumpBackward</a></td></tr>
<tr class="separator:ae010feb668404c839ca683ecef6ee7a2 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae87c31a4ebc3274b2b275bc5ac42f24c inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ae87c31a4ebc3274b2b275bc5ac42f24c">mediumJumpForward</a></td></tr>
<tr class="separator:ae87c31a4ebc3274b2b275bc5ac42f24c inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a78d5c9af63086443c9539c06b63bff4d inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a78d5c9af63086443c9539c06b63bff4d">longJumpBackward</a></td></tr>
<tr class="separator:a78d5c9af63086443c9539c06b63bff4d inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad04f7a86349209e50615afc1a513a768 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ad04f7a86349209e50615afc1a513a768">longJumpForward</a></td></tr>
<tr class="separator:ad04f7a86349209e50615afc1a513a768 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2309a13bb4aa332f3dd1eecead8831a3 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a2309a13bb4aa332f3dd1eecead8831a3">performNavigationAction:</a></td></tr>
<tr class="separator:a2309a13bb4aa332f3dd1eecead8831a3 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe9c128cdaa533d51c5209d4c17d5b09 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#abe9c128cdaa533d51c5209d4c17d5b09">updateViewpoint:pitch:roll:fov:absolute:</a></td></tr>
<tr class="separator:abe9c128cdaa533d51c5209d4c17d5b09 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0af0ae5ca6a1b5f68efc764a296b6876 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a0af0ae5ca6a1b5f68efc764a296b6876">startRecordingAtPath:</a></td></tr>
<tr class="separator:a0af0ae5ca6a1b5f68efc764a296b6876 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41917b1e63701715a0e66f16e22c6f63 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a41917b1e63701715a0e66f16e22c6f63">stopRecording</a></td></tr>
<tr class="separator:a41917b1e63701715a0e66f16e22c6f63 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44f8101ea62406d757c44520361f8930 inherit pub_methods_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a44f8101ea62406d757c44520361f8930">setRendererItem:</a></td></tr>
<tr class="separator:a44f8101ea62406d757c44520361f8930 inherit pub_methods_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Class Methods</h2></td></tr>
<tr class="memitem:a462740188cf07ef740c1669dcfd34c2b"><td class="memItemLeft" align="right" valign="top">((deprecated)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_stream_session.html#a462740188cf07ef740c1669dcfd34c2b">__attribute__</a></td></tr>
<tr class="separator:a462740188cf07ef740c1669dcfd34c2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="inherited"></a>
Additional Inherited Members</h2></td></tr>
<tr class="inherit_header pub_attribs_interface_v_l_c_media_player"><td colspan="2" onclick="javascript:toggleInherit('pub_attribs_interface_v_l_c_media_player')"><img src="closed.png" alt="-"/>&#160;Public Attributes inherited from <a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a></td></tr>
<tr class="memitem:a20081eb2719b21bd55b4d2ce185f86f2 inherit pub_attribs_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a20081eb2719b21bd55b4d2ce185f86f2">VLCChapterDescriptionName</a></td></tr>
<tr class="separator:a20081eb2719b21bd55b4d2ce185f86f2 inherit pub_attribs_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5d7190ce89c08ae1b14e6c2a827d104 inherit pub_attribs_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ab5d7190ce89c08ae1b14e6c2a827d104">VLCChapterDescriptionTimeOffset</a></td></tr>
<tr class="separator:ab5d7190ce89c08ae1b14e6c2a827d104 inherit pub_attribs_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d87f02211d47497f35bf616f7a0374e inherit pub_attribs_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a5d87f02211d47497f35bf616f7a0374e">VLCChapterDescriptionDuration</a></td></tr>
<tr class="separator:a5d87f02211d47497f35bf616f7a0374e inherit pub_attribs_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4bd07aad0599f2f61cbac7281981df7 inherit pub_attribs_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ac4bd07aad0599f2f61cbac7281981df7">VLCTitleDescriptionDuration</a></td></tr>
<tr class="separator:ac4bd07aad0599f2f61cbac7281981df7 inherit pub_attribs_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1cfbed633aa7783841c153d48088ba70 inherit pub_attribs_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a1cfbed633aa7783841c153d48088ba70">VLCTitleDescriptionIsMenu</a></td></tr>
<tr class="separator:a1cfbed633aa7783841c153d48088ba70 inherit pub_attribs_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="inherit_header properties_interface_v_l_c_media_player"><td colspan="2" onclick="javascript:toggleInherit('properties_interface_v_l_c_media_player')"><img src="closed.png" alt="-"/>&#160;Properties inherited from <a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a></td></tr>
<tr class="memitem:a32b5af405c337b3704957d7c15cbdd61 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a32b5af405c337b3704957d7c15cbdd61">libraryInstance</a></td></tr>
<tr class="separator:a32b5af405c337b3704957d7c15cbdd61 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a138068f5da49d36c7fbd43dabea55666 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">id&lt; <a class="el" href="protocol_v_l_c_media_player_delegate-p.html">VLCMediaPlayerDelegate</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a138068f5da49d36c7fbd43dabea55666">delegate</a></td></tr>
<tr class="separator:a138068f5da49d36c7fbd43dabea55666 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6fa6a9a81e9db3aa58596a1b5b48196 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">id&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#af6fa6a9a81e9db3aa58596a1b5b48196">drawable</a></td></tr>
<tr class="separator:af6fa6a9a81e9db3aa58596a1b5b48196 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ee849792344fed560e4308ebe8e4a76 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a3ee849792344fed560e4308ebe8e4a76">videoAspectRatio</a></td></tr>
<tr class="separator:a3ee849792344fed560e4308ebe8e4a76 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f0fd895e58be570f115ab6f09501ffe inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a3f0fd895e58be570f115ab6f09501ffe">videoCropGeometry</a></td></tr>
<tr class="separator:a3f0fd895e58be570f115ab6f09501ffe inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a773e8e2b5b169fa6cb0bcef37330d327 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a773e8e2b5b169fa6cb0bcef37330d327">scaleFactor</a></td></tr>
<tr class="separator:a773e8e2b5b169fa6cb0bcef37330d327 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7a158c0c906970b991154efab0300bd inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#af7a158c0c906970b991154efab0300bd">adjustFilterEnabled</a></td></tr>
<tr class="separator:af7a158c0c906970b991154efab0300bd inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee995a8531e197917dbad6516594a777 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aee995a8531e197917dbad6516594a777">contrast</a></td></tr>
<tr class="separator:aee995a8531e197917dbad6516594a777 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec3ba8ab1cc0e096e27e26718904d8a7 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aec3ba8ab1cc0e096e27e26718904d8a7">brightness</a></td></tr>
<tr class="separator:aec3ba8ab1cc0e096e27e26718904d8a7 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab276a7f5adfa522cfe25d0a3b637f646 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ab276a7f5adfa522cfe25d0a3b637f646">hue</a></td></tr>
<tr class="separator:ab276a7f5adfa522cfe25d0a3b637f646 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b7d12cf171b798406f128a3f5b54908 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a6b7d12cf171b798406f128a3f5b54908">saturation</a></td></tr>
<tr class="separator:a6b7d12cf171b798406f128a3f5b54908 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acab90fc3b5eef2c26e044df40fd84a61 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#acab90fc3b5eef2c26e044df40fd84a61">gamma</a></td></tr>
<tr class="separator:acab90fc3b5eef2c26e044df40fd84a61 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcfbd421109bce67c3950a8c45b0bbea inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#adcfbd421109bce67c3950a8c45b0bbea">rate</a></td></tr>
<tr class="separator:adcfbd421109bce67c3950a8c45b0bbea inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68098fd773aeae1824545f7490079f3c inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_audio.html">VLCAudio</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a68098fd773aeae1824545f7490079f3c">audio</a></td></tr>
<tr class="separator:a68098fd773aeae1824545f7490079f3c inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0734e2b2d4edebeaf3ca9e1cce85f361 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">CGSize&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a0734e2b2d4edebeaf3ca9e1cce85f361">videoSize</a></td></tr>
<tr class="separator:a0734e2b2d4edebeaf3ca9e1cce85f361 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa39d7bc28b9d74bca2e18e27357576eb inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aa39d7bc28b9d74bca2e18e27357576eb">hasVideoOut</a></td></tr>
<tr class="separator:aa39d7bc28b9d74bca2e18e27357576eb inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5915273012b273885dd9570d56777ccf inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_time.html">VLCTime</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a5915273012b273885dd9570d56777ccf">time</a></td></tr>
<tr class="separator:a5915273012b273885dd9570d56777ccf inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a994615b429c023db77a00d1efec06fd3 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_time.html">VLCTime</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a994615b429c023db77a00d1efec06fd3">remainingTime</a></td></tr>
<tr class="separator:a994615b429c023db77a00d1efec06fd3 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8715f7c45a4389fd57dc6730312ca43e inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a8715f7c45a4389fd57dc6730312ca43e">currentVideoTrackIndex</a></td></tr>
<tr class="separator:a8715f7c45a4389fd57dc6730312ca43e inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa31a3ee365dd1721064f19319bb8026 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aaa31a3ee365dd1721064f19319bb8026">videoTrackNames</a></td></tr>
<tr class="separator:aaa31a3ee365dd1721064f19319bb8026 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f1abde67436f198f0d07b885bd5ac59 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a4f1abde67436f198f0d07b885bd5ac59">videoTrackIndexes</a></td></tr>
<tr class="separator:a4f1abde67436f198f0d07b885bd5ac59 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af142280306f73367c1a3aa748f7233f9 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#af142280306f73367c1a3aa748f7233f9">numberOfVideoTracks</a></td></tr>
<tr class="separator:af142280306f73367c1a3aa748f7233f9 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23063281cc095d506b38f421d974bfe3 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a23063281cc095d506b38f421d974bfe3">currentVideoSubTitleIndex</a></td></tr>
<tr class="separator:a23063281cc095d506b38f421d974bfe3 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaaf3e36c370456bbf30058eedaf7844e inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aaaf3e36c370456bbf30058eedaf7844e">videoSubTitlesNames</a></td></tr>
<tr class="separator:aaaf3e36c370456bbf30058eedaf7844e inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c8a4af83a85f3e8606049aad6f75169 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a1c8a4af83a85f3e8606049aad6f75169">videoSubTitlesIndexes</a></td></tr>
<tr class="separator:a1c8a4af83a85f3e8606049aad6f75169 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6163ca36922d10f0b30a7275545a673 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ae6163ca36922d10f0b30a7275545a673">numberOfSubtitlesTracks</a></td></tr>
<tr class="separator:ae6163ca36922d10f0b30a7275545a673 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8fe537bb090fd54c8f223a8e682b76c8 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a8fe537bb090fd54c8f223a8e682b76c8">currentVideoSubTitleDelay</a></td></tr>
<tr class="separator:a8fe537bb090fd54c8f223a8e682b76c8 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83e7480a6144c4dc4dae14a413fa2ece inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a83e7480a6144c4dc4dae14a413fa2ece">currentChapterIndex</a></td></tr>
<tr class="separator:a83e7480a6144c4dc4dae14a413fa2ece inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefa58a904c759776773a8287225138b3 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aefa58a904c759776773a8287225138b3">currentTitleIndex</a></td></tr>
<tr class="separator:aefa58a904c759776773a8287225138b3 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73e07e681449f1122a4fa1f66d9fc52d inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a73e07e681449f1122a4fa1f66d9fc52d">numberOfTitles</a></td></tr>
<tr class="separator:a73e07e681449f1122a4fa1f66d9fc52d inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2907bb09c29757c5c0f89e5bbe7e7394 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a2907bb09c29757c5c0f89e5bbe7e7394">VLCTitleDescriptionName</a></td></tr>
<tr class="separator:a2907bb09c29757c5c0f89e5bbe7e7394 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a937a50fb274ec99b146d999fd8c02a1b inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a937a50fb274ec99b146d999fd8c02a1b">titleDescriptions</a></td></tr>
<tr class="separator:a937a50fb274ec99b146d999fd8c02a1b inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a93ba313f95351de59e84cdeeea720822 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a93ba313f95351de59e84cdeeea720822">indexOfLongestTitle</a></td></tr>
<tr class="separator:a93ba313f95351de59e84cdeeea720822 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4aedc95307034b1d3a0f8ec51802e7f4 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a4aedc95307034b1d3a0f8ec51802e7f4">currentAudioTrackIndex</a></td></tr>
<tr class="separator:a4aedc95307034b1d3a0f8ec51802e7f4 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adde3f17fc7a88a00221a58bd564120c8 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#adde3f17fc7a88a00221a58bd564120c8">audioTrackNames</a></td></tr>
<tr class="separator:adde3f17fc7a88a00221a58bd564120c8 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae67fa3af66466f93f46bd14c07c60780 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ae67fa3af66466f93f46bd14c07c60780">audioTrackIndexes</a></td></tr>
<tr class="separator:ae67fa3af66466f93f46bd14c07c60780 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a932d093bc73aea01f953a1b96023f401 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a932d093bc73aea01f953a1b96023f401">numberOfAudioTracks</a></td></tr>
<tr class="separator:a932d093bc73aea01f953a1b96023f401 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad29d5abf2c543de5d7f911a8a216480e inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ad29d5abf2c543de5d7f911a8a216480e">audioChannel</a></td></tr>
<tr class="separator:ad29d5abf2c543de5d7f911a8a216480e inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e32c4423cb48d9491591ba55df6cbd6 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a4e32c4423cb48d9491591ba55df6cbd6">currentAudioPlaybackDelay</a></td></tr>
<tr class="separator:a4e32c4423cb48d9491591ba55df6cbd6 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f5763e66e58c4b44045ef098bdb818a inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_media_loudness.html">VLCMediaLoudness</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a5f5763e66e58c4b44045ef098bdb818a">momentaryLoudness</a></td></tr>
<tr class="separator:a5f5763e66e58c4b44045ef098bdb818a inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9889583a2e2b6f6045c1a779a486859d inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a9889583a2e2b6f6045c1a779a486859d">equalizerProfiles</a></td></tr>
<tr class="separator:a9889583a2e2b6f6045c1a779a486859d inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1750c810467d11cb0cf4e835ea9163f3 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a1750c810467d11cb0cf4e835ea9163f3">equalizerEnabled</a></td></tr>
<tr class="separator:a1750c810467d11cb0cf4e835ea9163f3 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1dd4611ad95d596a0d086092ca0c571a inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">CGFloat&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a1dd4611ad95d596a0d086092ca0c571a">preAmplification</a></td></tr>
<tr class="separator:a1dd4611ad95d596a0d086092ca0c571a inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abfda4f0fc83029e50e631d2fbe1c8c48 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">unsigned&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#abfda4f0fc83029e50e631d2fbe1c8c48">numberOfBands</a></td></tr>
<tr class="separator:abfda4f0fc83029e50e631d2fbe1c8c48 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addd666feffd6b2e3ee0c6586f04983d4 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#addd666feffd6b2e3ee0c6586f04983d4">media</a></td></tr>
<tr class="separator:addd666feffd6b2e3ee0c6586f04983d4 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f8af3ae371f616810320e1fb447f6dc inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a5f8af3ae371f616810320e1fb447f6dc">yaw</a></td></tr>
<tr class="separator:a5f8af3ae371f616810320e1fb447f6dc inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7af67ee4b28da45c957bafca617840f inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ae7af67ee4b28da45c957bafca617840f">pitch</a></td></tr>
<tr class="separator:ae7af67ee4b28da45c957bafca617840f inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17e2d158c437a5bffd8da88673b99efc inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a17e2d158c437a5bffd8da88673b99efc">roll</a></td></tr>
<tr class="separator:a17e2d158c437a5bffd8da88673b99efc inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a43185c858bc8767f33a19a9971d34fc4 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a43185c858bc8767f33a19a9971d34fc4">fov</a></td></tr>
<tr class="separator:a43185c858bc8767f33a19a9971d34fc4 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6fa5e39a09fd25c262c9a2ea20e5b9df inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a6fa5e39a09fd25c262c9a2ea20e5b9df">playing</a></td></tr>
<tr class="separator:a6fa5e39a09fd25c262c9a2ea20e5b9df inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa55f09ffe39e021920248ff142ae0f75 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aa55f09ffe39e021920248ff142ae0f75">willPlay</a></td></tr>
<tr class="separator:aa55f09ffe39e021920248ff142ae0f75 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa800575a8facf5db251df3cc88bd44ea inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">VLCMediaPlayerState&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aa800575a8facf5db251df3cc88bd44ea">state</a></td></tr>
<tr class="separator:aa800575a8facf5db251df3cc88bd44ea inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af10549bcee345334f42548cfda9ce51c inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#af10549bcee345334f42548cfda9ce51c">position</a></td></tr>
<tr class="separator:af10549bcee345334f42548cfda9ce51c inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae3f8bc09a07c8b58935d4cd1cf58e69e inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ae3f8bc09a07c8b58935d4cd1cf58e69e">seekable</a></td></tr>
<tr class="separator:ae3f8bc09a07c8b58935d4cd1cf58e69e inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e2ece165a5fb056a2f8737ac1ff2367 inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a1e2ece165a5fb056a2f8737ac1ff2367">canPause</a></td></tr>
<tr class="separator:a1e2ece165a5fb056a2f8737ac1ff2367 inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada4fadb2ae81bd34fce217a34571872a inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ada4fadb2ae81bd34fce217a34571872a">snapshots</a></td></tr>
<tr class="separator:ada4fadb2ae81bd34fce217a34571872a inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0ce7b35bcd4b876e071f73fb558304df inherit properties_interface_v_l_c_media_player"><td class="memItemLeft" align="right" valign="top">NSImage *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a0ce7b35bcd4b876e071f73fb558304df">lastSnapshot</a></td></tr>
<tr class="separator:a0ce7b35bcd4b876e071f73fb558304df inherit properties_interface_v_l_c_media_player"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>control class for streaming sessions </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="afd34666df70b52b43ea64394796f4d52"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afd34666df70b52b43ea64394796f4d52">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[1/6]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_stream_output.html">VLCStreamOutput</a>* streamOutput) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000027">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

<p>Implements <a class="el" href="interface_v_l_c_media_player.html#ab59f33aa946850a6387d87630c2cca16">VLCMediaPlayer</a>.</p>

</div>
</div>
<a id="a8e5017ee1863eb61a649f7d28311fb30"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8e5017ee1863eb61a649f7d28311fb30">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[2/6]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL isComplete) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000028">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

<p>Implements <a class="el" href="interface_v_l_c_media_player.html#ab59f33aa946850a6387d87630c2cca16">VLCMediaPlayer</a>.</p>

</div>
</div>
<a id="a69590b40a62d896b8a9ba88ca2f69ecb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a69590b40a62d896b8a9ba88ca2f69ecb">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[3/6]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSUInteger reattemptedConnections) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000029">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

<p>Implements <a class="el" href="interface_v_l_c_media_player.html#ab59f33aa946850a6387d87630c2cca16">VLCMediaPlayer</a>.</p>

</div>
</div>
<a id="a462740188cf07ef740c1669dcfd34c2b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a462740188cf07ef740c1669dcfd34c2b">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[4/6]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ ((deprecated) __attribute__ </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000026">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a id="a462740188cf07ef740c1669dcfd34c2b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a462740188cf07ef740c1669dcfd34c2b">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[5/6]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- ((deprecated) __attribute__ </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000031">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a id="a462740188cf07ef740c1669dcfd34c2b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a462740188cf07ef740c1669dcfd34c2b">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[6/6]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- ((deprecated) __attribute__ </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000030">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_stream_session_8h_source.html">VLCStreamSession.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
