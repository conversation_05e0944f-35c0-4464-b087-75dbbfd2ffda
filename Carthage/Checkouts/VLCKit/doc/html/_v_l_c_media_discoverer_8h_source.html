<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Headers/Public/VLCMediaDiscoverer.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html">Headers</a></li><li class="navelem"><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html">Public</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCMediaDiscoverer.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/*****************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * VLCMediaDiscoverer.h: VLCKit.framework VLCMediaDiscoverer header</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *****************************************************************************</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Copyright (C) 2007 Pierre d&#39;Herbemont</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * Copyright (C) 2015 Felix Paul Kühne</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> * Copyright (C) 2007, 2015 VLC authors and VideoLAN</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> * $Id$</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> * Authors: <AUTHORS>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *          Felix Paul Kühne &lt;fkuehne # videolan.org&gt;</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> * This program is free software; you can redistribute it and/or modify it</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> * under the terms of the GNU Lesser General Public License as published by</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> * the Free Software Foundation; either version 2.1 of the License, or</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * (at your option) any later version.</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * This program is distributed in the hope that it will be useful,</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * but WITHOUT ANY WARRANTY; without even the implied warranty of</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * GNU Lesser General Public License for more details.</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * You should have received a copy of the GNU Lesser General Public License</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> * along with this program; if not, write to the Free Software Foundation,</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="comment"> *****************************************************************************/</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160; </div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#import &lt;Foundation/Foundation.h&gt;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#import &quot;VLCMediaList.h&quot;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160; </div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_library.html">VLCLibrary</a>;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media_list.html">VLCMediaList</a>;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media_discoverer.html">VLCMediaDiscoverer</a>;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160; </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160; </div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="keyword">typedef</span> <a class="code" href="interface_v_l_c_media.html#af4439eb3074ae064da27365b68ddbfc8">NS_ENUM</a>(<span class="keywordtype">unsigned</span>, VLCMediaDiscovererCategoryType)</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;{</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;    VLCMediaDiscovererCategoryTypeDevices = 0,</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;    VLCMediaDiscovererCategoryTypeLAN,</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;    VLCMediaDiscovererCategoryTypePodcasts,</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;    VLCMediaDiscovererCategoryTypeLocalDirectories</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;};</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160; </div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="comment">/* discoverer keys */</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMediaDiscovererName;</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMediaDiscovererLongName;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMediaDiscovererCategory;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160; </div>
<div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_discoverer.html">   52</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_media_discoverer.html">VLCMediaDiscoverer</a> : NSObject</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160; </div>
<div class="line"><a name="l00058"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_discoverer.html#aedebd3f4b61febe5ca436af1f1ea7508">   58</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly) <a class="code" href="interface_v_l_c_library.html">VLCLibrary</a> *<a class="code" href="interface_v_l_c_media_discoverer.html#a7c84bb252d3183d9314b447a16a68b06">libraryInstance</a>;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160; </div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;+ (NSArray *)availableMediaDiscoverer <a class="code" href="interface_v_l_c_media_discoverer.html#a57b40dcdb0b650aea2feed95fdd30d83">__attribute__</a>((deprecated));</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160; </div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;+ (NSArray *)availableMediaDiscovererForCategoryType:(VLCMediaDiscovererCategoryType)categoryType;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160; </div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="comment">/* Initializers */</span></div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;- (instancetype)initWithName:(NSString *)aServiceName;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160; </div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;- (instancetype)initWithName:(NSString *)aServiceName <a class="code" href="interface_v_l_c_media_discoverer.html#a7c84bb252d3183d9314b447a16a68b06">libraryInstance</a>:(<a class="code" href="interface_v_l_c_library.html">VLCLibrary</a> *)<a class="code" href="interface_v_l_c_media_discoverer.html#a7c84bb252d3183d9314b447a16a68b06">libraryInstance</a>;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160; </div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;- (int)<a class="code" href="interface_v_l_c_media_discoverer.html#ae24d8b9250ccead549c829071f7cf184">startDiscoverer</a>;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160; </div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;- (void)<a class="code" href="interface_v_l_c_media_discoverer.html#aedebd3f4b61febe5ca436af1f1ea7508">stopDiscoverer</a>;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160; </div>
<div class="line"><a name="l00102"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_discoverer.html#ace04441c5b6e7b968772a5f9c3da6ce9">  102</a></span>&#160;<span class="keyword">@property</span> (weak, readonly) <a class="code" href="interface_v_l_c_media_list.html">VLCMediaList</a> *<a class="code" href="interface_v_l_c_media_discoverer.html#ace04441c5b6e7b968772a5f9c3da6ce9">discoveredMedia</a>;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160; </div>
<div class="line"><a name="l00108"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_discoverer.html#abefec909660b69a00410cab13c2d9a5b">  108</a></span>&#160;<span class="keyword">@property</span> (readonly, copy) NSString *localizedName <a class="code" href="interface_v_l_c_media_discoverer.html#abefec909660b69a00410cab13c2d9a5b">__attribute__</a>((deprecated));</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160; </div>
<div class="line"><a name="l00114"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_discoverer.html#abbd18cde0dc5c6982ec6a5c356fe9f7e">  114</a></span>&#160;<span class="keyword">@property</span> (readonly) BOOL <a class="code" href="interface_v_l_c_media_discoverer.html#abbd18cde0dc5c6982ec6a5c356fe9f7e">isRunning</a>;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="keyword">@end</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="ainterface_v_l_c_media_html_af4439eb3074ae064da27365b68ddbfc8"><div class="ttname"><a href="interface_v_l_c_media.html#af4439eb3074ae064da27365b68ddbfc8">-[VLCMedia NS_ENUM]</a></div><div class="ttdeci">typedef NS_ENUM(NSUInteger, VLCMediaOrientation)</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:186</div></div>
<div class="ttc" id="ainterface_v_l_c_media_discoverer_html_a7c84bb252d3183d9314b447a16a68b06"><div class="ttname"><a href="interface_v_l_c_media_discoverer.html#a7c84bb252d3183d9314b447a16a68b06">VLCMediaDiscoverer::libraryInstance</a></div><div class="ttdeci">VLCLibrary * libraryInstance</div><div class="ttdef"><b>Definition:</b> VLCMediaDiscoverer.h:58</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_html"><div class="ttname"><a href="interface_v_l_c_media_list.html">VLCMediaList</a></div><div class="ttdef"><b>Definition:</b> VLCMediaList.h:68</div></div>
<div class="ttc" id="ainterface_v_l_c_media_discoverer_html_aedebd3f4b61febe5ca436af1f1ea7508"><div class="ttname"><a href="interface_v_l_c_media_discoverer.html#aedebd3f4b61febe5ca436af1f1ea7508">-[VLCMediaDiscoverer stopDiscoverer]</a></div><div class="ttdeci">void stopDiscoverer()</div></div>
<div class="ttc" id="ainterface_v_l_c_media_discoverer_html_abbd18cde0dc5c6982ec6a5c356fe9f7e"><div class="ttname"><a href="interface_v_l_c_media_discoverer.html#abbd18cde0dc5c6982ec6a5c356fe9f7e">VLCMediaDiscoverer::isRunning</a></div><div class="ttdeci">BOOL isRunning</div><div class="ttdef"><b>Definition:</b> VLCMediaDiscoverer.h:114</div></div>
<div class="ttc" id="ainterface_v_l_c_media_discoverer_html"><div class="ttname"><a href="interface_v_l_c_media_discoverer.html">VLCMediaDiscoverer</a></div><div class="ttdef"><b>Definition:</b> VLCMediaDiscoverer.h:53</div></div>
<div class="ttc" id="ainterface_v_l_c_media_discoverer_html_a57b40dcdb0b650aea2feed95fdd30d83"><div class="ttname"><a href="interface_v_l_c_media_discoverer.html#a57b40dcdb0b650aea2feed95fdd30d83">+[VLCMediaDiscoverer __attribute__]</a></div><div class="ttdeci">(deprecated __attribute__()</div></div>
<div class="ttc" id="ainterface_v_l_c_library_html"><div class="ttname"><a href="interface_v_l_c_library.html">VLCLibrary</a></div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:47</div></div>
<div class="ttc" id="ainterface_v_l_c_media_discoverer_html_abefec909660b69a00410cab13c2d9a5b"><div class="ttname"><a href="interface_v_l_c_media_discoverer.html#abefec909660b69a00410cab13c2d9a5b">-[VLCMediaDiscoverer __attribute__]</a></div><div class="ttdeci">NSString *localizedName __attribute__((deprecated))</div></div>
<div class="ttc" id="ainterface_v_l_c_media_discoverer_html_ace04441c5b6e7b968772a5f9c3da6ce9"><div class="ttname"><a href="interface_v_l_c_media_discoverer.html#ace04441c5b6e7b968772a5f9c3da6ce9">VLCMediaDiscoverer::discoveredMedia</a></div><div class="ttdeci">VLCMediaList * discoveredMedia</div><div class="ttdef"><b>Definition:</b> VLCMediaDiscoverer.h:102</div></div>
<div class="ttc" id="ainterface_v_l_c_media_discoverer_html_ae24d8b9250ccead549c829071f7cf184"><div class="ttname"><a href="interface_v_l_c_media_discoverer.html#ae24d8b9250ccead549c829071f7cf184">-[VLCMediaDiscoverer startDiscoverer]</a></div><div class="ttdeci">int startDiscoverer()</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
