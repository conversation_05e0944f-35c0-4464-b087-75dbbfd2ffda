<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_m"></a>- m -</h3><ul>
<li>media
: <a class="el" href="interface_v_l_c_media_player.html#addd666feffd6b2e3ee0c6586f04983d4">VLCMediaPlayer</a>
, <a class="el" href="interface_v_l_c_media_thumbnailer.html#aecc4d4aeea24bd960190a0d1a47a5a56">VLCMediaThumbnailer</a>
</li>
<li>mediaAsNodeWithName:
: <a class="el" href="interface_v_l_c_media.html#a3aaff98fc9546ceaf4c1871577c00a17">VLCMedia</a>
</li>
<li>mediaAtIndex:
: <a class="el" href="interface_v_l_c_media_list.html#af07ba2e377bd890b7b12c61ac34a9e5c">VLCMediaList</a>
</li>
<li>mediaDidFinishParsing:
: <a class="el" href="protocol_v_l_c_media_delegate-p.html#a90e4a5b0112fcaeda0338827cacd060f">&lt;VLCMediaDelegate&gt;</a>
</li>
<li>mediaList
: <a class="el" href="interface_v_l_c_media_list_player.html#a9a58ef35e80015f0441ae1861856a2bf">VLCMediaListPlayer</a>
</li>
<li>mediaList:mediaAdded:atIndex:
: <a class="el" href="protocol_v_l_c_media_list_delegate-p.html#a79ca6fefea1fca488d4f9fe5ffa1cfe0">&lt;VLCMediaListDelegate&gt;</a>
</li>
<li>mediaList:mediaRemovedAtIndex:
: <a class="el" href="protocol_v_l_c_media_list_delegate-p.html#a22672bbcaf4fc015d28e0b85303b02be">&lt;VLCMediaListDelegate&gt;</a>
</li>
<li>mediaListPlayer:nextMedia:
: <a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html#ab86eca2f5040184a50cb8f74f1ca3845">&lt;VLCMediaListPlayerDelegate&gt;</a>
</li>
<li>mediaListPlayerFinishedPlayback:
: <a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html#afe3046f44ecb7cdde5d5659d543365a8">&lt;VLCMediaListPlayerDelegate&gt;</a>
</li>
<li>mediaListPlayerStopped:
: <a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html#a22d8350c9779fb7bcdf30cc1076a7073">&lt;VLCMediaListPlayerDelegate&gt;</a>
</li>
<li>mediaMetaDataDidChange:
: <a class="el" href="protocol_v_l_c_media_delegate-p.html#acf9db32d94f11f07d9660587d4b8b0b5">&lt;VLCMediaDelegate&gt;</a>
</li>
<li>mediaPlayer
: <a class="el" href="interface_v_l_c_media_list_player.html#a77a18025616fa5fa2d06a539f6eea9f3">VLCMediaListPlayer</a>
</li>
<li>mediaPlayer:recordingStoppedAtPath:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#ae9712ac043c045a0d7eaee79281d6eef">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerChapterChanged:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#aa0cba7a31646e3b6dbb7b994776eaf4b">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerLoudnessChanged:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#a20baffdd00732f78f8b1ddf4689983cc">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerSnapshot:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#ac975d4b4100985c55cf5765fef978c29">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerStartedRecording:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#a3c1f8b9adb12e923abc5686e0dd7f5f8">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerStateChanged:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#afd15a9281d2a0d4c03b1b67ff0041ba8">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerTimeChanged:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#a5c1d64150f362598808f444b66046f7a">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerTitleChanged:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#a857838e3a322f96cf60fa94dc905e8d5">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaSizeSuitableForDevice
: <a class="el" href="interface_v_l_c_media.html#a2018328bfcb5934f725b285026fe4e98">VLCMedia</a>
</li>
<li>mediaThumbnailer:didFinishThumbnail:
: <a class="el" href="protocol_v_l_c_media_thumbnailer_delegate-p.html#ae28138f261878ef3dd0f503193f939a0">&lt;VLCMediaThumbnailerDelegate&gt;</a>
</li>
<li>mediaThumbnailerDidTimeOut:
: <a class="el" href="protocol_v_l_c_media_thumbnailer_delegate-p.html#acf2f91a0581735994a84a4efdd91fad0">&lt;VLCMediaThumbnailerDelegate&gt;</a>
</li>
<li>mediaType
: <a class="el" href="interface_v_l_c_media.html#a8f6e78f8cc5d52384047ddaea9e01dcf">VLCMedia</a>
</li>
<li>mediaWithPath:
: <a class="el" href="interface_v_l_c_media.html#ae65d970c9c066ab28ec0c8bdcc076101">VLCMedia</a>
</li>
<li>mediaWithURL:
: <a class="el" href="interface_v_l_c_media.html#a38e5fb8f18d50b6de684a7e56c1611fa">VLCMedia</a>
</li>
<li>mediumJumpBackward
: <a class="el" href="interface_v_l_c_media_player.html#ae010feb668404c839ca683ecef6ee7a2">VLCMediaPlayer</a>
</li>
<li>mediumJumpForward
: <a class="el" href="interface_v_l_c_media_player.html#ae87c31a4ebc3274b2b275bc5ac42f24c">VLCMediaPlayer</a>
</li>
<li>metadataForKey:
: <a class="el" href="interface_v_l_c_media.html#af495ec3452fdcdcd89aa2a13695ba6dd">VLCMedia</a>
</li>
<li>metaDictionary
: <a class="el" href="interface_v_l_c_media.html#ac61f729efe7481e86d26e7e92fff0dd2">VLCMedia</a>
</li>
<li>minuteStringValue
: <a class="el" href="interface_v_l_c_time.html#aa6d4bce56be3df94a0896763f1769921">VLCTime</a>
</li>
<li>momentaryLoudness
: <a class="el" href="interface_v_l_c_media_player.html#a5f5763e66e58c4b44045ef098bdb818a">VLCMediaPlayer</a>
</li>
<li>mpeg2StreamOutputWithFilePath:
: <a class="el" href="interface_v_l_c_stream_output.html#a1c1586457352350d81adbe686729eb92">VLCStreamOutput</a>
</li>
<li>mpeg4StreamOutputWithFilePath:
: <a class="el" href="interface_v_l_c_stream_output.html#a5c5ec3624e9cc31e1de8630e42430c40">VLCStreamOutput</a>
</li>
<li>muted
: <a class="el" href="interface_v_l_c_audio.html#aa2739a3d1ec35d69a9920e4a9588ef8c">VLCAudio</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
