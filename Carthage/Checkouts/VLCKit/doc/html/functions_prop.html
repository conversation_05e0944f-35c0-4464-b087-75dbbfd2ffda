<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Members - Properties</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index_a"></a>- a -</h3><ul>
<li>adjustFilterEnabled
: <a class="el" href="interface_v_l_c_media_player.html#af7a158c0c906970b991154efab0300bd">VLCMediaPlayer</a>
</li>
<li>audio
: <a class="el" href="interface_v_l_c_media_player.html#a68098fd773aeae1824545f7490079f3c">VLCMediaPlayer</a>
</li>
<li>audioChannel
: <a class="el" href="interface_v_l_c_media_player.html#ad29d5abf2c543de5d7f911a8a216480e">VLCMediaPlayer</a>
</li>
<li>audioTrackIndexes
: <a class="el" href="interface_v_l_c_media_player.html#ae67fa3af66466f93f46bd14c07c60780">VLCMediaPlayer</a>
</li>
<li>audioTrackNames
: <a class="el" href="interface_v_l_c_media_player.html#adde3f17fc7a88a00221a58bd564120c8">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_b"></a>- b -</h3><ul>
<li>backColor
: <a class="el" href="interface_v_l_c_video_view.html#a3a12c7c5dc64b90fc469e435e86c2ef4">VLCVideoView</a>
</li>
<li>brightness
: <a class="el" href="interface_v_l_c_media_player.html#aec3ba8ab1cc0e096e27e26718904d8a7">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_c"></a>- c -</h3><ul>
<li>canPause
: <a class="el" href="interface_v_l_c_media_player.html#a1e2ece165a5fb056a2f8737ac1ff2367">VLCMediaPlayer</a>
</li>
<li>changeset
: <a class="el" href="interface_v_l_c_library.html#a9201c5f4b1ee745e31e3875835914124">VLCLibrary</a>
</li>
<li>compiler
: <a class="el" href="interface_v_l_c_library.html#a91485a448eb7b18c7b5c36bba4c56184">VLCLibrary</a>
</li>
<li>contrast
: <a class="el" href="interface_v_l_c_media_player.html#aee995a8531e197917dbad6516594a777">VLCMediaPlayer</a>
</li>
<li>count
: <a class="el" href="interface_v_l_c_media_list.html#aa91841c1b03e5a4640ef1bed6c621b44">VLCMediaList</a>
</li>
<li>currentAudioPlaybackDelay
: <a class="el" href="interface_v_l_c_media_player.html#a4e32c4423cb48d9491591ba55df6cbd6">VLCMediaPlayer</a>
</li>
<li>currentAudioTrackIndex
: <a class="el" href="interface_v_l_c_media_player.html#a4aedc95307034b1d3a0f8ec51802e7f4">VLCMediaPlayer</a>
</li>
<li>currentChapterIndex
: <a class="el" href="interface_v_l_c_media_player.html#a83e7480a6144c4dc4dae14a413fa2ece">VLCMediaPlayer</a>
</li>
<li>currentTitleIndex
: <a class="el" href="interface_v_l_c_media_player.html#aefa58a904c759776773a8287225138b3">VLCMediaPlayer</a>
</li>
<li>currentVideoSubTitleDelay
: <a class="el" href="interface_v_l_c_media_player.html#a8fe537bb090fd54c8f223a8e682b76c8">VLCMediaPlayer</a>
</li>
<li>currentVideoSubTitleIndex
: <a class="el" href="interface_v_l_c_media_player.html#a23063281cc095d506b38f421d974bfe3">VLCMediaPlayer</a>
</li>
<li>currentVideoTrackIndex
: <a class="el" href="interface_v_l_c_media_player.html#a8715f7c45a4389fd57dc6730312ca43e">VLCMediaPlayer</a>
</li>
<li>customRenderer
: <a class="el" href="interface_v_l_c_dialog_provider.html#a15847411703d3b120abb0a9b4b7e8ed2">VLCDialogProvider</a>
</li>
</ul>


<h3><a id="index_d"></a>- d -</h3><ul>
<li>date
: <a class="el" href="interface_v_l_c_media_loudness.html#af2b5b96b54bc4b5f802ba1f8fbe2de43">VLCMediaLoudness</a>
</li>
<li>debugLogging
: <a class="el" href="interface_v_l_c_library.html#a30523c86de4b6a2fbc86f1d6a8274b59">VLCLibrary</a>
</li>
<li>debugLoggingLevel
: <a class="el" href="interface_v_l_c_library.html#a0b0d7e806f3c1c49243873f99c3608fa">VLCLibrary</a>
</li>
<li>debugLoggingTarget
: <a class="el" href="interface_v_l_c_library.html#a7194d31353ddeef7273dd9d9c394841a">VLCLibrary</a>
</li>
<li>delegate
: <a class="el" href="interface_v_l_c_media.html#a2cb849f8dceb22cebbac149921c785a5">VLCMedia</a>
, <a class="el" href="interface_v_l_c_media_list.html#aa5d8f85f2a5e5492177de14912028f63">VLCMediaList</a>
, <a class="el" href="interface_v_l_c_media_list_player.html#a122c940f85ebdc82eae8d029d106b512">VLCMediaListPlayer</a>
, <a class="el" href="interface_v_l_c_media_player.html#a138068f5da49d36c7fbd43dabea55666">VLCMediaPlayer</a>
, <a class="el" href="interface_v_l_c_media_thumbnailer.html#a48ad5cbc29377c223ed35eb92097d4ac">VLCMediaThumbnailer</a>
, <a class="el" href="interface_v_l_c_renderer_discoverer.html#a06b6ab494062dd31592e2bff75132fe1">VLCRendererDiscoverer</a>
, <a class="el" href="interface_v_l_c_transcoder.html#a66e9bdba467fe831193fc8633b11dfe2">VLCTranscoder</a>
</li>
<li>demuxBitrate
: <a class="el" href="interface_v_l_c_media.html#a1e060d1cb138c0e0ecffe53d985b2dd3">VLCMedia</a>
</li>
<li>discoveredMedia
: <a class="el" href="interface_v_l_c_media_discoverer.html#ace04441c5b6e7b968772a5f9c3da6ce9">VLCMediaDiscoverer</a>
</li>
<li>drawable
: <a class="el" href="interface_v_l_c_media_player.html#af6fa6a9a81e9db3aa58596a1b5b48196">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_e"></a>- e -</h3><ul>
<li>equalizerEnabled
: <a class="el" href="interface_v_l_c_media_player.html#a1750c810467d11cb0cf4e835ea9163f3">VLCMediaPlayer</a>
</li>
<li>equalizerProfiles
: <a class="el" href="interface_v_l_c_media_player.html#a9889583a2e2b6f6045c1a779a486859d">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_f"></a>- f -</h3><ul>
<li>fillScreen
: <a class="el" href="interface_v_l_c_video_layer.html#a01309499bf16baf74750e563db39d65d">VLCVideoLayer</a>
, <a class="el" href="interface_v_l_c_video_view.html#ac43fc86fcef23d9e87bcb84c477d68da">VLCVideoView</a>
</li>
<li>flags
: <a class="el" href="interface_v_l_c_renderer_item.html#a9f68e1e3618c4e521a3e52478672a36d">VLCRendererItem</a>
</li>
<li>fov
: <a class="el" href="interface_v_l_c_media_player.html#a43185c858bc8767f33a19a9971d34fc4">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_g"></a>- g -</h3><ul>
<li>gamma
: <a class="el" href="interface_v_l_c_media_player.html#acab90fc3b5eef2c26e044df40fd84a61">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_h"></a>- h -</h3><ul>
<li>hasVideo
: <a class="el" href="interface_v_l_c_video_layer.html#abcb91eb83d1612d155a3f440997e59ea">VLCVideoLayer</a>
, <a class="el" href="interface_v_l_c_video_view.html#a83cc53903dedd9770bb0a1fb68235db2">VLCVideoView</a>
</li>
<li>hasVideoOut
: <a class="el" href="interface_v_l_c_media_player.html#aa39d7bc28b9d74bca2e18e27357576eb">VLCMediaPlayer</a>
</li>
<li>hue
: <a class="el" href="interface_v_l_c_media_player.html#ab276a7f5adfa522cfe25d0a3b637f646">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_i"></a>- i -</h3><ul>
<li>iconURI
: <a class="el" href="interface_v_l_c_renderer_item.html#a2fd800be1553921e947962d62a767601">VLCRendererItem</a>
</li>
<li>indexOfLongestTitle
: <a class="el" href="interface_v_l_c_media_player.html#a93ba313f95351de59e84cdeeea720822">VLCMediaPlayer</a>
</li>
<li>inputBitrate
: <a class="el" href="interface_v_l_c_media.html#a2eb646a3d37eaec7de62ba174b9682f7">VLCMedia</a>
</li>
<li>instance
: <a class="el" href="interface_v_l_c_library.html#abb375a92ef38f3cacab3d1d501b747d3">VLCLibrary</a>
</li>
<li>intValue
: <a class="el" href="interface_v_l_c_time.html#aff49484cae12d9fbf65ce6b4a7da2cfd">VLCTime</a>
</li>
<li>isReadOnly
: <a class="el" href="interface_v_l_c_media_list.html#ad9f5b01b188f801ba9a2d675d6d94962">VLCMediaList</a>
</li>
<li>isRunning
: <a class="el" href="interface_v_l_c_media_discoverer.html#abbd18cde0dc5c6982ec6a5c356fe9f7e">VLCMediaDiscoverer</a>
</li>
</ul>


<h3><a id="index_l"></a>- l -</h3><ul>
<li>lastSnapshot
: <a class="el" href="interface_v_l_c_media_player.html#a0ce7b35bcd4b876e071f73fb558304df">VLCMediaPlayer</a>
</li>
<li>length
: <a class="el" href="interface_v_l_c_media.html#afd47b541ffd9e93a5864ced1f127101d">VLCMedia</a>
</li>
<li>libraryInstance
: <a class="el" href="interface_v_l_c_media_discoverer.html#a7c84bb252d3183d9314b447a16a68b06">VLCMediaDiscoverer</a>
, <a class="el" href="interface_v_l_c_media_player.html#a32b5af405c337b3704957d7c15cbdd61">VLCMediaPlayer</a>
</li>
<li>libVLCinstance
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#a14051a6f88fd60fb723c6de3aa5a7321">VLCMediaThumbnailer</a>
</li>
<li>longName
: <a class="el" href="interface_v_l_c_renderer_discoverer_description.html#a164296731eab83b21084576ded002861">VLCRendererDiscovererDescription</a>
</li>
<li>loudnessValue
: <a class="el" href="interface_v_l_c_media_loudness.html#a70a3e717400017a173498a9c802cc70f">VLCMediaLoudness</a>
</li>
</ul>


<h3><a id="index_m"></a>- m -</h3><ul>
<li>media
: <a class="el" href="interface_v_l_c_media_player.html#addd666feffd6b2e3ee0c6586f04983d4">VLCMediaPlayer</a>
, <a class="el" href="interface_v_l_c_media_thumbnailer.html#aecc4d4aeea24bd960190a0d1a47a5a56">VLCMediaThumbnailer</a>
</li>
<li>mediaList
: <a class="el" href="interface_v_l_c_media_list_player.html#a9a58ef35e80015f0441ae1861856a2bf">VLCMediaListPlayer</a>
</li>
<li>mediaPlayer
: <a class="el" href="interface_v_l_c_media_list_player.html#a77a18025616fa5fa2d06a539f6eea9f3">VLCMediaListPlayer</a>
</li>
<li>mediaSizeSuitableForDevice
: <a class="el" href="interface_v_l_c_media.html#a2018328bfcb5934f725b285026fe4e98">VLCMedia</a>
</li>
<li>mediaType
: <a class="el" href="interface_v_l_c_media.html#a8f6e78f8cc5d52384047ddaea9e01dcf">VLCMedia</a>
</li>
<li>metaDictionary
: <a class="el" href="interface_v_l_c_media.html#ac61f729efe7481e86d26e7e92fff0dd2">VLCMedia</a>
</li>
<li>minuteStringValue
: <a class="el" href="interface_v_l_c_time.html#aa6d4bce56be3df94a0896763f1769921">VLCTime</a>
</li>
<li>momentaryLoudness
: <a class="el" href="interface_v_l_c_media_player.html#a5f5763e66e58c4b44045ef098bdb818a">VLCMediaPlayer</a>
</li>
<li>muted
: <a class="el" href="interface_v_l_c_audio.html#aa2739a3d1ec35d69a9920e4a9588ef8c">VLCAudio</a>
</li>
</ul>


<h3><a id="index_n"></a>- n -</h3><ul>
<li>name
: <a class="el" href="interface_v_l_c_renderer_discoverer.html#a831fc60a7a5ea8bc1114bc36cb051f66">VLCRendererDiscoverer</a>
, <a class="el" href="interface_v_l_c_renderer_discoverer_description.html#af6af459a1c5ed50d53695149a2b49a6c">VLCRendererDiscovererDescription</a>
, <a class="el" href="interface_v_l_c_renderer_item.html#a5dcd15900cdcd7901c2e9a9cc6820831">VLCRendererItem</a>
</li>
<li>next
: <a class="el" href="interface_v_l_c_media_list_player.html#a9443120446a79cd785b1e2b71882f199">VLCMediaListPlayer</a>
</li>
<li>numberOfAudioTracks
: <a class="el" href="interface_v_l_c_media_player.html#a932d093bc73aea01f953a1b96023f401">VLCMediaPlayer</a>
</li>
<li>numberOfBands
: <a class="el" href="interface_v_l_c_media_player.html#abfda4f0fc83029e50e631d2fbe1c8c48">VLCMediaPlayer</a>
</li>
<li>numberOfCorruptedDataPackets
: <a class="el" href="interface_v_l_c_media.html#a289dc0ff117c7013b6b5363d9f35fd01">VLCMedia</a>
</li>
<li>numberOfDecodedAudioBlocks
: <a class="el" href="interface_v_l_c_media.html#ae5f6aa8f4cfd924c9f31cea1292739de">VLCMedia</a>
</li>
<li>numberOfDecodedVideoBlocks
: <a class="el" href="interface_v_l_c_media.html#a0d65e705f516777543cb6ac2df310779">VLCMedia</a>
</li>
<li>numberOfDiscontinuties
: <a class="el" href="interface_v_l_c_media.html#a148f7e15ef691ed0c6cf631eb3bc34d8">VLCMedia</a>
</li>
<li>numberOfDisplayedPictures
: <a class="el" href="interface_v_l_c_media.html#a42f0be6a3830572833122e758ddaafb1">VLCMedia</a>
</li>
<li>numberOfLostAudioBuffers
: <a class="el" href="interface_v_l_c_media.html#a13d927d07a8bc2cebab7363317c0a932">VLCMedia</a>
</li>
<li>numberOfLostPictures
: <a class="el" href="interface_v_l_c_media.html#ab7456ceac9f4ac4b395bcc50064d58dd">VLCMedia</a>
</li>
<li>numberOfPlayedAudioBuffers
: <a class="el" href="interface_v_l_c_media.html#a958ceff6c2c01085c9c11963fc00e9ab">VLCMedia</a>
</li>
<li>numberOfReadBytesOnDemux
: <a class="el" href="interface_v_l_c_media.html#a936f14e9dbdb6355604040bb963cf1b2">VLCMedia</a>
</li>
<li>numberOfReadBytesOnInput
: <a class="el" href="interface_v_l_c_media.html#aadc4d2257ae507913c39611e9c935665">VLCMedia</a>
</li>
<li>numberOfSentBytes
: <a class="el" href="interface_v_l_c_media.html#a4f663bcbd8cfea3c1fa23035a5e2e119">VLCMedia</a>
</li>
<li>numberOfSentPackets
: <a class="el" href="interface_v_l_c_media.html#a65060dbc9eefe3518c4aa81daba05320">VLCMedia</a>
</li>
<li>numberOfSubtitlesTracks
: <a class="el" href="interface_v_l_c_media_player.html#ae6163ca36922d10f0b30a7275545a673">VLCMediaPlayer</a>
</li>
<li>numberOfTitles
: <a class="el" href="interface_v_l_c_media_player.html#a73e07e681449f1122a4fa1f66d9fc52d">VLCMediaPlayer</a>
</li>
<li>numberOfVideoTracks
: <a class="el" href="interface_v_l_c_media_player.html#af142280306f73367c1a3aa748f7233f9">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_p"></a>- p -</h3><ul>
<li>parsedStatus
: <a class="el" href="interface_v_l_c_media.html#adc94b1c776ed671be57746c79e04f187">VLCMedia</a>
</li>
<li>passthrough
: <a class="el" href="interface_v_l_c_audio.html#ac37223907edb0cafcee6f609edc28782">VLCAudio</a>
</li>
<li>pitch
: <a class="el" href="interface_v_l_c_media_player.html#ae7af67ee4b28da45c957bafca617840f">VLCMediaPlayer</a>
</li>
<li>playing
: <a class="el" href="interface_v_l_c_media_player.html#a6fa5e39a09fd25c262c9a2ea20e5b9df">VLCMediaPlayer</a>
</li>
<li>position
: <a class="el" href="interface_v_l_c_media_player.html#af10549bcee345334f42548cfda9ce51c">VLCMediaPlayer</a>
</li>
<li>preAmplification
: <a class="el" href="interface_v_l_c_media_player.html#a1dd4611ad95d596a0d086092ca0c571a">VLCMediaPlayer</a>
</li>
<li>previous
: <a class="el" href="interface_v_l_c_media_list_player.html#afae8b92f265eeec5b87bd55bd0fd104c">VLCMediaListPlayer</a>
</li>
</ul>


<h3><a id="index_r"></a>- r -</h3><ul>
<li>rate
: <a class="el" href="interface_v_l_c_media_player.html#adcfbd421109bce67c3950a8c45b0bbea">VLCMediaPlayer</a>
</li>
<li>remainingTime
: <a class="el" href="interface_v_l_c_media_player.html#a994615b429c023db77a00d1efec06fd3">VLCMediaPlayer</a>
</li>
<li>renderers
: <a class="el" href="interface_v_l_c_renderer_discoverer.html#ad1782fa86584819376e5686797b765ee">VLCRendererDiscoverer</a>
</li>
<li>repeatMode
: <a class="el" href="interface_v_l_c_media_list_player.html#a7f868c26a93d999852c3448d28913af4">VLCMediaListPlayer</a>
</li>
<li>roll
: <a class="el" href="interface_v_l_c_media_player.html#a17e2d158c437a5bffd8da88673b99efc">VLCMediaPlayer</a>
</li>
<li>rootMedia
: <a class="el" href="interface_v_l_c_media_list_player.html#ac1a1b1d304efb4e1c62026f9ed321e9d">VLCMediaListPlayer</a>
</li>
</ul>


<h3><a id="index_s"></a>- s -</h3><ul>
<li>saturation
: <a class="el" href="interface_v_l_c_media_player.html#a6b7d12cf171b798406f128a3f5b54908">VLCMediaPlayer</a>
</li>
<li>saveMetadata
: <a class="el" href="interface_v_l_c_media.html#a2332173d72093469abebf56f4c70ae80">VLCMedia</a>
</li>
<li>scaleFactor
: <a class="el" href="interface_v_l_c_media_player.html#a773e8e2b5b169fa6cb0bcef37330d327">VLCMediaPlayer</a>
</li>
<li>seekable
: <a class="el" href="interface_v_l_c_media_player.html#ae3f8bc09a07c8b58935d4cd1cf58e69e">VLCMediaPlayer</a>
</li>
<li>snapshotPosition
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#ab6c36054a654a28cc678082d0a1e02fa">VLCMediaThumbnailer</a>
</li>
<li>snapshots
: <a class="el" href="interface_v_l_c_media_player.html#ada4fadb2ae81bd34fce217a34571872a">VLCMediaPlayer</a>
</li>
<li>state
: <a class="el" href="interface_v_l_c_media.html#af247fea93ce48e219ddef15bdaf256de">VLCMedia</a>
, <a class="el" href="interface_v_l_c_media_player.html#aa800575a8facf5db251df3cc88bd44ea">VLCMediaPlayer</a>
</li>
<li>stats
: <a class="el" href="interface_v_l_c_media.html#a9c2d85f9c2dba700d7b2ca18cf12049a">VLCMedia</a>
</li>
<li>streamOutputBitrate
: <a class="el" href="interface_v_l_c_media.html#afd944ae42af805d532f4ab36d5b0fe7d">VLCMedia</a>
</li>
<li>stringValue
: <a class="el" href="interface_v_l_c_time.html#a7aa23f16fef10097af66ac4d9754d0dc">VLCTime</a>
</li>
<li>subitems
: <a class="el" href="interface_v_l_c_media.html#a08f3d51d9b8199fd20143d178b368b2f">VLCMedia</a>
</li>
</ul>


<h3><a id="index_t"></a>- t -</h3><ul>
<li>thumbnail
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#a2cc0a6c7836be7d967858c67e51a5f50">VLCMediaThumbnailer</a>
</li>
<li>thumbnailHeight
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#ad183c5b8990465157f049c61c2e188a0">VLCMediaThumbnailer</a>
</li>
<li>thumbnailWidth
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#a8c71e4fc706ae741252eecb95d33a055">VLCMediaThumbnailer</a>
</li>
<li>time
: <a class="el" href="interface_v_l_c_media_player.html#a5915273012b273885dd9570d56777ccf">VLCMediaPlayer</a>
</li>
<li>titleDescriptions
: <a class="el" href="interface_v_l_c_media_player.html#a937a50fb274ec99b146d999fd8c02a1b">VLCMediaPlayer</a>
</li>
<li>tracksInformation
: <a class="el" href="interface_v_l_c_media.html#a7b098bacc67ab0ff8fa9d316bef987d6">VLCMedia</a>
</li>
<li>type
: <a class="el" href="interface_v_l_c_renderer_item.html#aaac905fef90a323b9a56d116aba7c840">VLCRendererItem</a>
</li>
</ul>


<h3><a id="index_u"></a>- u -</h3><ul>
<li>url
: <a class="el" href="interface_v_l_c_media.html#aef3995dbdd704cc5c8ed4fc2e383e0a6">VLCMedia</a>
</li>
</ul>


<h3><a id="index_v"></a>- v -</h3><ul>
<li>value
: <a class="el" href="interface_v_l_c_time.html#a66fccafd32f7627fc119318a6c811394">VLCTime</a>
</li>
<li>verboseStringValue
: <a class="el" href="interface_v_l_c_time.html#acde55e3e8f75f7242f4fbf5c11e64743">VLCTime</a>
</li>
<li>version
: <a class="el" href="interface_v_l_c_library.html#a99403afc6373f5f0ca5b89b6c64d3f32">VLCLibrary</a>
</li>
<li>videoAspectRatio
: <a class="el" href="interface_v_l_c_media_player.html#a3ee849792344fed560e4308ebe8e4a76">VLCMediaPlayer</a>
</li>
<li>videoCropGeometry
: <a class="el" href="interface_v_l_c_media_player.html#a3f0fd895e58be570f115ab6f09501ffe">VLCMediaPlayer</a>
</li>
<li>videoSize
: <a class="el" href="interface_v_l_c_media_player.html#a0734e2b2d4edebeaf3ca9e1cce85f361">VLCMediaPlayer</a>
</li>
<li>videoSubTitlesIndexes
: <a class="el" href="interface_v_l_c_media_player.html#a1c8a4af83a85f3e8606049aad6f75169">VLCMediaPlayer</a>
</li>
<li>videoSubTitlesNames
: <a class="el" href="interface_v_l_c_media_player.html#aaaf3e36c370456bbf30058eedaf7844e">VLCMediaPlayer</a>
</li>
<li>videoTrackIndexes
: <a class="el" href="interface_v_l_c_media_player.html#a4f1abde67436f198f0d07b885bd5ac59">VLCMediaPlayer</a>
</li>
<li>videoTrackNames
: <a class="el" href="interface_v_l_c_media_player.html#aaa31a3ee365dd1721064f19319bb8026">VLCMediaPlayer</a>
</li>
<li>VLCTitleDescriptionName
: <a class="el" href="interface_v_l_c_media_player.html#a2907bb09c29757c5c0f89e5bbe7e7394">VLCMediaPlayer</a>
</li>
<li>volume
: <a class="el" href="interface_v_l_c_audio.html#a553194cab2c141b89804db92e1a43f87">VLCAudio</a>
</li>
</ul>


<h3><a id="index_w"></a>- w -</h3><ul>
<li>willPlay
: <a class="el" href="interface_v_l_c_media_player.html#aa55f09ffe39e021920248ff142ae0f75">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_y"></a>- y -</h3><ul>
<li>yaw
: <a class="el" href="interface_v_l_c_media_player.html#a5f8af3ae371f616810320e1fb447f6dc">VLCMediaPlayer</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
