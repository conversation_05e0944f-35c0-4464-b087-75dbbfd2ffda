<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCMedia Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#pub-static-methods">Class Methods</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_media-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCMedia Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_media_8h_source.html">VLCMedia.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCMedia:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_media.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a17eb20a065d628caf152e8e4c83bc4cb"><td class="memItemLeft" align="right" valign="top">enum &#160;</td><td class="memItemRight" valign="bottom">{ <br />
&#160;&#160;<a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba96d692455552df3b6332ce02b5c955a1">VLCMediaParseLocal</a> = 0x00, 
<a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cbadac6d0a10ac08b36376db311ce6a91a5">VLCMediaParseNetwork</a> = 0x01, 
<a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba802f388c99aa52b9bd26a11f55ffd4f0">VLCMediaFetchLocal</a> = 0x02, 
<a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cbaf9a01d0578ab528242b3de74d5e216dc">VLCMediaFetchNetwork</a> = 0x04, 
<br />
&#160;&#160;<a class="el" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba0184ba9260e22d0182cf418b2d73c20c">VLCMediaDoInteract</a> = 0x08
<br />
 }</td></tr>
<tr class="separator:a17eb20a065d628caf152e8e4c83bc4cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8deaf0be0fb1bae484ce026866ff902b"><td class="memItemLeft" align="right" valign="top">typedef int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">VLCMediaParsingOptions</a></td></tr>
<tr class="separator:a8deaf0be0fb1bae484ce026866ff902b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a1a980dff03ccacf966e754c0a60bac49"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#a1a980dff03ccacf966e754c0a60bac49">initWithURL:</a></td></tr>
<tr class="separator:a1a980dff03ccacf966e754c0a60bac49"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4215c08e40a19e60bf10a9ea15b8fb85"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#a4215c08e40a19e60bf10a9ea15b8fb85">initWithPath:</a></td></tr>
<tr class="separator:a4215c08e40a19e60bf10a9ea15b8fb85"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0f4a6c10ac143fdcd19bc12fdb2bb71a"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#a0f4a6c10ac143fdcd19bc12fdb2bb71a">initWithStream:</a></td></tr>
<tr class="separator:a0f4a6c10ac143fdcd19bc12fdb2bb71a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af9a048525b9aeb4919c47e1148962638"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#af9a048525b9aeb4919c47e1148962638">initAsNodeWithName:</a></td></tr>
<tr class="separator:af9a048525b9aeb4919c47e1148962638"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af4439eb3074ae064da27365b68ddbfc8"><td class="memItemLeft" align="right" valign="top">(typedef)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#af4439eb3074ae064da27365b68ddbfc8">NS_ENUM</a></td></tr>
<tr class="separator:af4439eb3074ae064da27365b68ddbfc8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ae7faaa9307383b5651794c141b3e5e"><td class="memItemLeft" align="right" valign="top">(typedef)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#a3ae7faaa9307383b5651794c141b3e5e">NS_ENUM</a></td></tr>
<tr class="separator:a3ae7faaa9307383b5651794c141b3e5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a353de8b3f3676f9f422630ed595fdfcc"><td class="memItemLeft" align="right" valign="top">(typedef)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#a353de8b3f3676f9f422630ed595fdfcc">NS_ENUM</a></td></tr>
<tr class="separator:a353de8b3f3676f9f422630ed595fdfcc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac0b967f4529a5c2a23972cca9fd2a800"><td class="memItemLeft" align="right" valign="top">(NSComparisonResult)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#ac0b967f4529a5c2a23972cca9fd2a800">compare:</a></td></tr>
<tr class="separator:ac0b967f4529a5c2a23972cca9fd2a800"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a82e93da5f18bf8584beff1b714d496d4"><td class="memItemLeft" align="right" valign="top">(<a class="el" href="interface_v_l_c_time.html">VLCTime</a> *)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#a82e93da5f18bf8584beff1b714d496d4">lengthWaitUntilDate:</a></td></tr>
<tr class="separator:a82e93da5f18bf8584beff1b714d496d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac32a90c64851638af38108040b37e454"><td class="memItemLeft" align="right" valign="top">(BOOL isParsed)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#ac32a90c64851638af38108040b37e454">__attribute__</a></td></tr>
<tr class="separator:ac32a90c64851638af38108040b37e454"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ad7dacc361919932777b2bf5a141023"><td class="memItemLeft" align="right" valign="top">(typedef)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#a4ad7dacc361919932777b2bf5a141023">NS_ENUM</a></td></tr>
<tr class="separator:a4ad7dacc361919932777b2bf5a141023"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af495ec3452fdcdcd89aa2a13695ba6dd"><td class="memItemLeft" align="right" valign="top">(NSString *)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#af495ec3452fdcdcd89aa2a13695ba6dd">metadataForKey:</a></td></tr>
<tr class="separator:af495ec3452fdcdcd89aa2a13695ba6dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab1f738fbdeaa9efaf918223c0ed187e4"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#ab1f738fbdeaa9efaf918223c0ed187e4">setMetadata:forKey:</a></td></tr>
<tr class="separator:ab1f738fbdeaa9efaf918223c0ed187e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28c23c5d427727732476f86c6d0645ee"><td class="memItemLeft" align="right" valign="top">((deprecated)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#a28c23c5d427727732476f86c6d0645ee">__attribute__</a></td></tr>
<tr class="separator:a28c23c5d427727732476f86c6d0645ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a28c23c5d427727732476f86c6d0645ee"><td class="memItemLeft" align="right" valign="top">((deprecated)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#a28c23c5d427727732476f86c6d0645ee">__attribute__</a></td></tr>
<tr class="separator:a28c23c5d427727732476f86c6d0645ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aecfb52ec0989cd489fdc2966cd431586"><td class="memItemLeft" align="right" valign="top">(int)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#aecfb52ec0989cd489fdc2966cd431586">parseWithOptions:</a></td></tr>
<tr class="separator:aecfb52ec0989cd489fdc2966cd431586"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac00685e5d9a33652413b298c43423b5a"><td class="memItemLeft" align="right" valign="top">(int)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#ac00685e5d9a33652413b298c43423b5a">parseWithOptions:timeout:</a></td></tr>
<tr class="separator:ac00685e5d9a33652413b298c43423b5a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0213a3ea482353bce0d7bb59355d497a"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#a0213a3ea482353bce0d7bb59355d497a">parseStop</a></td></tr>
<tr class="separator:a0213a3ea482353bce0d7bb59355d497a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab09b1de8ddcfae6c2ecc331777d54119"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#ab09b1de8ddcfae6c2ecc331777d54119">addOption:</a></td></tr>
<tr class="separator:ab09b1de8ddcfae6c2ecc331777d54119"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4ef68cc558ac4af2180acbad69c07289"><td class="memItemLeft" align="right" valign="top"><a id="a4ef68cc558ac4af2180acbad69c07289"></a>
(void)&#160;</td><td class="memItemRight" valign="bottom">- <b>addOptions:</b></td></tr>
<tr class="separator:a4ef68cc558ac4af2180acbad69c07289"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac04b45047fa221e26ac0c589e28fc5ff"><td class="memItemLeft" align="right" valign="top">(int)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#ac04b45047fa221e26ac0c589e28fc5ff">storeCookie:forHost:path:</a></td></tr>
<tr class="separator:ac04b45047fa221e26ac0c589e28fc5ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51bdb09726b9f4d72072e144ea7314cc"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media.html#a51bdb09726b9f4d72072e144ea7314cc">clearStoredCookies</a></td></tr>
<tr class="separator:a51bdb09726b9f4d72072e144ea7314cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Class Methods</h2></td></tr>
<tr class="memitem:a38e5fb8f18d50b6de684a7e56c1611fa"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_media.html#a38e5fb8f18d50b6de684a7e56c1611fa">mediaWithURL:</a></td></tr>
<tr class="separator:a38e5fb8f18d50b6de684a7e56c1611fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae65d970c9c066ab28ec0c8bdcc076101"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_media.html#ae65d970c9c066ab28ec0c8bdcc076101">mediaWithPath:</a></td></tr>
<tr class="separator:ae65d970c9c066ab28ec0c8bdcc076101"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a98f23cb39854168dc79df832cecb7ff2"><td class="memItemLeft" align="right" valign="top">(NSString *)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_media.html#a98f23cb39854168dc79df832cecb7ff2">codecNameForFourCC:trackType:</a></td></tr>
<tr class="separator:a98f23cb39854168dc79df832cecb7ff2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3aaff98fc9546ceaf4c1871577c00a17"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_media.html#a3aaff98fc9546ceaf4c1871577c00a17">mediaAsNodeWithName:</a></td></tr>
<tr class="separator:a3aaff98fc9546ceaf4c1871577c00a17"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a0e44431952021460c5f59f600236630b"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a0e44431952021460c5f59f600236630b">VLCMediaTracksInformationCodec</a></td></tr>
<tr class="separator:a0e44431952021460c5f59f600236630b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a523d5f9351c2fcac0d9b600773734c81"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a523d5f9351c2fcac0d9b600773734c81">VLCMediaTracksInformationId</a></td></tr>
<tr class="separator:a523d5f9351c2fcac0d9b600773734c81"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac5ccaa4e433a8bc847e54739d69827b7"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#ac5ccaa4e433a8bc847e54739d69827b7">VLCMediaTracksInformationType</a></td></tr>
<tr class="separator:ac5ccaa4e433a8bc847e54739d69827b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a076812e00bd51440c4d47da823011f86"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a076812e00bd51440c4d47da823011f86">VLCMediaTracksInformationCodecProfile</a></td></tr>
<tr class="separator:a076812e00bd51440c4d47da823011f86"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae380aafa86ebd25ad38ab630a6dc86dd"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#ae380aafa86ebd25ad38ab630a6dc86dd">VLCMediaTracksInformationCodecLevel</a></td></tr>
<tr class="separator:ae380aafa86ebd25ad38ab630a6dc86dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:add3bac6827f60b1cbe44544c106b39c0"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#add3bac6827f60b1cbe44544c106b39c0">VLCMediaTracksInformationBitrate</a></td></tr>
<tr class="separator:add3bac6827f60b1cbe44544c106b39c0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6879d1635a7c5c306bafc23cbed755a"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#ac6879d1635a7c5c306bafc23cbed755a">VLCMediaTracksInformationLanguage</a></td></tr>
<tr class="separator:ac6879d1635a7c5c306bafc23cbed755a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a480f68a87f30c723f9364f00620de519"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a480f68a87f30c723f9364f00620de519">VLCMediaTracksInformationDescription</a></td></tr>
<tr class="separator:a480f68a87f30c723f9364f00620de519"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aabf94e7de92ae328dba46d6c53e5d869"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#aabf94e7de92ae328dba46d6c53e5d869">VLCMediaTracksInformationAudioChannelsNumber</a></td></tr>
<tr class="separator:aabf94e7de92ae328dba46d6c53e5d869"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f42247ad4cefc2cfa4a96bd95f53356"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a5f42247ad4cefc2cfa4a96bd95f53356">VLCMediaTracksInformationAudioRate</a></td></tr>
<tr class="separator:a5f42247ad4cefc2cfa4a96bd95f53356"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a52414aa5aff9e0e929d6b3dad0461dd2"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a52414aa5aff9e0e929d6b3dad0461dd2">VLCMediaTracksInformationVideoHeight</a></td></tr>
<tr class="separator:a52414aa5aff9e0e929d6b3dad0461dd2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a29ca0a5036cfa556f5d7098c44030123"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a29ca0a5036cfa556f5d7098c44030123">VLCMediaTracksInformationVideoWidth</a></td></tr>
<tr class="separator:a29ca0a5036cfa556f5d7098c44030123"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32e842b07314f6b7a965fd8d5770bf8d"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a32e842b07314f6b7a965fd8d5770bf8d">VLCMediaTracksInformationVideoOrientation</a></td></tr>
<tr class="separator:a32e842b07314f6b7a965fd8d5770bf8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac28eab6679f8761ce13ea02d61562d21"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#ac28eab6679f8761ce13ea02d61562d21">VLCMediaTracksInformationVideoProjection</a></td></tr>
<tr class="separator:ac28eab6679f8761ce13ea02d61562d21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada9d9ba5acf71414913ecc83cb975bf6"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#ada9d9ba5acf71414913ecc83cb975bf6">VLCMediaTracksInformationSourceAspectRatio</a></td></tr>
<tr class="separator:ada9d9ba5acf71414913ecc83cb975bf6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa3b1ead249368c4b73a544f07c84bcdc"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#aa3b1ead249368c4b73a544f07c84bcdc">VLCMediaTracksInformationSourceAspectRatioDenominator</a></td></tr>
<tr class="separator:aa3b1ead249368c4b73a544f07c84bcdc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd5e8623f3246506f21576ca006df47e"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#afd5e8623f3246506f21576ca006df47e">VLCMediaTracksInformationFrameRate</a></td></tr>
<tr class="separator:afd5e8623f3246506f21576ca006df47e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a635234c93bcb43393868435ab98ad0a8"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a635234c93bcb43393868435ab98ad0a8">VLCMediaTracksInformationFrameRateDenominator</a></td></tr>
<tr class="separator:a635234c93bcb43393868435ab98ad0a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad173cd33fb9d51175e676b62838cd980"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#ad173cd33fb9d51175e676b62838cd980">VLCMediaTracksInformationTextEncoding</a></td></tr>
<tr class="separator:ad173cd33fb9d51175e676b62838cd980"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6eb47ffe2b3a79f562e0164e83416b1"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#ac6eb47ffe2b3a79f562e0164e83416b1">VLCMediaTracksInformationTypeAudio</a></td></tr>
<tr class="separator:ac6eb47ffe2b3a79f562e0164e83416b1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2ac631a7a8c7416ac9a13b914efeb22e"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a2ac631a7a8c7416ac9a13b914efeb22e">VLCMediaTracksInformationTypeVideo</a></td></tr>
<tr class="separator:a2ac631a7a8c7416ac9a13b914efeb22e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3d8f7f156478e43c45dfedf7459c9939"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a3d8f7f156478e43c45dfedf7459c9939">VLCMediaTracksInformationTypeText</a></td></tr>
<tr class="separator:a3d8f7f156478e43c45dfedf7459c9939"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b91c4e456c6ce07682477d41772adc2"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a2b91c4e456c6ce07682477d41772adc2">VLCMediaTracksInformationTypeUnknown</a></td></tr>
<tr class="separator:a2b91c4e456c6ce07682477d41772adc2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a8f6e78f8cc5d52384047ddaea9e01dcf"><td class="memItemLeft" align="right" valign="top">VLCMediaType&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a8f6e78f8cc5d52384047ddaea9e01dcf">mediaType</a></td></tr>
<tr class="separator:a8f6e78f8cc5d52384047ddaea9e01dcf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cb849f8dceb22cebbac149921c785a5"><td class="memItemLeft" align="right" valign="top">id&lt; <a class="el" href="protocol_v_l_c_media_delegate-p.html">VLCMediaDelegate</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a2cb849f8dceb22cebbac149921c785a5">delegate</a></td></tr>
<tr class="separator:a2cb849f8dceb22cebbac149921c785a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd47b541ffd9e93a5864ced1f127101d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_time.html">VLCTime</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#afd47b541ffd9e93a5864ced1f127101d">length</a></td></tr>
<tr class="separator:afd47b541ffd9e93a5864ced1f127101d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adc94b1c776ed671be57746c79e04f187"><td class="memItemLeft" align="right" valign="top">VLCMediaParsedStatus&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#adc94b1c776ed671be57746c79e04f187">parsedStatus</a></td></tr>
<tr class="separator:adc94b1c776ed671be57746c79e04f187"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aef3995dbdd704cc5c8ed4fc2e383e0a6"><td class="memItemLeft" align="right" valign="top">NSURL *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#aef3995dbdd704cc5c8ed4fc2e383e0a6">url</a></td></tr>
<tr class="separator:aef3995dbdd704cc5c8ed4fc2e383e0a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08f3d51d9b8199fd20143d178b368b2f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a08f3d51d9b8199fd20143d178b368b2f">subitems</a></td></tr>
<tr class="separator:a08f3d51d9b8199fd20143d178b368b2f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2332173d72093469abebf56f4c70ae80"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a2332173d72093469abebf56f4c70ae80">saveMetadata</a></td></tr>
<tr class="separator:a2332173d72093469abebf56f4c70ae80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac61f729efe7481e86d26e7e92fff0dd2"><td class="memItemLeft" align="right" valign="top">NSDictionary *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#ac61f729efe7481e86d26e7e92fff0dd2">metaDictionary</a></td></tr>
<tr class="separator:ac61f729efe7481e86d26e7e92fff0dd2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af247fea93ce48e219ddef15bdaf256de"><td class="memItemLeft" align="right" valign="top">VLCMediaState&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#af247fea93ce48e219ddef15bdaf256de">state</a></td></tr>
<tr class="separator:af247fea93ce48e219ddef15bdaf256de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2018328bfcb5934f725b285026fe4e98"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a2018328bfcb5934f725b285026fe4e98">mediaSizeSuitableForDevice</a></td></tr>
<tr class="separator:a2018328bfcb5934f725b285026fe4e98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7b098bacc67ab0ff8fa9d316bef987d6"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a7b098bacc67ab0ff8fa9d316bef987d6">tracksInformation</a></td></tr>
<tr class="separator:a7b098bacc67ab0ff8fa9d316bef987d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9c2d85f9c2dba700d7b2ca18cf12049a"><td class="memItemLeft" align="right" valign="top">NSDictionary *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a9c2d85f9c2dba700d7b2ca18cf12049a">stats</a></td></tr>
<tr class="separator:a9c2d85f9c2dba700d7b2ca18cf12049a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aadc4d2257ae507913c39611e9c935665"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#aadc4d2257ae507913c39611e9c935665">numberOfReadBytesOnInput</a></td></tr>
<tr class="separator:aadc4d2257ae507913c39611e9c935665"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2eb646a3d37eaec7de62ba174b9682f7"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a2eb646a3d37eaec7de62ba174b9682f7">inputBitrate</a></td></tr>
<tr class="separator:a2eb646a3d37eaec7de62ba174b9682f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a936f14e9dbdb6355604040bb963cf1b2"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a936f14e9dbdb6355604040bb963cf1b2">numberOfReadBytesOnDemux</a></td></tr>
<tr class="separator:a936f14e9dbdb6355604040bb963cf1b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e060d1cb138c0e0ecffe53d985b2dd3"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a1e060d1cb138c0e0ecffe53d985b2dd3">demuxBitrate</a></td></tr>
<tr class="separator:a1e060d1cb138c0e0ecffe53d985b2dd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0d65e705f516777543cb6ac2df310779"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a0d65e705f516777543cb6ac2df310779">numberOfDecodedVideoBlocks</a></td></tr>
<tr class="separator:a0d65e705f516777543cb6ac2df310779"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae5f6aa8f4cfd924c9f31cea1292739de"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#ae5f6aa8f4cfd924c9f31cea1292739de">numberOfDecodedAudioBlocks</a></td></tr>
<tr class="separator:ae5f6aa8f4cfd924c9f31cea1292739de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42f0be6a3830572833122e758ddaafb1"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a42f0be6a3830572833122e758ddaafb1">numberOfDisplayedPictures</a></td></tr>
<tr class="separator:a42f0be6a3830572833122e758ddaafb1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7456ceac9f4ac4b395bcc50064d58dd"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#ab7456ceac9f4ac4b395bcc50064d58dd">numberOfLostPictures</a></td></tr>
<tr class="separator:ab7456ceac9f4ac4b395bcc50064d58dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a958ceff6c2c01085c9c11963fc00e9ab"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a958ceff6c2c01085c9c11963fc00e9ab">numberOfPlayedAudioBuffers</a></td></tr>
<tr class="separator:a958ceff6c2c01085c9c11963fc00e9ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a13d927d07a8bc2cebab7363317c0a932"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a13d927d07a8bc2cebab7363317c0a932">numberOfLostAudioBuffers</a></td></tr>
<tr class="separator:a13d927d07a8bc2cebab7363317c0a932"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a65060dbc9eefe3518c4aa81daba05320"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a65060dbc9eefe3518c4aa81daba05320">numberOfSentPackets</a></td></tr>
<tr class="separator:a65060dbc9eefe3518c4aa81daba05320"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f663bcbd8cfea3c1fa23035a5e2e119"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a4f663bcbd8cfea3c1fa23035a5e2e119">numberOfSentBytes</a></td></tr>
<tr class="separator:a4f663bcbd8cfea3c1fa23035a5e2e119"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afd944ae42af805d532f4ab36d5b0fe7d"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#afd944ae42af805d532f4ab36d5b0fe7d">streamOutputBitrate</a></td></tr>
<tr class="separator:afd944ae42af805d532f4ab36d5b0fe7d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a289dc0ff117c7013b6b5363d9f35fd01"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a289dc0ff117c7013b6b5363d9f35fd01">numberOfCorruptedDataPackets</a></td></tr>
<tr class="separator:a289dc0ff117c7013b6b5363d9f35fd01"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a148f7e15ef691ed0c6cf631eb3bc34d8"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media.html#a148f7e15ef691ed0c6cf631eb3bc34d8">numberOfDiscontinuties</a></td></tr>
<tr class="separator:a148f7e15ef691ed0c6cf631eb3bc34d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Defines files and streams as a managed object. Each media object can be administered seperately. <a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> or <a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a> must be used to execute the appropriate playback functions. </p><dl class="section see"><dt>See also</dt><dd><a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a> </dd></dl>
</div><h2 class="groupheader">Member Typedef Documentation</h2>
<a id="a8deaf0be0fb1bae484ce026866ff902b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8deaf0be0fb1bae484ce026866ff902b">&#9670;&nbsp;</a></span>VLCMediaParsingOptions</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (typedef int) <a class="el" href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">VLCMediaParsingOptions</a></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>enum of available options for use with parseWithOptions </p><dl class="section note"><dt>Note</dt><dd>you may pipe multiple values for the single parameter </dd></dl>

</div>
</div>
<h2 class="groupheader">Member Enumeration Documentation</h2>
<a id="a17eb20a065d628caf152e8e4c83bc4cb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a17eb20a065d628caf152e8e4c83bc4cb">&#9670;&nbsp;</a></span>anonymous enum</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (anonymous) enum</td>
        </tr>
      </table>
</div><div class="memdoc">
<table class="fieldtable">
<tr><th colspan="2">Enumerator</th></tr><tr><td class="fieldname"><a id="a17eb20a065d628caf152e8e4c83bc4cba96d692455552df3b6332ce02b5c955a1"></a>VLCMediaParseLocal&#160;</td><td class="fielddoc"><p>Parse media if it's a local file. </p>
</td></tr>
<tr><td class="fieldname"><a id="a17eb20a065d628caf152e8e4c83bc4cbadac6d0a10ac08b36376db311ce6a91a5"></a>VLCMediaParseNetwork&#160;</td><td class="fielddoc"><p>Parse media even if it's a network file. </p>
</td></tr>
<tr><td class="fieldname"><a id="a17eb20a065d628caf152e8e4c83bc4cba802f388c99aa52b9bd26a11f55ffd4f0"></a>VLCMediaFetchLocal&#160;</td><td class="fielddoc"><p>Fetch meta and covert art using local resources. </p>
</td></tr>
<tr><td class="fieldname"><a id="a17eb20a065d628caf152e8e4c83bc4cbaf9a01d0578ab528242b3de74d5e216dc"></a>VLCMediaFetchNetwork&#160;</td><td class="fielddoc"><p>Fetch meta and covert art using network resources. </p>
</td></tr>
<tr><td class="fieldname"><a id="a17eb20a065d628caf152e8e4c83bc4cba0184ba9260e22d0182cf418b2d73c20c"></a>VLCMediaDoInteract&#160;</td><td class="fielddoc"><p>Interact with the user when preparsing this item (and not its sub items). Set this flag in order to receive a callback when the input is asking for credentials. </p>
</td></tr>
</table>

</div>
</div>
<h2 class="groupheader">Method Documentation</h2>
<a id="ac32a90c64851638af38108040b37e454"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac32a90c64851638af38108040b37e454">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[1/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL isParsed) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Determines if the media has already been preparsed. </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000002">Deprecated:</a></b></dt><dd>use parseStatus instead </dd></dl>

</div>
</div>
<a id="a28c23c5d427727732476f86c6d0645ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a28c23c5d427727732476f86c6d0645ee">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[2/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- ((deprecated) __attribute__ </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Start asynchronously to parse the media. This will attempt to fetch the meta data and tracks information.</p>
<p>This is automatically done when an accessor requiring parsing is called.</p>
<dl class="section see"><dt>See also</dt><dd>-[<a class="el" href="protocol_v_l_c_media_delegate-p.html">VLCMediaDelegate</a> mediaDidFinishParsing:] </dd></dl>
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000003">Deprecated:</a></b></dt><dd>Use parseWithOptions: instead </dd></dl>

</div>
</div>
<a id="a28c23c5d427727732476f86c6d0645ee"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a28c23c5d427727732476f86c6d0645ee">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[3/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- ((deprecated) __attribute__ </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Trigger a synchronous parsing of the media the selector won't return until parsing finished</p>
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000004">Deprecated:</a></b></dt><dd>Use parseWithOptions: instead </dd></dl>

</div>
</div>
<a id="ab09b1de8ddcfae6c2ecc331777d54119"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab09b1de8ddcfae6c2ecc331777d54119">&#9670;&nbsp;</a></span>addOption:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) addOption: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>option</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add options to the media, that will be used to determine how <a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> will read the media. This allow to use VLC advanced reading/streaming options in a per-media basis</p>
<p>The options are detailed in vlc &ndash;long-help, for instance "--sout-all" And on the web: <a href="http://wiki.videolan.org/VLC_command-line_help">http://wiki.videolan.org/VLC_command-line_help</a> </p>

</div>
</div>
<a id="a51bdb09726b9f4d72072e144ea7314cc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a51bdb09726b9f4d72072e144ea7314cc">&#9670;&nbsp;</a></span>clearStoredCookies</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) clearStoredCookies </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Clear the stored cookies of a media.</p>
<dl class="section note"><dt>Note</dt><dd>must be called before the first call of play() to take effect. The cookie jar is only used for http/https. </dd></dl>
<dl class="section warning"><dt>Warning</dt><dd>This method will never succeed on macOS, but requires iOS or tvOS </dd></dl>

</div>
</div>
<a id="a98f23cb39854168dc79df832cecb7ff2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a98f23cb39854168dc79df832cecb7ff2">&#9670;&nbsp;</a></span>codecNameForFourCC:trackType:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (NSString *) codecNameForFourCC: </td>
          <td></td>
          <td class="paramtype">(uint32_t)&#160;</td>
          <td class="paramname"><em>fourcc</em></td>
        </tr>
        <tr>
          <td class="paramkey">trackType:</td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>trackType</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>convienience method to return a user-readable codec name for the given FourCC </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fourcc</td><td>the FourCC to process </td></tr>
    <tr><td class="paramname">trackType</td><td>a VLC track type if known to speed-up the name search </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>a NSString containing the codec name if recognized, else an empty string </dd></dl>

</div>
</div>
<a id="ac0b967f4529a5c2a23972cca9fd2a800"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac0b967f4529a5c2a23972cca9fd2a800">&#9670;&nbsp;</a></span>compare:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSComparisonResult) compare: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *)&#160;</td>
          <td class="paramname"><em>media</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns an NSComparisonResult value that indicates the lexical ordering of the receiver and a given meda. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">media</td><td>The media with which to compare with the receiver. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NSOrderedAscending if the URL of the receiver precedes media in lexical ordering, NSOrderedSame if the URL of the receiver and media are equivalent in lexical value, and NSOrderedDescending if the URL of the receiver follows media. If media is nil, returns NSOrderedDescending. </dd></dl>

</div>
</div>
<a id="af9a048525b9aeb4919c47e1148962638"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af9a048525b9aeb4919c47e1148962638">&#9670;&nbsp;</a></span>initAsNodeWithName:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initAsNodeWithName: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>aName</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>TODO </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aName</td><td>TODO </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A new <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> object, only if there were no errors. </dd></dl>

</div>
</div>
<a id="a4215c08e40a19e60bf10a9ea15b8fb85"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4215c08e40a19e60bf10a9ea15b8fb85">&#9670;&nbsp;</a></span>initWithPath:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithPath: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>aPath</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Initializes a new <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> object to use the specified path. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aPath</td><td>Path to media to be accessed. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A new <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> object, only if there were no errors. </dd></dl>

</div>
</div>
<a id="a0f4a6c10ac143fdcd19bc12fdb2bb71a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0f4a6c10ac143fdcd19bc12fdb2bb71a">&#9670;&nbsp;</a></span>initWithStream:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithStream: </td>
          <td></td>
          <td class="paramtype">(NSInputStream *)&#160;</td>
          <td class="paramname"><em>stream</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Initializes a new <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> object to use an input stream.</p>
<dl class="section note"><dt>Note</dt><dd>By default, NSStream instances that are not file-based are non-seekable, you may subclass NSInputStream whose instances are capable of seeking through a stream. This subclass must allow setting NSStreamFileCurrentOffsetKey property. </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> will open stream if it is not already opened, and will close eventually. You can't pass an already closed input stream. </dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">stream</td><td>Input stream for media to be accessed. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A new <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> object, only if there were no errors. </dd></dl>

</div>
</div>
<a id="a1a980dff03ccacf966e754c0a60bac49"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1a980dff03ccacf966e754c0a60bac49">&#9670;&nbsp;</a></span>initWithURL:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithURL: </td>
          <td></td>
          <td class="paramtype">(NSURL *)&#160;</td>
          <td class="paramname"><em>anURL</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Initializes a new <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> object to use the specified URL. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">anURL</td><td>the URL to media to be accessed. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A new <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> object, only if there were no errors. </dd></dl>

</div>
</div>
<a id="a82e93da5f18bf8584beff1b714d496d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a82e93da5f18bf8584beff1b714d496d4">&#9670;&nbsp;</a></span>lengthWaitUntilDate:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_time.html">VLCTime</a> *) lengthWaitUntilDate: </td>
          <td></td>
          <td class="paramtype">(NSDate *)&#160;</td>
          <td class="paramname"><em>aDate</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Returns a <a class="el" href="interface_v_l_c_time.html">VLCTime</a> object describing the length of the media resource, however, this is a blocking operation and will wait until the preparsing is completed before returning anything. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aDate</td><td>Time for operation to wait until, if there are no results before specified date then nil is returned. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>The length of the media resource, nil if it couldn't wait for it. </dd></dl>

</div>
</div>
<a id="a3aaff98fc9546ceaf4c1871577c00a17"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3aaff98fc9546ceaf4c1871577c00a17">&#9670;&nbsp;</a></span>mediaAsNodeWithName:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (instancetype) mediaAsNodeWithName: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>aName</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>TODO </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aName</td><td>TODO </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>a new <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> object, only if there were no errors. This object will be automatically released. </dd></dl>
<dl class="section see"><dt>See also</dt><dd>initAsNodeWithName </dd></dl>

</div>
</div>
<a id="ae65d970c9c066ab28ec0c8bdcc076101"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae65d970c9c066ab28ec0c8bdcc076101">&#9670;&nbsp;</a></span>mediaWithPath:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (instancetype) mediaWithPath: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>aPath</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Manufactures a new <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> object using the path specified. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aPath</td><td>Path to the media to be accessed. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A new <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> object, only if there were no errors. This object will be automatically released. </dd></dl>
<dl class="section see"><dt>See also</dt><dd>initWithPath </dd></dl>

</div>
</div>
<a id="a38e5fb8f18d50b6de684a7e56c1611fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a38e5fb8f18d50b6de684a7e56c1611fa">&#9670;&nbsp;</a></span>mediaWithURL:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (instancetype) mediaWithURL: </td>
          <td></td>
          <td class="paramtype">(NSURL *)&#160;</td>
          <td class="paramname"><em>anURL</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Manufactures a new <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> object using the URL specified. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">anURL</td><td>URL to media to be accessed. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A new <a class="el" href="interface_v_l_c_media.html">VLCMedia</a> object, only if there were no errors. This object will be automatically released. </dd></dl>
<dl class="section see"><dt>See also</dt><dd>initWithMediaURL </dd></dl>

</div>
</div>
<a id="af495ec3452fdcdcd89aa2a13695ba6dd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af495ec3452fdcdcd89aa2a13695ba6dd">&#9670;&nbsp;</a></span>metadataForKey:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString *) metadataForKey: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>key</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>get meta property for key </p><dl class="section note"><dt>Note</dt><dd>for performance reasons, fetching the metaDictionary will be faster! </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="interface_v_l_c_media.html#ac61f729efe7481e86d26e7e92fff0dd2">metaDictionary</a> </dd>
<dd>
dictionary keys above </dd></dl>

</div>
</div>
<a id="af4439eb3074ae064da27365b68ddbfc8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af4439eb3074ae064da27365b68ddbfc8">&#9670;&nbsp;</a></span>NS_ENUM <span class="overload">[1/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (typedef) NS_ENUM </td>
          <td></td>
          <td class="paramtype">(NSUInteger)&#160;</td>
          <td class="paramname"></td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">(VLCMediaOrientation)&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>list of possible media orientation. </p>

</div>
</div>
<a id="a3ae7faaa9307383b5651794c141b3e5e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3ae7faaa9307383b5651794c141b3e5e">&#9670;&nbsp;</a></span>NS_ENUM <span class="overload">[2/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (typedef) NS_ENUM </td>
          <td></td>
          <td class="paramtype">(NSUInteger)&#160;</td>
          <td class="paramname"></td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">(VLCMediaProjection)&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>list of possible media projection. </p>

</div>
</div>
<a id="a353de8b3f3676f9f422630ed595fdfcc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a353de8b3f3676f9f422630ed595fdfcc">&#9670;&nbsp;</a></span>NS_ENUM <span class="overload">[3/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (typedef) NS_ENUM </td>
          <td></td>
          <td class="paramtype">(NSUInteger)&#160;</td>
          <td class="paramname"></td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">(VLCMediaType)&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>list of possible media types that could be returned by "mediaType" </p>

</div>
</div>
<a id="a4ad7dacc361919932777b2bf5a141023"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4ad7dacc361919932777b2bf5a141023">&#9670;&nbsp;</a></span>NS_ENUM <span class="overload">[4/4]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (typedef) NS_ENUM </td>
          <td></td>
          <td class="paramtype">(unsigned)&#160;</td>
          <td class="paramname"></td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">(VLCMediaParsedStatus)&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>list of possible parsed states returnable by parsedStatus </p>

</div>
</div>
<a id="a0213a3ea482353bce0d7bb59355d497a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0213a3ea482353bce0d7bb59355d497a">&#9670;&nbsp;</a></span>parseStop</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) parseStop </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Stop the parsing of the media</p>
<p>When the media parsing is stopped, the mediaDidFinishParsing will be sent with the VLCMediaParsedStatusTimeout status. </p>

</div>
</div>
<a id="aecfb52ec0989cd489fdc2966cd431586"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aecfb52ec0989cd489fdc2966cd431586">&#9670;&nbsp;</a></span>parseWithOptions:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (int) parseWithOptions: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">VLCMediaParsingOptions</a>)&#160;</td>
          <td class="paramname"><em>options</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>triggers an asynchronous parse of the media item using the given options </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">options</td><td>the option mask based on VLCMediaParsingOptions </td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">VLCMediaParsingOptions</a> </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>an int. 0 on success, -1 in case of error </dd></dl>
<dl class="section note"><dt>Note</dt><dd>listen to the "parsed" key value or the mediaDidFinishParsing: delegate method to be notified about parsing results. Those triggers will <em>NOT</em> be raised if parsing fails and this method returns an error. </dd></dl>

</div>
</div>
<a id="ac00685e5d9a33652413b298c43423b5a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac00685e5d9a33652413b298c43423b5a">&#9670;&nbsp;</a></span>parseWithOptions:timeout:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (int) <a class="el" href="interface_v_l_c_media.html#aecfb52ec0989cd489fdc2966cd431586">parseWithOptions:</a> </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">VLCMediaParsingOptions</a>)&#160;</td>
          <td class="paramname"><em>options</em></td>
        </tr>
        <tr>
          <td class="paramkey">timeout:</td>
          <td></td>
          <td class="paramtype">(int)&#160;</td>
          <td class="paramname"><em>timeoutValue</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>triggers an asynchronous parse of the media item using the given options </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">options</td><td>the option mask based on VLCMediaParsingOptions </td></tr>
    <tr><td class="paramname">timeoutValue</td><td>a time-out value in milliseconds (-1 for default, 0 for infinite) </td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">VLCMediaParsingOptions</a> </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>an int. 0 on success, -1 in case of error </dd></dl>
<dl class="section note"><dt>Note</dt><dd>listen to the "parsed" key value or the mediaDidFinishParsing: delegate method to be notified about parsing results. Those triggers will <em>NOT</em> be raised if parsing fails and this method returns an error. </dd></dl>

</div>
</div>
<a id="ab1f738fbdeaa9efaf918223c0ed187e4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab1f738fbdeaa9efaf918223c0ed187e4">&#9670;&nbsp;</a></span>setMetadata:forKey:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) setMetadata: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>data</em></td>
        </tr>
        <tr>
          <td class="paramkey">forKey:</td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>key</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>set meta property for key </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">data</td><td>the metadata to set as NSString </td></tr>
    <tr><td class="paramname">key</td><td>the metadata key </td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>dictionary keys above </dd></dl>

</div>
</div>
<a id="ac04b45047fa221e26ac0c589e28fc5ff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac04b45047fa221e26ac0c589e28fc5ff">&#9670;&nbsp;</a></span>storeCookie:forHost:path:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (int) storeCookie: </td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>cookie</em></td>
        </tr>
        <tr>
          <td class="paramkey">forHost:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>host</em></td>
        </tr>
        <tr>
          <td class="paramkey">path:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>path</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Parse a value of an incoming Set-Cookie header (see RFC 6265) and append the cookie to the stored cookies if appropriate. The "secure" attribute can be added to cookie to limit the scope of the cookie to secured channels (https).</p>
<dl class="section note"><dt>Note</dt><dd>must be called before the first call of play() to take effect. The cookie storage is only used for http/https. </dd></dl>
<dl class="section warning"><dt>Warning</dt><dd>This method will never succeed on macOS, but requires iOS or tvOS</dd></dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">cookie</td><td>header field value of Set-Cookie: "name=value&lt;;attributes&gt;" </td></tr>
    <tr><td class="paramname">host</td><td>host to which the cookie will be sent </td></tr>
    <tr><td class="paramname">path</td><td>scope of the cookie</td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>0 on success, -1 on error. </dd></dl>

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a id="aabf94e7de92ae328dba46d6c53e5d869"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aabf94e7de92ae328dba46d6c53e5d869">&#9670;&nbsp;</a></span>VLCMediaTracksInformationAudioChannelsNumber</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationAudioChannelsNumber</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>number of audio channels of a given track </p><dl class="section note"><dt>Note</dt><dd>returns the audio channel number as NSNumber </dd></dl>

</div>
</div>
<a id="a5f42247ad4cefc2cfa4a96bd95f53356"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f42247ad4cefc2cfa4a96bd95f53356">&#9670;&nbsp;</a></span>VLCMediaTracksInformationAudioRate</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationAudioRate</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>audio rate </p><dl class="section note"><dt>Note</dt><dd>returns the audio rate as NSNumber </dd></dl>

</div>
</div>
<a id="add3bac6827f60b1cbe44544c106b39c0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#add3bac6827f60b1cbe44544c106b39c0">&#9670;&nbsp;</a></span>VLCMediaTracksInformationBitrate</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationBitrate</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>track bitrate </p><dl class="section note"><dt>Note</dt><dd>returns the bitrate as NSNumber </dd></dl>

</div>
</div>
<a id="a0e44431952021460c5f59f600236630b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0e44431952021460c5f59f600236630b">&#9670;&nbsp;</a></span>VLCMediaTracksInformationCodec</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationCodec</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Tracks information NSDictionary Possible Keys Codec information </p><dl class="section note"><dt>Note</dt><dd>returns a NSNumber </dd></dl>

</div>
</div>
<a id="ae380aafa86ebd25ad38ab630a6dc86dd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae380aafa86ebd25ad38ab630a6dc86dd">&#9670;&nbsp;</a></span>VLCMediaTracksInformationCodecLevel</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationCodecLevel</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>codec level </p><dl class="section note"><dt>Note</dt><dd>returns a NSNumber </dd></dl>

</div>
</div>
<a id="a076812e00bd51440c4d47da823011f86"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a076812e00bd51440c4d47da823011f86">&#9670;&nbsp;</a></span>VLCMediaTracksInformationCodecProfile</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationCodecProfile</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>codec profile </p><dl class="section note"><dt>Note</dt><dd>returns a NSNumber </dd></dl>

</div>
</div>
<a id="a480f68a87f30c723f9364f00620de519"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a480f68a87f30c723f9364f00620de519">&#9670;&nbsp;</a></span>VLCMediaTracksInformationDescription</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationDescription</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>track description </p><dl class="section note"><dt>Note</dt><dd>returns the description as NSString </dd></dl>

</div>
</div>
<a id="afd5e8623f3246506f21576ca006df47e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afd5e8623f3246506f21576ca006df47e">&#9670;&nbsp;</a></span>VLCMediaTracksInformationFrameRate</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationFrameRate</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>frame rate </p><dl class="section note"><dt>Note</dt><dd>returns the frame rate as NSNumber </dd></dl>

</div>
</div>
<a id="a635234c93bcb43393868435ab98ad0a8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a635234c93bcb43393868435ab98ad0a8">&#9670;&nbsp;</a></span>VLCMediaTracksInformationFrameRateDenominator</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationFrameRateDenominator</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>frame rate denominator </p><dl class="section note"><dt>Note</dt><dd>returns the frame rate denominator as NSNumber </dd></dl>

</div>
</div>
<a id="a523d5f9351c2fcac0d9b600773734c81"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a523d5f9351c2fcac0d9b600773734c81">&#9670;&nbsp;</a></span>VLCMediaTracksInformationId</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationId</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>tracks information ID </p><dl class="section note"><dt>Note</dt><dd>returns a NSNumber </dd></dl>

</div>
</div>
<a id="ac6879d1635a7c5c306bafc23cbed755a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac6879d1635a7c5c306bafc23cbed755a">&#9670;&nbsp;</a></span>VLCMediaTracksInformationLanguage</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationLanguage</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>track language </p><dl class="section note"><dt>Note</dt><dd>returns the language as NSString </dd></dl>

</div>
</div>
<a id="ada9d9ba5acf71414913ecc83cb975bf6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada9d9ba5acf71414913ecc83cb975bf6">&#9670;&nbsp;</a></span>VLCMediaTracksInformationSourceAspectRatio</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationSourceAspectRatio</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>source aspect ratio </p><dl class="section note"><dt>Note</dt><dd>returns the source aspect ratio as NSNumber </dd></dl>

</div>
</div>
<a id="aa3b1ead249368c4b73a544f07c84bcdc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa3b1ead249368c4b73a544f07c84bcdc">&#9670;&nbsp;</a></span>VLCMediaTracksInformationSourceAspectRatioDenominator</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationSourceAspectRatioDenominator</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>source aspect ratio denominator </p><dl class="section note"><dt>Note</dt><dd>returns the source aspect ratio denominator as NSNumber </dd></dl>

</div>
</div>
<a id="ad173cd33fb9d51175e676b62838cd980"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad173cd33fb9d51175e676b62838cd980">&#9670;&nbsp;</a></span>VLCMediaTracksInformationTextEncoding</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationTextEncoding</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>text encoding </p><dl class="section note"><dt>Note</dt><dd>returns the text encoding as NSString </dd></dl>

</div>
</div>
<a id="ac5ccaa4e433a8bc847e54739d69827b7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac5ccaa4e433a8bc847e54739d69827b7">&#9670;&nbsp;</a></span>VLCMediaTracksInformationType</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationType</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>track information type </p><dl class="section note"><dt>Note</dt><dd>returns a NSString </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="interface_v_l_c_media.html#ac6eb47ffe2b3a79f562e0164e83416b1">VLCMediaTracksInformationTypeAudio</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#a2ac631a7a8c7416ac9a13b914efeb22e">VLCMediaTracksInformationTypeVideo</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#a3d8f7f156478e43c45dfedf7459c9939">VLCMediaTracksInformationTypeText</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#a2b91c4e456c6ce07682477d41772adc2">VLCMediaTracksInformationTypeUnknown</a> </dd></dl>

</div>
</div>
<a id="ac6eb47ffe2b3a79f562e0164e83416b1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac6eb47ffe2b3a79f562e0164e83416b1">&#9670;&nbsp;</a></span>VLCMediaTracksInformationTypeAudio</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationTypeAudio</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>audio track information NSDictionary value for VLCMediaTracksInformationType </p>

</div>
</div>
<a id="a3d8f7f156478e43c45dfedf7459c9939"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3d8f7f156478e43c45dfedf7459c9939">&#9670;&nbsp;</a></span>VLCMediaTracksInformationTypeText</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationTypeText</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>text / subtitles track information NSDictionary value for VLCMediaTracksInformationType </p>

</div>
</div>
<a id="a2b91c4e456c6ce07682477d41772adc2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2b91c4e456c6ce07682477d41772adc2">&#9670;&nbsp;</a></span>VLCMediaTracksInformationTypeUnknown</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationTypeUnknown</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>unknown track information NSDictionary value for VLCMediaTracksInformationType </p>

</div>
</div>
<a id="a2ac631a7a8c7416ac9a13b914efeb22e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2ac631a7a8c7416ac9a13b914efeb22e">&#9670;&nbsp;</a></span>VLCMediaTracksInformationTypeVideo</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationTypeVideo</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>video track information NSDictionary value for VLCMediaTracksInformationType </p>

</div>
</div>
<a id="a52414aa5aff9e0e929d6b3dad0461dd2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a52414aa5aff9e0e929d6b3dad0461dd2">&#9670;&nbsp;</a></span>VLCMediaTracksInformationVideoHeight</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationVideoHeight</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>video track height </p><dl class="section note"><dt>Note</dt><dd>returns the height as NSNumber </dd></dl>

</div>
</div>
<a id="a32e842b07314f6b7a965fd8d5770bf8d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a32e842b07314f6b7a965fd8d5770bf8d">&#9670;&nbsp;</a></span>VLCMediaTracksInformationVideoOrientation</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationVideoOrientation</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>video track orientation </p><dl class="section note"><dt>Note</dt><dd>returns the orientation as NSNumber </dd></dl>

</div>
</div>
<a id="ac28eab6679f8761ce13ea02d61562d21"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac28eab6679f8761ce13ea02d61562d21">&#9670;&nbsp;</a></span>VLCMediaTracksInformationVideoProjection</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationVideoProjection</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>video track projection </p><dl class="section note"><dt>Note</dt><dd>the projection as NSNumber </dd></dl>

</div>
</div>
<a id="a29ca0a5036cfa556f5d7098c44030123"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a29ca0a5036cfa556f5d7098c44030123">&#9670;&nbsp;</a></span>VLCMediaTracksInformationVideoWidth</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCMediaTracksInformationVideoWidth</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>video track width </p><dl class="section note"><dt>Note</dt><dd>the width as NSNumber </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a2cb849f8dceb22cebbac149921c785a5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2cb849f8dceb22cebbac149921c785a5">&#9670;&nbsp;</a></span>delegate</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (id&lt;<a class="el" href="protocol_v_l_c_media_delegate-p.html">VLCMediaDelegate</a>&gt;) delegate</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">weak</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Receiver's delegate. </p>

</div>
</div>
<a id="a1e060d1cb138c0e0ecffe53d985b2dd3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1e060d1cb138c0e0ecffe53d985b2dd3">&#9670;&nbsp;</a></span>demuxBitrate</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) demuxBitrate</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the current demux bitrate. may be 0 if the buffer is empty </p><dl class="section return"><dt>Returns</dt><dd>a float of the current demux bitrate </dd></dl>

</div>
</div>
<a id="a2eb646a3d37eaec7de62ba174b9682f7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2eb646a3d37eaec7de62ba174b9682f7">&#9670;&nbsp;</a></span>inputBitrate</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) inputBitrate</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the current input bitrate. may be 0 if the buffer is full </p><dl class="section return"><dt>Returns</dt><dd>a float of the current input bitrate </dd></dl>

</div>
</div>
<a id="afd47b541ffd9e93a5864ced1f127101d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afd47b541ffd9e93a5864ced1f127101d">&#9670;&nbsp;</a></span>length</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_time.html">VLCTime</a>*) length</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">strong</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>A <a class="el" href="interface_v_l_c_time.html">VLCTime</a> object describing the length of the media resource, only if it is available. Use lengthWaitUntilDate: to wait for a specified length of time. </p><dl class="section see"><dt>See also</dt><dd>lengthWaitUntilDate </dd></dl>

</div>
</div>
<a id="a2018328bfcb5934f725b285026fe4e98"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2018328bfcb5934f725b285026fe4e98">&#9670;&nbsp;</a></span>mediaSizeSuitableForDevice</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) mediaSizeSuitableForDevice</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns a bool whether is the media is expected to play fluently on this device or not. It always returns YES on a Mac. </p>

</div>
</div>
<a id="a8f6e78f8cc5d52384047ddaea9e01dcf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8f6e78f8cc5d52384047ddaea9e01dcf">&#9670;&nbsp;</a></span>mediaType</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (VLCMediaType) mediaType</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>media type </p><dl class="section return"><dt>Returns</dt><dd>returns the type of a media (VLCMediaType) </dd></dl>

</div>
</div>
<a id="ac61f729efe7481e86d26e7e92fff0dd2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac61f729efe7481e86d26e7e92fff0dd2">&#9670;&nbsp;</a></span>metaDictionary</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSDictionary*) metaDictionary</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The receiver's meta data as a NSDictionary object. </p>

</div>
</div>
<a id="a289dc0ff117c7013b6b5363d9f35fd01"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a289dc0ff117c7013b6b5363d9f35fd01">&#9670;&nbsp;</a></span>numberOfCorruptedDataPackets</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) numberOfCorruptedDataPackets</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the total number of corrupted data packets during current sout session </p><dl class="section note"><dt>Note</dt><dd>value is 0 on non-stream-output operations </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>a NSInteger with the total number of corrupted data packets </dd></dl>

</div>
</div>
<a id="ae5f6aa8f4cfd924c9f31cea1292739de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae5f6aa8f4cfd924c9f31cea1292739de">&#9670;&nbsp;</a></span>numberOfDecodedAudioBlocks</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) numberOfDecodedAudioBlocks</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the total number of decoded audio blocks in the current media session </p><dl class="section return"><dt>Returns</dt><dd>a NSInteger with the total number of decoded blocks </dd></dl>

</div>
</div>
<a id="a0d65e705f516777543cb6ac2df310779"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0d65e705f516777543cb6ac2df310779">&#9670;&nbsp;</a></span>numberOfDecodedVideoBlocks</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) numberOfDecodedVideoBlocks</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the total number of decoded video blocks in the current media session </p><dl class="section return"><dt>Returns</dt><dd>a NSInteger with the total number of decoded blocks </dd></dl>

</div>
</div>
<a id="a148f7e15ef691ed0c6cf631eb3bc34d8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a148f7e15ef691ed0c6cf631eb3bc34d8">&#9670;&nbsp;</a></span>numberOfDiscontinuties</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) numberOfDiscontinuties</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the total number of discontinuties during current sout session </p><dl class="section note"><dt>Note</dt><dd>value is 0 on non-stream-output operations </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>a NSInteger with the total number of discontinuties </dd></dl>

</div>
</div>
<a id="a42f0be6a3830572833122e758ddaafb1"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a42f0be6a3830572833122e758ddaafb1">&#9670;&nbsp;</a></span>numberOfDisplayedPictures</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) numberOfDisplayedPictures</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the total number of displayed pictures during the current media session </p><dl class="section return"><dt>Returns</dt><dd>a NSInteger with the total number of displayed pictures </dd></dl>

</div>
</div>
<a id="a13d927d07a8bc2cebab7363317c0a932"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a13d927d07a8bc2cebab7363317c0a932">&#9670;&nbsp;</a></span>numberOfLostAudioBuffers</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) numberOfLostAudioBuffers</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the total number of audio buffers lost during the current media session </p><dl class="section return"><dt>Returns</dt><dd>a NSInteger with the total number of displayed pictures </dd></dl>

</div>
</div>
<a id="ab7456ceac9f4ac4b395bcc50064d58dd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab7456ceac9f4ac4b395bcc50064d58dd">&#9670;&nbsp;</a></span>numberOfLostPictures</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) numberOfLostPictures</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the total number of pictures lost during the current media session </p><dl class="section return"><dt>Returns</dt><dd>a NSInteger with the total number of lost pictures </dd></dl>

</div>
</div>
<a id="a958ceff6c2c01085c9c11963fc00e9ab"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a958ceff6c2c01085c9c11963fc00e9ab">&#9670;&nbsp;</a></span>numberOfPlayedAudioBuffers</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) numberOfPlayedAudioBuffers</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the total number of played audio buffers during the current media session </p><dl class="section return"><dt>Returns</dt><dd>a NSInteger with the total number of played audio buffers </dd></dl>

</div>
</div>
<a id="a936f14e9dbdb6355604040bb963cf1b2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a936f14e9dbdb6355604040bb963cf1b2">&#9670;&nbsp;</a></span>numberOfReadBytesOnDemux</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) numberOfReadBytesOnDemux</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the number of bytes read by the current demux module </p><dl class="section return"><dt>Returns</dt><dd>a NSInteger with the raw number of bytes </dd></dl>

</div>
</div>
<a id="aadc4d2257ae507913c39611e9c935665"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aadc4d2257ae507913c39611e9c935665">&#9670;&nbsp;</a></span>numberOfReadBytesOnInput</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) numberOfReadBytesOnInput</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the number of bytes read by the current input module </p><dl class="section return"><dt>Returns</dt><dd>a NSInteger with the raw number of bytes </dd></dl>

</div>
</div>
<a id="a4f663bcbd8cfea3c1fa23035a5e2e119"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f663bcbd8cfea3c1fa23035a5e2e119">&#9670;&nbsp;</a></span>numberOfSentBytes</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) numberOfSentBytes</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the total number of raw bytes sent during the current media session </p><dl class="section return"><dt>Returns</dt><dd>a NSInteger with the total number of sent bytes </dd></dl>

</div>
</div>
<a id="a65060dbc9eefe3518c4aa81daba05320"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a65060dbc9eefe3518c4aa81daba05320">&#9670;&nbsp;</a></span>numberOfSentPackets</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) numberOfSentPackets</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the total number of packets sent during the current media session </p><dl class="section return"><dt>Returns</dt><dd>a NSInteger with the total number of sent packets </dd></dl>

</div>
</div>
<a id="adc94b1c776ed671be57746c79e04f187"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adc94b1c776ed671be57746c79e04f187">&#9670;&nbsp;</a></span>parsedStatus</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (VLCMediaParsedStatus) parsedStatus</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>Returns the parse status of the media </dd></dl>

</div>
</div>
<a id="a2332173d72093469abebf56f4c70ae80"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2332173d72093469abebf56f4c70ae80">&#9670;&nbsp;</a></span>saveMetadata</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) saveMetadata</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Save the previously changed metadata </p><dl class="section return"><dt>Returns</dt><dd>true if saving was successful </dd></dl>

</div>
</div>
<a id="af247fea93ce48e219ddef15bdaf256de"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af247fea93ce48e219ddef15bdaf256de">&#9670;&nbsp;</a></span>state</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (VLCMediaState) state</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The receiver's state, such as Playing, Error, NothingSpecial, Buffering. </p>

</div>
</div>
<a id="a9c2d85f9c2dba700d7b2ca18cf12049a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9c2d85f9c2dba700d7b2ca18cf12049a">&#9670;&nbsp;</a></span>stats</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSDictionary*) stats</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Getter for statistics information Returns a NSDictionary with NSNumbers for values. </p>

</div>
</div>
<a id="afd944ae42af805d532f4ab36d5b0fe7d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afd944ae42af805d532f4ab36d5b0fe7d">&#9670;&nbsp;</a></span>streamOutputBitrate</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) streamOutputBitrate</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the current bitrate of sent bytes </p><dl class="section return"><dt>Returns</dt><dd>a float of the current bitrate of sent bits </dd></dl>

</div>
</div>
<a id="a08f3d51d9b8199fd20143d178b368b2f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a08f3d51d9b8199fd20143d178b368b2f">&#9670;&nbsp;</a></span>subitems</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a>*) subitems</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">strong</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The receiver's sub list. </p>

</div>
</div>
<a id="a7b098bacc67ab0ff8fa9d316bef987d6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7b098bacc67ab0ff8fa9d316bef987d6">&#9670;&nbsp;</a></span>tracksInformation</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray*) tracksInformation</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the tracks information.</p>
<p>This is an array of NSDictionary representing each track. It can contain the following keys:</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="interface_v_l_c_media.html#a0e44431952021460c5f59f600236630b">VLCMediaTracksInformationCodec</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#a523d5f9351c2fcac0d9b600773734c81">VLCMediaTracksInformationId</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#ac5ccaa4e433a8bc847e54739d69827b7">VLCMediaTracksInformationType</a></dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#a076812e00bd51440c4d47da823011f86">VLCMediaTracksInformationCodecProfile</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#ae380aafa86ebd25ad38ab630a6dc86dd">VLCMediaTracksInformationCodecLevel</a></dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#add3bac6827f60b1cbe44544c106b39c0">VLCMediaTracksInformationBitrate</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#ac6879d1635a7c5c306bafc23cbed755a">VLCMediaTracksInformationLanguage</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#a480f68a87f30c723f9364f00620de519">VLCMediaTracksInformationDescription</a></dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#aabf94e7de92ae328dba46d6c53e5d869">VLCMediaTracksInformationAudioChannelsNumber</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#a5f42247ad4cefc2cfa4a96bd95f53356">VLCMediaTracksInformationAudioRate</a></dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#a52414aa5aff9e0e929d6b3dad0461dd2">VLCMediaTracksInformationVideoHeight</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#a29ca0a5036cfa556f5d7098c44030123">VLCMediaTracksInformationVideoWidth</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#a32e842b07314f6b7a965fd8d5770bf8d">VLCMediaTracksInformationVideoOrientation</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#ac28eab6679f8761ce13ea02d61562d21">VLCMediaTracksInformationVideoProjection</a></dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#ada9d9ba5acf71414913ecc83cb975bf6">VLCMediaTracksInformationSourceAspectRatio</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#aa3b1ead249368c4b73a544f07c84bcdc">VLCMediaTracksInformationSourceAspectRatioDenominator</a></dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#afd5e8623f3246506f21576ca006df47e">VLCMediaTracksInformationFrameRate</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#a635234c93bcb43393868435ab98ad0a8">VLCMediaTracksInformationFrameRateDenominator</a></dd>
<dd>
<a class="el" href="interface_v_l_c_media.html#ad173cd33fb9d51175e676b62838cd980">VLCMediaTracksInformationTextEncoding</a> </dd></dl>

</div>
</div>
<a id="aef3995dbdd704cc5c8ed4fc2e383e0a6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aef3995dbdd704cc5c8ed4fc2e383e0a6">&#9670;&nbsp;</a></span>url</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSURL*) url</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">strong</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The URL for the receiver's media resource. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_media_8h_source.html">VLCMedia.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
