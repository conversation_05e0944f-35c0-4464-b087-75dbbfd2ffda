<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCTime Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#pub-static-methods">Class Methods</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_time-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCTime Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_time_8h_source.html">VLCTime.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCTime:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_time.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a4681ebad8d27e7243ebf6d83679e8a24"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_time.html#a4681ebad8d27e7243ebf6d83679e8a24">initWithNumber:</a></td></tr>
<tr class="separator:a4681ebad8d27e7243ebf6d83679e8a24"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac9717d5978ab18fa6547cde44904cfad"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_time.html#ac9717d5978ab18fa6547cde44904cfad">initWithInt:</a></td></tr>
<tr class="separator:ac9717d5978ab18fa6547cde44904cfad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4614e337e6304712ddd9182861e347ed"><td class="memItemLeft" align="right" valign="top">(NSNumber *numberValue)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_time.html#a4614e337e6304712ddd9182861e347ed">__attribute__</a></td></tr>
<tr class="separator:a4614e337e6304712ddd9182861e347ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4bde16fb3441c6e7ff40a75e2f206148"><td class="memItemLeft" align="right" valign="top">(NSComparisonResult)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_time.html#a4bde16fb3441c6e7ff40a75e2f206148">compare:</a></td></tr>
<tr class="separator:a4bde16fb3441c6e7ff40a75e2f206148"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a02ff06b1610b86d9bb1770a93837be42"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_time.html#a02ff06b1610b86d9bb1770a93837be42">isEqual:</a></td></tr>
<tr class="separator:a02ff06b1610b86d9bb1770a93837be42"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f24e1e825a0676c3da2f6a3d5bc0a13"><td class="memItemLeft" align="right" valign="top">(NSUInteger)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_time.html#a5f24e1e825a0676c3da2f6a3d5bc0a13">hash</a></td></tr>
<tr class="separator:a5f24e1e825a0676c3da2f6a3d5bc0a13"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Class Methods</h2></td></tr>
<tr class="memitem:ae4b7157f2152d3eddafe14e126a4b96c"><td class="memItemLeft" align="right" valign="top">(<a class="el" href="interface_v_l_c_time.html">VLCTime</a> *)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_time.html#ae4b7157f2152d3eddafe14e126a4b96c">nullTime</a></td></tr>
<tr class="separator:ae4b7157f2152d3eddafe14e126a4b96c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af9b7c469a7cb064a3d9c2c5bcaa769bb"><td class="memItemLeft" align="right" valign="top">(<a class="el" href="interface_v_l_c_time.html">VLCTime</a> *)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_time.html#af9b7c469a7cb064a3d9c2c5bcaa769bb">timeWithNumber:</a></td></tr>
<tr class="separator:af9b7c469a7cb064a3d9c2c5bcaa769bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a936e42eed02e03b404f14bdbce13d06f"><td class="memItemLeft" align="right" valign="top">(<a class="el" href="interface_v_l_c_time.html">VLCTime</a> *)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_time.html#a936e42eed02e03b404f14bdbce13d06f">timeWithInt:</a></td></tr>
<tr class="separator:a936e42eed02e03b404f14bdbce13d06f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a66fccafd32f7627fc119318a6c811394"><td class="memItemLeft" align="right" valign="top">NSNumber *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_time.html#a66fccafd32f7627fc119318a6c811394">value</a></td></tr>
<tr class="memdesc:a66fccafd32f7627fc119318a6c811394"><td class="mdescLeft">&#160;</td><td class="mdescRight">Holds, in milliseconds, the <a class="el" href="interface_v_l_c_time.html">VLCTime</a> value.  <a href="interface_v_l_c_time.html#a66fccafd32f7627fc119318a6c811394">More...</a><br /></td></tr>
<tr class="separator:a66fccafd32f7627fc119318a6c811394"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7aa23f16fef10097af66ac4d9754d0dc"><td class="memItemLeft" align="right" valign="top">NSString *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_time.html#a7aa23f16fef10097af66ac4d9754d0dc">stringValue</a></td></tr>
<tr class="separator:a7aa23f16fef10097af66ac4d9754d0dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acde55e3e8f75f7242f4fbf5c11e64743"><td class="memItemLeft" align="right" valign="top">NSString *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_time.html#acde55e3e8f75f7242f4fbf5c11e64743">verboseStringValue</a></td></tr>
<tr class="separator:acde55e3e8f75f7242f4fbf5c11e64743"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa6d4bce56be3df94a0896763f1769921"><td class="memItemLeft" align="right" valign="top">NSString *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_time.html#aa6d4bce56be3df94a0896763f1769921">minuteStringValue</a></td></tr>
<tr class="separator:aa6d4bce56be3df94a0896763f1769921"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aff49484cae12d9fbf65ce6b4a7da2cfd"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_time.html#aff49484cae12d9fbf65ce6b4a7da2cfd">intValue</a></td></tr>
<tr class="separator:aff49484cae12d9fbf65ce6b4a7da2cfd"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Provides an object to define <a class="el" href="interface_v_l_c_media.html">VLCMedia</a>'s time. </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="a4614e337e6304712ddd9182861e347ed"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4614e337e6304712ddd9182861e347ed">&#9670;&nbsp;</a></span>__attribute__</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSNumber* numberValue) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>the current time value as NSNumber </p><dl class="section return"><dt>Returns</dt><dd>the NSNumber object </dd></dl>
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000032">Deprecated:</a></b></dt><dd>use value instead </dd></dl>

</div>
</div>
<a id="a4bde16fb3441c6e7ff40a75e2f206148"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4bde16fb3441c6e7ff40a75e2f206148">&#9670;&nbsp;</a></span>compare:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSComparisonResult) compare: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_time.html">VLCTime</a> *)&#160;</td>
          <td class="paramname"><em>aTime</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>compare the current <a class="el" href="interface_v_l_c_time.html">VLCTime</a> instance against another instance </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aTime</td><td>the <a class="el" href="interface_v_l_c_time.html">VLCTime</a> instance to compare against </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>a NSComparisonResult </dd></dl>

</div>
</div>
<a id="a5f24e1e825a0676c3da2f6a3d5bc0a13"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f24e1e825a0676c3da2f6a3d5bc0a13">&#9670;&nbsp;</a></span>hash</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSUInteger) hash </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Calculcate a unique hash for the current time instance </p><dl class="section return"><dt>Returns</dt><dd>a hash value </dd></dl>

</div>
</div>
<a id="ac9717d5978ab18fa6547cde44904cfad"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac9717d5978ab18fa6547cde44904cfad">&#9670;&nbsp;</a></span>initWithInt:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithInt: </td>
          <td></td>
          <td class="paramtype">(int)&#160;</td>
          <td class="paramname"><em>aInt</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>init a time object with a given integer </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aInt</td><td>the int with a time in milliseconds </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the <a class="el" href="interface_v_l_c_time.html">VLCTime</a> object </dd></dl>

</div>
</div>
<a id="a4681ebad8d27e7243ebf6d83679e8a24"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4681ebad8d27e7243ebf6d83679e8a24">&#9670;&nbsp;</a></span>initWithNumber:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithNumber: </td>
          <td></td>
          <td class="paramtype">(NSNumber *)&#160;</td>
          <td class="paramname"><em>aNumber</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>init a time object with a given number object </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aNumber</td><td>the NSNumber object with a time in milliseconds </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the <a class="el" href="interface_v_l_c_time.html">VLCTime</a> object </dd></dl>

</div>
</div>
<a id="a02ff06b1610b86d9bb1770a93837be42"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a02ff06b1610b86d9bb1770a93837be42">&#9670;&nbsp;</a></span>isEqual:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) isEqual: </td>
          <td></td>
          <td class="paramtype">(id)&#160;</td>
          <td class="paramname"><em>object</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>compare the current <a class="el" href="interface_v_l_c_time.html">VLCTime</a> instance against another instance </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">object</td><td>the <a class="el" href="interface_v_l_c_time.html">VLCTime</a> instance to compare against </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>a BOOL whether the instances are equal or not </dd></dl>

</div>
</div>
<a id="ae4b7157f2152d3eddafe14e126a4b96c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae4b7157f2152d3eddafe14e126a4b96c">&#9670;&nbsp;</a></span>nullTime</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (<a class="el" href="interface_v_l_c_time.html">VLCTime</a> *) nullTime </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>factorize an empty time object </p><dl class="section return"><dt>Returns</dt><dd>the <a class="el" href="interface_v_l_c_time.html">VLCTime</a> object </dd></dl>

</div>
</div>
<a id="a936e42eed02e03b404f14bdbce13d06f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a936e42eed02e03b404f14bdbce13d06f">&#9670;&nbsp;</a></span>timeWithInt:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (<a class="el" href="interface_v_l_c_time.html">VLCTime</a> *) timeWithInt: </td>
          <td></td>
          <td class="paramtype">(int)&#160;</td>
          <td class="paramname"><em>aInt</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>factorize a time object with a given integer </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aInt</td><td>the int with a time in milliseconds </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the <a class="el" href="interface_v_l_c_time.html">VLCTime</a> object </dd></dl>

</div>
</div>
<a id="af9b7c469a7cb064a3d9c2c5bcaa769bb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af9b7c469a7cb064a3d9c2c5bcaa769bb">&#9670;&nbsp;</a></span>timeWithNumber:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (<a class="el" href="interface_v_l_c_time.html">VLCTime</a> *) timeWithNumber: </td>
          <td></td>
          <td class="paramtype">(NSNumber *)&#160;</td>
          <td class="paramname"><em>aNumber</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>factorize a time object with a given number object </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aNumber</td><td>the NSNumber object with a time in milliseconds </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the <a class="el" href="interface_v_l_c_time.html">VLCTime</a> object </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="aff49484cae12d9fbf65ce6b4a7da2cfd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aff49484cae12d9fbf65ce6b4a7da2cfd">&#9670;&nbsp;</a></span>intValue</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) intValue</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>the current time value as int value </p><dl class="section return"><dt>Returns</dt><dd>the int </dd></dl>

</div>
</div>
<a id="aa6d4bce56be3df94a0896763f1769921"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa6d4bce56be3df94a0896763f1769921">&#9670;&nbsp;</a></span>minuteStringValue</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString*) minuteStringValue</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>the current time value as string value localized for the current environment representing the time in minutes </p><dl class="section return"><dt>Returns</dt><dd>the NSString object </dd></dl>

</div>
</div>
<a id="a7aa23f16fef10097af66ac4d9754d0dc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7aa23f16fef10097af66ac4d9754d0dc">&#9670;&nbsp;</a></span>stringValue</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString*) stringValue</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>the current time value as string value localized for the current environment </p><dl class="section return"><dt>Returns</dt><dd>the NSString object </dd></dl>

</div>
</div>
<a id="a66fccafd32f7627fc119318a6c811394"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a66fccafd32f7627fc119318a6c811394">&#9670;&nbsp;</a></span>value</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSNumber*) value</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

<p>Holds, in milliseconds, the <a class="el" href="interface_v_l_c_time.html">VLCTime</a> value. </p>
<p>the current time value as NSNumber </p><dl class="section return"><dt>Returns</dt><dd>the NSNumber object </dd></dl>

</div>
</div>
<a id="acde55e3e8f75f7242f4fbf5c11e64743"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acde55e3e8f75f7242f4fbf5c11e64743">&#9670;&nbsp;</a></span>verboseStringValue</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString*) verboseStringValue</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>the current time value as verbose string value localized for the current environment </p><dl class="section return"><dt>Returns</dt><dd>the NSString object </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_time_8h_source.html">VLCTime.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
