<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCMediaListPlayer Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_media_list_player-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCMediaListPlayer Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_media_list_player_8h_source.html">VLCMediaListPlayer.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCMediaListPlayer:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_media_list_player.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a39e3151a069ead5d9bdc4bd1d8b0e815"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list_player.html#a39e3151a069ead5d9bdc4bd1d8b0e815">initWithDrawable:</a></td></tr>
<tr class="separator:a39e3151a069ead5d9bdc4bd1d8b0e815"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2026b15a0a74d3be733346074f44ba18"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list_player.html#a2026b15a0a74d3be733346074f44ba18">initWithOptions:</a></td></tr>
<tr class="separator:a2026b15a0a74d3be733346074f44ba18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9229793245755c2bf82cedf7d4032a90"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list_player.html#a9229793245755c2bf82cedf7d4032a90">initWithOptions:andDrawable:</a></td></tr>
<tr class="separator:a9229793245755c2bf82cedf7d4032a90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2f22768be90786ad490b4c3ee07900c9"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list_player.html#a2f22768be90786ad490b4c3ee07900c9">play</a></td></tr>
<tr class="separator:a2f22768be90786ad490b4c3ee07900c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac0e7b5153919a1108723be359773f7f9"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list_player.html#ac0e7b5153919a1108723be359773f7f9">pause</a></td></tr>
<tr class="separator:ac0e7b5153919a1108723be359773f7f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab4473d33d43e75b4c73fd9ed1ec5cc2c"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list_player.html#ab4473d33d43e75b4c73fd9ed1ec5cc2c">stop</a></td></tr>
<tr class="separator:ab4473d33d43e75b4c73fd9ed1ec5cc2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b70c60e898b6ac244486fe11f8ec6b3"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list_player.html#a0b70c60e898b6ac244486fe11f8ec6b3">playItemAtIndex:</a></td></tr>
<tr class="separator:a0b70c60e898b6ac244486fe11f8ec6b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08850682934ccc36da966054281b34d3"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list_player.html#a08850682934ccc36da966054281b34d3">playItemAtNumber:</a></td></tr>
<tr class="separator:a08850682934ccc36da966054281b34d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a001f13e8971cb4073dd4c148192a23a9"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_list_player.html#a001f13e8971cb4073dd4c148192a23a9">playMedia:</a></td></tr>
<tr class="separator:a001f13e8971cb4073dd4c148192a23a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a9a58ef35e80015f0441ae1861856a2bf"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_list_player.html#a9a58ef35e80015f0441ae1861856a2bf">mediaList</a></td></tr>
<tr class="separator:a9a58ef35e80015f0441ae1861856a2bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac1a1b1d304efb4e1c62026f9ed321e9d"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_list_player.html#ac1a1b1d304efb4e1c62026f9ed321e9d">rootMedia</a></td></tr>
<tr class="separator:ac1a1b1d304efb4e1c62026f9ed321e9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77a18025616fa5fa2d06a539f6eea9f3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_list_player.html#a77a18025616fa5fa2d06a539f6eea9f3">mediaPlayer</a></td></tr>
<tr class="separator:a77a18025616fa5fa2d06a539f6eea9f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a122c940f85ebdc82eae8d029d106b512"><td class="memItemLeft" align="right" valign="top">id&lt; <a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html">VLCMediaListPlayerDelegate</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_list_player.html#a122c940f85ebdc82eae8d029d106b512">delegate</a></td></tr>
<tr class="separator:a122c940f85ebdc82eae8d029d106b512"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9443120446a79cd785b1e2b71882f199"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_list_player.html#a9443120446a79cd785b1e2b71882f199">next</a></td></tr>
<tr class="separator:a9443120446a79cd785b1e2b71882f199"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afae8b92f265eeec5b87bd55bd0fd104c"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_list_player.html#afae8b92f265eeec5b87bd55bd0fd104c">previous</a></td></tr>
<tr class="separator:afae8b92f265eeec5b87bd55bd0fd104c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7f868c26a93d999852c3448d28913af4"><td class="memItemLeft" align="right" valign="top">VLCRepeatMode&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_list_player.html#a7f868c26a93d999852c3448d28913af4">repeatMode</a></td></tr>
<tr class="separator:a7f868c26a93d999852c3448d28913af4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>A media list player, which eases the use of playlists </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="a39e3151a069ead5d9bdc4bd1d8b0e815"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a39e3151a069ead5d9bdc4bd1d8b0e815">&#9670;&nbsp;</a></span>initWithDrawable:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithDrawable: </td>
          <td></td>
          <td class="paramtype">(id)&#160;</td>
          <td class="paramname"><em>drawable</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>initializer with a certain drawable </p><dl class="section note"><dt>Note</dt><dd>drawable can also be set later </dd></dl>

</div>
</div>
<a id="a2026b15a0a74d3be733346074f44ba18"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2026b15a0a74d3be733346074f44ba18">&#9670;&nbsp;</a></span>initWithOptions:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithOptions: </td>
          <td></td>
          <td class="paramtype">(NSArray *)&#160;</td>
          <td class="paramname"><em>options</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>initializer with a custom options </p><dl class="section note"><dt>Note</dt><dd>This is will initialize a new <a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> instance, which WILL have a major memory impact </dd></dl>

</div>
</div>
<a id="a9229793245755c2bf82cedf7d4032a90"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9229793245755c2bf82cedf7d4032a90">&#9670;&nbsp;</a></span>initWithOptions:andDrawable:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) <a class="el" href="interface_v_l_c_media_list_player.html#a2026b15a0a74d3be733346074f44ba18">initWithOptions:</a> </td>
          <td></td>
          <td class="paramtype">(NSArray *)&#160;</td>
          <td class="paramname"><em>options</em></td>
        </tr>
        <tr>
          <td class="paramkey">andDrawable:</td>
          <td></td>
          <td class="paramtype">(id)&#160;</td>
          <td class="paramname"><em>drawable</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>initializer with a certain drawable and options </p><dl class="section see"><dt>See also</dt><dd>initWithDrawable </dd>
<dd>
initWithOptions </dd></dl>

</div>
</div>
<a id="ac0e7b5153919a1108723be359773f7f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac0e7b5153919a1108723be359773f7f9">&#9670;&nbsp;</a></span>pause</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) pause </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>pause playback </p>

</div>
</div>
<a id="a2f22768be90786ad490b4c3ee07900c9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2f22768be90786ad490b4c3ee07900c9">&#9670;&nbsp;</a></span>play</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) play </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>start playback </p>

</div>
</div>
<a id="a0b70c60e898b6ac244486fe11f8ec6b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0b70c60e898b6ac244486fe11f8ec6b3">&#9670;&nbsp;</a></span>playItemAtIndex:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) playItemAtIndex: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>play an item at at a given index in the media list attached to the player </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000009">Deprecated:</a></b></dt><dd>This method is not thread safe. Use playItemAtNumber: instead </dd></dl>

</div>
</div>
<a id="a08850682934ccc36da966054281b34d3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a08850682934ccc36da966054281b34d3">&#9670;&nbsp;</a></span>playItemAtNumber:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) playItemAtNumber: </td>
          <td></td>
          <td class="paramtype">(NSNumber *)&#160;</td>
          <td class="paramname"><em>index</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>play an item at a given index in the media list attached to the player </p>

</div>
</div>
<a id="a001f13e8971cb4073dd4c148192a23a9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a001f13e8971cb4073dd4c148192a23a9">&#9670;&nbsp;</a></span>playMedia:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) playMedia: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *)&#160;</td>
          <td class="paramname"><em>media</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>media must be in the current media list. </p>

</div>
</div>
<a id="ab4473d33d43e75b4c73fd9ed1ec5cc2c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab4473d33d43e75b4c73fd9ed1ec5cc2c">&#9670;&nbsp;</a></span>stop</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) stop </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>stop playback </p>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a122c940f85ebdc82eae8d029d106b512"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a122c940f85ebdc82eae8d029d106b512">&#9670;&nbsp;</a></span>delegate</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (id&lt;<a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html">VLCMediaListPlayerDelegate</a>&gt;) delegate</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">weak</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Receiver's delegate </p>

</div>
</div>
<a id="a9a58ef35e80015f0441ae1861856a2bf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9a58ef35e80015f0441ae1861856a2bf">&#9670;&nbsp;</a></span>mediaList</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a>*) mediaList</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>setter/getter for mediaList to play within the player </p><dl class="section note"><dt>Note</dt><dd>This list is erased when setting a rootMedia on the list player instance </dd></dl>

</div>
</div>
<a id="a77a18025616fa5fa2d06a539f6eea9f3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a77a18025616fa5fa2d06a539f6eea9f3">&#9670;&nbsp;</a></span>mediaPlayer</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a>*) mediaPlayer</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>the media player instance used for playback by the list player </p>

</div>
</div>
<a id="a9443120446a79cd785b1e2b71882f199"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9443120446a79cd785b1e2b71882f199">&#9670;&nbsp;</a></span>next</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) next</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>play next item </p><dl class="section return"><dt>Returns</dt><dd>YES on success, NO if there is no such item </dd></dl>

</div>
</div>
<a id="afae8b92f265eeec5b87bd55bd0fd104c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afae8b92f265eeec5b87bd55bd0fd104c">&#9670;&nbsp;</a></span>previous</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) previous</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>play previous item </p><dl class="section return"><dt>Returns</dt><dd>YES on success, NO if there is no such item </dd></dl>

</div>
</div>
<a id="a7f868c26a93d999852c3448d28913af4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7f868c26a93d999852c3448d28913af4">&#9670;&nbsp;</a></span>repeatMode</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (VLCRepeatMode) repeatMode</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Playmode selection (don't repeat anything, repeat one, repeat all) See VLCRepeatMode. </p>

</div>
</div>
<a id="ac1a1b1d304efb4e1c62026f9ed321e9d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac1a1b1d304efb4e1c62026f9ed321e9d">&#9670;&nbsp;</a></span>rootMedia</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_media.html">VLCMedia</a>*) rootMedia</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>rootMedia - Use this method to play a media and its subitems. This will erase mediaList. Setting mediaList will erase rootMedia. </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_media_list_player_8h_source.html">VLCMediaListPlayer.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
