<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Members - Variables</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;<ul>
<li>VLCChapterDescriptionDuration
: <a class="el" href="interface_v_l_c_media_player.html#a5d87f02211d47497f35bf616f7a0374e">VLCMediaPlayer</a>
</li>
<li>VLCChapterDescriptionName
: <a class="el" href="interface_v_l_c_media_player.html#a20081eb2719b21bd55b4d2ce185f86f2">VLCMediaPlayer</a>
</li>
<li>VLCChapterDescriptionTimeOffset
: <a class="el" href="interface_v_l_c_media_player.html#ab5d7190ce89c08ae1b14e6c2a827d104">VLCMediaPlayer</a>
</li>
<li>VLCMediaTracksInformationAudioChannelsNumber
: <a class="el" href="interface_v_l_c_media.html#aabf94e7de92ae328dba46d6c53e5d869">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationAudioRate
: <a class="el" href="interface_v_l_c_media.html#a5f42247ad4cefc2cfa4a96bd95f53356">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationBitrate
: <a class="el" href="interface_v_l_c_media.html#add3bac6827f60b1cbe44544c106b39c0">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationCodec
: <a class="el" href="interface_v_l_c_media.html#a0e44431952021460c5f59f600236630b">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationCodecLevel
: <a class="el" href="interface_v_l_c_media.html#ae380aafa86ebd25ad38ab630a6dc86dd">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationCodecProfile
: <a class="el" href="interface_v_l_c_media.html#a076812e00bd51440c4d47da823011f86">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationDescription
: <a class="el" href="interface_v_l_c_media.html#a480f68a87f30c723f9364f00620de519">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationFrameRate
: <a class="el" href="interface_v_l_c_media.html#afd5e8623f3246506f21576ca006df47e">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationFrameRateDenominator
: <a class="el" href="interface_v_l_c_media.html#a635234c93bcb43393868435ab98ad0a8">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationId
: <a class="el" href="interface_v_l_c_media.html#a523d5f9351c2fcac0d9b600773734c81">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationLanguage
: <a class="el" href="interface_v_l_c_media.html#ac6879d1635a7c5c306bafc23cbed755a">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationSourceAspectRatio
: <a class="el" href="interface_v_l_c_media.html#ada9d9ba5acf71414913ecc83cb975bf6">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationSourceAspectRatioDenominator
: <a class="el" href="interface_v_l_c_media.html#aa3b1ead249368c4b73a544f07c84bcdc">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationTextEncoding
: <a class="el" href="interface_v_l_c_media.html#ad173cd33fb9d51175e676b62838cd980">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationType
: <a class="el" href="interface_v_l_c_media.html#ac5ccaa4e433a8bc847e54739d69827b7">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationTypeAudio
: <a class="el" href="interface_v_l_c_media.html#ac6eb47ffe2b3a79f562e0164e83416b1">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationTypeText
: <a class="el" href="interface_v_l_c_media.html#a3d8f7f156478e43c45dfedf7459c9939">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationTypeUnknown
: <a class="el" href="interface_v_l_c_media.html#a2b91c4e456c6ce07682477d41772adc2">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationTypeVideo
: <a class="el" href="interface_v_l_c_media.html#a2ac631a7a8c7416ac9a13b914efeb22e">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationVideoHeight
: <a class="el" href="interface_v_l_c_media.html#a52414aa5aff9e0e929d6b3dad0461dd2">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationVideoOrientation
: <a class="el" href="interface_v_l_c_media.html#a32e842b07314f6b7a965fd8d5770bf8d">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationVideoProjection
: <a class="el" href="interface_v_l_c_media.html#ac28eab6679f8761ce13ea02d61562d21">VLCMedia</a>
</li>
<li>VLCMediaTracksInformationVideoWidth
: <a class="el" href="interface_v_l_c_media.html#a29ca0a5036cfa556f5d7098c44030123">VLCMedia</a>
</li>
<li>VLCTitleDescriptionDuration
: <a class="el" href="interface_v_l_c_media_player.html#ac4bd07aad0599f2f61cbac7281981df7">VLCMediaPlayer</a>
</li>
<li>VLCTitleDescriptionIsMenu
: <a class="el" href="interface_v_l_c_media_player.html#a1cfbed633aa7783841c153d48088ba70">VLCMediaPlayer</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
