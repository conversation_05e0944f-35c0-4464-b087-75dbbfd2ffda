<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Headers/Public/VLCMediaListPlayer.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html">Headers</a></li><li class="navelem"><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html">Public</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCMediaListPlayer.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/*****************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * VLCMediaListPlayer.h: VLCKit.framework VLCMediaListPlayer implementation</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *****************************************************************************</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Copyright (C) 2009 Pierre d&#39;Herbemont</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * Partial Copyright (C) 2009-2013 Felix Paul Kühne</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> * Copyright (C) 2009-2019 VLC authors and VideoLAN</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"></span> </div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> * $Id$</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> * Authors: <AUTHORS>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *          Felix Paul Kühne &lt;fkuehne # videolan.org&gt;</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *          Soomin Lee &lt;bubu # mikan.io&gt;</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> * This program is free software; you can redistribute it and/or modify it</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * under the terms of the GNU Lesser General Public License as published by</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * the Free Software Foundation; either version 2.1 of the License, or</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * (at your option) any later version.</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * This program is distributed in the hope that it will be useful,</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * but WITHOUT ANY WARRANTY; without even the implied warranty of</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * GNU Lesser General Public License for more details.</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> * You should have received a copy of the GNU Lesser General Public License</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="comment"> * along with this program; if not, write to the Free Software Foundation,</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="comment"> * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="comment"> *****************************************************************************/</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160; </div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media.html">VLCMedia</a>, <a class="code" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a>, <a class="code" href="interface_v_l_c_media_list.html">VLCMediaList</a>, <a class="code" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a>;</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160; </div>
<div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="protocol_v_l_c_media_list_player_delegate-p.html#a22d8350c9779fb7bcdf30cc1076a7073">   35</a></span>&#160;<span class="keyword">typedef</span> NS_ENUM(NSInteger, VLCRepeatMode) {</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;    VLCDoNotRepeat,</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;    VLCRepeatCurrentItem,</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;    VLCRepeatAllItems</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;};</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160; </div>
<div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="protocol_v_l_c_media_list_player_delegate-p.html">   41</a></span>&#160;<span class="keyword">@protocol </span><a class="code" href="protocol_v_l_c_media_list_player_delegate-p.html">VLCMediaListPlayerDelegate</a> &lt;NSObject&gt;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="keyword">@optional</span></div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;- (void)mediaListPlayerFinishedPlayback:(<a class="code" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a> *)player;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160; </div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;- (void)mediaListPlayer:(<a class="code" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a> *)player</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;              nextMedia:(<a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *)media;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160; </div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;- (void)mediaListPlayerStopped:(<a class="code" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a> *)player;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160; </div>
<div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list_player.html">   65</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a> : NSObject</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160; </div>
<div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list_player.html#a9a58ef35e80015f0441ae1861856a2bf">   71</a></span>&#160;<span class="keyword">@property</span> (readwrite) <a class="code" href="interface_v_l_c_media_list.html">VLCMediaList</a> *<a class="code" href="interface_v_l_c_media_list_player.html#a9a58ef35e80015f0441ae1861856a2bf">mediaList</a>;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160; </div>
<div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list_player.html#ac1a1b1d304efb4e1c62026f9ed321e9d">   78</a></span>&#160;<span class="keyword">@property</span> (readwrite) <a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *<a class="code" href="interface_v_l_c_media_list_player.html#ac1a1b1d304efb4e1c62026f9ed321e9d">rootMedia</a>;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160; </div>
<div class="line"><a name="l00083"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list_player.html#a77a18025616fa5fa2d06a539f6eea9f3">   83</a></span>&#160;<span class="keyword">@property</span> (readonly) <a class="code" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> *<a class="code" href="interface_v_l_c_media_list_player.html#a77a18025616fa5fa2d06a539f6eea9f3">mediaPlayer</a>;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160; </div>
<div class="line"><a name="l00088"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list_player.html#ab4473d33d43e75b4c73fd9ed1ec5cc2c">   88</a></span>&#160;<span class="keyword">@property</span> (nonatomic, weak) id &lt;VLCMediaListPlayerDelegate&gt; <a class="code" href="interface_v_l_c_media_list_player.html#a122c940f85ebdc82eae8d029d106b512">delegate</a>;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160; </div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;- (instancetype)initWithDrawable:(<span class="keywordtype">id</span>)drawable;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;- (instancetype)initWithOptions:(NSArray *)options;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;- (instancetype)initWithOptions:(NSArray *)options andDrawable:(<span class="keywordtype">id</span>)drawable;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160; </div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;- (void)<a class="code" href="interface_v_l_c_media_list_player.html#a2f22768be90786ad490b4c3ee07900c9">play</a>;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;- (void)<a class="code" href="interface_v_l_c_media_list_player.html#ac0e7b5153919a1108723be359773f7f9">pause</a>;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;- (void)<a class="code" href="interface_v_l_c_media_list_player.html#ab4473d33d43e75b4c73fd9ed1ec5cc2c">stop</a>;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160; </div>
<div class="line"><a name="l00124"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list_player.html#a9443120446a79cd785b1e2b71882f199">  124</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) BOOL <a class="code" href="interface_v_l_c_media_list_player.html#a9443120446a79cd785b1e2b71882f199">next</a>;</div>
<div class="line"><a name="l00129"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list_player.html#afae8b92f265eeec5b87bd55bd0fd104c">  129</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) BOOL <a class="code" href="interface_v_l_c_media_list_player.html#afae8b92f265eeec5b87bd55bd0fd104c">previous</a>;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160; </div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;- (BOOL)playItemAtIndex:(<span class="keywordtype">int</span>)index  __attribute__((deprecated));</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160; </div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;- (void)playItemAtNumber:(NSNumber *)index;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160; </div>
<div class="line"><a name="l00146"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list_player.html#a7f868c26a93d999852c3448d28913af4">  146</a></span>&#160;<span class="keyword">@property</span> (readwrite) VLCRepeatMode <a class="code" href="interface_v_l_c_media_list_player.html#a7f868c26a93d999852c3448d28913af4">repeatMode</a>;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160; </div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;- (void)playMedia:(<a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *)media;</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160; </div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;<span class="keyword">@end</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="ainterface_v_l_c_media_list_player_html_a2f22768be90786ad490b4c3ee07900c9"><div class="ttname"><a href="interface_v_l_c_media_list_player.html#a2f22768be90786ad490b4c3ee07900c9">-[VLCMediaListPlayer play]</a></div><div class="ttdeci">void play()</div></div>
<div class="ttc" id="aprotocol_v_l_c_media_list_player_delegate-p_html"><div class="ttname"><a href="protocol_v_l_c_media_list_player_delegate-p.html">VLCMediaListPlayerDelegate-p</a></div><div class="ttdef"><b>Definition:</b> VLCMediaListPlayer.h:41</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_player_html_a7f868c26a93d999852c3448d28913af4"><div class="ttname"><a href="interface_v_l_c_media_list_player.html#a7f868c26a93d999852c3448d28913af4">VLCMediaListPlayer::repeatMode</a></div><div class="ttdeci">VLCRepeatMode repeatMode</div><div class="ttdef"><b>Definition:</b> VLCMediaListPlayer.h:146</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_html"><div class="ttname"><a href="interface_v_l_c_media_list.html">VLCMediaList</a></div><div class="ttdef"><b>Definition:</b> VLCMediaList.h:68</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html"><div class="ttname"><a href="interface_v_l_c_media.html">VLCMedia</a></div><div class="ttdef"><b>Definition:</b> VLCMedia.h:113</div></div>
<div class="ttc" id="ainterface_v_l_c_media_player_html"><div class="ttname"><a href="interface_v_l_c_media_player.html">VLCMediaPlayer</a></div><div class="ttdef"><b>Definition:</b> VLCMediaPlayer.h:186</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_player_html_a122c940f85ebdc82eae8d029d106b512"><div class="ttname"><a href="interface_v_l_c_media_list_player.html#a122c940f85ebdc82eae8d029d106b512">VLCMediaListPlayer::delegate</a></div><div class="ttdeci">id&lt; VLCMediaListPlayerDelegate &gt; delegate</div><div class="ttdef"><b>Definition:</b> VLCMediaListPlayer.h:88</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_player_html_a9a58ef35e80015f0441ae1861856a2bf"><div class="ttname"><a href="interface_v_l_c_media_list_player.html#a9a58ef35e80015f0441ae1861856a2bf">VLCMediaListPlayer::mediaList</a></div><div class="ttdeci">VLCMediaList * mediaList</div><div class="ttdef"><b>Definition:</b> VLCMediaListPlayer.h:71</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_player_html"><div class="ttname"><a href="interface_v_l_c_media_list_player.html">VLCMediaListPlayer</a></div><div class="ttdef"><b>Definition:</b> VLCMediaListPlayer.h:66</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_player_html_a77a18025616fa5fa2d06a539f6eea9f3"><div class="ttname"><a href="interface_v_l_c_media_list_player.html#a77a18025616fa5fa2d06a539f6eea9f3">VLCMediaListPlayer::mediaPlayer</a></div><div class="ttdeci">VLCMediaPlayer * mediaPlayer</div><div class="ttdef"><b>Definition:</b> VLCMediaListPlayer.h:83</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_player_html_ab4473d33d43e75b4c73fd9ed1ec5cc2c"><div class="ttname"><a href="interface_v_l_c_media_list_player.html#ab4473d33d43e75b4c73fd9ed1ec5cc2c">-[VLCMediaListPlayer stop]</a></div><div class="ttdeci">void stop()</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_player_html_ac1a1b1d304efb4e1c62026f9ed321e9d"><div class="ttname"><a href="interface_v_l_c_media_list_player.html#ac1a1b1d304efb4e1c62026f9ed321e9d">VLCMediaListPlayer::rootMedia</a></div><div class="ttdeci">VLCMedia * rootMedia</div><div class="ttdef"><b>Definition:</b> VLCMediaListPlayer.h:78</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_player_html_ac0e7b5153919a1108723be359773f7f9"><div class="ttname"><a href="interface_v_l_c_media_list_player.html#ac0e7b5153919a1108723be359773f7f9">-[VLCMediaListPlayer pause]</a></div><div class="ttdeci">void pause()</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_player_html_a9443120446a79cd785b1e2b71882f199"><div class="ttname"><a href="interface_v_l_c_media_list_player.html#a9443120446a79cd785b1e2b71882f199">VLCMediaListPlayer::next</a></div><div class="ttdeci">BOOL next</div><div class="ttdef"><b>Definition:</b> VLCMediaListPlayer.h:124</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_player_html_afae8b92f265eeec5b87bd55bd0fd104c"><div class="ttname"><a href="interface_v_l_c_media_list_player.html#afae8b92f265eeec5b87bd55bd0fd104c">VLCMediaListPlayer::previous</a></div><div class="ttdeci">BOOL previous</div><div class="ttdef"><b>Definition:</b> VLCMediaListPlayer.h:129</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
