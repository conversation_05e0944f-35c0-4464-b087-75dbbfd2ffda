<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Deprecated List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="PageDoc"><div class="header">
  <div class="headertitle">
<div class="title">Deprecated List </div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><dl class="reflist">
<dt>Member <a class="el" href="interface_v_l_c_audio.html#ab5c770cd553794ec294c77fd4e56668d">[VLCAudio setMute:]</a>  </dt>
<dd><a class="anchor" id="_deprecated000001"></a>This selector will be removed in the next release  </dd>
<dt>Member <a class="el" href="interface_v_l_c_media.html#ac32a90c64851638af38108040b37e454">[VLCMedia __attribute__]</a>  </dt>
<dd><a class="anchor" id="_deprecated000002"></a>use parseStatus instead  </dd>
<dt>Member <a class="el" href="interface_v_l_c_media.html#a28c23c5d427727732476f86c6d0645ee">[VLCMedia __attribute__]</a>  </dt>
<dd><p class="startdd"><a class="anchor" id="_deprecated000003"></a>Use parseWithOptions: instead </p>
<p class="enddd"><a class="anchor" id="_deprecated000004"></a>Use parseWithOptions: instead  </p>
</dd>
<dt>Member <a class="el" href="interface_v_l_c_media_discoverer.html#a57b40dcdb0b650aea2feed95fdd30d83">[VLCMediaDiscoverer __attribute__]</a>  </dt>
<dd><a class="anchor" id="_deprecated000005"></a>use availableMediaDiscovererForCategoryType instead  </dd>
<dt>Member <a class="el" href="interface_v_l_c_media_discoverer.html#abefec909660b69a00410cab13c2d9a5b">[VLCMediaDiscoverer __attribute__]</a>  </dt>
<dd><a class="anchor" id="_deprecated000006"></a>Will be removed in the next major release, may return an empty string for binary compatibility  </dd>
<dt>Member <a class="el" href="interface_v_l_c_media_library.html#afbb02ac470223ee5bebba45220326675">[VLCMediaLibrary __attribute__]</a>  </dt>
<dd><a class="anchor" id="_deprecated000007"></a>will be removed in the next release  </dd>
<dt>Member <a class="el" href="interface_v_l_c_media_library.html#ab7a54a9b8754b31a7f19b6bbf2b65df5">[VLCMediaLibrary __attribute__]</a>  </dt>
<dd><a class="anchor" id="_deprecated000008"></a>will be removed in the next release  </dd>
<dt>Member <a class="el" href="interface_v_l_c_media_list_player.html#a0b70c60e898b6ac244486fe11f8ec6b3">[VLCMediaListPlayer playItemAtIndex:]</a>  </dt>
<dd><a class="anchor" id="_deprecated000009"></a>This method is not thread safe. Use playItemAtNumber: instead  </dd>
<dt>Member <a class="el" href="interface_v_l_c_media_player.html#ab59f33aa946850a6387d87630c2cca16">[VLCMediaPlayer __attribute__]</a>  </dt>
<dd><a class="anchor" id="_deprecated000010"></a>provided for API compatibility only, to retrieve a media's FPS, use VLCMediaTracksInformationFrameRate.  </dd>
<dt>Member <a class="el" href="interface_v_l_c_media_player.html#a18546cd8ca1b827eb5ddb9384e172166">[VLCMediaPlayer openVideoSubTitlesFromFile:]</a>  </dt>
<dd><a class="anchor" id="_deprecated000011"></a>use addPlaybackSlave:type:enforce: instead  </dd>
<dt>Member <a class="el" href="interface_v_l_c_media_player.html#a59fbfd5a29004a32b9f64328ad6b57a4">[VLCMediaPlayer chaptersForTitleIndex:]</a>  </dt>
<dd><a class="anchor" id="_deprecated000012"></a>Use chapterDescriptionsOfTitle instead  </dd>
<dt>Member <a class="el" href="interface_v_l_c_media_player.html#aae0b500274a748ac5c035c2a3c46c366">[VLCMediaPlayer __attribute__]</a>  </dt>
<dd><p class="startdd"><a class="anchor" id="_deprecated000013"></a>Use numberOfTitles instead </p>
<p class="enddd"><a class="anchor" id="_deprecated000014"></a>Use titleDescriptions instead  </p>
</dd>
<dt>Member <a class="el" href="interface_v_l_c_stream_output.html#a11c02ad55e225c5afa473aeb15db13e0">[VLCStreamOutput initWithOptionDictionary:]</a>  </dt>
<dd><a class="anchor" id="_deprecated000018"></a>will be removed in the next release  </dd>
<dt>Member <a class="el" href="interface_v_l_c_stream_output.html#ae31bb946515ad904af62b14ad4c0174c">[VLCStreamOutput streamOutputWithOptionDictionary:]</a>  </dt>
<dd><a class="anchor" id="_deprecated000019"></a>will be removed in the next release  </dd>
<dt>Member <a class="el" href="interface_v_l_c_stream_output.html#a37d21584a752b9ddaecd248d171bdf59">[VLCStreamOutput rtpBroadcastStreamOutputWithSAPAnnounce:]</a>  </dt>
<dd><a class="anchor" id="_deprecated000020"></a>will be removed in the next release  </dd>
<dt>Member <a class="el" href="interface_v_l_c_stream_output.html#aa573b7c97a017789e58ab392812e3032">[VLCStreamOutput __attribute__]</a>  </dt>
<dd><a class="anchor" id="_deprecated000021"></a>will be removed in the next release  </dd>
<dt>Member <a class="el" href="interface_v_l_c_stream_output.html#a518d4dd8d9b9733acd58dce332f8d004">[VLCStreamOutput ipodStreamOutputWithFilePath:]</a>  </dt>
<dd><a class="anchor" id="_deprecated000022"></a>will be removed in the next release  </dd>
<dt>Member <a class="el" href="interface_v_l_c_stream_output.html#abacda1a7636077fc336abde914fb4877">[VLCStreamOutput streamOutputWithFilePath:]</a>  </dt>
<dd><a class="anchor" id="_deprecated000023"></a>will be removed in the next release  </dd>
<dt>Member <a class="el" href="interface_v_l_c_stream_output.html#a1c1586457352350d81adbe686729eb92">[VLCStreamOutput mpeg2StreamOutputWithFilePath:]</a>  </dt>
<dd><a class="anchor" id="_deprecated000024"></a>will be removed in the next release  </dd>
<dt>Member <a class="el" href="interface_v_l_c_stream_output.html#a5c5ec3624e9cc31e1de8630e42430c40">[VLCStreamOutput mpeg4StreamOutputWithFilePath:]</a>  </dt>
<dd><a class="anchor" id="_deprecated000025"></a>will be removed in the next release  </dd>
<dt>Member <a class="el" href="interface_v_l_c_stream_session.html#a462740188cf07ef740c1669dcfd34c2b">[VLCStreamSession __attribute__]</a>  </dt>
<dd><a class="anchor" id="_deprecated000026"></a>will be removed in the next release  </dd>
<dt>Member <a class="el" href="interface_v_l_c_stream_session.html#afd34666df70b52b43ea64394796f4d52">[VLCStreamSession __attribute__]</a>  </dt>
<dd><p class="startdd"><a class="anchor" id="_deprecated000027"></a>will be removed in the next release </p>
<p class="interdd"><a class="anchor" id="_deprecated000028"></a>will be removed in the next release </p>
<p class="enddd"><a class="anchor" id="_deprecated000029"></a>will be removed in the next release  </p>
</dd>
<dt>Member <a class="el" href="interface_v_l_c_stream_session.html#a462740188cf07ef740c1669dcfd34c2b">[VLCStreamSession __attribute__]</a>  </dt>
<dd><p class="startdd"><a class="anchor" id="_deprecated000030"></a>will be removed in the next release </p>
<p class="enddd"><a class="anchor" id="_deprecated000031"></a>will be removed in the next release  </p>
</dd>
<dt>Member <a class="el" href="interface_v_l_c_time.html#a4614e337e6304712ddd9182861e347ed">[VLCTime __attribute__]</a>  </dt>
<dd><a class="anchor" id="_deprecated000032"></a>use value instead </dd>
</dl>
</div></div><!-- contents -->
</div><!-- PageDoc -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
