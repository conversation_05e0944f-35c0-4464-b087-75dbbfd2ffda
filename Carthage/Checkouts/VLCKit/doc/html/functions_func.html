<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Members - Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
&#160;

<h3><a id="index__5F"></a>- _ -</h3><ul>
<li>__attribute__
: <a class="el" href="interface_v_l_c_media_discoverer.html#a57b40dcdb0b650aea2feed95fdd30d83">VLCMediaDiscoverer</a>
, <a class="el" href="interface_v_l_c_media_library.html#afbb02ac470223ee5bebba45220326675">VLCMediaLibrary</a>
, <a class="el" href="interface_v_l_c_stream_output.html#aa573b7c97a017789e58ab392812e3032">VLCStreamOutput</a>
, <a class="el" href="interface_v_l_c_stream_session.html#a462740188cf07ef740c1669dcfd34c2b">VLCStreamSession</a>
, <a class="el" href="interface_v_l_c_media.html#a28c23c5d427727732476f86c6d0645ee">VLCMedia</a>
, <a class="el" href="interface_v_l_c_media_discoverer.html#abefec909660b69a00410cab13c2d9a5b">VLCMediaDiscoverer</a>
, <a class="el" href="interface_v_l_c_media_library.html#ab7a54a9b8754b31a7f19b6bbf2b65df5">VLCMediaLibrary</a>
, <a class="el" href="interface_v_l_c_media_player.html#aae0b500274a748ac5c035c2a3c46c366">VLCMediaPlayer</a>
, <a class="el" href="interface_v_l_c_stream_session.html#a462740188cf07ef740c1669dcfd34c2b">VLCStreamSession</a>
, <a class="el" href="interface_v_l_c_time.html#a4614e337e6304712ddd9182861e347ed">VLCTime</a>
</li>
</ul>


<h3><a id="index_a"></a>- a -</h3><ul>
<li>addMedia:
: <a class="el" href="interface_v_l_c_media_list.html#a42eb32becfd0b41b61094a8f37dd2ed0">VLCMediaList</a>
</li>
<li>addOption:
: <a class="el" href="interface_v_l_c_media.html#ab09b1de8ddcfae6c2ecc331777d54119">VLCMedia</a>
</li>
<li>addPlaybackSlave:type:enforce:
: <a class="el" href="interface_v_l_c_media_player.html#a9477125c5c85a4c598e4088c031331e2">VLCMediaPlayer</a>
</li>
<li>amplificationOfBand:
: <a class="el" href="interface_v_l_c_media_player.html#ab60ddfe3c2f9d1943e094ce169ac1dbf">VLCMediaPlayer</a>
</li>
<li>availableMediaDiscovererForCategoryType:
: <a class="el" href="interface_v_l_c_media_discoverer.html#a867e500dc5948bcb63206f814dffc138">VLCMediaDiscoverer</a>
</li>
</ul>


<h3><a id="index_c"></a>- c -</h3><ul>
<li>cancelDialogWithReference:
: <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html#a2a33b761b5aa9da31e6998d2ae9726b0">&lt;VLCCustomDialogRendererProtocol&gt;</a>
</li>
<li>chapterDescriptionsOfTitle:
: <a class="el" href="interface_v_l_c_media_player.html#a637f12081f1748693c9234b33c752640">VLCMediaPlayer</a>
</li>
<li>chaptersForTitleIndex:
: <a class="el" href="interface_v_l_c_media_player.html#a59fbfd5a29004a32b9f64328ad6b57a4">VLCMediaPlayer</a>
</li>
<li>clearStoredCookies
: <a class="el" href="interface_v_l_c_media.html#a51bdb09726b9f4d72072e144ea7314cc">VLCMedia</a>
</li>
<li>codecNameForFourCC:trackType:
: <a class="el" href="interface_v_l_c_media.html#a98f23cb39854168dc79df832cecb7ff2">VLCMedia</a>
</li>
<li>compare:
: <a class="el" href="interface_v_l_c_media.html#ac0b967f4529a5c2a23972cca9fd2a800">VLCMedia</a>
, <a class="el" href="interface_v_l_c_time.html#a4bde16fb3441c6e7ff40a75e2f206148">VLCTime</a>
</li>
</ul>


<h3><a id="index_d"></a>- d -</h3><ul>
<li>dismissDialogWithReference:
: <a class="el" href="interface_v_l_c_dialog_provider.html#a067cfa85cd47a387ccccba06b6a14b3e">VLCDialogProvider</a>
</li>
</ul>


<h3><a id="index_e"></a>- e -</h3><ul>
<li>extraShortJumpBackward
: <a class="el" href="interface_v_l_c_media_player.html#aefc367fa665de839effe00bccb4b7261">VLCMediaPlayer</a>
</li>
<li>extraShortJumpForward
: <a class="el" href="interface_v_l_c_media_player.html#ac77c500c9dbfb4bf3f3228db0f970e50">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_f"></a>- f -</h3><ul>
<li>fastForward
: <a class="el" href="interface_v_l_c_media_player.html#a1ad6cf29b157d2042f125b03a739c6ea">VLCMediaPlayer</a>
</li>
<li>fastForwardAtRate:
: <a class="el" href="interface_v_l_c_media_player.html#ae0790b3fdd424f5c59472f416b8ee5f4">VLCMediaPlayer</a>
</li>
<li>fetchThumbnail
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#a8c0bce3c2eef22eae4d8b046ce37761b">VLCMediaThumbnailer</a>
</li>
<li>frequencyOfBandAtIndex:
: <a class="el" href="interface_v_l_c_media_player.html#aa46d4dfad32097dc1cac62f795e06f39">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_g"></a>- g -</h3><ul>
<li>gotoNextFrame
: <a class="el" href="interface_v_l_c_media_player.html#a0efca8c5d59212429f7aa715f61b7637">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_h"></a>- h -</h3><ul>
<li>handleMessage:debugLevel:
: <a class="el" href="protocol_v_l_c_library_log_receiver_protocol-p.html#a7eb2f0df947fe52bb7bca12b44dffef1">&lt;VLCLibraryLogReceiverProtocol&gt;</a>
</li>
<li>hash
: <a class="el" href="interface_v_l_c_time.html#a5f24e1e825a0676c3da2f6a3d5bc0a13">VLCTime</a>
</li>
</ul>


<h3><a id="index_i"></a>- i -</h3><ul>
<li>indexOfMedia:
: <a class="el" href="interface_v_l_c_media_list.html#a38eeb1fa92b98b7b4823acf39e8e8865">VLCMediaList</a>
</li>
<li>initAsNodeWithName:
: <a class="el" href="interface_v_l_c_media.html#af9a048525b9aeb4919c47e1148962638">VLCMedia</a>
</li>
<li>initWithArray:
: <a class="el" href="interface_v_l_c_media_list.html#a29976e5d1b3eae05b6b052d2765f5090">VLCMediaList</a>
</li>
<li>initWithDrawable:
: <a class="el" href="interface_v_l_c_media_list_player.html#a39e3151a069ead5d9bdc4bd1d8b0e815">VLCMediaListPlayer</a>
</li>
<li>initWithInt:
: <a class="el" href="interface_v_l_c_time.html#ac9717d5978ab18fa6547cde44904cfad">VLCTime</a>
</li>
<li>initWithLibrary:customUI:
: <a class="el" href="interface_v_l_c_dialog_provider.html#a51bf3d3c491d976761a78ac431f9e986">VLCDialogProvider</a>
</li>
<li>initWithLibVLCInstance:andLibrary:
: <a class="el" href="interface_v_l_c_media_player.html#a91e1bad6eaedb58cbfe097f633c50ebd">VLCMediaPlayer</a>
</li>
<li>initWithName:
: <a class="el" href="interface_v_l_c_media_discoverer.html#a41cd6e81501ef6d0f90788c6dc761cce">VLCMediaDiscoverer</a>
, <a class="el" href="interface_v_l_c_renderer_discoverer.html#a955ed444b32f6fd9dedbecc880142567">VLCRendererDiscoverer</a>
</li>
<li>initWithName:libraryInstance:
: <a class="el" href="interface_v_l_c_media_discoverer.html#a4795127962209e66c1b316381177205a">VLCMediaDiscoverer</a>
</li>
<li>initWithName:longName:
: <a class="el" href="interface_v_l_c_renderer_discoverer_description.html#abc3330cc3e3de86b0f247981daf5dfb6">VLCRendererDiscovererDescription</a>
</li>
<li>initWithNumber:
: <a class="el" href="interface_v_l_c_time.html#a4681ebad8d27e7243ebf6d83679e8a24">VLCTime</a>
</li>
<li>initWithOptionDictionary:
: <a class="el" href="interface_v_l_c_stream_output.html#a11c02ad55e225c5afa473aeb15db13e0">VLCStreamOutput</a>
</li>
<li>initWithOptions:
: <a class="el" href="interface_v_l_c_library.html#ab029c774c0b133dc9c15b9f4446de8c5">VLCLibrary</a>
, <a class="el" href="interface_v_l_c_media_list_player.html#a2026b15a0a74d3be733346074f44ba18">VLCMediaListPlayer</a>
, <a class="el" href="interface_v_l_c_media_player.html#a80fb8e4483b8675f8752ad006fd0f361">VLCMediaPlayer</a>
</li>
<li>initWithOptions:andDrawable:
: <a class="el" href="interface_v_l_c_media_list_player.html#a9229793245755c2bf82cedf7d4032a90">VLCMediaListPlayer</a>
</li>
<li>initWithPath:
: <a class="el" href="interface_v_l_c_media.html#a4215c08e40a19e60bf10a9ea15b8fb85">VLCMedia</a>
</li>
<li>initWithStream:
: <a class="el" href="interface_v_l_c_media.html#a0f4a6c10ac143fdcd19bc12fdb2bb71a">VLCMedia</a>
</li>
<li>initWithURL:
: <a class="el" href="interface_v_l_c_media.html#a1a980dff03ccacf966e754c0a60bac49">VLCMedia</a>
</li>
<li>initWithVideoLayer:
: <a class="el" href="interface_v_l_c_media_player.html#ad00097d1bab674773134188770396d1d">VLCMediaPlayer</a>
</li>
<li>initWithVideoView:
: <a class="el" href="interface_v_l_c_media_player.html#a432545540f6be27394824275fa6e3d10">VLCMediaPlayer</a>
</li>
<li>insertMedia:atIndex:
: <a class="el" href="interface_v_l_c_media_list.html#a6855e74eaf93ec3e512d683423ecc2e3">VLCMediaList</a>
</li>
<li>ipodStreamOutputWithFilePath:
: <a class="el" href="interface_v_l_c_stream_output.html#a518d4dd8d9b9733acd58dce332f8d004">VLCStreamOutput</a>
</li>
<li>isEqual:
: <a class="el" href="interface_v_l_c_time.html#a02ff06b1610b86d9bb1770a93837be42">VLCTime</a>
</li>
</ul>


<h3><a id="index_j"></a>- j -</h3><ul>
<li>jumpBackward:
: <a class="el" href="interface_v_l_c_media_player.html#a86d420386d6dde28818ae2a7e56f6198">VLCMediaPlayer</a>
</li>
<li>jumpForward:
: <a class="el" href="interface_v_l_c_media_player.html#a51e8067be7f55f64d3cfa3e4acccfafc">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_l"></a>- l -</h3><ul>
<li>lengthWaitUntilDate:
: <a class="el" href="interface_v_l_c_media.html#a82e93da5f18bf8584beff1b714d496d4">VLCMedia</a>
</li>
<li>list
: <a class="el" href="interface_v_l_c_renderer_discoverer.html#aa6ffdcd169141a032021df59fdfe3e14">VLCRendererDiscoverer</a>
</li>
<li>lock
: <a class="el" href="interface_v_l_c_media_list.html#aa852de8d98be00568958006826b24661">VLCMediaList</a>
</li>
<li>longJumpBackward
: <a class="el" href="interface_v_l_c_media_player.html#a78d5c9af63086443c9539c06b63bff4d">VLCMediaPlayer</a>
</li>
<li>longJumpForward
: <a class="el" href="interface_v_l_c_media_player.html#ad04f7a86349209e50615afc1a513a768">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_m"></a>- m -</h3><ul>
<li>mediaAsNodeWithName:
: <a class="el" href="interface_v_l_c_media.html#a3aaff98fc9546ceaf4c1871577c00a17">VLCMedia</a>
</li>
<li>mediaAtIndex:
: <a class="el" href="interface_v_l_c_media_list.html#af07ba2e377bd890b7b12c61ac34a9e5c">VLCMediaList</a>
</li>
<li>mediaDidFinishParsing:
: <a class="el" href="protocol_v_l_c_media_delegate-p.html#a90e4a5b0112fcaeda0338827cacd060f">&lt;VLCMediaDelegate&gt;</a>
</li>
<li>mediaList:mediaAdded:atIndex:
: <a class="el" href="protocol_v_l_c_media_list_delegate-p.html#a79ca6fefea1fca488d4f9fe5ffa1cfe0">&lt;VLCMediaListDelegate&gt;</a>
</li>
<li>mediaList:mediaRemovedAtIndex:
: <a class="el" href="protocol_v_l_c_media_list_delegate-p.html#a22672bbcaf4fc015d28e0b85303b02be">&lt;VLCMediaListDelegate&gt;</a>
</li>
<li>mediaListPlayer:nextMedia:
: <a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html#ab86eca2f5040184a50cb8f74f1ca3845">&lt;VLCMediaListPlayerDelegate&gt;</a>
</li>
<li>mediaListPlayerFinishedPlayback:
: <a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html#afe3046f44ecb7cdde5d5659d543365a8">&lt;VLCMediaListPlayerDelegate&gt;</a>
</li>
<li>mediaListPlayerStopped:
: <a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html#a22d8350c9779fb7bcdf30cc1076a7073">&lt;VLCMediaListPlayerDelegate&gt;</a>
</li>
<li>mediaMetaDataDidChange:
: <a class="el" href="protocol_v_l_c_media_delegate-p.html#acf9db32d94f11f07d9660587d4b8b0b5">&lt;VLCMediaDelegate&gt;</a>
</li>
<li>mediaPlayer:recordingStoppedAtPath:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#ae9712ac043c045a0d7eaee79281d6eef">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerChapterChanged:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#aa0cba7a31646e3b6dbb7b994776eaf4b">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerLoudnessChanged:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#a20baffdd00732f78f8b1ddf4689983cc">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerSnapshot:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#ac975d4b4100985c55cf5765fef978c29">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerStartedRecording:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#a3c1f8b9adb12e923abc5686e0dd7f5f8">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerStateChanged:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#afd15a9281d2a0d4c03b1b67ff0041ba8">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerTimeChanged:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#a5c1d64150f362598808f444b66046f7a">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaPlayerTitleChanged:
: <a class="el" href="protocol_v_l_c_media_player_delegate-p.html#a857838e3a322f96cf60fa94dc905e8d5">&lt;VLCMediaPlayerDelegate&gt;</a>
</li>
<li>mediaThumbnailer:didFinishThumbnail:
: <a class="el" href="protocol_v_l_c_media_thumbnailer_delegate-p.html#ae28138f261878ef3dd0f503193f939a0">&lt;VLCMediaThumbnailerDelegate&gt;</a>
</li>
<li>mediaThumbnailerDidTimeOut:
: <a class="el" href="protocol_v_l_c_media_thumbnailer_delegate-p.html#acf2f91a0581735994a84a4efdd91fad0">&lt;VLCMediaThumbnailerDelegate&gt;</a>
</li>
<li>mediaWithPath:
: <a class="el" href="interface_v_l_c_media.html#ae65d970c9c066ab28ec0c8bdcc076101">VLCMedia</a>
</li>
<li>mediaWithURL:
: <a class="el" href="interface_v_l_c_media.html#a38e5fb8f18d50b6de684a7e56c1611fa">VLCMedia</a>
</li>
<li>mediumJumpBackward
: <a class="el" href="interface_v_l_c_media_player.html#ae010feb668404c839ca683ecef6ee7a2">VLCMediaPlayer</a>
</li>
<li>mediumJumpForward
: <a class="el" href="interface_v_l_c_media_player.html#ae87c31a4ebc3274b2b275bc5ac42f24c">VLCMediaPlayer</a>
</li>
<li>metadataForKey:
: <a class="el" href="interface_v_l_c_media.html#af495ec3452fdcdcd89aa2a13695ba6dd">VLCMedia</a>
</li>
<li>mpeg2StreamOutputWithFilePath:
: <a class="el" href="interface_v_l_c_stream_output.html#a1c1586457352350d81adbe686729eb92">VLCStreamOutput</a>
</li>
<li>mpeg4StreamOutputWithFilePath:
: <a class="el" href="interface_v_l_c_stream_output.html#a5c5ec3624e9cc31e1de8630e42430c40">VLCStreamOutput</a>
</li>
</ul>


<h3><a id="index_n"></a>- n -</h3><ul>
<li>nextChapter
: <a class="el" href="interface_v_l_c_media_player.html#acfaac1c1cf8ae35bd40d80afa42bf1f0">VLCMediaPlayer</a>
</li>
<li>NS_ENUM
: <a class="el" href="interface_v_l_c_media.html#af4439eb3074ae064da27365b68ddbfc8">VLCMedia</a>
, <a class="el" href="interface_v_l_c_media_player.html#a7eb2b3aeb06985b3b655c1c76609924f">VLCMediaPlayer</a>
</li>
<li>NS_UNAVAILABLE
: <a class="el" href="interface_v_l_c_renderer_item.html#a7d4e309821a947227bdf33e2f9eb668e">VLCRendererItem</a>
</li>
<li>nullTime
: <a class="el" href="interface_v_l_c_time.html#ae4b7157f2152d3eddafe14e126a4b96c">VLCTime</a>
</li>
<li>numberOfChaptersForTitle:
: <a class="el" href="interface_v_l_c_media_player.html#a42a283dd2c12d510f247d10510deffff">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_o"></a>- o -</h3><ul>
<li>openVideoSubTitlesFromFile:
: <a class="el" href="interface_v_l_c_media_player.html#a18546cd8ca1b827eb5ddb9384e172166">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_p"></a>- p -</h3><ul>
<li>parseStop
: <a class="el" href="interface_v_l_c_media.html#a0213a3ea482353bce0d7bb59355d497a">VLCMedia</a>
</li>
<li>parseWithOptions:
: <a class="el" href="interface_v_l_c_media.html#aecfb52ec0989cd489fdc2966cd431586">VLCMedia</a>
</li>
<li>parseWithOptions:timeout:
: <a class="el" href="interface_v_l_c_media.html#ac00685e5d9a33652413b298c43423b5a">VLCMedia</a>
</li>
<li>pause
: <a class="el" href="interface_v_l_c_media_list_player.html#ac0e7b5153919a1108723be359773f7f9">VLCMediaListPlayer</a>
, <a class="el" href="interface_v_l_c_media_player.html#aaef26685e063e62599a5b0248a072a0f">VLCMediaPlayer</a>
</li>
<li>performNavigationAction:
: <a class="el" href="interface_v_l_c_media_player.html#a2309a13bb4aa332f3dd1eecead8831a3">VLCMediaPlayer</a>
</li>
<li>play
: <a class="el" href="interface_v_l_c_media_list_player.html#a2f22768be90786ad490b4c3ee07900c9">VLCMediaListPlayer</a>
, <a class="el" href="interface_v_l_c_media_player.html#a7da1ef4be33931daadf5937cd2365924">VLCMediaPlayer</a>
</li>
<li>playItemAtIndex:
: <a class="el" href="interface_v_l_c_media_list_player.html#a0b70c60e898b6ac244486fe11f8ec6b3">VLCMediaListPlayer</a>
</li>
<li>playItemAtNumber:
: <a class="el" href="interface_v_l_c_media_list_player.html#a08850682934ccc36da966054281b34d3">VLCMediaListPlayer</a>
</li>
<li>playMedia:
: <a class="el" href="interface_v_l_c_media_list_player.html#a001f13e8971cb4073dd4c148192a23a9">VLCMediaListPlayer</a>
</li>
<li>postAction:forDialogReference:
: <a class="el" href="interface_v_l_c_dialog_provider.html#a8a0eefde74fa37648c5362bc864a3492">VLCDialogProvider</a>
</li>
<li>postUsername:andPassword:forDialogReference:store:
: <a class="el" href="interface_v_l_c_dialog_provider.html#a75004021734803d0acbd1043dfcb59de">VLCDialogProvider</a>
</li>
<li>previousChapter
: <a class="el" href="interface_v_l_c_media_player.html#ae2a2398ebf77aaa0dd5218d6e17a61f4">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_r"></a>- r -</h3><ul>
<li>reencodeAndMuxSRTFile:toMP4File:outputPath:
: <a class="el" href="interface_v_l_c_transcoder.html#a0ec64622da15a7b9826a99b1d242778f">VLCTranscoder</a>
</li>
<li>removeMediaAtIndex:
: <a class="el" href="interface_v_l_c_media_list.html#a16aeab5bf78f68472369386d7a2eaae7">VLCMediaList</a>
</li>
<li>renderers
: <a class="el" href="interface_v_l_c_renderer_discoverer.html#acb4c6a5e9321cdd8432bd0f6e6ebee1d">VLCRendererDiscoverer</a>
</li>
<li>resetEqualizerFromProfile:
: <a class="el" href="interface_v_l_c_media_player.html#abb2864039496be0bde196467971dd873">VLCMediaPlayer</a>
</li>
<li>rewind
: <a class="el" href="interface_v_l_c_media_player.html#ac03553439681974c0da7014c44b104d6">VLCMediaPlayer</a>
</li>
<li>rewindAtRate:
: <a class="el" href="interface_v_l_c_media_player.html#ade646072ef74f61b84e4afdc260a0f36">VLCMediaPlayer</a>
</li>
<li>rtpBroadcastStreamOutputWithSAPAnnounce:
: <a class="el" href="interface_v_l_c_stream_output.html#a37d21584a752b9ddaecd248d171bdf59">VLCStreamOutput</a>
</li>
</ul>


<h3><a id="index_s"></a>- s -</h3><ul>
<li>saveVideoSnapshotAt:withWidth:andHeight:
: <a class="el" href="interface_v_l_c_media_player.html#ac0e16c49e1746e74a35fd50f870c5e31">VLCMediaPlayer</a>
</li>
<li>setAmplification:forBand:
: <a class="el" href="interface_v_l_c_media_player.html#ae61e529ff86b246ebe54cc29b0a96c0e">VLCMediaPlayer</a>
</li>
<li>setApplicationIdentifier:withVersion:andApplicationIconName:
: <a class="el" href="interface_v_l_c_library.html#afe1e5231b6bc4b9b12e0fd2c44862dbb">VLCLibrary</a>
</li>
<li>setDebugLoggingToFile:
: <a class="el" href="interface_v_l_c_library.html#a86c8d6b68e6852dea7045a40a9110dde">VLCLibrary</a>
</li>
<li>setDeinterlace:withFilter:
: <a class="el" href="interface_v_l_c_media_player.html#a236ed2450c49f8acd0846499bf26fbb5">VLCMediaPlayer</a>
</li>
<li>setDeinterlaceFilter:
: <a class="el" href="interface_v_l_c_media_player.html#a95abbbed4ddab2adb3fb5c4a8b8ae076">VLCMediaPlayer</a>
</li>
<li>setHumanReadableName:withHTTPUserAgent:
: <a class="el" href="interface_v_l_c_library.html#ae91040ee78413a160918871e1e9475ff">VLCLibrary</a>
</li>
<li>setMetadata:forKey:
: <a class="el" href="interface_v_l_c_media.html#ab1f738fbdeaa9efaf918223c0ed187e4">VLCMedia</a>
</li>
<li>setMute:
: <a class="el" href="interface_v_l_c_audio.html#ab5c770cd553794ec294c77fd4e56668d">VLCAudio</a>
</li>
<li>setRendererItem:
: <a class="el" href="interface_v_l_c_media_player.html#a44f8101ea62406d757c44520361f8930">VLCMediaPlayer</a>
</li>
<li>setVideoLayer:
: <a class="el" href="interface_v_l_c_media_player.html#a76dc478bf25fae8e0f671e39d006ce25">VLCMediaPlayer</a>
</li>
<li>setVideoView:
: <a class="el" href="interface_v_l_c_media_player.html#a1eb2229ede2d006bec1650e4d4b0fa02">VLCMediaPlayer</a>
</li>
<li>sharedLibrary
: <a class="el" href="interface_v_l_c_library.html#a7eddfd69ce66ffc5603bc68a5777187e">VLCLibrary</a>
</li>
<li>shortJumpBackward
: <a class="el" href="interface_v_l_c_media_player.html#a8a8548e2888bbcf9bf8ed5a35fc30cf4">VLCMediaPlayer</a>
</li>
<li>shortJumpForward
: <a class="el" href="interface_v_l_c_media_player.html#ab09028de82e23ba963e7b645949aa212">VLCMediaPlayer</a>
</li>
<li>showErrorWithTitle:message:
: <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html#a55ed777080c858e89615b774bed8bd89">&lt;VLCCustomDialogRendererProtocol&gt;</a>
</li>
<li>showLoginWithTitle:message:defaultUsername:askingForStorage:withReference:
: <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html#ac2a775dcb6a40617feb1874ccea4de37">&lt;VLCCustomDialogRendererProtocol&gt;</a>
</li>
<li>showProgressWithTitle:message:isIndeterminate:position:cancelString:withReference:
: <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html#a9b752a12e5565ad7182ae7cc07c52a2e">&lt;VLCCustomDialogRendererProtocol&gt;</a>
</li>
<li>showQuestionWithTitle:message:type:cancelString:action1String:action2String:withReference:
: <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html#a1c2544adcc4dfbf22c7218c34f74c905">&lt;VLCCustomDialogRendererProtocol&gt;</a>
</li>
<li>start
: <a class="el" href="interface_v_l_c_renderer_discoverer.html#a5f344593f78573d3cba3bc9b89ccea14">VLCRendererDiscoverer</a>
</li>
<li>startDiscoverer
: <a class="el" href="interface_v_l_c_media_discoverer.html#ae24d8b9250ccead549c829071f7cf184">VLCMediaDiscoverer</a>
</li>
<li>startRecordingAtPath:
: <a class="el" href="interface_v_l_c_media_player.html#a0af0ae5ca6a1b5f68efc764a296b6876">VLCMediaPlayer</a>
</li>
<li>stop
: <a class="el" href="interface_v_l_c_media_list_player.html#ab4473d33d43e75b4c73fd9ed1ec5cc2c">VLCMediaListPlayer</a>
, <a class="el" href="interface_v_l_c_media_player.html#ad7184261982c10d5d1307e37ed16fb52">VLCMediaPlayer</a>
, <a class="el" href="interface_v_l_c_renderer_discoverer.html#ab7ca6afa57b5f53f6284791fde3f8839">VLCRendererDiscoverer</a>
</li>
<li>stopDiscoverer
: <a class="el" href="interface_v_l_c_media_discoverer.html#aedebd3f4b61febe5ca436af1f1ea7508">VLCMediaDiscoverer</a>
</li>
<li>stopRecording
: <a class="el" href="interface_v_l_c_media_player.html#a41917b1e63701715a0e66f16e22c6f63">VLCMediaPlayer</a>
</li>
<li>storeCookie:forHost:path:
: <a class="el" href="interface_v_l_c_media.html#ac04b45047fa221e26ac0c589e28fc5ff">VLCMedia</a>
</li>
<li>streamOutputWithFilePath:
: <a class="el" href="interface_v_l_c_stream_output.html#abacda1a7636077fc336abde914fb4877">VLCStreamOutput</a>
</li>
<li>streamOutputWithOptionDictionary:
: <a class="el" href="interface_v_l_c_stream_output.html#ae31bb946515ad904af62b14ad4c0174c">VLCStreamOutput</a>
</li>
</ul>


<h3><a id="index_t"></a>- t -</h3><ul>
<li>thumbnailerWithMedia:andDelegate:
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#a364d20f11c696b300c42fd5eb404c3f9">VLCMediaThumbnailer</a>
</li>
<li>thumbnailerWithMedia:delegate:andVLCLibrary:
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#a8b4b7e2a8aa3b3c51146916ef33dc043">VLCMediaThumbnailer</a>
</li>
<li>timeWithInt:
: <a class="el" href="interface_v_l_c_time.html#a936e42eed02e03b404f14bdbce13d06f">VLCTime</a>
</li>
<li>timeWithNumber:
: <a class="el" href="interface_v_l_c_time.html#af9b7c469a7cb064a3d9c2c5bcaa769bb">VLCTime</a>
</li>
<li>transcode:finishedSucessfully:
: <a class="el" href="protocol_v_l_c_transcoder_delegate-p.html#ae3f6bd7dfd97c113a2d2cb2cbb52571f">&lt;VLCTranscoderDelegate&gt;</a>
</li>
</ul>


<h3><a id="index_u"></a>- u -</h3><ul>
<li>unlock
: <a class="el" href="interface_v_l_c_media_list.html#a1a697c6eebd811e4a9db798b9ee4044f">VLCMediaList</a>
</li>
<li>updateProgressWithReference:message:position:
: <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html#a8f81ae57df8581c1df11d5407d4ce1e0">&lt;VLCCustomDialogRendererProtocol&gt;</a>
</li>
<li>updateViewpoint:pitch:roll:fov:absolute:
: <a class="el" href="interface_v_l_c_media_player.html#abe9c128cdaa533d51c5209d4c17d5b09">VLCMediaPlayer</a>
</li>
</ul>


<h3><a id="index_v"></a>- v -</h3><ul>
<li>volumeDown
: <a class="el" href="interface_v_l_c_audio.html#a35b0ee4fbc502b52f7ae89970b27a246">VLCAudio</a>
</li>
<li>volumeUp
: <a class="el" href="interface_v_l_c_audio.html#abce4117645f4fc354f457dca9a991aa3">VLCAudio</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
