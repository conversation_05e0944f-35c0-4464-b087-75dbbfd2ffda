<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Hierarchy</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="headertitle">
<div class="title">Class Hierarchy</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock">This inheritance list is sorted roughly, but not completely, alphabetically:</div><div class="directory">
<div class="levels">[detail level <span onclick="javascript:toggleLevel(1);">1</span><span onclick="javascript:toggleLevel(2);">2</span><span onclick="javascript:toggleLevel(3);">3</span>]</div><table class="directory">
<tr id="row_0_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_0_" class="arrow" onclick="toggleFolder('0_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>CALayer</b></td><td class="desc"></td></tr>
<tr id="row_0_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_video_layer.html" target="_self">VLCVideoLayer</a></td><td class="desc"></td></tr>
<tr id="row_1_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_1_" class="arrow" onclick="toggleFolder('1_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>&lt;NSObject&gt;</b></td><td class="desc"></td></tr>
<tr id="row_1_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_audio.html" target="_self">VLCAudio</a></td><td class="desc"></td></tr>
<tr id="row_1_1_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html" target="_self">&lt;VLCCustomDialogRendererProtocol&gt;</a></td><td class="desc"></td></tr>
<tr id="row_1_2_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_dialog_provider.html" target="_self">VLCDialogProvider</a></td><td class="desc"></td></tr>
<tr id="row_1_3_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_library.html" target="_self">VLCLibrary</a></td><td class="desc"></td></tr>
<tr id="row_1_4_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_media.html" target="_self">VLCMedia</a></td><td class="desc"></td></tr>
<tr id="row_1_5_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="protocol_v_l_c_media_delegate-p.html" target="_self">&lt;VLCMediaDelegate&gt;</a></td><td class="desc"></td></tr>
<tr id="row_1_6_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_media_discoverer.html" target="_self">VLCMediaDiscoverer</a></td><td class="desc"></td></tr>
<tr id="row_1_7_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_media_library.html" target="_self">VLCMediaLibrary</a></td><td class="desc"></td></tr>
<tr id="row_1_8_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_media_list.html" target="_self">VLCMediaList</a></td><td class="desc"></td></tr>
<tr id="row_1_9_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_media_list_player.html" target="_self">VLCMediaListPlayer</a></td><td class="desc"></td></tr>
<tr id="row_1_10_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="protocol_v_l_c_media_list_player_delegate-p.html" target="_self">&lt;VLCMediaListPlayerDelegate&gt;</a></td><td class="desc"></td></tr>
<tr id="row_1_11_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_media_loudness.html" target="_self">VLCMediaLoudness</a></td><td class="desc"></td></tr>
<tr id="row_1_12_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span id="arr_1_12_" class="arrow" onclick="toggleFolder('1_12_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_media_player.html" target="_self">VLCMediaPlayer</a></td><td class="desc"></td></tr>
<tr id="row_1_12_0_" class="even"><td class="entry"><span style="width:48px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_stream_session.html" target="_self">VLCStreamSession</a></td><td class="desc"></td></tr>
<tr id="row_1_13_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_media_thumbnailer.html" target="_self">VLCMediaThumbnailer</a></td><td class="desc"></td></tr>
<tr id="row_1_14_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_renderer_discoverer.html" target="_self">VLCRendererDiscoverer</a></td><td class="desc"></td></tr>
<tr id="row_1_15_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="protocol_v_l_c_renderer_discoverer_delegate-p.html" target="_self">&lt;VLCRendererDiscovererDelegate&gt;</a></td><td class="desc"></td></tr>
<tr id="row_1_16_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_renderer_discoverer_description.html" target="_self">VLCRendererDiscovererDescription</a></td><td class="desc"></td></tr>
<tr id="row_1_17_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_renderer_item.html" target="_self">VLCRendererItem</a></td><td class="desc"></td></tr>
<tr id="row_1_18_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_stream_output.html" target="_self">VLCStreamOutput</a></td><td class="desc"></td></tr>
<tr id="row_1_19_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_time.html" target="_self">VLCTime</a></td><td class="desc"></td></tr>
<tr id="row_1_20_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_transcoder.html" target="_self">VLCTranscoder</a></td><td class="desc"></td></tr>
<tr id="row_1_21_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="protocol_v_l_c_transcoder_delegate-p.html" target="_self">&lt;VLCTranscoderDelegate&gt;</a></td><td class="desc"></td></tr>
<tr id="row_2_" class="even"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_2_" class="arrow" onclick="toggleFolder('2_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>&lt;NSObjectNSObject&gt;</b></td><td class="desc"></td></tr>
<tr id="row_2_0_"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="protocol_v_l_c_library_log_receiver_protocol-p.html" target="_self">&lt;VLCLibraryLogReceiverProtocol&gt;</a></td><td class="desc"></td></tr>
<tr id="row_2_1_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="protocol_v_l_c_media_player_delegate-p.html" target="_self">&lt;VLCMediaPlayerDelegate&gt;</a></td><td class="desc"></td></tr>
<tr id="row_3_"><td class="entry"><span style="width:0px;display:inline-block;">&#160;</span><span id="arr_3_" class="arrow" onclick="toggleFolder('3_')">&#9660;</span><span class="icona"><span class="icon">C</span></span><b>NSView</b></td><td class="desc"></td></tr>
<tr id="row_3_0_" class="even"><td class="entry"><span style="width:32px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="interface_v_l_c_video_view.html" target="_self">VLCVideoView</a></td><td class="desc"></td></tr>
<tr id="row_4_"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="protocol_v_l_c_media_list_delegate-p.html" target="_self">&lt;VLCMediaListDelegate&gt;</a></td><td class="desc"></td></tr>
<tr id="row_5_" class="even"><td class="entry"><span style="width:16px;display:inline-block;">&#160;</span><span class="icona"><span class="icon">C</span></span><a class="el" href="protocol_v_l_c_media_thumbnailer_delegate-p.html" target="_self">&lt;VLCMediaThumbnailerDelegate&gt;</a></td><td class="desc"></td></tr>
</table>
</div><!-- directory -->
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
