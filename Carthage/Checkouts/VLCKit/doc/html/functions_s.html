<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_s"></a>- s -</h3><ul>
<li>saturation
: <a class="el" href="interface_v_l_c_media_player.html#a6b7d12cf171b798406f128a3f5b54908">VLCMediaPlayer</a>
</li>
<li>saveMetadata
: <a class="el" href="interface_v_l_c_media.html#a2332173d72093469abebf56f4c70ae80">VLCMedia</a>
</li>
<li>saveVideoSnapshotAt:withWidth:andHeight:
: <a class="el" href="interface_v_l_c_media_player.html#ac0e16c49e1746e74a35fd50f870c5e31">VLCMediaPlayer</a>
</li>
<li>scaleFactor
: <a class="el" href="interface_v_l_c_media_player.html#a773e8e2b5b169fa6cb0bcef37330d327">VLCMediaPlayer</a>
</li>
<li>seekable
: <a class="el" href="interface_v_l_c_media_player.html#ae3f8bc09a07c8b58935d4cd1cf58e69e">VLCMediaPlayer</a>
</li>
<li>setAmplification:forBand:
: <a class="el" href="interface_v_l_c_media_player.html#ae61e529ff86b246ebe54cc29b0a96c0e">VLCMediaPlayer</a>
</li>
<li>setApplicationIdentifier:withVersion:andApplicationIconName:
: <a class="el" href="interface_v_l_c_library.html#afe1e5231b6bc4b9b12e0fd2c44862dbb">VLCLibrary</a>
</li>
<li>setDebugLoggingToFile:
: <a class="el" href="interface_v_l_c_library.html#a86c8d6b68e6852dea7045a40a9110dde">VLCLibrary</a>
</li>
<li>setDeinterlace:withFilter:
: <a class="el" href="interface_v_l_c_media_player.html#a236ed2450c49f8acd0846499bf26fbb5">VLCMediaPlayer</a>
</li>
<li>setDeinterlaceFilter:
: <a class="el" href="interface_v_l_c_media_player.html#a95abbbed4ddab2adb3fb5c4a8b8ae076">VLCMediaPlayer</a>
</li>
<li>setHumanReadableName:withHTTPUserAgent:
: <a class="el" href="interface_v_l_c_library.html#ae91040ee78413a160918871e1e9475ff">VLCLibrary</a>
</li>
<li>setMetadata:forKey:
: <a class="el" href="interface_v_l_c_media.html#ab1f738fbdeaa9efaf918223c0ed187e4">VLCMedia</a>
</li>
<li>setMute:
: <a class="el" href="interface_v_l_c_audio.html#ab5c770cd553794ec294c77fd4e56668d">VLCAudio</a>
</li>
<li>setRendererItem:
: <a class="el" href="interface_v_l_c_media_player.html#a44f8101ea62406d757c44520361f8930">VLCMediaPlayer</a>
</li>
<li>setVideoLayer:
: <a class="el" href="interface_v_l_c_media_player.html#a76dc478bf25fae8e0f671e39d006ce25">VLCMediaPlayer</a>
</li>
<li>setVideoView:
: <a class="el" href="interface_v_l_c_media_player.html#a1eb2229ede2d006bec1650e4d4b0fa02">VLCMediaPlayer</a>
</li>
<li>sharedLibrary
: <a class="el" href="interface_v_l_c_library.html#a7eddfd69ce66ffc5603bc68a5777187e">VLCLibrary</a>
</li>
<li>shortJumpBackward
: <a class="el" href="interface_v_l_c_media_player.html#a8a8548e2888bbcf9bf8ed5a35fc30cf4">VLCMediaPlayer</a>
</li>
<li>shortJumpForward
: <a class="el" href="interface_v_l_c_media_player.html#ab09028de82e23ba963e7b645949aa212">VLCMediaPlayer</a>
</li>
<li>showErrorWithTitle:message:
: <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html#a55ed777080c858e89615b774bed8bd89">&lt;VLCCustomDialogRendererProtocol&gt;</a>
</li>
<li>showLoginWithTitle:message:defaultUsername:askingForStorage:withReference:
: <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html#ac2a775dcb6a40617feb1874ccea4de37">&lt;VLCCustomDialogRendererProtocol&gt;</a>
</li>
<li>showProgressWithTitle:message:isIndeterminate:position:cancelString:withReference:
: <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html#a9b752a12e5565ad7182ae7cc07c52a2e">&lt;VLCCustomDialogRendererProtocol&gt;</a>
</li>
<li>showQuestionWithTitle:message:type:cancelString:action1String:action2String:withReference:
: <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html#a1c2544adcc4dfbf22c7218c34f74c905">&lt;VLCCustomDialogRendererProtocol&gt;</a>
</li>
<li>snapshotPosition
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#ab6c36054a654a28cc678082d0a1e02fa">VLCMediaThumbnailer</a>
</li>
<li>snapshots
: <a class="el" href="interface_v_l_c_media_player.html#ada4fadb2ae81bd34fce217a34571872a">VLCMediaPlayer</a>
</li>
<li>start
: <a class="el" href="interface_v_l_c_renderer_discoverer.html#a5f344593f78573d3cba3bc9b89ccea14">VLCRendererDiscoverer</a>
</li>
<li>startDiscoverer
: <a class="el" href="interface_v_l_c_media_discoverer.html#ae24d8b9250ccead549c829071f7cf184">VLCMediaDiscoverer</a>
</li>
<li>startRecordingAtPath:
: <a class="el" href="interface_v_l_c_media_player.html#a0af0ae5ca6a1b5f68efc764a296b6876">VLCMediaPlayer</a>
</li>
<li>state
: <a class="el" href="interface_v_l_c_media.html#af247fea93ce48e219ddef15bdaf256de">VLCMedia</a>
, <a class="el" href="interface_v_l_c_media_player.html#aa800575a8facf5db251df3cc88bd44ea">VLCMediaPlayer</a>
</li>
<li>stats
: <a class="el" href="interface_v_l_c_media.html#a9c2d85f9c2dba700d7b2ca18cf12049a">VLCMedia</a>
</li>
<li>stop
: <a class="el" href="interface_v_l_c_media_list_player.html#ab4473d33d43e75b4c73fd9ed1ec5cc2c">VLCMediaListPlayer</a>
, <a class="el" href="interface_v_l_c_media_player.html#ad7184261982c10d5d1307e37ed16fb52">VLCMediaPlayer</a>
, <a class="el" href="interface_v_l_c_renderer_discoverer.html#ab7ca6afa57b5f53f6284791fde3f8839">VLCRendererDiscoverer</a>
</li>
<li>stopDiscoverer
: <a class="el" href="interface_v_l_c_media_discoverer.html#aedebd3f4b61febe5ca436af1f1ea7508">VLCMediaDiscoverer</a>
</li>
<li>stopRecording
: <a class="el" href="interface_v_l_c_media_player.html#a41917b1e63701715a0e66f16e22c6f63">VLCMediaPlayer</a>
</li>
<li>storeCookie:forHost:path:
: <a class="el" href="interface_v_l_c_media.html#ac04b45047fa221e26ac0c589e28fc5ff">VLCMedia</a>
</li>
<li>streamOutputBitrate
: <a class="el" href="interface_v_l_c_media.html#afd944ae42af805d532f4ab36d5b0fe7d">VLCMedia</a>
</li>
<li>streamOutputWithFilePath:
: <a class="el" href="interface_v_l_c_stream_output.html#abacda1a7636077fc336abde914fb4877">VLCStreamOutput</a>
</li>
<li>streamOutputWithOptionDictionary:
: <a class="el" href="interface_v_l_c_stream_output.html#ae31bb946515ad904af62b14ad4c0174c">VLCStreamOutput</a>
</li>
<li>stringValue
: <a class="el" href="interface_v_l_c_time.html#a7aa23f16fef10097af66ac4d9754d0dc">VLCTime</a>
</li>
<li>subitems
: <a class="el" href="interface_v_l_c_media.html#a08f3d51d9b8199fd20143d178b368b2f">VLCMedia</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
