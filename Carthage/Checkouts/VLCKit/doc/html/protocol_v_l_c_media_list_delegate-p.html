<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: &lt;VLCMediaListDelegate&gt; Protocol Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="protocol_v_l_c_media_list_delegate-p-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">&lt;VLCMediaListDelegate&gt; Protocol Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_media_list_8h_source.html">VLCMediaList.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a79ca6fefea1fca488d4f9fe5ffa1cfe0"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_list_delegate-p.html#a79ca6fefea1fca488d4f9fe5ffa1cfe0">mediaList:mediaAdded:atIndex:</a></td></tr>
<tr class="separator:a79ca6fefea1fca488d4f9fe5ffa1cfe0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a22672bbcaf4fc015d28e0b85303b02be"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_media_list_delegate-p.html#a22672bbcaf4fc015d28e0b85303b02be">mediaList:mediaRemovedAtIndex:</a></td></tr>
<tr class="separator:a22672bbcaf4fc015d28e0b85303b02be"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="protocol_v_l_c_media_list_delegate-p.html">VLCMediaListDelegate</a> </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="a79ca6fefea1fca488d4f9fe5ffa1cfe0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a79ca6fefea1fca488d4f9fe5ffa1cfe0">&#9670;&nbsp;</a></span>mediaList:mediaAdded:atIndex:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaList: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a> *)&#160;</td>
          <td class="paramname"><em>aMediaList</em></td>
        </tr>
        <tr>
          <td class="paramkey">mediaAdded:</td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *)&#160;</td>
          <td class="paramname"><em>media</em></td>
        </tr>
        <tr>
          <td class="paramkey">atIndex:</td>
          <td></td>
          <td class="paramtype">(NSUInteger)&#160;</td>
          <td class="paramname"><em>index</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>delegate method triggered when a media was added to the list</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aMediaList</td><td>the media list </td></tr>
    <tr><td class="paramname">media</td><td>the media object that was added </td></tr>
    <tr><td class="paramname">index</td><td>the index the media object was added at </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a22672bbcaf4fc015d28e0b85303b02be"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a22672bbcaf4fc015d28e0b85303b02be">&#9670;&nbsp;</a></span>mediaList:mediaRemovedAtIndex:</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediaList: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a> *)&#160;</td>
          <td class="paramname"><em>aMediaList</em></td>
        </tr>
        <tr>
          <td class="paramkey">mediaRemovedAtIndex:</td>
          <td></td>
          <td class="paramtype">(NSUInteger)&#160;</td>
          <td class="paramname"><em>index</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">optional</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>delegate method triggered when a media was removed from the list</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aMediaList</td><td>the media list </td></tr>
    <tr><td class="paramname">index</td><td>the index a media item was deleted at </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this protocol was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_media_list_8h_source.html">VLCMediaList.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
