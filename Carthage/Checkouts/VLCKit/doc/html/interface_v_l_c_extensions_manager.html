<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>VLCKit: VLCExtensionsManager Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Related&#160;Pages</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#pub-static-methods">Class Methods</a> &#124;
<a href="class_v_l_c_extensions_manager-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCExtensionsManager Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_extensions_manager_8h_source.html">VLCExtensionsManager.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCExtensionsManager:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_extensions_manager.png" usemap="#VLCExtensionsManager_map" alt=""/>
  <map id="VLCExtensionsManager_map" name="VLCExtensionsManager_map">
</map>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a7cf94669829921ac28efd0bc78881d9d"><td class="memItemLeft" align="right" valign="top">(NSArray *extensions)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_extensions_manager.html#a7cf94669829921ac28efd0bc78881d9d">__attribute__</a></td></tr>
<tr class="separator:a7cf94669829921ac28efd0bc78881d9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af5cb92dc2f89e98a2e9c4633f77bff0b"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_extensions_manager.html#af5cb92dc2f89e98a2e9c4633f77bff0b">runExtension:</a></td></tr>
<tr class="separator:af5cb92dc2f89e98a2e9c4633f77bff0b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8e4ac0200df7ca73167f39df5c931493"><td class="memItemLeft" align="right" valign="top">(<a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a> *mediaPlayer)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_extensions_manager.html#a8e4ac0200df7ca73167f39df5c931493">__attribute__</a></td></tr>
<tr class="separator:a8e4ac0200df7ca73167f39df5c931493"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Class Methods</h2></td></tr>
<tr class="memitem:a7062873fb7e27aedc33791663783045a"><td class="memItemLeft" align="right" valign="top">((deprecated)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_extensions_manager.html#a7062873fb7e27aedc33791663783045a">__attribute__</a></td></tr>
<tr class="separator:a7062873fb7e27aedc33791663783045a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>manager class for lua extensions within VLC </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a class="anchor" id="a7cf94669829921ac28efd0bc78881d9d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray* extensions) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>list of available extensions </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000007">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a class="anchor" id="a7062873fb7e27aedc33791663783045a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ ((deprecated) __attribute__ </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>singleton manager instance </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000006">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a class="anchor" id="a8e4ac0200df7ca73167f39df5c931493"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_media_player.html">VLCMediaPlayer</a>* mediaPlayer) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>player instance to use with the extensions </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000009">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a class="anchor" id="af5cb92dc2f89e98a2e9c4633f77bff0b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) runExtension: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>start execution of a given extension instance </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">extension</td><td>the extension to run </td></tr>
  </table>
  </dd>
</dl>
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000008">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_extensions_manager_8h_source.html">VLCExtensionsManager.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
