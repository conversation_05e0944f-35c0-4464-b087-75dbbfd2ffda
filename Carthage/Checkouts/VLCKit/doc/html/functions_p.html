<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_p"></a>- p -</h3><ul>
<li>parsedStatus
: <a class="el" href="interface_v_l_c_media.html#adc94b1c776ed671be57746c79e04f187">VLCMedia</a>
</li>
<li>parseStop
: <a class="el" href="interface_v_l_c_media.html#a0213a3ea482353bce0d7bb59355d497a">VLCMedia</a>
</li>
<li>parseWithOptions:
: <a class="el" href="interface_v_l_c_media.html#aecfb52ec0989cd489fdc2966cd431586">VLCMedia</a>
</li>
<li>parseWithOptions:timeout:
: <a class="el" href="interface_v_l_c_media.html#ac00685e5d9a33652413b298c43423b5a">VLCMedia</a>
</li>
<li>passthrough
: <a class="el" href="interface_v_l_c_audio.html#ac37223907edb0cafcee6f609edc28782">VLCAudio</a>
</li>
<li>pause
: <a class="el" href="interface_v_l_c_media_list_player.html#ac0e7b5153919a1108723be359773f7f9">VLCMediaListPlayer</a>
, <a class="el" href="interface_v_l_c_media_player.html#aaef26685e063e62599a5b0248a072a0f">VLCMediaPlayer</a>
</li>
<li>performNavigationAction:
: <a class="el" href="interface_v_l_c_media_player.html#a2309a13bb4aa332f3dd1eecead8831a3">VLCMediaPlayer</a>
</li>
<li>pitch
: <a class="el" href="interface_v_l_c_media_player.html#ae7af67ee4b28da45c957bafca617840f">VLCMediaPlayer</a>
</li>
<li>play
: <a class="el" href="interface_v_l_c_media_list_player.html#a2f22768be90786ad490b4c3ee07900c9">VLCMediaListPlayer</a>
, <a class="el" href="interface_v_l_c_media_player.html#a7da1ef4be33931daadf5937cd2365924">VLCMediaPlayer</a>
</li>
<li>playing
: <a class="el" href="interface_v_l_c_media_player.html#a6fa5e39a09fd25c262c9a2ea20e5b9df">VLCMediaPlayer</a>
</li>
<li>playItemAtIndex:
: <a class="el" href="interface_v_l_c_media_list_player.html#a0b70c60e898b6ac244486fe11f8ec6b3">VLCMediaListPlayer</a>
</li>
<li>playItemAtNumber:
: <a class="el" href="interface_v_l_c_media_list_player.html#a08850682934ccc36da966054281b34d3">VLCMediaListPlayer</a>
</li>
<li>playMedia:
: <a class="el" href="interface_v_l_c_media_list_player.html#a001f13e8971cb4073dd4c148192a23a9">VLCMediaListPlayer</a>
</li>
<li>position
: <a class="el" href="interface_v_l_c_media_player.html#af10549bcee345334f42548cfda9ce51c">VLCMediaPlayer</a>
</li>
<li>postAction:forDialogReference:
: <a class="el" href="interface_v_l_c_dialog_provider.html#a8a0eefde74fa37648c5362bc864a3492">VLCDialogProvider</a>
</li>
<li>postUsername:andPassword:forDialogReference:store:
: <a class="el" href="interface_v_l_c_dialog_provider.html#a75004021734803d0acbd1043dfcb59de">VLCDialogProvider</a>
</li>
<li>preAmplification
: <a class="el" href="interface_v_l_c_media_player.html#a1dd4611ad95d596a0d086092ca0c571a">VLCMediaPlayer</a>
</li>
<li>previous
: <a class="el" href="interface_v_l_c_media_list_player.html#afae8b92f265eeec5b87bd55bd0fd104c">VLCMediaListPlayer</a>
</li>
<li>previousChapter
: <a class="el" href="interface_v_l_c_media_player.html#ae2a2398ebf77aaa0dd5218d6e17a61f4">VLCMediaPlayer</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
