<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_n"></a>- n -</h3><ul>
<li>name
: <a class="el" href="interface_v_l_c_renderer_discoverer.html#a831fc60a7a5ea8bc1114bc36cb051f66">VLCRendererDiscoverer</a>
, <a class="el" href="interface_v_l_c_renderer_discoverer_description.html#af6af459a1c5ed50d53695149a2b49a6c">VLCRendererDiscovererDescription</a>
, <a class="el" href="interface_v_l_c_renderer_item.html#a5dcd15900cdcd7901c2e9a9cc6820831">VLCRendererItem</a>
</li>
<li>next
: <a class="el" href="interface_v_l_c_media_list_player.html#a9443120446a79cd785b1e2b71882f199">VLCMediaListPlayer</a>
</li>
<li>nextChapter
: <a class="el" href="interface_v_l_c_media_player.html#acfaac1c1cf8ae35bd40d80afa42bf1f0">VLCMediaPlayer</a>
</li>
<li>NS_ENUM
: <a class="el" href="interface_v_l_c_media.html#a353de8b3f3676f9f422630ed595fdfcc">VLCMedia</a>
, <a class="el" href="interface_v_l_c_media_player.html#a7eb2b3aeb06985b3b655c1c76609924f">VLCMediaPlayer</a>
</li>
<li>NS_UNAVAILABLE
: <a class="el" href="interface_v_l_c_renderer_item.html#a7d4e309821a947227bdf33e2f9eb668e">VLCRendererItem</a>
</li>
<li>nullTime
: <a class="el" href="interface_v_l_c_time.html#ae4b7157f2152d3eddafe14e126a4b96c">VLCTime</a>
</li>
<li>numberOfAudioTracks
: <a class="el" href="interface_v_l_c_media_player.html#a932d093bc73aea01f953a1b96023f401">VLCMediaPlayer</a>
</li>
<li>numberOfBands
: <a class="el" href="interface_v_l_c_media_player.html#abfda4f0fc83029e50e631d2fbe1c8c48">VLCMediaPlayer</a>
</li>
<li>numberOfChaptersForTitle:
: <a class="el" href="interface_v_l_c_media_player.html#a42a283dd2c12d510f247d10510deffff">VLCMediaPlayer</a>
</li>
<li>numberOfCorruptedDataPackets
: <a class="el" href="interface_v_l_c_media.html#a289dc0ff117c7013b6b5363d9f35fd01">VLCMedia</a>
</li>
<li>numberOfDecodedAudioBlocks
: <a class="el" href="interface_v_l_c_media.html#ae5f6aa8f4cfd924c9f31cea1292739de">VLCMedia</a>
</li>
<li>numberOfDecodedVideoBlocks
: <a class="el" href="interface_v_l_c_media.html#a0d65e705f516777543cb6ac2df310779">VLCMedia</a>
</li>
<li>numberOfDiscontinuties
: <a class="el" href="interface_v_l_c_media.html#a148f7e15ef691ed0c6cf631eb3bc34d8">VLCMedia</a>
</li>
<li>numberOfDisplayedPictures
: <a class="el" href="interface_v_l_c_media.html#a42f0be6a3830572833122e758ddaafb1">VLCMedia</a>
</li>
<li>numberOfLostAudioBuffers
: <a class="el" href="interface_v_l_c_media.html#a13d927d07a8bc2cebab7363317c0a932">VLCMedia</a>
</li>
<li>numberOfLostPictures
: <a class="el" href="interface_v_l_c_media.html#ab7456ceac9f4ac4b395bcc50064d58dd">VLCMedia</a>
</li>
<li>numberOfPlayedAudioBuffers
: <a class="el" href="interface_v_l_c_media.html#a958ceff6c2c01085c9c11963fc00e9ab">VLCMedia</a>
</li>
<li>numberOfReadBytesOnDemux
: <a class="el" href="interface_v_l_c_media.html#a936f14e9dbdb6355604040bb963cf1b2">VLCMedia</a>
</li>
<li>numberOfReadBytesOnInput
: <a class="el" href="interface_v_l_c_media.html#aadc4d2257ae507913c39611e9c935665">VLCMedia</a>
</li>
<li>numberOfSentBytes
: <a class="el" href="interface_v_l_c_media.html#a4f663bcbd8cfea3c1fa23035a5e2e119">VLCMedia</a>
</li>
<li>numberOfSentPackets
: <a class="el" href="interface_v_l_c_media.html#a65060dbc9eefe3518c4aa81daba05320">VLCMedia</a>
</li>
<li>numberOfSubtitlesTracks
: <a class="el" href="interface_v_l_c_media_player.html#ae6163ca36922d10f0b30a7275545a673">VLCMediaPlayer</a>
</li>
<li>numberOfTitles
: <a class="el" href="interface_v_l_c_media_player.html#a73e07e681449f1122a4fa1f66d9fc52d">VLCMediaPlayer</a>
</li>
<li>numberOfVideoTracks
: <a class="el" href="interface_v_l_c_media_player.html#af142280306f73367c1a3aa748f7233f9">VLCMediaPlayer</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
