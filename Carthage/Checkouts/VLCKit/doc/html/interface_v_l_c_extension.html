<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>VLCKit: VLCExtension Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Related&#160;Pages</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="class_v_l_c_extension-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCExtension Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_extension_8h_source.html">VLCExtension.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCExtension:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_extension.png" usemap="#VLCExtension_map" alt=""/>
  <map id="VLCExtension_map" name="VLCExtension_map">
</map>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:acecb4294998ecfa4c50c972fe48f3142"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_extension.html#acecb4294998ecfa4c50c972fe48f3142">initWithInstance:</a></td></tr>
<tr class="separator:acecb4294998ecfa4c50c972fe48f3142"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6cd06d338dbb93d34b2f463808b9020"><td class="memItemLeft" align="right" valign="top">(struct extension_t *instance)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_extension.html#ae6cd06d338dbb93d34b2f463808b9020">__attribute__</a></td></tr>
<tr class="separator:ae6cd06d338dbb93d34b2f463808b9020"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a43cff7fac36b80381908c2a5f06e7e35"><td class="memItemLeft" align="right" valign="top">(NSString *name)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_extension.html#a43cff7fac36b80381908c2a5f06e7e35">__attribute__</a></td></tr>
<tr class="separator:a43cff7fac36b80381908c2a5f06e7e35"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a186e9fcd44b717766171932b81cb2c31"><td class="memItemLeft" align="right" valign="top">(NSString *title)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_extension.html#a186e9fcd44b717766171932b81cb2c31">__attribute__</a></td></tr>
<tr class="separator:a186e9fcd44b717766171932b81cb2c31"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>wrapper class for lua extensions within VLCKit </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a class="anchor" id="ae6cd06d338dbb93d34b2f463808b9020"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (struct extension_t* instance) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>the extension instance used to init the wrapper with </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000003">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a class="anchor" id="a43cff7fac36b80381908c2a5f06e7e35"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* name) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>technical name of the extension </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000004">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a class="anchor" id="a186e9fcd44b717766171932b81cb2c31"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* title) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>user-visible name of the extension </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000005">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a class="anchor" id="acecb4294998ecfa4c50c972fe48f3142"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithInstance: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>initializer for wrapper class </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">instance</td><td>the extension_t instance to init the wrapper with </td></tr>
  </table>
  </dd>
</dl>
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000002">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_extension_8h_source.html">VLCExtension.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
