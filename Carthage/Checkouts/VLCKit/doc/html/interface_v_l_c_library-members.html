<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Member List</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCLibrary Member List</div>  </div>
</div><!--header-->
<div class="contents">

<p>This is the complete list of members for <a class="el" href="interface_v_l_c_library.html">VLCLibrary</a>, including all inherited members.</p>
<table class="directory">
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#a9201c5f4b1ee745e31e3875835914124">changeset</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#a91485a448eb7b18c7b5c36bba4c56184">compiler</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#a30523c86de4b6a2fbc86f1d6a8274b59">debugLogging</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#a0b0d7e806f3c1c49243873f99c3608fa">debugLoggingLevel</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#a7194d31353ddeef7273dd9d9c394841a">debugLoggingTarget</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_library.html#ab029c774c0b133dc9c15b9f4446de8c5">initWithOptions:</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#abb375a92ef38f3cacab3d1d501b747d3">instance</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_library.html#afe1e5231b6bc4b9b12e0fd2c44862dbb">setApplicationIdentifier:withVersion:andApplicationIconName:</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_library.html#a86c8d6b68e6852dea7045a40a9110dde">setDebugLoggingToFile:</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr><td class="entry">-&#160;</td><td><a class="el" href="interface_v_l_c_library.html#ae91040ee78413a160918871e1e9475ff">setHumanReadableName:withHTTPUserAgent:</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
  <tr class="even"><td class="entry">+&#160;</td><td><a class="el" href="interface_v_l_c_library.html#a7eddfd69ce66ffc5603bc68a5777187e">sharedLibrary</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"><span class="mlabel">static</span></td></tr>
  <tr><td class="entry"></td><td class="entry"><a class="el" href="interface_v_l_c_library.html#a99403afc6373f5f0ca5b89b6c64d3f32">version</a></td><td class="entry"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a></td><td class="entry"></td></tr>
</table></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
