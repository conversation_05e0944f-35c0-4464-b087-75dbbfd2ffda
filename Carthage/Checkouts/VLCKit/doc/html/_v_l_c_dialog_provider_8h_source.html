<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Headers/Public/VLCDialogProvider.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html">Headers</a></li><li class="navelem"><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html">Public</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCDialogProvider.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/*****************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * VLCDialogProvider.h: an implementation of the libvlc dialog API</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *****************************************************************************</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Copyright (C) 2016 VideoLabs SAS</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * $Id$</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> * Authors: <AUTHORS>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> * This program is free software; you can redistribute it and/or modify it</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> * under the terms of the GNU Lesser General Public License as published by</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> * the Free Software Foundation; either version 2.1 of the License, or</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> * (at your option) any later version.</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> * This program is distributed in the hope that it will be useful,</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * but WITHOUT ANY WARRANTY; without even the implied warranty of</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * GNU Lesser General Public License for more details.</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * You should have received a copy of the GNU Lesser General Public License</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * along with this program; if not, write to the Free Software Foundation,</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> *****************************************************************************/</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160; </div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_library.html">VLCLibrary</a>;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160; </div>
<div class="line"><a name="l00026"></a><span class="lineno"><a class="line" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html#a8f81ae57df8581c1df11d5407d4ce1e0">   26</a></span>&#160;<span class="keyword">typedef</span> NS_ENUM(NSUInteger, VLCDialogQuestionType) {</div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;    VLCDialogQuestionNormal,</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;    VLCDialogQuestionWarning,</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;    VLCDialogQuestionCritical,</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;};</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160; </div>
<div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html">   35</a></span>&#160;<span class="keyword">@protocol </span><a class="code" href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html">VLCCustomDialogRendererProtocol</a> &lt;NSObject&gt;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;- (void)showErrorWithTitle:(NSString * _Nonnull)error</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;      message:(NSString * _Nonnull)message;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160; </div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;- (void)showLoginWithTitle:(NSString * _Nonnull)title</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;                   message:(NSString * _Nonnull)message</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;           defaultUsername:(NSString * _Nullable)username</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;          askingForStorage:(BOOL)askingForStorage</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;             withReference:(NSValue * _Nonnull)reference;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160; </div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;- (void)showQuestionWithTitle:(NSString * _Nonnull)title</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;                      message:(NSString * _Nonnull)message</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;                         type:(VLCDialogQuestionType)questionType</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;                 cancelString:(NSString * _Nullable)cancelString</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;                action1String:(NSString * _Nullable)action1String</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;                action2String:(NSString * _Nullable)action2String</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                withReference:(NSValue * _Nonnull)reference;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160; </div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;- (void)showProgressWithTitle:(NSString * _Nonnull)title</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;                      message:(NSString * _Nonnull)message</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;              isIndeterminate:(BOOL)isIndeterminate</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;                     position:(<span class="keywordtype">float</span>)position</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;                 cancelString:(NSString * _Nullable)cancelString</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;                withReference:(NSValue * _Nonnull)reference;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160; </div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;- (void)updateProgressWithReference:(NSValue * _Nonnull)reference</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;                            message:(NSString * _Nullable)message</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;                            position:(<span class="keywordtype">float</span>)position;</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160; </div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;- (void)cancelDialogWithReference:(NSValue * _Nonnull)reference;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160; </div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160; </div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160; </div>
<div class="line"><a name="l00115"></a><span class="lineno"><a class="line" href="interface_v_l_c_dialog_provider.html">  115</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_dialog_provider.html">VLCDialogProvider</a> : NSObject</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160; </div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;- (instancetype _Nullable)initWithLibrary:(<a class="code" href="interface_v_l_c_library.html">VLCLibrary</a> * _Nullable)library</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;                                 customUI:(BOOL)customUI;</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160; </div>
<div class="line"><a name="l00133"></a><span class="lineno"><a class="line" href="interface_v_l_c_dialog_provider.html#a75004021734803d0acbd1043dfcb59de">  133</a></span>&#160;<span class="keyword">@property</span> (weak, readwrite, nonatomic, nullable) id&lt;VLCCustomDialogRendererProtocol&gt; <a class="code" href="interface_v_l_c_dialog_provider.html#a15847411703d3b120abb0a9b4b7e8ed2">customRenderer</a>;</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160; </div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;- (void)postUsername:(NSString * _Nonnull)username</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;         andPassword:(NSString * _Nonnull)password</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  forDialogReference:(NSValue * _Nonnull)dialogReference</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;               store:(BOOL)store;</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160; </div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;- (void)postAction:(<span class="keywordtype">int</span>)buttonNumber</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;forDialogReference:(NSValue * _Nonnull)dialogReference;</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160; </div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;- (void)dismissDialogWithReference:(NSValue * _Nonnull)dialogReference;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160; </div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;<span class="keyword">@end</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="aprotocol_v_l_c_custom_dialog_renderer_protocol-p_html"><div class="ttname"><a href="protocol_v_l_c_custom_dialog_renderer_protocol-p.html">VLCCustomDialogRendererProtocol-p</a></div><div class="ttdef"><b>Definition:</b> VLCDialogProvider.h:35</div></div>
<div class="ttc" id="ainterface_v_l_c_dialog_provider_html"><div class="ttname"><a href="interface_v_l_c_dialog_provider.html">VLCDialogProvider</a></div><div class="ttdef"><b>Definition:</b> VLCDialogProvider.h:116</div></div>
<div class="ttc" id="ainterface_v_l_c_library_html"><div class="ttname"><a href="interface_v_l_c_library.html">VLCLibrary</a></div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:47</div></div>
<div class="ttc" id="ainterface_v_l_c_dialog_provider_html_a15847411703d3b120abb0a9b4b7e8ed2"><div class="ttname"><a href="interface_v_l_c_dialog_provider.html#a15847411703d3b120abb0a9b4b7e8ed2">VLCDialogProvider::customRenderer</a></div><div class="ttdeci">id&lt; VLCCustomDialogRendererProtocol &gt; customRenderer</div><div class="ttdef"><b>Definition:</b> VLCDialogProvider.h:133</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
