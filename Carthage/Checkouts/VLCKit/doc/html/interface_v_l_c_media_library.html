<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCMediaLibrary Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#pub-static-methods">Class Methods</a> &#124;
<a href="interface_v_l_c_media_library-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCMediaLibrary Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_media_library_8h_source.html">VLCMediaLibrary.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCMediaLibrary:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_media_library.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:ab7a54a9b8754b31a7f19b6bbf2b65df5"><td class="memItemLeft" align="right" valign="top">(<a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a> *allMedia)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_library.html#ab7a54a9b8754b31a7f19b6bbf2b65df5">__attribute__</a></td></tr>
<tr class="separator:ab7a54a9b8754b31a7f19b6bbf2b65df5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Class Methods</h2></td></tr>
<tr class="memitem:afbb02ac470223ee5bebba45220326675"><td class="memItemLeft" align="right" valign="top">((deprecated)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_media_library.html#afbb02ac470223ee5bebba45220326675">__attribute__</a></td></tr>
<tr class="separator:afbb02ac470223ee5bebba45220326675"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>media library stub </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="ab7a54a9b8754b31a7f19b6bbf2b65df5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab7a54a9b8754b31a7f19b6bbf2b65df5">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a>* allMedia) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>list of all media </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000008">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<a id="afbb02ac470223ee5bebba45220326675"></a>
<h2 class="memtitle"><span class="permalink"><a href="#afbb02ac470223ee5bebba45220326675">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ ((deprecated) __attribute__ </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>library singleton </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000007">Deprecated:</a></b></dt><dd>will be removed in the next release </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_media_library_8h_source.html">VLCMediaLibrary.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
