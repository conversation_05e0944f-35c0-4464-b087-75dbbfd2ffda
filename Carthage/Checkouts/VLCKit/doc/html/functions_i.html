<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_i"></a>- i -</h3><ul>
<li>iconURI
: <a class="el" href="interface_v_l_c_renderer_item.html#a2fd800be1553921e947962d62a767601">VLCRendererItem</a>
</li>
<li>indexOfLongestTitle
: <a class="el" href="interface_v_l_c_media_player.html#a93ba313f95351de59e84cdeeea720822">VLCMediaPlayer</a>
</li>
<li>indexOfMedia:
: <a class="el" href="interface_v_l_c_media_list.html#a38eeb1fa92b98b7b4823acf39e8e8865">VLCMediaList</a>
</li>
<li>initAsNodeWithName:
: <a class="el" href="interface_v_l_c_media.html#af9a048525b9aeb4919c47e1148962638">VLCMedia</a>
</li>
<li>initWithArray:
: <a class="el" href="interface_v_l_c_media_list.html#a29976e5d1b3eae05b6b052d2765f5090">VLCMediaList</a>
</li>
<li>initWithDrawable:
: <a class="el" href="interface_v_l_c_media_list_player.html#a39e3151a069ead5d9bdc4bd1d8b0e815">VLCMediaListPlayer</a>
</li>
<li>initWithInt:
: <a class="el" href="interface_v_l_c_time.html#ac9717d5978ab18fa6547cde44904cfad">VLCTime</a>
</li>
<li>initWithLibrary:customUI:
: <a class="el" href="interface_v_l_c_dialog_provider.html#a51bf3d3c491d976761a78ac431f9e986">VLCDialogProvider</a>
</li>
<li>initWithLibVLCInstance:andLibrary:
: <a class="el" href="interface_v_l_c_media_player.html#a91e1bad6eaedb58cbfe097f633c50ebd">VLCMediaPlayer</a>
</li>
<li>initWithName:
: <a class="el" href="interface_v_l_c_media_discoverer.html#a41cd6e81501ef6d0f90788c6dc761cce">VLCMediaDiscoverer</a>
, <a class="el" href="interface_v_l_c_renderer_discoverer.html#a955ed444b32f6fd9dedbecc880142567">VLCRendererDiscoverer</a>
</li>
<li>initWithName:libraryInstance:
: <a class="el" href="interface_v_l_c_media_discoverer.html#a4795127962209e66c1b316381177205a">VLCMediaDiscoverer</a>
</li>
<li>initWithName:longName:
: <a class="el" href="interface_v_l_c_renderer_discoverer_description.html#abc3330cc3e3de86b0f247981daf5dfb6">VLCRendererDiscovererDescription</a>
</li>
<li>initWithNumber:
: <a class="el" href="interface_v_l_c_time.html#a4681ebad8d27e7243ebf6d83679e8a24">VLCTime</a>
</li>
<li>initWithOptionDictionary:
: <a class="el" href="interface_v_l_c_stream_output.html#a11c02ad55e225c5afa473aeb15db13e0">VLCStreamOutput</a>
</li>
<li>initWithOptions:
: <a class="el" href="interface_v_l_c_library.html#ab029c774c0b133dc9c15b9f4446de8c5">VLCLibrary</a>
, <a class="el" href="interface_v_l_c_media_list_player.html#a2026b15a0a74d3be733346074f44ba18">VLCMediaListPlayer</a>
, <a class="el" href="interface_v_l_c_media_player.html#a80fb8e4483b8675f8752ad006fd0f361">VLCMediaPlayer</a>
</li>
<li>initWithOptions:andDrawable:
: <a class="el" href="interface_v_l_c_media_list_player.html#a9229793245755c2bf82cedf7d4032a90">VLCMediaListPlayer</a>
</li>
<li>initWithPath:
: <a class="el" href="interface_v_l_c_media.html#a4215c08e40a19e60bf10a9ea15b8fb85">VLCMedia</a>
</li>
<li>initWithStream:
: <a class="el" href="interface_v_l_c_media.html#a0f4a6c10ac143fdcd19bc12fdb2bb71a">VLCMedia</a>
</li>
<li>initWithURL:
: <a class="el" href="interface_v_l_c_media.html#a1a980dff03ccacf966e754c0a60bac49">VLCMedia</a>
</li>
<li>initWithVideoLayer:
: <a class="el" href="interface_v_l_c_media_player.html#ad00097d1bab674773134188770396d1d">VLCMediaPlayer</a>
</li>
<li>initWithVideoView:
: <a class="el" href="interface_v_l_c_media_player.html#a432545540f6be27394824275fa6e3d10">VLCMediaPlayer</a>
</li>
<li>inputBitrate
: <a class="el" href="interface_v_l_c_media.html#a2eb646a3d37eaec7de62ba174b9682f7">VLCMedia</a>
</li>
<li>insertMedia:atIndex:
: <a class="el" href="interface_v_l_c_media_list.html#a6855e74eaf93ec3e512d683423ecc2e3">VLCMediaList</a>
</li>
<li>instance
: <a class="el" href="interface_v_l_c_library.html#abb375a92ef38f3cacab3d1d501b747d3">VLCLibrary</a>
</li>
<li>intValue
: <a class="el" href="interface_v_l_c_time.html#aff49484cae12d9fbf65ce6b4a7da2cfd">VLCTime</a>
</li>
<li>ipodStreamOutputWithFilePath:
: <a class="el" href="interface_v_l_c_stream_output.html#a518d4dd8d9b9733acd58dce332f8d004">VLCStreamOutput</a>
</li>
<li>isEqual:
: <a class="el" href="interface_v_l_c_time.html#a02ff06b1610b86d9bb1770a93837be42">VLCTime</a>
</li>
<li>isReadOnly
: <a class="el" href="interface_v_l_c_media_list.html#ad9f5b01b188f801ba9a2d675d6d94962">VLCMediaList</a>
</li>
<li>isRunning
: <a class="el" href="interface_v_l_c_media_discoverer.html#abbd18cde0dc5c6982ec6a5c356fe9f7e">VLCMediaDiscoverer</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
