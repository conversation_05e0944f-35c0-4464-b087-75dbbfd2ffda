<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Headers/Public/VLCAudio.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html">Headers</a></li><li class="navelem"><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html">Public</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCAudio.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/*****************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * VLCAudio.h: VLCKit.framework VLCAudio header</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *****************************************************************************</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Copyright (C) 2007 Faustino E. Osuna</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * Copyright (C) 2007, 2014 VLC authors and VideoLAN</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> * $Id$</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> * Authors: <AUTHORS>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> * This program is free software; you can redistribute it and/or modify it</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> * under the terms of the GNU Lesser General Public License as published by</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> * the Free Software Foundation; either version 2.1 of the License, or</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> * (at your option) any later version.</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * This program is distributed in the hope that it will be useful,</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * but WITHOUT ANY WARRANTY; without even the implied warranty of</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * GNU Lesser General Public License for more details.</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * You should have received a copy of the GNU Lesser General Public License</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * along with this program; if not, write to the Free Software Foundation,</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *****************************************************************************/</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160; </div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#import &lt;Foundation/Foundation.h&gt;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160; </div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="comment">/* Notification Messages */</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMediaPlayerVolumeChanged;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160; </div>
<div class="line"><a name="l00036"></a><span class="lineno"><a class="line" href="interface_v_l_c_audio.html">   36</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_audio.html">VLCAudio</a> : NSObject</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160; </div>
<div class="line"><a name="l00042"></a><span class="lineno"><a class="line" href="interface_v_l_c_audio.html#aa2739a3d1ec35d69a9920e4a9588ef8c">   42</a></span>&#160;<span class="keyword">@property</span> (getter=isMuted) BOOL <a class="code" href="interface_v_l_c_audio.html#aa2739a3d1ec35d69a9920e4a9588ef8c">muted</a>;</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160; </div>
<div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="interface_v_l_c_audio.html#a553194cab2c141b89804db92e1a43f87">   46</a></span>&#160;<span class="keyword">@property</span> (assign) <span class="keywordtype">int</span> <a class="code" href="interface_v_l_c_audio.html#a553194cab2c141b89804db92e1a43f87">volume</a>;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160; </div>
<div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="interface_v_l_c_audio.html#abce4117645f4fc354f457dca9a991aa3">   51</a></span>&#160;<span class="keyword">@property</span> (readwrite) BOOL <a class="code" href="interface_v_l_c_audio.html#ac37223907edb0cafcee6f609edc28782">passthrough</a>;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160; </div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;- (void)setMute:(BOOL)value __attribute__((deprecated));</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160; </div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;- (void)<a class="code" href="interface_v_l_c_audio.html#a35b0ee4fbc502b52f7ae89970b27a246">volumeDown</a>;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160; </div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;- (void)<a class="code" href="interface_v_l_c_audio.html#abce4117645f4fc354f457dca9a991aa3">volumeUp</a>;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160; </div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;<span class="keyword">@end</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="ainterface_v_l_c_audio_html_a553194cab2c141b89804db92e1a43f87"><div class="ttname"><a href="interface_v_l_c_audio.html#a553194cab2c141b89804db92e1a43f87">VLCAudio::volume</a></div><div class="ttdeci">int volume</div><div class="ttdef"><b>Definition:</b> VLCAudio.h:46</div></div>
<div class="ttc" id="ainterface_v_l_c_audio_html_ac37223907edb0cafcee6f609edc28782"><div class="ttname"><a href="interface_v_l_c_audio.html#ac37223907edb0cafcee6f609edc28782">VLCAudio::passthrough</a></div><div class="ttdeci">BOOL passthrough</div><div class="ttdef"><b>Definition:</b> VLCAudio.h:51</div></div>
<div class="ttc" id="ainterface_v_l_c_audio_html_a35b0ee4fbc502b52f7ae89970b27a246"><div class="ttname"><a href="interface_v_l_c_audio.html#a35b0ee4fbc502b52f7ae89970b27a246">-[VLCAudio volumeDown]</a></div><div class="ttdeci">void volumeDown()</div></div>
<div class="ttc" id="ainterface_v_l_c_audio_html_abce4117645f4fc354f457dca9a991aa3"><div class="ttname"><a href="interface_v_l_c_audio.html#abce4117645f4fc354f457dca9a991aa3">-[VLCAudio volumeUp]</a></div><div class="ttdeci">void volumeUp()</div></div>
<div class="ttc" id="ainterface_v_l_c_audio_html"><div class="ttname"><a href="interface_v_l_c_audio.html">VLCAudio</a></div><div class="ttdef"><b>Definition:</b> VLCAudio.h:37</div></div>
<div class="ttc" id="ainterface_v_l_c_audio_html_aa2739a3d1ec35d69a9920e4a9588ef8c"><div class="ttname"><a href="interface_v_l_c_audio.html#aa2739a3d1ec35d69a9920e4a9588ef8c">VLCAudio::muted</a></div><div class="ttdeci">BOOL muted</div><div class="ttdef"><b>Definition:</b> VLCAudio.h:42</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
