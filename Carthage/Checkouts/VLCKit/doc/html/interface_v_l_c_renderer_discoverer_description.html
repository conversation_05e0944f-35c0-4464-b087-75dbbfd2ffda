<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCRendererDiscovererDescription Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_renderer_discoverer_description-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCRendererDiscovererDescription Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_renderer_discoverer_8h_source.html">VLCRendererDiscoverer.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCRendererDiscovererDescription:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_renderer_discoverer_description.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:abc3330cc3e3de86b0f247981daf5dfb6"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_renderer_discoverer_description.html#abc3330cc3e3de86b0f247981daf5dfb6">initWithName:longName:</a></td></tr>
<tr class="separator:abc3330cc3e3de86b0f247981daf5dfb6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:af6af459a1c5ed50d53695149a2b49a6c"><td class="memItemLeft" align="right" valign="top">NSString *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_renderer_discoverer_description.html#af6af459a1c5ed50d53695149a2b49a6c">name</a></td></tr>
<tr class="separator:af6af459a1c5ed50d53695149a2b49a6c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a164296731eab83b21084576ded002861"><td class="memItemLeft" align="right" valign="top">NSString *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_renderer_discoverer_description.html#a164296731eab83b21084576ded002861">longName</a></td></tr>
<tr class="separator:a164296731eab83b21084576ded002861"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>Renderer Discoverer description </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="abc3330cc3e3de86b0f247981daf5dfb6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abc3330cc3e3de86b0f247981daf5dfb6">&#9670;&nbsp;</a></span>initWithName:longName:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithName: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>name</em></td>
        </tr>
        <tr>
          <td class="paramkey">longName:</td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>longName</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Instanciates an object that holds information about a renderer discoverer </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">name</td><td>Name of the renderer discoverer </td></tr>
    <tr><td class="paramname">longName</td><td>Long name of the renderer discoverer </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>A new <code><a class="el" href="interface_v_l_c_renderer_discoverer_description.html">VLCRendererDiscovererDescription</a></code> object, only if there were no errors </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a164296731eab83b21084576ded002861"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a164296731eab83b21084576ded002861">&#9670;&nbsp;</a></span>longName</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString*) longName</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Long name of the renderer discoverer </p>

</div>
</div>
<a id="af6af459a1c5ed50d53695149a2b49a6c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af6af459a1c5ed50d53695149a2b49a6c">&#9670;&nbsp;</a></span>name</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString*) name</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Name of the renderer discoverer </p>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_renderer_discoverer_8h_source.html">VLCRendererDiscoverer.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
