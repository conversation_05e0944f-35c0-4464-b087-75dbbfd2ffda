<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCMediaPlayer Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_media_player-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCMediaPlayer Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_media_player_8h_source.html">VLCMediaPlayer.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCMediaPlayer:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_media_player.png" usemap="#VLCMediaPlayer_map" alt=""/>
  <map id="VLCMediaPlayer_map" name="VLCMediaPlayer_map">
<area href="interface_v_l_c_stream_session.html" alt="VLCStreamSession" shape="rect" coords="0,112,120,136"/>
  </map>
</div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a432545540f6be27394824275fa6e3d10"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a432545540f6be27394824275fa6e3d10">initWithVideoView:</a></td></tr>
<tr class="separator:a432545540f6be27394824275fa6e3d10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad00097d1bab674773134188770396d1d"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ad00097d1bab674773134188770396d1d">initWithVideoLayer:</a></td></tr>
<tr class="separator:ad00097d1bab674773134188770396d1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80fb8e4483b8675f8752ad006fd0f361"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a80fb8e4483b8675f8752ad006fd0f361">initWithOptions:</a></td></tr>
<tr class="separator:a80fb8e4483b8675f8752ad006fd0f361"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a91e1bad6eaedb58cbfe097f633c50ebd"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a91e1bad6eaedb58cbfe097f633c50ebd">initWithLibVLCInstance:andLibrary:</a></td></tr>
<tr class="separator:a91e1bad6eaedb58cbfe097f633c50ebd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1eb2229ede2d006bec1650e4d4b0fa02"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a1eb2229ede2d006bec1650e4d4b0fa02">setVideoView:</a></td></tr>
<tr class="separator:a1eb2229ede2d006bec1650e4d4b0fa02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a76dc478bf25fae8e0f671e39d006ce25"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a76dc478bf25fae8e0f671e39d006ce25">setVideoLayer:</a></td></tr>
<tr class="separator:a76dc478bf25fae8e0f671e39d006ce25"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac0e16c49e1746e74a35fd50f870c5e31"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ac0e16c49e1746e74a35fd50f870c5e31">saveVideoSnapshotAt:withWidth:andHeight:</a></td></tr>
<tr class="separator:ac0e16c49e1746e74a35fd50f870c5e31"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a95abbbed4ddab2adb3fb5c4a8b8ae076"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a95abbbed4ddab2adb3fb5c4a8b8ae076">setDeinterlaceFilter:</a></td></tr>
<tr class="separator:a95abbbed4ddab2adb3fb5c4a8b8ae076"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a236ed2450c49f8acd0846499bf26fbb5"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a236ed2450c49f8acd0846499bf26fbb5">setDeinterlace:withFilter:</a></td></tr>
<tr class="separator:a236ed2450c49f8acd0846499bf26fbb5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab59f33aa946850a6387d87630c2cca16"><td class="memItemLeft" align="right" valign="top">(float framesPerSecond)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ab59f33aa946850a6387d87630c2cca16">__attribute__</a></td></tr>
<tr class="separator:ab59f33aa946850a6387d87630c2cca16"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a18546cd8ca1b827eb5ddb9384e172166"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a18546cd8ca1b827eb5ddb9384e172166">openVideoSubTitlesFromFile:</a></td></tr>
<tr class="separator:a18546cd8ca1b827eb5ddb9384e172166"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7eb2b3aeb06985b3b655c1c76609924f"><td class="memItemLeft" align="right" valign="top">(typedef)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a7eb2b3aeb06985b3b655c1c76609924f">NS_ENUM</a></td></tr>
<tr class="separator:a7eb2b3aeb06985b3b655c1c76609924f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9477125c5c85a4c598e4088c031331e2"><td class="memItemLeft" align="right" valign="top">(int)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a9477125c5c85a4c598e4088c031331e2">addPlaybackSlave:type:enforce:</a></td></tr>
<tr class="separator:a9477125c5c85a4c598e4088c031331e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae2a2398ebf77aaa0dd5218d6e17a61f4"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ae2a2398ebf77aaa0dd5218d6e17a61f4">previousChapter</a></td></tr>
<tr class="separator:ae2a2398ebf77aaa0dd5218d6e17a61f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acfaac1c1cf8ae35bd40d80afa42bf1f0"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#acfaac1c1cf8ae35bd40d80afa42bf1f0">nextChapter</a></td></tr>
<tr class="separator:acfaac1c1cf8ae35bd40d80afa42bf1f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a42a283dd2c12d510f247d10510deffff"><td class="memItemLeft" align="right" valign="top">(int)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a42a283dd2c12d510f247d10510deffff">numberOfChaptersForTitle:</a></td></tr>
<tr class="separator:a42a283dd2c12d510f247d10510deffff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59fbfd5a29004a32b9f64328ad6b57a4"><td class="memItemLeft" align="right" valign="top">(NSArray *)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a59fbfd5a29004a32b9f64328ad6b57a4">chaptersForTitleIndex:</a></td></tr>
<tr class="separator:a59fbfd5a29004a32b9f64328ad6b57a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a637f12081f1748693c9234b33c752640"><td class="memItemLeft" align="right" valign="top">(NSArray *)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a637f12081f1748693c9234b33c752640">chapterDescriptionsOfTitle:</a></td></tr>
<tr class="separator:a637f12081f1748693c9234b33c752640"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae0b500274a748ac5c035c2a3c46c366"><td class="memItemLeft" align="right" valign="top">(NSUInteger countOfTitles)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#aae0b500274a748ac5c035c2a3c46c366">__attribute__</a></td></tr>
<tr class="separator:aae0b500274a748ac5c035c2a3c46c366"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e26f7c5c05ef844fc1d8a3ae8e99ad4"><td class="memItemLeft" align="right" valign="top">(NSArray *titles)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a4e26f7c5c05ef844fc1d8a3ae8e99ad4">__attribute__</a></td></tr>
<tr class="separator:a4e26f7c5c05ef844fc1d8a3ae8e99ad4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb2864039496be0bde196467971dd873"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#abb2864039496be0bde196467971dd873">resetEqualizerFromProfile:</a></td></tr>
<tr class="separator:abb2864039496be0bde196467971dd873"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa46d4dfad32097dc1cac62f795e06f39"><td class="memItemLeft" align="right" valign="top">(CGFloat)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#aa46d4dfad32097dc1cac62f795e06f39">frequencyOfBandAtIndex:</a></td></tr>
<tr class="separator:aa46d4dfad32097dc1cac62f795e06f39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae61e529ff86b246ebe54cc29b0a96c0e"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ae61e529ff86b246ebe54cc29b0a96c0e">setAmplification:forBand:</a></td></tr>
<tr class="separator:ae61e529ff86b246ebe54cc29b0a96c0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab60ddfe3c2f9d1943e094ce169ac1dbf"><td class="memItemLeft" align="right" valign="top">(CGFloat)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ab60ddfe3c2f9d1943e094ce169ac1dbf">amplificationOfBand:</a></td></tr>
<tr class="separator:ab60ddfe3c2f9d1943e094ce169ac1dbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7da1ef4be33931daadf5937cd2365924"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a7da1ef4be33931daadf5937cd2365924">play</a></td></tr>
<tr class="separator:a7da1ef4be33931daadf5937cd2365924"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaef26685e063e62599a5b0248a072a0f"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#aaef26685e063e62599a5b0248a072a0f">pause</a></td></tr>
<tr class="separator:aaef26685e063e62599a5b0248a072a0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad7184261982c10d5d1307e37ed16fb52"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ad7184261982c10d5d1307e37ed16fb52">stop</a></td></tr>
<tr class="separator:ad7184261982c10d5d1307e37ed16fb52"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0efca8c5d59212429f7aa715f61b7637"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a0efca8c5d59212429f7aa715f61b7637">gotoNextFrame</a></td></tr>
<tr class="separator:a0efca8c5d59212429f7aa715f61b7637"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ad6cf29b157d2042f125b03a739c6ea"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a1ad6cf29b157d2042f125b03a739c6ea">fastForward</a></td></tr>
<tr class="separator:a1ad6cf29b157d2042f125b03a739c6ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0790b3fdd424f5c59472f416b8ee5f4"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ae0790b3fdd424f5c59472f416b8ee5f4">fastForwardAtRate:</a></td></tr>
<tr class="separator:ae0790b3fdd424f5c59472f416b8ee5f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac03553439681974c0da7014c44b104d6"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ac03553439681974c0da7014c44b104d6">rewind</a></td></tr>
<tr class="separator:ac03553439681974c0da7014c44b104d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ade646072ef74f61b84e4afdc260a0f36"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ade646072ef74f61b84e4afdc260a0f36">rewindAtRate:</a></td></tr>
<tr class="separator:ade646072ef74f61b84e4afdc260a0f36"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a86d420386d6dde28818ae2a7e56f6198"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a86d420386d6dde28818ae2a7e56f6198">jumpBackward:</a></td></tr>
<tr class="separator:a86d420386d6dde28818ae2a7e56f6198"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a51e8067be7f55f64d3cfa3e4acccfafc"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a51e8067be7f55f64d3cfa3e4acccfafc">jumpForward:</a></td></tr>
<tr class="separator:a51e8067be7f55f64d3cfa3e4acccfafc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefc367fa665de839effe00bccb4b7261"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#aefc367fa665de839effe00bccb4b7261">extraShortJumpBackward</a></td></tr>
<tr class="separator:aefc367fa665de839effe00bccb4b7261"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac77c500c9dbfb4bf3f3228db0f970e50"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ac77c500c9dbfb4bf3f3228db0f970e50">extraShortJumpForward</a></td></tr>
<tr class="separator:ac77c500c9dbfb4bf3f3228db0f970e50"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a8548e2888bbcf9bf8ed5a35fc30cf4"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a8a8548e2888bbcf9bf8ed5a35fc30cf4">shortJumpBackward</a></td></tr>
<tr class="separator:a8a8548e2888bbcf9bf8ed5a35fc30cf4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab09028de82e23ba963e7b645949aa212"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ab09028de82e23ba963e7b645949aa212">shortJumpForward</a></td></tr>
<tr class="separator:ab09028de82e23ba963e7b645949aa212"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae010feb668404c839ca683ecef6ee7a2"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ae010feb668404c839ca683ecef6ee7a2">mediumJumpBackward</a></td></tr>
<tr class="separator:ae010feb668404c839ca683ecef6ee7a2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae87c31a4ebc3274b2b275bc5ac42f24c"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ae87c31a4ebc3274b2b275bc5ac42f24c">mediumJumpForward</a></td></tr>
<tr class="separator:ae87c31a4ebc3274b2b275bc5ac42f24c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a78d5c9af63086443c9539c06b63bff4d"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a78d5c9af63086443c9539c06b63bff4d">longJumpBackward</a></td></tr>
<tr class="separator:a78d5c9af63086443c9539c06b63bff4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad04f7a86349209e50615afc1a513a768"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#ad04f7a86349209e50615afc1a513a768">longJumpForward</a></td></tr>
<tr class="separator:ad04f7a86349209e50615afc1a513a768"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2309a13bb4aa332f3dd1eecead8831a3"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a2309a13bb4aa332f3dd1eecead8831a3">performNavigationAction:</a></td></tr>
<tr class="separator:a2309a13bb4aa332f3dd1eecead8831a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe9c128cdaa533d51c5209d4c17d5b09"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#abe9c128cdaa533d51c5209d4c17d5b09">updateViewpoint:pitch:roll:fov:absolute:</a></td></tr>
<tr class="separator:abe9c128cdaa533d51c5209d4c17d5b09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0af0ae5ca6a1b5f68efc764a296b6876"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a0af0ae5ca6a1b5f68efc764a296b6876">startRecordingAtPath:</a></td></tr>
<tr class="separator:a0af0ae5ca6a1b5f68efc764a296b6876"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a41917b1e63701715a0e66f16e22c6f63"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a41917b1e63701715a0e66f16e22c6f63">stopRecording</a></td></tr>
<tr class="separator:a41917b1e63701715a0e66f16e22c6f63"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44f8101ea62406d757c44520361f8930"><td class="memItemLeft" align="right" valign="top">(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_player.html#a44f8101ea62406d757c44520361f8930">setRendererItem:</a></td></tr>
<tr class="separator:a44f8101ea62406d757c44520361f8930"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a20081eb2719b21bd55b4d2ce185f86f2"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a20081eb2719b21bd55b4d2ce185f86f2">VLCChapterDescriptionName</a></td></tr>
<tr class="separator:a20081eb2719b21bd55b4d2ce185f86f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab5d7190ce89c08ae1b14e6c2a827d104"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ab5d7190ce89c08ae1b14e6c2a827d104">VLCChapterDescriptionTimeOffset</a></td></tr>
<tr class="separator:ab5d7190ce89c08ae1b14e6c2a827d104"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d87f02211d47497f35bf616f7a0374e"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a5d87f02211d47497f35bf616f7a0374e">VLCChapterDescriptionDuration</a></td></tr>
<tr class="separator:a5d87f02211d47497f35bf616f7a0374e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4bd07aad0599f2f61cbac7281981df7"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ac4bd07aad0599f2f61cbac7281981df7">VLCTitleDescriptionDuration</a></td></tr>
<tr class="separator:ac4bd07aad0599f2f61cbac7281981df7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1cfbed633aa7783841c153d48088ba70"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a1cfbed633aa7783841c153d48088ba70">VLCTitleDescriptionIsMenu</a></td></tr>
<tr class="separator:a1cfbed633aa7783841c153d48088ba70"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a32b5af405c337b3704957d7c15cbdd61"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a32b5af405c337b3704957d7c15cbdd61">libraryInstance</a></td></tr>
<tr class="separator:a32b5af405c337b3704957d7c15cbdd61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a138068f5da49d36c7fbd43dabea55666"><td class="memItemLeft" align="right" valign="top">id&lt; <a class="el" href="protocol_v_l_c_media_player_delegate-p.html">VLCMediaPlayerDelegate</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a138068f5da49d36c7fbd43dabea55666">delegate</a></td></tr>
<tr class="separator:a138068f5da49d36c7fbd43dabea55666"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6fa6a9a81e9db3aa58596a1b5b48196"><td class="memItemLeft" align="right" valign="top">id&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#af6fa6a9a81e9db3aa58596a1b5b48196">drawable</a></td></tr>
<tr class="separator:af6fa6a9a81e9db3aa58596a1b5b48196"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3ee849792344fed560e4308ebe8e4a76"><td class="memItemLeft" align="right" valign="top">char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a3ee849792344fed560e4308ebe8e4a76">videoAspectRatio</a></td></tr>
<tr class="separator:a3ee849792344fed560e4308ebe8e4a76"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3f0fd895e58be570f115ab6f09501ffe"><td class="memItemLeft" align="right" valign="top">char *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a3f0fd895e58be570f115ab6f09501ffe">videoCropGeometry</a></td></tr>
<tr class="separator:a3f0fd895e58be570f115ab6f09501ffe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a773e8e2b5b169fa6cb0bcef37330d327"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a773e8e2b5b169fa6cb0bcef37330d327">scaleFactor</a></td></tr>
<tr class="separator:a773e8e2b5b169fa6cb0bcef37330d327"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af7a158c0c906970b991154efab0300bd"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#af7a158c0c906970b991154efab0300bd">adjustFilterEnabled</a></td></tr>
<tr class="separator:af7a158c0c906970b991154efab0300bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aee995a8531e197917dbad6516594a777"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aee995a8531e197917dbad6516594a777">contrast</a></td></tr>
<tr class="separator:aee995a8531e197917dbad6516594a777"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aec3ba8ab1cc0e096e27e26718904d8a7"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aec3ba8ab1cc0e096e27e26718904d8a7">brightness</a></td></tr>
<tr class="separator:aec3ba8ab1cc0e096e27e26718904d8a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab276a7f5adfa522cfe25d0a3b637f646"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ab276a7f5adfa522cfe25d0a3b637f646">hue</a></td></tr>
<tr class="separator:ab276a7f5adfa522cfe25d0a3b637f646"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6b7d12cf171b798406f128a3f5b54908"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a6b7d12cf171b798406f128a3f5b54908">saturation</a></td></tr>
<tr class="separator:a6b7d12cf171b798406f128a3f5b54908"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:acab90fc3b5eef2c26e044df40fd84a61"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#acab90fc3b5eef2c26e044df40fd84a61">gamma</a></td></tr>
<tr class="separator:acab90fc3b5eef2c26e044df40fd84a61"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcfbd421109bce67c3950a8c45b0bbea"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#adcfbd421109bce67c3950a8c45b0bbea">rate</a></td></tr>
<tr class="separator:adcfbd421109bce67c3950a8c45b0bbea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a68098fd773aeae1824545f7490079f3c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_audio.html">VLCAudio</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a68098fd773aeae1824545f7490079f3c">audio</a></td></tr>
<tr class="separator:a68098fd773aeae1824545f7490079f3c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0734e2b2d4edebeaf3ca9e1cce85f361"><td class="memItemLeft" align="right" valign="top">CGSize&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a0734e2b2d4edebeaf3ca9e1cce85f361">videoSize</a></td></tr>
<tr class="separator:a0734e2b2d4edebeaf3ca9e1cce85f361"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa39d7bc28b9d74bca2e18e27357576eb"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aa39d7bc28b9d74bca2e18e27357576eb">hasVideoOut</a></td></tr>
<tr class="separator:aa39d7bc28b9d74bca2e18e27357576eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5915273012b273885dd9570d56777ccf"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_time.html">VLCTime</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a5915273012b273885dd9570d56777ccf">time</a></td></tr>
<tr class="separator:a5915273012b273885dd9570d56777ccf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a994615b429c023db77a00d1efec06fd3"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_time.html">VLCTime</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a994615b429c023db77a00d1efec06fd3">remainingTime</a></td></tr>
<tr class="separator:a994615b429c023db77a00d1efec06fd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8715f7c45a4389fd57dc6730312ca43e"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a8715f7c45a4389fd57dc6730312ca43e">currentVideoTrackIndex</a></td></tr>
<tr class="separator:a8715f7c45a4389fd57dc6730312ca43e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaa31a3ee365dd1721064f19319bb8026"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aaa31a3ee365dd1721064f19319bb8026">videoTrackNames</a></td></tr>
<tr class="separator:aaa31a3ee365dd1721064f19319bb8026"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4f1abde67436f198f0d07b885bd5ac59"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a4f1abde67436f198f0d07b885bd5ac59">videoTrackIndexes</a></td></tr>
<tr class="separator:a4f1abde67436f198f0d07b885bd5ac59"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af142280306f73367c1a3aa748f7233f9"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#af142280306f73367c1a3aa748f7233f9">numberOfVideoTracks</a></td></tr>
<tr class="separator:af142280306f73367c1a3aa748f7233f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a23063281cc095d506b38f421d974bfe3"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a23063281cc095d506b38f421d974bfe3">currentVideoSubTitleIndex</a></td></tr>
<tr class="separator:a23063281cc095d506b38f421d974bfe3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaaf3e36c370456bbf30058eedaf7844e"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aaaf3e36c370456bbf30058eedaf7844e">videoSubTitlesNames</a></td></tr>
<tr class="separator:aaaf3e36c370456bbf30058eedaf7844e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c8a4af83a85f3e8606049aad6f75169"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a1c8a4af83a85f3e8606049aad6f75169">videoSubTitlesIndexes</a></td></tr>
<tr class="separator:a1c8a4af83a85f3e8606049aad6f75169"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6163ca36922d10f0b30a7275545a673"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ae6163ca36922d10f0b30a7275545a673">numberOfSubtitlesTracks</a></td></tr>
<tr class="separator:ae6163ca36922d10f0b30a7275545a673"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8fe537bb090fd54c8f223a8e682b76c8"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a8fe537bb090fd54c8f223a8e682b76c8">currentVideoSubTitleDelay</a></td></tr>
<tr class="separator:a8fe537bb090fd54c8f223a8e682b76c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a83e7480a6144c4dc4dae14a413fa2ece"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a83e7480a6144c4dc4dae14a413fa2ece">currentChapterIndex</a></td></tr>
<tr class="separator:a83e7480a6144c4dc4dae14a413fa2ece"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aefa58a904c759776773a8287225138b3"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aefa58a904c759776773a8287225138b3">currentTitleIndex</a></td></tr>
<tr class="separator:aefa58a904c759776773a8287225138b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a73e07e681449f1122a4fa1f66d9fc52d"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a73e07e681449f1122a4fa1f66d9fc52d">numberOfTitles</a></td></tr>
<tr class="separator:a73e07e681449f1122a4fa1f66d9fc52d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2907bb09c29757c5c0f89e5bbe7e7394"><td class="memItemLeft" align="right" valign="top">NSString *const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a2907bb09c29757c5c0f89e5bbe7e7394">VLCTitleDescriptionName</a></td></tr>
<tr class="separator:a2907bb09c29757c5c0f89e5bbe7e7394"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a937a50fb274ec99b146d999fd8c02a1b"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a937a50fb274ec99b146d999fd8c02a1b">titleDescriptions</a></td></tr>
<tr class="separator:a937a50fb274ec99b146d999fd8c02a1b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a93ba313f95351de59e84cdeeea720822"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a93ba313f95351de59e84cdeeea720822">indexOfLongestTitle</a></td></tr>
<tr class="separator:a93ba313f95351de59e84cdeeea720822"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4aedc95307034b1d3a0f8ec51802e7f4"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a4aedc95307034b1d3a0f8ec51802e7f4">currentAudioTrackIndex</a></td></tr>
<tr class="separator:a4aedc95307034b1d3a0f8ec51802e7f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adde3f17fc7a88a00221a58bd564120c8"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#adde3f17fc7a88a00221a58bd564120c8">audioTrackNames</a></td></tr>
<tr class="separator:adde3f17fc7a88a00221a58bd564120c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae67fa3af66466f93f46bd14c07c60780"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ae67fa3af66466f93f46bd14c07c60780">audioTrackIndexes</a></td></tr>
<tr class="separator:ae67fa3af66466f93f46bd14c07c60780"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a932d093bc73aea01f953a1b96023f401"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a932d093bc73aea01f953a1b96023f401">numberOfAudioTracks</a></td></tr>
<tr class="separator:a932d093bc73aea01f953a1b96023f401"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad29d5abf2c543de5d7f911a8a216480e"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ad29d5abf2c543de5d7f911a8a216480e">audioChannel</a></td></tr>
<tr class="separator:ad29d5abf2c543de5d7f911a8a216480e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e32c4423cb48d9491591ba55df6cbd6"><td class="memItemLeft" align="right" valign="top">NSInteger&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a4e32c4423cb48d9491591ba55df6cbd6">currentAudioPlaybackDelay</a></td></tr>
<tr class="separator:a4e32c4423cb48d9491591ba55df6cbd6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f5763e66e58c4b44045ef098bdb818a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_media_loudness.html">VLCMediaLoudness</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a5f5763e66e58c4b44045ef098bdb818a">momentaryLoudness</a></td></tr>
<tr class="separator:a5f5763e66e58c4b44045ef098bdb818a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9889583a2e2b6f6045c1a779a486859d"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a9889583a2e2b6f6045c1a779a486859d">equalizerProfiles</a></td></tr>
<tr class="separator:a9889583a2e2b6f6045c1a779a486859d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1750c810467d11cb0cf4e835ea9163f3"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a1750c810467d11cb0cf4e835ea9163f3">equalizerEnabled</a></td></tr>
<tr class="separator:a1750c810467d11cb0cf4e835ea9163f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1dd4611ad95d596a0d086092ca0c571a"><td class="memItemLeft" align="right" valign="top">CGFloat&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a1dd4611ad95d596a0d086092ca0c571a">preAmplification</a></td></tr>
<tr class="separator:a1dd4611ad95d596a0d086092ca0c571a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abfda4f0fc83029e50e631d2fbe1c8c48"><td class="memItemLeft" align="right" valign="top">unsigned&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#abfda4f0fc83029e50e631d2fbe1c8c48">numberOfBands</a></td></tr>
<tr class="separator:abfda4f0fc83029e50e631d2fbe1c8c48"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:addd666feffd6b2e3ee0c6586f04983d4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#addd666feffd6b2e3ee0c6586f04983d4">media</a></td></tr>
<tr class="separator:addd666feffd6b2e3ee0c6586f04983d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5f8af3ae371f616810320e1fb447f6dc"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a5f8af3ae371f616810320e1fb447f6dc">yaw</a></td></tr>
<tr class="separator:a5f8af3ae371f616810320e1fb447f6dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7af67ee4b28da45c957bafca617840f"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ae7af67ee4b28da45c957bafca617840f">pitch</a></td></tr>
<tr class="separator:ae7af67ee4b28da45c957bafca617840f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a17e2d158c437a5bffd8da88673b99efc"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a17e2d158c437a5bffd8da88673b99efc">roll</a></td></tr>
<tr class="separator:a17e2d158c437a5bffd8da88673b99efc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a43185c858bc8767f33a19a9971d34fc4"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a43185c858bc8767f33a19a9971d34fc4">fov</a></td></tr>
<tr class="separator:a43185c858bc8767f33a19a9971d34fc4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6fa5e39a09fd25c262c9a2ea20e5b9df"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a6fa5e39a09fd25c262c9a2ea20e5b9df">playing</a></td></tr>
<tr class="separator:a6fa5e39a09fd25c262c9a2ea20e5b9df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa55f09ffe39e021920248ff142ae0f75"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aa55f09ffe39e021920248ff142ae0f75">willPlay</a></td></tr>
<tr class="separator:aa55f09ffe39e021920248ff142ae0f75"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa800575a8facf5db251df3cc88bd44ea"><td class="memItemLeft" align="right" valign="top">VLCMediaPlayerState&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#aa800575a8facf5db251df3cc88bd44ea">state</a></td></tr>
<tr class="separator:aa800575a8facf5db251df3cc88bd44ea"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af10549bcee345334f42548cfda9ce51c"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#af10549bcee345334f42548cfda9ce51c">position</a></td></tr>
<tr class="separator:af10549bcee345334f42548cfda9ce51c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae3f8bc09a07c8b58935d4cd1cf58e69e"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ae3f8bc09a07c8b58935d4cd1cf58e69e">seekable</a></td></tr>
<tr class="separator:ae3f8bc09a07c8b58935d4cd1cf58e69e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1e2ece165a5fb056a2f8737ac1ff2367"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a1e2ece165a5fb056a2f8737ac1ff2367">canPause</a></td></tr>
<tr class="separator:a1e2ece165a5fb056a2f8737ac1ff2367"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ada4fadb2ae81bd34fce217a34571872a"><td class="memItemLeft" align="right" valign="top">NSArray *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#ada4fadb2ae81bd34fce217a34571872a">snapshots</a></td></tr>
<tr class="separator:ada4fadb2ae81bd34fce217a34571872a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0ce7b35bcd4b876e071f73fb558304df"><td class="memItemLeft" align="right" valign="top">NSImage *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_player.html#a0ce7b35bcd4b876e071f73fb558304df">lastSnapshot</a></td></tr>
<tr class="separator:a0ce7b35bcd4b876e071f73fb558304df"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>The player base class needed to do any playback </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="ab59f33aa946850a6387d87630c2cca16"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab59f33aa946850a6387d87630c2cca16">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[1/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (float framesPerSecond) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Frames per second </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000010">Deprecated:</a></b></dt><dd>provided for API compatibility only, to retrieve a media's FPS, use VLCMediaTracksInformationFrameRate. </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>0 </dd></dl>

<p>Implemented in <a class="el" href="interface_v_l_c_stream_session.html#a69590b40a62d896b8a9ba88ca2f69ecb">VLCStreamSession</a>, <a class="el" href="interface_v_l_c_stream_session.html#a8e5017ee1863eb61a649f7d28311fb30">VLCStreamSession</a>, and <a class="el" href="interface_v_l_c_stream_session.html#afd34666df70b52b43ea64394796f4d52">VLCStreamSession</a>.</p>

</div>
</div>
<a id="aae0b500274a748ac5c035c2a3c46c366"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aae0b500274a748ac5c035c2a3c46c366">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[2/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSUInteger countOfTitles) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>count of titles </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000013">Deprecated:</a></b></dt><dd>Use numberOfTitles instead </dd></dl>

<p>Implemented in <a class="el" href="interface_v_l_c_stream_session.html#a69590b40a62d896b8a9ba88ca2f69ecb">VLCStreamSession</a>, <a class="el" href="interface_v_l_c_stream_session.html#a8e5017ee1863eb61a649f7d28311fb30">VLCStreamSession</a>, and <a class="el" href="interface_v_l_c_stream_session.html#afd34666df70b52b43ea64394796f4d52">VLCStreamSession</a>.</p>

</div>
</div>
<a id="a4e26f7c5c05ef844fc1d8a3ae8e99ad4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4e26f7c5c05ef844fc1d8a3ae8e99ad4">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[3/3]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray* titles) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>array of available titles </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000014">Deprecated:</a></b></dt><dd>Use titleDescriptions instead </dd></dl>

<p>Implemented in <a class="el" href="interface_v_l_c_stream_session.html#a69590b40a62d896b8a9ba88ca2f69ecb">VLCStreamSession</a>, <a class="el" href="interface_v_l_c_stream_session.html#a8e5017ee1863eb61a649f7d28311fb30">VLCStreamSession</a>, and <a class="el" href="interface_v_l_c_stream_session.html#afd34666df70b52b43ea64394796f4d52">VLCStreamSession</a>.</p>

</div>
</div>
<a id="a9477125c5c85a4c598e4088c031331e2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9477125c5c85a4c598e4088c031331e2">&#9670;&nbsp;</a></span>addPlaybackSlave:type:enforce:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (int) addPlaybackSlave: </td>
          <td></td>
          <td class="paramtype">(NSURL *)&#160;</td>
          <td class="paramname"><em>slaveURL</em></td>
        </tr>
        <tr>
          <td class="paramkey">type:</td>
          <td></td>
          <td class="paramtype">(VLCMediaPlaybackSlaveType)&#160;</td>
          <td class="paramname"><em>slaveType</em></td>
        </tr>
        <tr>
          <td class="paramkey">enforce:</td>
          <td></td>
          <td class="paramtype">(BOOL)&#160;</td>
          <td class="paramname"><em>enforceSelection</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Add additional input sources to a playing media item This way, you can add subtitles or audio files to an existing input stream For the user, it will appear as if they were part of the existing stream </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">slaveURL</td><td>of the content to be added </td></tr>
    <tr><td class="paramname">slaveType</td><td>content type </td></tr>
    <tr><td class="paramname">enforceSelection</td><td>switch to the added accessory content </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ab60ddfe3c2f9d1943e094ce169ac1dbf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab60ddfe3c2f9d1943e094ce169ac1dbf">&#9670;&nbsp;</a></span>amplificationOfBand:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (CGFloat) amplificationOfBand: </td>
          <td></td>
          <td class="paramtype">(unsigned)&#160;</td>
          <td class="paramname"><em>index</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>amplification of band </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">index</td><td>of the band </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>current amplification value (clamped to the -20.0 to +20.0 range) </dd></dl>

</div>
</div>
<a id="a637f12081f1748693c9234b33c752640"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a637f12081f1748693c9234b33c752640">&#9670;&nbsp;</a></span>chapterDescriptionsOfTitle:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray *) chapterDescriptionsOfTitle: </td>
          <td></td>
          <td class="paramtype">(int)&#160;</td>
          <td class="paramname"><em>titleIndex</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>chapter descriptions an array of all chapters of the given title including information about chapter name, time offset and duration </p><dl class="section note"><dt>Note</dt><dd>if no title value is provided, information about the chapters of the current title is returned </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>array describing the titles in details </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="interface_v_l_c_media_player.html#a20081eb2719b21bd55b4d2ce185f86f2">VLCChapterDescriptionName</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media_player.html#ab5d7190ce89c08ae1b14e6c2a827d104">VLCChapterDescriptionTimeOffset</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media_player.html#a5d87f02211d47497f35bf616f7a0374e">VLCChapterDescriptionDuration</a> </dd></dl>

</div>
</div>
<a id="a59fbfd5a29004a32b9f64328ad6b57a4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a59fbfd5a29004a32b9f64328ad6b57a4">&#9670;&nbsp;</a></span>chaptersForTitleIndex:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray *) chaptersForTitleIndex: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Chapters of a given title index </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000012">Deprecated:</a></b></dt><dd>Use chapterDescriptionsOfTitle instead </dd></dl>

</div>
</div>
<a id="aefc367fa665de839effe00bccb4b7261"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aefc367fa665de839effe00bccb4b7261">&#9670;&nbsp;</a></span>extraShortJumpBackward</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) extraShortJumpBackward </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Jumps shortly backward in current stream if seeking is supported. </p>

</div>
</div>
<a id="ac77c500c9dbfb4bf3f3228db0f970e50"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac77c500c9dbfb4bf3f3228db0f970e50">&#9670;&nbsp;</a></span>extraShortJumpForward</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) extraShortJumpForward </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Jumps shortly forward in current stream if seeking is supported. </p>

</div>
</div>
<a id="a1ad6cf29b157d2042f125b03a739c6ea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1ad6cf29b157d2042f125b03a739c6ea">&#9670;&nbsp;</a></span>fastForward</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) fastForward </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Fast forwards through the feed at the standard 1x rate. </p>

</div>
</div>
<a id="ae0790b3fdd424f5c59472f416b8ee5f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae0790b3fdd424f5c59472f416b8ee5f4">&#9670;&nbsp;</a></span>fastForwardAtRate:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) fastForwardAtRate: </td>
          <td></td>
          <td class="paramtype">(float)&#160;</td>
          <td class="paramname"><em>rate</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Fast forwards through the feed at the rate specified. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">rate</td><td>Rate at which the feed should be fast forwarded. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aa46d4dfad32097dc1cac62f795e06f39"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa46d4dfad32097dc1cac62f795e06f39">&#9670;&nbsp;</a></span>frequencyOfBandAtIndex:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (CGFloat) frequencyOfBandAtIndex: </td>
          <td></td>
          <td class="paramtype">(unsigned)&#160;</td>
          <td class="paramname"><em>index</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>frequency of equalizer band </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">index</td><td>the band index </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>frequency of the requested equalizer band </dd></dl>

</div>
</div>
<a id="a0efca8c5d59212429f7aa715f61b7637"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0efca8c5d59212429f7aa715f61b7637">&#9670;&nbsp;</a></span>gotoNextFrame</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) gotoNextFrame </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Advance one frame. </p>

</div>
</div>
<a id="a91e1bad6eaedb58cbfe097f633c50ebd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a91e1bad6eaedb58cbfe097f633c50ebd">&#9670;&nbsp;</a></span>initWithLibVLCInstance:andLibrary:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithLibVLCInstance: </td>
          <td></td>
          <td class="paramtype">(void *)&#160;</td>
          <td class="paramname"><em>playerInstance</em></td>
        </tr>
        <tr>
          <td class="paramkey">andLibrary:</td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> *)&#160;</td>
          <td class="paramname"><em>library</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>initialize player with a certain libvlc instance and <a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">playerInstance</td><td>the libvlc instance </td></tr>
    <tr><td class="paramname">library</td><td>the library instance </td></tr>
  </table>
  </dd>
</dl>
<dl class="section note"><dt>Note</dt><dd>This is an advanced initializer for very specialized environments </dd></dl>

</div>
</div>
<a id="a80fb8e4483b8675f8752ad006fd0f361"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a80fb8e4483b8675f8752ad006fd0f361">&#9670;&nbsp;</a></span>initWithOptions:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithOptions: </td>
          <td></td>
          <td class="paramtype">(NSArray *)&#160;</td>
          <td class="paramname"><em>options</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>initialize player with a given set of options </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">options</td><td>an array of private options </td></tr>
  </table>
  </dd>
</dl>
<dl class="section note"><dt>Note</dt><dd>This will allocate a new libvlc and <a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> instance, which will have a memory impact </dd></dl>

</div>
</div>
<a id="ad00097d1bab674773134188770396d1d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad00097d1bab674773134188770396d1d">&#9670;&nbsp;</a></span>initWithVideoLayer:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithVideoLayer: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_video_layer.html">VLCVideoLayer</a> *)&#160;</td>
          <td class="paramname"><em>aVideoLayer</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>initialize player with a given video layer </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aVideoLayer</td><td>an instance of <a class="el" href="interface_v_l_c_video_layer.html">VLCVideoLayer</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section note"><dt>Note</dt><dd>This initializer is for macOS only </dd></dl>

</div>
</div>
<a id="a432545540f6be27394824275fa6e3d10"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a432545540f6be27394824275fa6e3d10">&#9670;&nbsp;</a></span>initWithVideoView:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithVideoView: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_video_view.html">VLCVideoView</a> *)&#160;</td>
          <td class="paramname"><em>aVideoView</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>initialize player with a given video view </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aVideoView</td><td>an instance of <a class="el" href="interface_v_l_c_video_view.html">VLCVideoView</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section note"><dt>Note</dt><dd>This initializer is for macOS only </dd></dl>

</div>
</div>
<a id="a86d420386d6dde28818ae2a7e56f6198"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a86d420386d6dde28818ae2a7e56f6198">&#9670;&nbsp;</a></span>jumpBackward:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) jumpBackward: </td>
          <td></td>
          <td class="paramtype">(int)&#160;</td>
          <td class="paramname"><em>interval</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Jumps shortly backward in current stream if seeking is supported. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">interval</td><td>to skip, in sec. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a51e8067be7f55f64d3cfa3e4acccfafc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a51e8067be7f55f64d3cfa3e4acccfafc">&#9670;&nbsp;</a></span>jumpForward:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) jumpForward: </td>
          <td></td>
          <td class="paramtype">(int)&#160;</td>
          <td class="paramname"><em>interval</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Jumps shortly forward in current stream if seeking is supported. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">interval</td><td>to skip, in sec. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a78d5c9af63086443c9539c06b63bff4d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a78d5c9af63086443c9539c06b63bff4d">&#9670;&nbsp;</a></span>longJumpBackward</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) longJumpBackward </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Jumps shortly backward in current stream if seeking is supported. </p>

</div>
</div>
<a id="ad04f7a86349209e50615afc1a513a768"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad04f7a86349209e50615afc1a513a768">&#9670;&nbsp;</a></span>longJumpForward</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) longJumpForward </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Jumps shortly forward in current stream if seeking is supported. </p>

</div>
</div>
<a id="ae010feb668404c839ca683ecef6ee7a2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae010feb668404c839ca683ecef6ee7a2">&#9670;&nbsp;</a></span>mediumJumpBackward</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediumJumpBackward </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Jumps shortly backward in current stream if seeking is supported. </p>

</div>
</div>
<a id="ae87c31a4ebc3274b2b275bc5ac42f24c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae87c31a4ebc3274b2b275bc5ac42f24c">&#9670;&nbsp;</a></span>mediumJumpForward</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) mediumJumpForward </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Jumps shortly forward in current stream if seeking is supported. </p>

</div>
</div>
<a id="acfaac1c1cf8ae35bd40d80afa42bf1f0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acfaac1c1cf8ae35bd40d80afa42bf1f0">&#9670;&nbsp;</a></span>nextChapter</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) nextChapter </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>switch to the next chapter </p>

</div>
</div>
<a id="a7eb2b3aeb06985b3b655c1c76609924f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7eb2b3aeb06985b3b655c1c76609924f">&#9670;&nbsp;</a></span>NS_ENUM</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (typedef) NS_ENUM </td>
          <td></td>
          <td class="paramtype">(unsigned)&#160;</td>
          <td class="paramname"></td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">(VLCMediaPlaybackSlaveType)&#160;</td>
          <td class="paramname">&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>VLCMediaPlaybackNavigationAction describes actions which can be performed to navigate an interactive title </p>

</div>
</div>
<a id="a42a283dd2c12d510f247d10510deffff"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a42a283dd2c12d510f247d10510deffff">&#9670;&nbsp;</a></span>numberOfChaptersForTitle:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (int) numberOfChaptersForTitle: </td>
          <td></td>
          <td class="paramtype">(int)&#160;</td>
          <td class="paramname"><em>titleIndex</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>returns the number of chapters for a given title </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">titleIndex</td><td>the index of the title you are requesting the chapters for </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a18546cd8ca1b827eb5ddb9384e172166"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a18546cd8ca1b827eb5ddb9384e172166">&#9670;&nbsp;</a></span>openVideoSubTitlesFromFile:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) openVideoSubTitlesFromFile: </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"><em>__attribute__</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Load and set a specific video subtitle, from a file.</p>
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000011">Deprecated:</a></b></dt><dd>use addPlaybackSlave:type:enforce: instead </dd></dl>

</div>
</div>
<a id="aaef26685e063e62599a5b0248a072a0f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaef26685e063e62599a5b0248a072a0f">&#9670;&nbsp;</a></span>pause</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) pause </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Set the pause state of the feed. Do nothing if already paused. </p>

</div>
</div>
<a id="a2309a13bb4aa332f3dd1eecead8831a3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2309a13bb4aa332f3dd1eecead8831a3">&#9670;&nbsp;</a></span>performNavigationAction:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) performNavigationAction: </td>
          <td></td>
          <td class="paramtype">(VLCMediaPlaybackNavigationAction)&#160;</td>
          <td class="paramname"><em>action</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>performs navigation actions on interactive titles </p>

</div>
</div>
<a id="a7da1ef4be33931daadf5937cd2365924"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7da1ef4be33931daadf5937cd2365924">&#9670;&nbsp;</a></span>play</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) play </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Plays a media resource using the currently selected media controller (or default controller. If feed was paused then the feed resumes at the position it was paused in. </p>

</div>
</div>
<a id="ae2a2398ebf77aaa0dd5218d6e17a61f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae2a2398ebf77aaa0dd5218d6e17a61f4">&#9670;&nbsp;</a></span>previousChapter</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) previousChapter </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>switch to the previous chapter </p>

</div>
</div>
<a id="abb2864039496be0bde196467971dd873"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abb2864039496be0bde196467971dd873">&#9670;&nbsp;</a></span>resetEqualizerFromProfile:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) resetEqualizerFromProfile: </td>
          <td></td>
          <td class="paramtype">(unsigned)&#160;</td>
          <td class="paramname"><em>profile</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Re-set the equalizer to a profile retrieved from the list </p><dl class="section note"><dt>Note</dt><dd>This doesn't enable the Equalizer automagically </dd></dl>

</div>
</div>
<a id="ac03553439681974c0da7014c44b104d6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac03553439681974c0da7014c44b104d6">&#9670;&nbsp;</a></span>rewind</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) rewind </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Rewinds through the feed at the standard 1x rate. </p>

</div>
</div>
<a id="ade646072ef74f61b84e4afdc260a0f36"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ade646072ef74f61b84e4afdc260a0f36">&#9670;&nbsp;</a></span>rewindAtRate:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) rewindAtRate: </td>
          <td></td>
          <td class="paramtype">(float)&#160;</td>
          <td class="paramname"><em>rate</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Rewinds through the feed at the rate specified. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">rate</td><td>Rate at which the feed should be fast rewound. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ac0e16c49e1746e74a35fd50f870c5e31"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac0e16c49e1746e74a35fd50f870c5e31">&#9670;&nbsp;</a></span>saveVideoSnapshotAt:withWidth:andHeight:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) saveVideoSnapshotAt: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>path</em></td>
        </tr>
        <tr>
          <td class="paramkey">withWidth:</td>
          <td></td>
          <td class="paramtype">(int)&#160;</td>
          <td class="paramname"><em>width</em></td>
        </tr>
        <tr>
          <td class="paramkey">andHeight:</td>
          <td></td>
          <td class="paramtype">(int)&#160;</td>
          <td class="paramname"><em>height</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Take a snapshot of the current video.</p>
<p>If width AND height is 0, original size is used. If width OR height is 0, original aspect-ratio is preserved.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">path</td><td>the path where to save the screenshot to </td></tr>
    <tr><td class="paramname">width</td><td>the snapshot's width </td></tr>
    <tr><td class="paramname">height</td><td>the snapshot's height </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ae61e529ff86b246ebe54cc29b0a96c0e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae61e529ff86b246ebe54cc29b0a96c0e">&#9670;&nbsp;</a></span>setAmplification:forBand:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) setAmplification: </td>
          <td></td>
          <td class="paramtype">(CGFloat)&#160;</td>
          <td class="paramname"><em>amplification</em></td>
        </tr>
        <tr>
          <td class="paramkey">forBand:</td>
          <td></td>
          <td class="paramtype">(unsigned)&#160;</td>
          <td class="paramname"><em>index</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>set amplification for band </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">amplification</td><td>value (clamped to the -20.0 to +20.0 range) </td></tr>
    <tr><td class="paramname">index</td><td>of the respective band </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a236ed2450c49f8acd0846499bf26fbb5"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a236ed2450c49f8acd0846499bf26fbb5">&#9670;&nbsp;</a></span>setDeinterlace:withFilter:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) setDeinterlace: </td>
          <td></td>
          <td class="paramtype">(VLCDeinterlace)&#160;</td>
          <td class="paramname"><em>deinterlace</em></td>
        </tr>
        <tr>
          <td class="paramkey">withFilter:</td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>name</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Enable or disable deinterlace and specify which filter to use</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">deinterlace</td><td>mode for deinterlacing: enable, disable or auto </td></tr>
    <tr><td class="paramname">name</td><td>of deinterlace filter to use (availability depends on underlying VLC version). </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a95abbbed4ddab2adb3fb5c4a8b8ae076"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a95abbbed4ddab2adb3fb5c4a8b8ae076">&#9670;&nbsp;</a></span>setDeinterlaceFilter:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) setDeinterlaceFilter: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>name</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Enable or disable deinterlace filter</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">name</td><td>of deinterlace filter to use (availability depends on underlying VLC version), NULL to disable. </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a44f8101ea62406d757c44520361f8930"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a44f8101ea62406d757c44520361f8930">&#9670;&nbsp;</a></span>setRendererItem:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) setRendererItem: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a> *)&#160;</td>
          <td class="paramname"><em>item</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Sets a <code><a class="el" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a></code> to the current media player </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">item</td><td><code><a class="el" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a></code> discovered by <code><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a></code> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><code>YES</code> if successful, <code>NO</code> otherwise </dd></dl>
<dl class="section note"><dt>Note</dt><dd>Must be called before the first call of <code>play</code> to take effect </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="interface_v_l_c_renderer_discoverer.html">VLCRendererDiscoverer</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_renderer_item.html">VLCRendererItem</a> </dd></dl>

</div>
</div>
<a id="a76dc478bf25fae8e0f671e39d006ce25"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a76dc478bf25fae8e0f671e39d006ce25">&#9670;&nbsp;</a></span>setVideoLayer:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) setVideoLayer: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_video_layer.html">VLCVideoLayer</a> *)&#160;</td>
          <td class="paramname"><em>aVideoLayer</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>set a video layer for rendering </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aVideoLayer</td><td>instance of <a class="el" href="interface_v_l_c_video_layer.html">VLCVideoLayer</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section note"><dt>Note</dt><dd>This setter is macOS only </dd></dl>

</div>
</div>
<a id="a1eb2229ede2d006bec1650e4d4b0fa02"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1eb2229ede2d006bec1650e4d4b0fa02">&#9670;&nbsp;</a></span>setVideoView:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) setVideoView: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_video_view.html">VLCVideoView</a> *)&#160;</td>
          <td class="paramname"><em>aVideoView</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>set a video view for rendering </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aVideoView</td><td>instance of <a class="el" href="interface_v_l_c_video_view.html">VLCVideoView</a> </td></tr>
  </table>
  </dd>
</dl>
<dl class="section note"><dt>Note</dt><dd>This setter is macOS only </dd></dl>

</div>
</div>
<a id="a8a8548e2888bbcf9bf8ed5a35fc30cf4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8a8548e2888bbcf9bf8ed5a35fc30cf4">&#9670;&nbsp;</a></span>shortJumpBackward</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) shortJumpBackward </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Jumps shortly backward in current stream if seeking is supported. </p>

</div>
</div>
<a id="ab09028de82e23ba963e7b645949aa212"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab09028de82e23ba963e7b645949aa212">&#9670;&nbsp;</a></span>shortJumpForward</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) shortJumpForward </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Jumps shortly forward in current stream if seeking is supported. </p>

</div>
</div>
<a id="a0af0ae5ca6a1b5f68efc764a296b6876"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0af0ae5ca6a1b5f68efc764a296b6876">&#9670;&nbsp;</a></span>startRecordingAtPath:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) startRecordingAtPath: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>path</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Start recording at given <b>directory</b> path </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">path</td><td>directory where the recording should go </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>an BOOL with the success status </dd></dl>

</div>
</div>
<a id="ad7184261982c10d5d1307e37ed16fb52"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad7184261982c10d5d1307e37ed16fb52">&#9670;&nbsp;</a></span>stop</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) stop </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Stop the playing. </p>

</div>
</div>
<a id="a41917b1e63701715a0e66f16e22c6f63"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a41917b1e63701715a0e66f16e22c6f63">&#9670;&nbsp;</a></span>stopRecording</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) stopRecording </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Stop current recording </p><dl class="section return"><dt>Returns</dt><dd>an BOOL with the success status </dd></dl>

</div>
</div>
<a id="abe9c128cdaa533d51c5209d4c17d5b09"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abe9c128cdaa533d51c5209d4c17d5b09">&#9670;&nbsp;</a></span>updateViewpoint:pitch:roll:fov:absolute:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) updateViewpoint: </td>
          <td></td>
          <td class="paramtype">(float)&#160;</td>
          <td class="paramname"><em>yaw</em></td>
        </tr>
        <tr>
          <td class="paramkey">pitch:</td>
          <td></td>
          <td class="paramtype">(float)&#160;</td>
          <td class="paramname"><em>pitch</em></td>
        </tr>
        <tr>
          <td class="paramkey">roll:</td>
          <td></td>
          <td class="paramtype">(float)&#160;</td>
          <td class="paramname"><em>roll</em></td>
        </tr>
        <tr>
          <td class="paramkey">fov:</td>
          <td></td>
          <td class="paramtype">(float)&#160;</td>
          <td class="paramname"><em>fov</em></td>
        </tr>
        <tr>
          <td class="paramkey">absolute:</td>
          <td></td>
          <td class="paramtype">(BOOL)&#160;</td>
          <td class="paramname"><em>absolute</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Updates viewpoint with given values. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">yaw</td><td>view point yaw in degrees ]-180;180] </td></tr>
    <tr><td class="paramname">pitch</td><td>view point pitch in degrees ]-90;90] </td></tr>
    <tr><td class="paramname">roll</td><td>view point roll in degrees ]-180;180] </td></tr>
    <tr><td class="paramname">fov</td><td>field of view in degrees ]0;180[ (default 80.) </td></tr>
    <tr><td class="paramname">absolute</td><td>if true replace the old viewpoint with the new one. If false, increase/decrease it. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>NO in case of error, YES otherwise </dd></dl>
<dl class="section note"><dt>Note</dt><dd>This will create a viewpoint instance if not present. </dd></dl>

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a id="a5d87f02211d47497f35bf616f7a0374e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5d87f02211d47497f35bf616f7a0374e">&#9670;&nbsp;</a></span>VLCChapterDescriptionDuration</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCChapterDescriptionDuration</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>dictionary value for the chapter's duration </p>

</div>
</div>
<a id="a20081eb2719b21bd55b4d2ce185f86f2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a20081eb2719b21bd55b4d2ce185f86f2">&#9670;&nbsp;</a></span>VLCChapterDescriptionName</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCChapterDescriptionName</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>dictionary value for the user-facing chapter name </p>

</div>
</div>
<a id="ab5d7190ce89c08ae1b14e6c2a827d104"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab5d7190ce89c08ae1b14e6c2a827d104">&#9670;&nbsp;</a></span>VLCChapterDescriptionTimeOffset</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCChapterDescriptionTimeOffset</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>dictionary value for the chapter's time offset </p>

</div>
</div>
<a id="ac4bd07aad0599f2f61cbac7281981df7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ac4bd07aad0599f2f61cbac7281981df7">&#9670;&nbsp;</a></span>VLCTitleDescriptionDuration</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCTitleDescriptionDuration</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>dictionary value for the title's duration </p>

</div>
</div>
<a id="a1cfbed633aa7783841c153d48088ba70"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1cfbed633aa7783841c153d48088ba70">&#9670;&nbsp;</a></span>VLCTitleDescriptionIsMenu</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCTitleDescriptionIsMenu</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>dictionary value whether the title is a menu or not </p>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="af7a158c0c906970b991154efab0300bd"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af7a158c0c906970b991154efab0300bd">&#9670;&nbsp;</a></span>adjustFilterEnabled</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) adjustFilterEnabled</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Enable or disable adjust video filter (contrast, brightness, hue, saturation, gamma)</p>
<dl class="section return"><dt>Returns</dt><dd>bool value </dd></dl>

</div>
</div>
<a id="a68098fd773aeae1824545f7490079f3c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a68098fd773aeae1824545f7490079f3c">&#9670;&nbsp;</a></span>audio</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_audio.html">VLCAudio</a>*) audio</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">weak</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>an audio controller object </p><dl class="section return"><dt>Returns</dt><dd>instance of <a class="el" href="interface_v_l_c_audio.html">VLCAudio</a> </dd></dl>

</div>
</div>
<a id="ad29d5abf2c543de5d7f911a8a216480e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad29d5abf2c543de5d7f911a8a216480e">&#9670;&nbsp;</a></span>audioChannel</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) audioChannel</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>sets / returns the current audio channel </p><dl class="section return"><dt>Returns</dt><dd>the currently set audio channel </dd></dl>

</div>
</div>
<a id="ae67fa3af66466f93f46bd14c07c60780"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae67fa3af66466f93f46bd14c07c60780">&#9670;&nbsp;</a></span>audioTrackIndexes</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray*) audioTrackIndexes</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the audio track IDs those are needed to set the video index </p>

</div>
</div>
<a id="adde3f17fc7a88a00221a58bd564120c8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adde3f17fc7a88a00221a58bd564120c8">&#9670;&nbsp;</a></span>audioTrackNames</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray*) audioTrackNames</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the audio track names, usually a language name or a description It includes the "Disabled" fake track at index 0. </p>

</div>
</div>
<a id="aec3ba8ab1cc0e096e27e26718904d8a7"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aec3ba8ab1cc0e096e27e26718904d8a7">&#9670;&nbsp;</a></span>brightness</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) brightness</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set/Get the adjust filter's brightness value</p>
<dl class="section return"><dt>Returns</dt><dd>float value (range: 0-2, default: 1.0) </dd></dl>

</div>
</div>
<a id="a1e2ece165a5fb056a2f8737ac1ff2367"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1e2ece165a5fb056a2f8737ac1ff2367">&#9670;&nbsp;</a></span>canPause</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) canPause</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>property whether the currently playing media can be paused (or not) </p><dl class="section return"><dt>Returns</dt><dd>BOOL value </dd></dl>

</div>
</div>
<a id="aee995a8531e197917dbad6516594a777"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aee995a8531e197917dbad6516594a777">&#9670;&nbsp;</a></span>contrast</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) contrast</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set/Get the adjust filter's contrast value</p>
<dl class="section return"><dt>Returns</dt><dd>float value (range: 0-2, default: 1.0) </dd></dl>

</div>
</div>
<a id="a4e32c4423cb48d9491591ba55df6cbd6"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4e32c4423cb48d9491591ba55df6cbd6">&#9670;&nbsp;</a></span>currentAudioPlaybackDelay</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) currentAudioPlaybackDelay</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get the current audio delay. Positive values means audio is delayed further, negative values less.</p>
<dl class="section return"><dt>Returns</dt><dd>time (in microseconds) the audio playback is being delayed </dd></dl>

</div>
</div>
<a id="a4aedc95307034b1d3a0f8ec51802e7f4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4aedc95307034b1d3a0f8ec51802e7f4">&#9670;&nbsp;</a></span>currentAudioTrackIndex</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) currentAudioTrackIndex</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Return the current audio track index</p>
<dl class="section return"><dt>Returns</dt><dd>current audio track index, -1 if none or no media track</dd></dl>
<p>Pass -1 to disable. </p>

</div>
</div>
<a id="a83e7480a6144c4dc4dae14a413fa2ece"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a83e7480a6144c4dc4dae14a413fa2ece">&#9670;&nbsp;</a></span>currentChapterIndex</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) currentChapterIndex</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Chapter selection and enumeration, it is bound to a title option. Return the current chapter index </p><dl class="section return"><dt>Returns</dt><dd>current chapter index or -1 if there is no chapter </dd></dl>

</div>
</div>
<a id="aefa58a904c759776773a8287225138b3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aefa58a904c759776773a8287225138b3">&#9670;&nbsp;</a></span>currentTitleIndex</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) currentTitleIndex</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Return the current title index </p><dl class="section return"><dt>Returns</dt><dd>title index currently playing, or -1 if none </dd></dl>

</div>
</div>
<a id="a8fe537bb090fd54c8f223a8e682b76c8"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8fe537bb090fd54c8f223a8e682b76c8">&#9670;&nbsp;</a></span>currentVideoSubTitleDelay</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSInteger) currentVideoSubTitleDelay</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get the current subtitle delay. Positive values means subtitles are being displayed later, negative values earlier.</p>
<dl class="section return"><dt>Returns</dt><dd>time (in microseconds) the display of subtitles is being delayed </dd></dl>

</div>
</div>
<a id="a23063281cc095d506b38f421d974bfe3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a23063281cc095d506b38f421d974bfe3">&#9670;&nbsp;</a></span>currentVideoSubTitleIndex</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) currentVideoSubTitleIndex</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Return the current video subtitle index</p>
<dl class="section return"><dt>Returns</dt><dd>current video subtitle index, -1 if none</dd></dl>
<p>Pass -1 to disable. </p>

</div>
</div>
<a id="a8715f7c45a4389fd57dc6730312ca43e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8715f7c45a4389fd57dc6730312ca43e">&#9670;&nbsp;</a></span>currentVideoTrackIndex</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) currentVideoTrackIndex</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Return the current video track index</p>
<dl class="section return"><dt>Returns</dt><dd>current video track index, -1 if none or no media track</dd></dl>
<p>Pass -1 to disable. </p>

</div>
</div>
<a id="a138068f5da49d36c7fbd43dabea55666"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a138068f5da49d36c7fbd43dabea55666">&#9670;&nbsp;</a></span>delegate</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (id&lt;<a class="el" href="protocol_v_l_c_media_player_delegate-p.html">VLCMediaPlayerDelegate</a>&gt;) delegate</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">weak</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>the delegate object implementing the optional protocol </p>

</div>
</div>
<a id="af6fa6a9a81e9db3aa58596a1b5b48196"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af6fa6a9a81e9db3aa58596a1b5b48196">&#9670;&nbsp;</a></span>drawable</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (id) drawable</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">strong</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>set/retrieve a video view for rendering This can be any UIView or NSView or instances of <a class="el" href="interface_v_l_c_video_view.html">VLCVideoView</a> / <a class="el" href="interface_v_l_c_video_layer.html">VLCVideoLayer</a> if running on macOS </p>

</div>
</div>
<a id="a1750c810467d11cb0cf4e835ea9163f3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1750c810467d11cb0cf4e835ea9163f3">&#9670;&nbsp;</a></span>equalizerEnabled</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) equalizerEnabled</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Toggle equalizer state param: bool value to enable/disable the equalizer </p><dl class="section note"><dt>Note</dt><dd>this can fail, if failed the value will not be changed </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>current state </dd></dl>

</div>
</div>
<a id="a9889583a2e2b6f6045c1a779a486859d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a9889583a2e2b6f6045c1a779a486859d">&#9670;&nbsp;</a></span>equalizerProfiles</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray*) equalizerProfiles</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">weak</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get a list of available equalizer profiles </p><dl class="section note"><dt>Note</dt><dd>Current versions do not allow the addition of further profiles so you need to handle this in your app.</dd></dl>
<dl class="section return"><dt>Returns</dt><dd>array of equalizer profiles </dd></dl>

</div>
</div>
<a id="a43185c858bc8767f33a19a9971d34fc4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a43185c858bc8767f33a19a9971d34fc4">&#9670;&nbsp;</a></span>fov</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) fov</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set/Get the adjust filter's gamma value</p>
<dl class="section return"><dt>Returns</dt><dd>field of view in degrees ]0;180[ (default 80.) </dd></dl>

</div>
</div>
<a id="acab90fc3b5eef2c26e044df40fd84a61"></a>
<h2 class="memtitle"><span class="permalink"><a href="#acab90fc3b5eef2c26e044df40fd84a61">&#9670;&nbsp;</a></span>gamma</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) gamma</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set/Get the adjust filter's gamma value</p>
<dl class="section return"><dt>Returns</dt><dd>float value (range: 0-10, default: 1.0) </dd></dl>

</div>
</div>
<a id="aa39d7bc28b9d74bca2e18e27357576eb"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa39d7bc28b9d74bca2e18e27357576eb">&#9670;&nbsp;</a></span>hasVideoOut</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) hasVideoOut</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Does the current media have a video output? </p><dl class="section note"><dt>Note</dt><dd>a false return value doesn't mean that the video doesn't have any video </dd>
<dd>
tracks. Those might just be disabled. </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>current video output status </dd></dl>

</div>
</div>
<a id="ab276a7f5adfa522cfe25d0a3b637f646"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab276a7f5adfa522cfe25d0a3b637f646">&#9670;&nbsp;</a></span>hue</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) hue</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set/Get the adjust filter's hue value</p>
<dl class="section return"><dt>Returns</dt><dd>float value (range: -180-180, default: 0.) </dd></dl>

</div>
</div>
<a id="a93ba313f95351de59e84cdeeea720822"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a93ba313f95351de59e84cdeeea720822">&#9670;&nbsp;</a></span>indexOfLongestTitle</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) indexOfLongestTitle</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>the title with the longest duration </p><dl class="section return"><dt>Returns</dt><dd>int matching the title index </dd></dl>

</div>
</div>
<a id="a0ce7b35bcd4b876e071f73fb558304df"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0ce7b35bcd4b876e071f73fb558304df">&#9670;&nbsp;</a></span>lastSnapshot</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSImage*) lastSnapshot</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get last snapshot available. </p><dl class="section return"><dt>Returns</dt><dd>an NSImage with the last snapshot available. </dd></dl>
<dl class="section note"><dt>Note</dt><dd>return value is nil if there is no snapshot </dd>
<dd>
This property is not available to iOS and tvOS </dd></dl>

</div>
</div>
<a id="a32b5af405c337b3704957d7c15cbdd61"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a32b5af405c337b3704957d7c15cbdd61">&#9670;&nbsp;</a></span>libraryInstance</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_library.html">VLCLibrary</a>*) libraryInstance</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>the library instance in use by the player instance </p>

</div>
</div>
<a id="addd666feffd6b2e3ee0c6586f04983d4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#addd666feffd6b2e3ee0c6586f04983d4">&#9670;&nbsp;</a></span>media</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_media.html">VLCMedia</a>*) media</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">strong</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The currently media instance set to play </p>

</div>
</div>
<a id="a5f5763e66e58c4b44045ef098bdb818a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f5763e66e58c4b44045ef098bdb818a">&#9670;&nbsp;</a></span>momentaryLoudness</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_media_loudness.html">VLCMediaLoudness</a>*) momentaryLoudness</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get the last available loudness description for the current media (last 400ms) </p>

</div>
</div>
<a id="a932d093bc73aea01f953a1b96023f401"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a932d093bc73aea01f953a1b96023f401">&#9670;&nbsp;</a></span>numberOfAudioTracks</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) numberOfAudioTracks</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the number of audio tracks available in the current media </p><dl class="section return"><dt>Returns</dt><dd>number of tracks </dd></dl>

</div>
</div>
<a id="abfda4f0fc83029e50e631d2fbe1c8c48"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abfda4f0fc83029e50e631d2fbe1c8c48">&#9670;&nbsp;</a></span>numberOfBands</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (unsigned) numberOfBands</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Number of equalizer bands </p><dl class="section return"><dt>Returns</dt><dd>the number of equalizer bands available in the current release </dd></dl>

</div>
</div>
<a id="ae6163ca36922d10f0b30a7275545a673"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae6163ca36922d10f0b30a7275545a673">&#9670;&nbsp;</a></span>numberOfSubtitlesTracks</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) numberOfSubtitlesTracks</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the number of SPU tracks available in the current media </p><dl class="section return"><dt>Returns</dt><dd>number of tracks </dd></dl>

</div>
</div>
<a id="a73e07e681449f1122a4fa1f66d9fc52d"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a73e07e681449f1122a4fa1f66d9fc52d">&#9670;&nbsp;</a></span>numberOfTitles</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) numberOfTitles</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>number of titles available for the current media </p><dl class="section return"><dt>Returns</dt><dd>the number of titles </dd></dl>

</div>
</div>
<a id="af142280306f73367c1a3aa748f7233f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af142280306f73367c1a3aa748f7233f9">&#9670;&nbsp;</a></span>numberOfVideoTracks</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (int) numberOfVideoTracks</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>returns the number of video tracks available in the current media </p><dl class="section return"><dt>Returns</dt><dd>number of tracks </dd></dl>

</div>
</div>
<a id="ae7af67ee4b28da45c957bafca617840f"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae7af67ee4b28da45c957bafca617840f">&#9670;&nbsp;</a></span>pitch</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) pitch</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get the view point pitch in degrees</p>
<dl class="section return"><dt>Returns</dt><dd>view point pitch in degrees ]-90;90] </dd></dl>

</div>
</div>
<a id="a6fa5e39a09fd25c262c9a2ea20e5b9df"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6fa5e39a09fd25c262c9a2ea20e5b9df">&#9670;&nbsp;</a></span>playing</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) playing</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Playback state flag identifying that the stream is currently playing. </p><dl class="section return"><dt>Returns</dt><dd>TRUE if the feed is playing, FALSE if otherwise. </dd></dl>

</div>
</div>
<a id="af10549bcee345334f42548cfda9ce51c"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af10549bcee345334f42548cfda9ce51c">&#9670;&nbsp;</a></span>position</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) position</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the receiver's position in the reading. </p><dl class="section return"><dt>Returns</dt><dd>movie position as percentage between 0.0 and 1.0. </dd></dl>

</div>
</div>
<a id="a1dd4611ad95d596a0d086092ca0c571a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1dd4611ad95d596a0d086092ca0c571a">&#9670;&nbsp;</a></span>preAmplification</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (CGFloat) preAmplification</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set amplification level param: The supplied amplification value will be clamped to the -20.0 to +20.0 range. </p><dl class="section note"><dt>Note</dt><dd>this will create and enabled an Equalizer instance if not present </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>current amplification level </dd></dl>

</div>
</div>
<a id="adcfbd421109bce67c3950a8c45b0bbea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#adcfbd421109bce67c3950a8c45b0bbea">&#9670;&nbsp;</a></span>rate</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) rate</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get the requested movie play rate. </p><dl class="section warning"><dt>Warning</dt><dd>Depending on the underlying media, the requested rate may be different from the real playback rate. Due to limitations of some protocols this option may not be taken into account at all, if set.</dd></dl>
<dl class="section return"><dt>Returns</dt><dd>movie play rate </dd></dl>

</div>
</div>
<a id="a994615b429c023db77a00d1efec06fd3"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a994615b429c023db77a00d1efec06fd3">&#9670;&nbsp;</a></span>remainingTime</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_time.html">VLCTime</a>*) remainingTime</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">weak</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the current position (or time) of the feed, inversed if a duration is available </p><dl class="section return"><dt>Returns</dt><dd><a class="el" href="interface_v_l_c_time.html">VLCTime</a> object with requested time </dd></dl>
<dl class="section note"><dt>Note</dt><dd><a class="el" href="interface_v_l_c_time.html">VLCTime</a> will be a nullTime if no duration can be calculated for the current input </dd></dl>

</div>
</div>
<a id="a17e2d158c437a5bffd8da88673b99efc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a17e2d158c437a5bffd8da88673b99efc">&#9670;&nbsp;</a></span>roll</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) roll</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get the view point roll in degrees</p>
<dl class="section return"><dt>Returns</dt><dd>view point roll in degrees ]-180;180] </dd></dl>

</div>
</div>
<a id="a6b7d12cf171b798406f128a3f5b54908"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a6b7d12cf171b798406f128a3f5b54908">&#9670;&nbsp;</a></span>saturation</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) saturation</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set/Get the adjust filter's saturation value</p>
<dl class="section return"><dt>Returns</dt><dd>float value (range: 0-3, default: 1.0) </dd></dl>

</div>
</div>
<a id="a773e8e2b5b169fa6cb0bcef37330d327"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a773e8e2b5b169fa6cb0bcef37330d327">&#9670;&nbsp;</a></span>scaleFactor</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) scaleFactor</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set/Get the current video scaling factor. That is the ratio of the number of pixels on screen to the number of pixels in the original decoded video in each dimension. Zero is a special value; it will adjust the video to the output window/drawable (in windowed mode) or the entire screen.</p>
<p>param: relative scale factor as float </p>

</div>
</div>
<a id="ae3f8bc09a07c8b58935d4cd1cf58e69e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae3f8bc09a07c8b58935d4cd1cf58e69e">&#9670;&nbsp;</a></span>seekable</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) seekable</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>property whether the current input is seekable or not, e.g. it's a live stream </p><dl class="section note"><dt>Note</dt><dd>Setting position or time for non-seekable inputs does not have any effect and will fail silently </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>BOOL value </dd></dl>

</div>
</div>
<a id="ada4fadb2ae81bd34fce217a34571872a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ada4fadb2ae81bd34fce217a34571872a">&#9670;&nbsp;</a></span>snapshots</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray*) snapshots</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Array of taken snapshots of the current video output </p><dl class="section return"><dt>Returns</dt><dd>a NSArray of NSString instances containing the names </dd></dl>
<dl class="section note"><dt>Note</dt><dd>This property is not available to macOS </dd></dl>

</div>
</div>
<a id="aa800575a8facf5db251df3cc88bd44ea"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa800575a8facf5db251df3cc88bd44ea">&#9670;&nbsp;</a></span>state</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (VLCMediaPlayerState) state</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Playback's current state. </p><dl class="section see"><dt>See also</dt><dd>VLCMediaState </dd></dl>

</div>
</div>
<a id="a5915273012b273885dd9570d56777ccf"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5915273012b273885dd9570d56777ccf">&#9670;&nbsp;</a></span>time</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_time.html">VLCTime</a>*) time</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">strong</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Sets the current position (or time) of the feed. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">value</td><td>New time to set the current position to. If time is [<a class="el" href="interface_v_l_c_time.html">VLCTime</a> nullTime], 0 is assumed. Returns the current position (or time) of the feed. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd><a class="el" href="interface_v_l_c_time.html">VLCTime</a> object with current time. </dd></dl>

</div>
</div>
<a id="a937a50fb274ec99b146d999fd8c02a1b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a937a50fb274ec99b146d999fd8c02a1b">&#9670;&nbsp;</a></span>titleDescriptions</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray*) titleDescriptions</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>title descriptions an array of all titles of the current media including information of name, duration and potential menu state </p><dl class="section return"><dt>Returns</dt><dd>array describing the titles in details </dd></dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="interface_v_l_c_media_player.html#a2907bb09c29757c5c0f89e5bbe7e7394">VLCTitleDescriptionName</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media_player.html#ac4bd07aad0599f2f61cbac7281981df7">VLCTitleDescriptionDuration</a> </dd>
<dd>
<a class="el" href="interface_v_l_c_media_player.html#a1cfbed633aa7783841c153d48088ba70">VLCTitleDescriptionIsMenu</a> </dd></dl>

</div>
</div>
<a id="a3ee849792344fed560e4308ebe8e4a76"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3ee849792344fed560e4308ebe8e4a76">&#9670;&nbsp;</a></span>videoAspectRatio</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (char*) videoAspectRatio</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set/Get current video aspect ratio.</p>
<p>param: psz_aspect new video aspect-ratio or NULL to reset to default </p><dl class="section note"><dt>Note</dt><dd>Invalid aspect ratios are ignored. </dd></dl>
<dl class="section return"><dt>Returns</dt><dd>the video aspect ratio or NULL if unspecified (the result must be released with free()). </dd></dl>

</div>
</div>
<a id="a3f0fd895e58be570f115ab6f09501ffe"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a3f0fd895e58be570f115ab6f09501ffe">&#9670;&nbsp;</a></span>videoCropGeometry</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (char*) videoCropGeometry</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Set/Get current crop filter geometry.</p>
<p>param: psz_geometry new crop filter geometry (NULL to unset) </p><dl class="section return"><dt>Returns</dt><dd>the crop filter geometry or NULL if unset </dd></dl>

</div>
</div>
<a id="a0734e2b2d4edebeaf3ca9e1cce85f361"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a0734e2b2d4edebeaf3ca9e1cce85f361">&#9670;&nbsp;</a></span>videoSize</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (CGSize) videoSize</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get the current video size </p><dl class="section return"><dt>Returns</dt><dd>video size as CGSize </dd></dl>

</div>
</div>
<a id="a1c8a4af83a85f3e8606049aad6f75169"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a1c8a4af83a85f3e8606049aad6f75169">&#9670;&nbsp;</a></span>videoSubTitlesIndexes</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray*) videoSubTitlesIndexes</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the video subtitle track IDs those are needed to set the video subtitle index </p>

</div>
</div>
<a id="aaaf3e36c370456bbf30058eedaf7844e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaaf3e36c370456bbf30058eedaf7844e">&#9670;&nbsp;</a></span>videoSubTitlesNames</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray*) videoSubTitlesNames</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the video subtitle track names, usually a language name or a description It includes the "Disabled" fake track at index 0. </p>

</div>
</div>
<a id="a4f1abde67436f198f0d07b885bd5ac59"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4f1abde67436f198f0d07b885bd5ac59">&#9670;&nbsp;</a></span>videoTrackIndexes</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray*) videoTrackIndexes</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the video track IDs those are needed to set the video index </p>

</div>
</div>
<a id="aaa31a3ee365dd1721064f19319bb8026"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaa31a3ee365dd1721064f19319bb8026">&#9670;&nbsp;</a></span>videoTrackNames</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (NSArray*) videoTrackNames</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">copy</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Returns the video track names, usually a language name or a description It includes the "Disabled" fake track at index 0. </p>

</div>
</div>
<a id="a2907bb09c29757c5c0f89e5bbe7e7394"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2907bb09c29757c5c0f89e5bbe7e7394">&#9670;&nbsp;</a></span>VLCTitleDescriptionName</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* const) VLCTitleDescriptionName</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>dictionary value for the user-facing title name </p>

</div>
</div>
<a id="aa55f09ffe39e021920248ff142ae0f75"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aa55f09ffe39e021920248ff142ae0f75">&#9670;&nbsp;</a></span>willPlay</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) willPlay</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Playback state flag identifying wheather the stream will play. </p><dl class="section return"><dt>Returns</dt><dd>TRUE if the feed is ready for playback, FALSE if otherwise. </dd></dl>

</div>
</div>
<a id="a5f8af3ae371f616810320e1fb447f6dc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a5f8af3ae371f616810320e1fb447f6dc">&#9670;&nbsp;</a></span>yaw</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) yaw</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Get the view point yaw in degrees</p>
<dl class="section return"><dt>Returns</dt><dd>view point yaw in degrees ]-180;180] </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_media_player_8h_source.html">VLCMediaPlayer.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
