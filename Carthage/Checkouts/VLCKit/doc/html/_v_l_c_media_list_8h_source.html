<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Headers/Public/VLCMediaList.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html">Headers</a></li><li class="navelem"><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html">Public</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCMediaList.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/*****************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * VLCMediaList.h: VLCKit.framework VLCMediaList header</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *****************************************************************************</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Copyright (C) 2007 Pierre d&#39;Herbemont</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * Copyright (C) 2015 Felix Paul Kühne</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> * Copyright (C) 2007, 2015 VLC authors and VideoLAN</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> * $Id$</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> * Authors: <AUTHORS>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> * This program is free software; you can redistribute it and/or modify it</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> * under the terms of the GNU Lesser General Public License as published by</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> * the Free Software Foundation; either version 2.1 of the License, or</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> * (at your option) any later version.</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * This program is distributed in the hope that it will be useful,</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * but WITHOUT ANY WARRANTY; without even the implied warranty of</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * GNU Lesser General Public License for more details.</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * You should have received a copy of the GNU Lesser General Public License</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * along with this program; if not, write to the Free Software Foundation,</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> *****************************************************************************/</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160; </div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#import &lt;Foundation/Foundation.h&gt;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#import &quot;VLCMedia.h&quot;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160; </div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMediaListItemAdded;</div>
<div class="line"><a name="l00036"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list.html#a1a697c6eebd811e4a9db798b9ee4044f">   36</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMediaListItemDeleted;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160; </div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media.html">VLCMedia</a>;</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media_list.html">VLCMediaList</a>;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160; </div>
<div class="line"><a name="l00044"></a><span class="lineno"><a class="line" href="protocol_v_l_c_media_list_delegate-p.html">   44</a></span>&#160;<span class="keyword">@protocol </span><a class="code" href="protocol_v_l_c_media_list_delegate-p.html">VLCMediaListDelegate</a></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">@optional</span></div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;- (void)mediaList:(<a class="code" href="interface_v_l_c_media_list.html">VLCMediaList</a> *)aMediaList mediaAdded:(<a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *)media atIndex:(NSUInteger)index;</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160; </div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;- (void)mediaList:(<a class="code" href="interface_v_l_c_media_list.html">VLCMediaList</a> *)aMediaList mediaRemovedAtIndex:(NSUInteger)index;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160; </div>
<div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list.html">   67</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_media_list.html">VLCMediaList</a> : NSObject</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160; </div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;- (instancetype)initWithArray:(NSArray *)array;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160; </div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;<span class="comment">/* Operations */</span></div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;- (void)<a class="code" href="interface_v_l_c_media_list.html#aa852de8d98be00568958006826b24661">lock</a>;</div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160; </div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;- (void)<a class="code" href="interface_v_l_c_media_list.html#a1a697c6eebd811e4a9db798b9ee4044f">unlock</a>;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160; </div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;- (NSUInteger)addMedia:(<a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *)media;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160; </div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;- (void)insertMedia:(<a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *)media atIndex:(NSUInteger)index;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160; </div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;- (BOOL)removeMediaAtIndex:(NSUInteger)index;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160; </div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;- (<a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *)mediaAtIndex:(NSUInteger)index;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160; </div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;- (NSUInteger)indexOfMedia:(<a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *)media;</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160; </div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="comment">/* Properties */</span></div>
<div class="line"><a name="l00138"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list.html#aa91841c1b03e5a4640ef1bed6c621b44">  138</a></span>&#160;<span class="keyword">@property</span> (readonly) NSInteger <a class="code" href="interface_v_l_c_media_list.html#aa91841c1b03e5a4640ef1bed6c621b44">count</a>;</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160; </div>
<div class="line"><a name="l00143"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list.html#aa5d8f85f2a5e5492177de14912028f63">  143</a></span>&#160;<span class="keyword">@property</span> (weak, nonatomic) <span class="keywordtype">id</span> <a class="code" href="interface_v_l_c_media_list.html#aa5d8f85f2a5e5492177de14912028f63">delegate</a>;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160; </div>
<div class="line"><a name="l00149"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_list.html#ad9f5b01b188f801ba9a2d675d6d94962">  149</a></span>&#160;<span class="keyword">@property</span> (readonly) BOOL <a class="code" href="interface_v_l_c_media_list.html#ad9f5b01b188f801ba9a2d675d6d94962">isReadOnly</a>;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160; </div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;<span class="keyword">@end</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="ainterface_v_l_c_media_list_html_a1a697c6eebd811e4a9db798b9ee4044f"><div class="ttname"><a href="interface_v_l_c_media_list.html#a1a697c6eebd811e4a9db798b9ee4044f">-[VLCMediaList unlock]</a></div><div class="ttdeci">void unlock()</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_html"><div class="ttname"><a href="interface_v_l_c_media_list.html">VLCMediaList</a></div><div class="ttdef"><b>Definition:</b> VLCMediaList.h:68</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html"><div class="ttname"><a href="interface_v_l_c_media.html">VLCMedia</a></div><div class="ttdef"><b>Definition:</b> VLCMedia.h:113</div></div>
<div class="ttc" id="aprotocol_v_l_c_media_list_delegate-p_html"><div class="ttname"><a href="protocol_v_l_c_media_list_delegate-p.html">VLCMediaListDelegate-p</a></div><div class="ttdef"><b>Definition:</b> VLCMediaList.h:44</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_html_aa852de8d98be00568958006826b24661"><div class="ttname"><a href="interface_v_l_c_media_list.html#aa852de8d98be00568958006826b24661">-[VLCMediaList lock]</a></div><div class="ttdeci">void lock()</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_html_aa91841c1b03e5a4640ef1bed6c621b44"><div class="ttname"><a href="interface_v_l_c_media_list.html#aa91841c1b03e5a4640ef1bed6c621b44">VLCMediaList::count</a></div><div class="ttdeci">NSInteger count</div><div class="ttdef"><b>Definition:</b> VLCMediaList.h:138</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_html_ad9f5b01b188f801ba9a2d675d6d94962"><div class="ttname"><a href="interface_v_l_c_media_list.html#ad9f5b01b188f801ba9a2d675d6d94962">VLCMediaList::isReadOnly</a></div><div class="ttdeci">BOOL isReadOnly</div><div class="ttdef"><b>Definition:</b> VLCMediaList.h:149</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_html_aa5d8f85f2a5e5492177de14912028f63"><div class="ttname"><a href="interface_v_l_c_media_list.html#aa5d8f85f2a5e5492177de14912028f63">VLCMediaList::delegate</a></div><div class="ttdeci">id delegate</div><div class="ttdef"><b>Definition:</b> VLCMediaList.h:143</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
