<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_r"></a>- r -</h3><ul>
<li>rate
: <a class="el" href="interface_v_l_c_media_player.html#adcfbd421109bce67c3950a8c45b0bbea">VLCMediaPlayer</a>
</li>
<li>reencodeAndMuxSRTFile:toMP4File:outputPath:
: <a class="el" href="interface_v_l_c_transcoder.html#a0ec64622da15a7b9826a99b1d242778f">VLCTranscoder</a>
</li>
<li>remainingTime
: <a class="el" href="interface_v_l_c_media_player.html#a994615b429c023db77a00d1efec06fd3">VLCMediaPlayer</a>
</li>
<li>removeMediaAtIndex:
: <a class="el" href="interface_v_l_c_media_list.html#a16aeab5bf78f68472369386d7a2eaae7">VLCMediaList</a>
</li>
<li>renderers
: <a class="el" href="interface_v_l_c_renderer_discoverer.html#acb4c6a5e9321cdd8432bd0f6e6ebee1d">VLCRendererDiscoverer</a>
</li>
<li>repeatMode
: <a class="el" href="interface_v_l_c_media_list_player.html#a7f868c26a93d999852c3448d28913af4">VLCMediaListPlayer</a>
</li>
<li>resetEqualizerFromProfile:
: <a class="el" href="interface_v_l_c_media_player.html#abb2864039496be0bde196467971dd873">VLCMediaPlayer</a>
</li>
<li>rewind
: <a class="el" href="interface_v_l_c_media_player.html#ac03553439681974c0da7014c44b104d6">VLCMediaPlayer</a>
</li>
<li>rewindAtRate:
: <a class="el" href="interface_v_l_c_media_player.html#ade646072ef74f61b84e4afdc260a0f36">VLCMediaPlayer</a>
</li>
<li>roll
: <a class="el" href="interface_v_l_c_media_player.html#a17e2d158c437a5bffd8da88673b99efc">VLCMediaPlayer</a>
</li>
<li>rootMedia
: <a class="el" href="interface_v_l_c_media_list_player.html#ac1a1b1d304efb4e1c62026f9ed321e9d">VLCMediaListPlayer</a>
</li>
<li>rtpBroadcastStreamOutputWithSAPAnnounce:
: <a class="el" href="interface_v_l_c_stream_output.html#a37d21584a752b9ddaecd248d171bdf59">VLCStreamOutput</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
