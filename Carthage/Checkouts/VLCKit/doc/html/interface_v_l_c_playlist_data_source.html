<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>VLCKit: VLCPlaylistDataSource Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="pages.html"><span>Related&#160;Pages</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#pro-attribs">Protected Attributes</a> &#124;
<a href="class_v_l_c_playlist_data_source-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCPlaylistDataSource Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_playlist_data_source_8h_source.html">VLCPlaylistDataSource.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCPlaylistDataSource:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_playlist_data_source.png" usemap="#VLCPlaylistDataSource_map" alt=""/>
  <map id="VLCPlaylistDataSource_map" name="VLCPlaylistDataSource_map">
</map>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a9ddde88df19ea875e643602af3c58cdd"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a9ddde88df19ea875e643602af3c58cdd"></a>
(id)&#160;</td><td class="memItemRight" valign="bottom">- <b>initWithPlaylist:</b></td></tr>
<tr class="separator:a9ddde88df19ea875e643602af3c58cdd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a16defa38a189b85e6905c86c24e247ab"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a16defa38a189b85e6905c86c24e247ab"></a>
(id)&#160;</td><td class="memItemRight" valign="bottom">- <b>initWithPlaylist:videoView:</b></td></tr>
<tr class="separator:a16defa38a189b85e6905c86c24e247ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac430498d54154531c474b75ef5d62d8b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ac430498d54154531c474b75ef5d62d8b"></a>
(VLCPlaylist *)&#160;</td><td class="memItemRight" valign="bottom">- <b>playlist</b></td></tr>
<tr class="separator:ac430498d54154531c474b75ef5d62d8b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace3fd0f0875329200bbee5d5cea06498"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ace3fd0f0875329200bbee5d5cea06498"></a>
(<a class="el" href="interface_v_l_c_video_view.html">VLCVideoView</a> *)&#160;</td><td class="memItemRight" valign="bottom">- <b>videoView</b></td></tr>
<tr class="separator:ace3fd0f0875329200bbee5d5cea06498"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae3f692ece954f6f11ba98d677f26ca82"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ae3f692ece954f6f11ba98d677f26ca82"></a>
(id)&#160;</td><td class="memItemRight" valign="bottom">- <b>outlineView:child:ofItem:</b></td></tr>
<tr class="separator:ae3f692ece954f6f11ba98d677f26ca82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a44fe64f23ea6e001a1377b647fc12f8f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a44fe64f23ea6e001a1377b647fc12f8f"></a>
(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <b>outlineView:isItemExpandable:</b></td></tr>
<tr class="separator:a44fe64f23ea6e001a1377b647fc12f8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abe2700059b620bec9cfd68f0b118398b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="abe2700059b620bec9cfd68f0b118398b"></a>
(int)&#160;</td><td class="memItemRight" valign="bottom">- <b>outlineView:numberOfChildrenOfItem:</b></td></tr>
<tr class="separator:abe2700059b620bec9cfd68f0b118398b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1a5c0c64f3fe2ea7fe7c70638a551d3b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a1a5c0c64f3fe2ea7fe7c70638a551d3b"></a>
(id)&#160;</td><td class="memItemRight" valign="bottom">- <b>outlineView:objectValueForTableColumn:byItem:</b></td></tr>
<tr class="separator:a1a5c0c64f3fe2ea7fe7c70638a551d3b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a67e74a94b82669cf37e4ffd6627824ae"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a67e74a94b82669cf37e4ffd6627824ae"></a>
(BOOL)&#160;</td><td class="memItemRight" valign="bottom">- <b>outlineView:acceptDrop:item:childIndex:</b></td></tr>
<tr class="separator:a67e74a94b82669cf37e4ffd6627824ae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aa82fae3508c29143afff3ed7eb83fbb7"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="aa82fae3508c29143afff3ed7eb83fbb7"></a>
(NSDragOperation)&#160;</td><td class="memItemRight" valign="bottom">- <b>outlineView:validateDrop:proposedItem:proposedChildIndex:</b></td></tr>
<tr class="separator:aa82fae3508c29143afff3ed7eb83fbb7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pro-attribs"></a>
Protected Attributes</h2></td></tr>
<tr class="memitem:a96e505ef40d6b831d5e5b579bf388df4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a96e505ef40d6b831d5e5b579bf388df4"></a>
VLCPlaylist *&#160;</td><td class="memItemRight" valign="bottom"><b>playlist</b></td></tr>
<tr class="separator:a96e505ef40d6b831d5e5b579bf388df4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4e824918f6892e0684d734de07ba110a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a4e824918f6892e0684d734de07ba110a"></a>
<a class="el" href="interface_v_l_c_video_view.html">VLCVideoView</a> *&#160;</td><td class="memItemRight" valign="bottom"><b>videoView</b></td></tr>
<tr class="separator:a4e824918f6892e0684d734de07ba110a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a59a00ed2d5c2b59ce12761191fe4dc8a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="a59a00ed2d5c2b59ce12761191fe4dc8a"></a>
NSOutlineView *&#160;</td><td class="memItemRight" valign="bottom"><b>outlineView</b></td></tr>
<tr class="separator:a59a00ed2d5c2b59ce12761191fe4dc8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>This class can be used as a data source for an NSOutlineView it will display the playlist content. If provided the videoView will automatically be associated to the given playlist, and actions in the outlineView will trigger the videoView, visual feedback of the current item in the videoview will be displayed in the outlineview </p>
</div><hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_playlist_data_source_8h_source.html">VLCPlaylistDataSource.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
