<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCMediaDiscoverer Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#pub-static-methods">Class Methods</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_media_discoverer-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCMediaDiscoverer Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_media_discoverer_8h_source.html">VLCMediaDiscoverer.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCMediaDiscoverer:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_media_discoverer.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a41cd6e81501ef6d0f90788c6dc761cce"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_discoverer.html#a41cd6e81501ef6d0f90788c6dc761cce">initWithName:</a></td></tr>
<tr class="separator:a41cd6e81501ef6d0f90788c6dc761cce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4795127962209e66c1b316381177205a"><td class="memItemLeft" align="right" valign="top">(instancetype)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_discoverer.html#a4795127962209e66c1b316381177205a">initWithName:libraryInstance:</a></td></tr>
<tr class="separator:a4795127962209e66c1b316381177205a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae24d8b9250ccead549c829071f7cf184"><td class="memItemLeft" align="right" valign="top">(int)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_discoverer.html#ae24d8b9250ccead549c829071f7cf184">startDiscoverer</a></td></tr>
<tr class="separator:ae24d8b9250ccead549c829071f7cf184"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aedebd3f4b61febe5ca436af1f1ea7508"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_discoverer.html#aedebd3f4b61febe5ca436af1f1ea7508">stopDiscoverer</a></td></tr>
<tr class="separator:aedebd3f4b61febe5ca436af1f1ea7508"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abefec909660b69a00410cab13c2d9a5b"><td class="memItemLeft" align="right" valign="top">(NSString *localizedName)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_discoverer.html#abefec909660b69a00410cab13c2d9a5b">__attribute__</a></td></tr>
<tr class="separator:abefec909660b69a00410cab13c2d9a5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Class Methods</h2></td></tr>
<tr class="memitem:a57b40dcdb0b650aea2feed95fdd30d83"><td class="memItemLeft" align="right" valign="top">((deprecated)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_media_discoverer.html#a57b40dcdb0b650aea2feed95fdd30d83">__attribute__</a></td></tr>
<tr class="separator:a57b40dcdb0b650aea2feed95fdd30d83"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a867e500dc5948bcb63206f814dffc138"><td class="memItemLeft" align="right" valign="top">(NSArray *)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_media_discoverer.html#a867e500dc5948bcb63206f814dffc138">availableMediaDiscovererForCategoryType:</a></td></tr>
<tr class="separator:a867e500dc5948bcb63206f814dffc138"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a7c84bb252d3183d9314b447a16a68b06"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_discoverer.html#a7c84bb252d3183d9314b447a16a68b06">libraryInstance</a></td></tr>
<tr class="separator:a7c84bb252d3183d9314b447a16a68b06"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ace04441c5b6e7b968772a5f9c3da6ce9"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_discoverer.html#ace04441c5b6e7b968772a5f9c3da6ce9">discoveredMedia</a></td></tr>
<tr class="separator:ace04441c5b6e7b968772a5f9c3da6ce9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abbd18cde0dc5c6982ec6a5c356fe9f7e"><td class="memItemLeft" align="right" valign="top">BOOL&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_discoverer.html#abbd18cde0dc5c6982ec6a5c356fe9f7e">isRunning</a></td></tr>
<tr class="separator:abbd18cde0dc5c6982ec6a5c356fe9f7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="interface_v_l_c_media_discoverer.html">VLCMediaDiscoverer</a> </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="abefec909660b69a00410cab13c2d9a5b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abefec909660b69a00410cab13c2d9a5b">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[1/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (NSString* localizedName) __attribute__ </td>
          <td></td>
          <td class="paramtype">((deprecated))&#160;</td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>localized name of the discovery module if available, otherwise in US English </p><dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000006">Deprecated:</a></b></dt><dd>Will be removed in the next major release, may return an empty string for binary compatibility </dd></dl>

</div>
</div>
<a id="a57b40dcdb0b650aea2feed95fdd30d83"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a57b40dcdb0b650aea2feed95fdd30d83">&#9670;&nbsp;</a></span>__attribute__ <span class="overload">[2/2]</span></h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ ((deprecated) __attribute__ </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>The full list of available media discoverers </p><dl class="section return"><dt>Returns</dt><dd>returns an empty array for binary compatibility, will be removed in subsequent releases </dd></dl>
<dl class="deprecated"><dt><b><a class="el" href="deprecated.html#_deprecated000005">Deprecated:</a></b></dt><dd>use availableMediaDiscovererForCategoryType instead </dd></dl>

</div>
</div>
<a id="a867e500dc5948bcb63206f814dffc138"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a867e500dc5948bcb63206f814dffc138">&#9670;&nbsp;</a></span>availableMediaDiscovererForCategoryType:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (NSArray *) availableMediaDiscovererForCategoryType: </td>
          <td></td>
          <td class="paramtype">(VLCMediaDiscovererCategoryType)&#160;</td>
          <td class="paramname"><em>categoryType</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">categoryType</td><td>VLCMediaDiscovererCategory you are looking for </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>an array of dictionaries describing the available discoverers for the requested type </dd></dl>

</div>
</div>
<a id="a41cd6e81501ef6d0f90788c6dc761cce"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a41cd6e81501ef6d0f90788c6dc761cce">&#9670;&nbsp;</a></span>initWithName:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) initWithName: </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>aServiceName</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Initializes new object with specified name. </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">aServiceName</td><td>Name of the service for this <a class="el" href="interface_v_l_c_media_discoverer.html">VLCMediaDiscoverer</a> object. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Newly created media discoverer. </dd></dl>
<dl class="section note"><dt>Note</dt><dd>with VLCKit 3.0 and above, you need to start the discoverer explicitly after creation </dd></dl>

</div>
</div>
<a id="a4795127962209e66c1b316381177205a"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a4795127962209e66c1b316381177205a">&#9670;&nbsp;</a></span>initWithName:libraryInstance:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (instancetype) <a class="el" href="interface_v_l_c_media_discoverer.html#a41cd6e81501ef6d0f90788c6dc761cce">initWithName:</a> </td>
          <td></td>
          <td class="paramtype">(NSString *)&#160;</td>
          <td class="paramname"><em>aServiceName</em></td>
        </tr>
        <tr>
          <td class="paramkey">libraryInstance:</td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> *)&#160;</td>
          <td class="paramname"><em>libraryInstance</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>same as above but with a custom <a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> instance </p><dl class="section note"><dt>Note</dt><dd>Using this mode can lead to a significant performance impact - use only if you know what you are doing </dd></dl>

</div>
</div>
<a id="ae24d8b9250ccead549c829071f7cf184"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ae24d8b9250ccead549c829071f7cf184">&#9670;&nbsp;</a></span>startDiscoverer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (int) startDiscoverer </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>start media discovery </p><dl class="section return"><dt>Returns</dt><dd>-1 if start failed, otherwise 0 </dd></dl>

</div>
</div>
<a id="aedebd3f4b61febe5ca436af1f1ea7508"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aedebd3f4b61febe5ca436af1f1ea7508">&#9670;&nbsp;</a></span>stopDiscoverer</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) stopDiscoverer </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>stop media discovery </p>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="ace04441c5b6e7b968772a5f9c3da6ce9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ace04441c5b6e7b968772a5f9c3da6ce9">&#9670;&nbsp;</a></span>discoveredMedia</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_media_list.html">VLCMediaList</a>*) discoveredMedia</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">weak</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>a read-only property to retrieve the list of discovered media items </p>

</div>
</div>
<a id="abbd18cde0dc5c6982ec6a5c356fe9f7e"></a>
<h2 class="memtitle"><span class="permalink"><a href="#abbd18cde0dc5c6982ec6a5c356fe9f7e">&#9670;&nbsp;</a></span>isRunning</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (BOOL) isRunning</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>read-only property to check if the discovery service is active </p><dl class="section return"><dt>Returns</dt><dd>boolean value </dd></dl>

</div>
</div>
<a id="a7c84bb252d3183d9314b447a16a68b06"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7c84bb252d3183d9314b447a16a68b06">&#9670;&nbsp;</a></span>libraryInstance</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_library.html">VLCLibrary</a>*) libraryInstance</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The library instance used by the discoverers </p><dl class="section note"><dt>Note</dt><dd>unless for debug, you are wrong if you want to use this selector </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_media_discoverer_8h_source.html">VLCMediaDiscoverer.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
