<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Headers/Public/VLCMediaThumbnailer.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html">Headers</a></li><li class="navelem"><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html">Public</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCMediaThumbnailer.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_thumbnailer.html#a8b4b7e2a8aa3b3c51146916ef33dc043">    1</a></span>&#160;<span class="comment">/*****************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * VLCKit: VLCMediaThumbnailer</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *****************************************************************************</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Copyright (C) 2010-2012 Pierre d&#39;Herbemont and VideoLAN</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> * Authors: <AUTHORS>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> * This program is free software; you can redistribute it and/or modify it</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> * under the terms of the GNU Lesser General Public License as published by</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> * the Free Software Foundation; either version 2.1 of the License, or</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> * (at your option) any later version.</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> * This program is distributed in the hope that it will be useful,</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> * but WITHOUT ANY WARRANTY; without even the implied warranty of</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * GNU Lesser General Public License for more details.</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * You should have received a copy of the GNU Lesser General Public License</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * along with this program; if not, write to the Free Software Foundation,</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> *****************************************************************************/</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160; </div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#import &lt;Foundation/Foundation.h&gt;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#if TARGET_OS_IPHONE</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor"># import &lt;CoreGraphics/CoreGraphics.h&gt;</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160; </div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media.html">VLCMedia</a>;</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_library.html">VLCLibrary</a>;</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="keyword">@protocol </span><a class="code" href="protocol_v_l_c_media_thumbnailer_delegate-p.html">VLCMediaThumbnailerDelegate</a>;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160; </div>
<div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_thumbnailer.html">   35</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a> : NSObject</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;+ (<a class="code" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a> *)thumbnailerWithMedia:(<a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *)media andDelegate:(<span class="keywordtype">id</span>&lt;<a class="code" href="protocol_v_l_c_media_thumbnailer_delegate-p.html">VLCMediaThumbnailerDelegate</a>&gt;)delegate;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;+ (<a class="code" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a> *)thumbnailerWithMedia:(<a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *)media delegate:(<span class="keywordtype">id</span>&lt;<a class="code" href="protocol_v_l_c_media_thumbnailer_delegate-p.html">VLCMediaThumbnailerDelegate</a>&gt;)delegate andVLCLibrary:(<a class="code" href="interface_v_l_c_library.html">VLCLibrary</a> *)library;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160; </div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;- (void)<a class="code" href="interface_v_l_c_media_thumbnailer.html#a8c0bce3c2eef22eae4d8b046ce37761b">fetchThumbnail</a>;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160; </div>
<div class="line"><a name="l00062"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_thumbnailer.html#a48ad5cbc29377c223ed35eb92097d4ac">   62</a></span>&#160;<span class="keyword">@property</span> (readwrite, weak, nonatomic) id&lt;VLCMediaThumbnailerDelegate&gt; <a class="code" href="interface_v_l_c_media_thumbnailer.html#a48ad5cbc29377c223ed35eb92097d4ac">delegate</a>;</div>
<div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_thumbnailer.html#aecc4d4aeea24bd960190a0d1a47a5a56">   66</a></span>&#160;<span class="keyword">@property</span> (readwrite, nonatomic) <a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *<a class="code" href="interface_v_l_c_media_thumbnailer.html#aecc4d4aeea24bd960190a0d1a47a5a56">media</a>;</div>
<div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_thumbnailer.html#a2cc0a6c7836be7d967858c67e51a5f50">   70</a></span>&#160;<span class="keyword">@property</span> (readwrite, assign, nonatomic) CGImageRef <a class="code" href="interface_v_l_c_media_thumbnailer.html#a2cc0a6c7836be7d967858c67e51a5f50">thumbnail</a>;</div>
<div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_thumbnailer.html#a14051a6f88fd60fb723c6de3aa5a7321">   75</a></span>&#160;<span class="keyword">@property</span> (readwrite) <span class="keywordtype">void</span> * <a class="code" href="interface_v_l_c_media_thumbnailer.html#a14051a6f88fd60fb723c6de3aa5a7321">libVLCinstance</a>;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160; </div>
<div class="line"><a name="l00083"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_thumbnailer.html#ad183c5b8990465157f049c61c2e188a0">   83</a></span>&#160;<span class="keyword">@property</span> (readwrite, assign, nonatomic) CGFloat <a class="code" href="interface_v_l_c_media_thumbnailer.html#ad183c5b8990465157f049c61c2e188a0">thumbnailHeight</a>;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160; </div>
<div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_thumbnailer.html#a8c71e4fc706ae741252eecb95d33a055">   91</a></span>&#160;<span class="keyword">@property</span> (readwrite, assign, nonatomic) CGFloat <a class="code" href="interface_v_l_c_media_thumbnailer.html#a8c71e4fc706ae741252eecb95d33a055">thumbnailWidth</a>;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160; </div>
<div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="interface_v_l_c_media_thumbnailer.html#ab6c36054a654a28cc678082d0a1e02fa">   99</a></span>&#160;<span class="keyword">@property</span> (readwrite, assign, nonatomic) <span class="keywordtype">float</span> <a class="code" href="interface_v_l_c_media_thumbnailer.html#ab6c36054a654a28cc678082d0a1e02fa">snapshotPosition</a>;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160; </div>
<div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="protocol_v_l_c_media_thumbnailer_delegate-p.html">  105</a></span>&#160;<span class="keyword">@protocol </span><a class="code" href="protocol_v_l_c_media_thumbnailer_delegate-p.html">VLCMediaThumbnailerDelegate</a></div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="keyword">@required</span></div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;- (void)mediaThumbnailerDidTimeOut:(<a class="code" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a> *)mediaThumbnailer;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;- (void)mediaThumbnailer:(<a class="code" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a> *)mediaThumbnailer didFinishThumbnail:(CGImageRef)thumbnail;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="keyword">@end</span></div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="ainterface_v_l_c_media_thumbnailer_html_ab6c36054a654a28cc678082d0a1e02fa"><div class="ttname"><a href="interface_v_l_c_media_thumbnailer.html#ab6c36054a654a28cc678082d0a1e02fa">VLCMediaThumbnailer::snapshotPosition</a></div><div class="ttdeci">float snapshotPosition</div><div class="ttdef"><b>Definition:</b> VLCMediaThumbnailer.h:99</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html"><div class="ttname"><a href="interface_v_l_c_media.html">VLCMedia</a></div><div class="ttdef"><b>Definition:</b> VLCMedia.h:113</div></div>
<div class="ttc" id="ainterface_v_l_c_media_thumbnailer_html_a2cc0a6c7836be7d967858c67e51a5f50"><div class="ttname"><a href="interface_v_l_c_media_thumbnailer.html#a2cc0a6c7836be7d967858c67e51a5f50">VLCMediaThumbnailer::thumbnail</a></div><div class="ttdeci">CGImageRef thumbnail</div><div class="ttdef"><b>Definition:</b> VLCMediaThumbnailer.h:70</div></div>
<div class="ttc" id="ainterface_v_l_c_media_thumbnailer_html"><div class="ttname"><a href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a></div><div class="ttdef"><b>Definition:</b> VLCMediaThumbnailer.h:36</div></div>
<div class="ttc" id="ainterface_v_l_c_media_thumbnailer_html_a14051a6f88fd60fb723c6de3aa5a7321"><div class="ttname"><a href="interface_v_l_c_media_thumbnailer.html#a14051a6f88fd60fb723c6de3aa5a7321">VLCMediaThumbnailer::libVLCinstance</a></div><div class="ttdeci">void * libVLCinstance</div><div class="ttdef"><b>Definition:</b> VLCMediaThumbnailer.h:75</div></div>
<div class="ttc" id="ainterface_v_l_c_media_thumbnailer_html_ad183c5b8990465157f049c61c2e188a0"><div class="ttname"><a href="interface_v_l_c_media_thumbnailer.html#ad183c5b8990465157f049c61c2e188a0">VLCMediaThumbnailer::thumbnailHeight</a></div><div class="ttdeci">CGFloat thumbnailHeight</div><div class="ttdef"><b>Definition:</b> VLCMediaThumbnailer.h:83</div></div>
<div class="ttc" id="ainterface_v_l_c_library_html"><div class="ttname"><a href="interface_v_l_c_library.html">VLCLibrary</a></div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:47</div></div>
<div class="ttc" id="ainterface_v_l_c_media_thumbnailer_html_a48ad5cbc29377c223ed35eb92097d4ac"><div class="ttname"><a href="interface_v_l_c_media_thumbnailer.html#a48ad5cbc29377c223ed35eb92097d4ac">VLCMediaThumbnailer::delegate</a></div><div class="ttdeci">id&lt; VLCMediaThumbnailerDelegate &gt; delegate</div><div class="ttdef"><b>Definition:</b> VLCMediaThumbnailer.h:62</div></div>
<div class="ttc" id="ainterface_v_l_c_media_thumbnailer_html_a8c0bce3c2eef22eae4d8b046ce37761b"><div class="ttname"><a href="interface_v_l_c_media_thumbnailer.html#a8c0bce3c2eef22eae4d8b046ce37761b">-[VLCMediaThumbnailer fetchThumbnail]</a></div><div class="ttdeci">void fetchThumbnail()</div></div>
<div class="ttc" id="ainterface_v_l_c_media_thumbnailer_html_a8c71e4fc706ae741252eecb95d33a055"><div class="ttname"><a href="interface_v_l_c_media_thumbnailer.html#a8c71e4fc706ae741252eecb95d33a055">VLCMediaThumbnailer::thumbnailWidth</a></div><div class="ttdeci">CGFloat thumbnailWidth</div><div class="ttdef"><b>Definition:</b> VLCMediaThumbnailer.h:91</div></div>
<div class="ttc" id="aprotocol_v_l_c_media_thumbnailer_delegate-p_html"><div class="ttname"><a href="protocol_v_l_c_media_thumbnailer_delegate-p.html">VLCMediaThumbnailerDelegate-p</a></div><div class="ttdef"><b>Definition:</b> VLCMediaThumbnailer.h:105</div></div>
<div class="ttc" id="ainterface_v_l_c_media_thumbnailer_html_aecc4d4aeea24bd960190a0d1a47a5a56"><div class="ttname"><a href="interface_v_l_c_media_thumbnailer.html#aecc4d4aeea24bd960190a0d1a47a5a56">VLCMediaThumbnailer::media</a></div><div class="ttdeci">VLCMedia * media</div><div class="ttdef"><b>Definition:</b> VLCMediaThumbnailer.h:66</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
