<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Headers/Public/VLCMedia.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html">Headers</a></li><li class="navelem"><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html">Public</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCMedia.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/*****************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * VLCMedia.h: VLCKit.framework VLCMedia header</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *****************************************************************************</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Copyright (C) 2007 Pierre d&#39;Herbemont</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * Copyright (C) 2013 Felix Paul Kühne</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> * Copyright (C) 2007-2013 VLC authors and VideoLAN</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> * $Id$</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> * Authors: <AUTHORS>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *          Felix Paul Kühne &lt;fkuehne # videolan.org&gt;</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *          Soomin Lee &lt;TheHungryBu # gmail.com&gt;</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> * This program is free software; you can redistribute it and/or modify it</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> * under the terms of the GNU Lesser General Public License as published by</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * the Free Software Foundation; either version 2.1 of the License, or</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * (at your option) any later version.</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * This program is distributed in the hope that it will be useful,</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * but WITHOUT ANY WARRANTY; without even the implied warranty of</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * GNU Lesser General Public License for more details.</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> * You should have received a copy of the GNU Lesser General Public License</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> * along with this program; if not, write to the Free Software Foundation,</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="comment"> * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="comment"> *****************************************************************************/</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160; </div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#import &lt;Foundation/Foundation.h&gt;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#import &quot;VLCMediaList.h&quot;</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#import &quot;VLCTime.h&quot;</span></div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160; </div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;NS_ASSUME_NONNULL_BEGIN</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160; </div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="comment">/* Meta Dictionary Keys */</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationTitle;          <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationArtist;         <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationGenre;          <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationCopyright;      <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationAlbum;          <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationTrackNumber;    <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationDescription;    <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationRating;         <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationDate;           <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationSetting;        <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationURL;            <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationLanguage;       <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationNowPlaying;     <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationPublisher;      <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationEncodedBy;      <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationArtworkURL;     <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationArtwork;        <span class="comment">/* NSImage  */</span></div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationTrackID;        <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationTrackTotal;     <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationDirector;       <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationSeason;         <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationEpisode;        <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationShowName;       <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationActors;         <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationAlbumArtist;    <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMetaInformationDiscNumber;     <span class="comment">/* NSString */</span></div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160; </div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="comment">/* Notification Messages */</span></div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> VLCMediaMetaChanged;  </div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160; </div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;<span class="comment">// Forward declarations, supresses compiler error messages</span></div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media_list.html">VLCMediaList</a>;</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_media.html">VLCMedia</a>;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160; </div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;<span class="keyword">typedef</span> NS_ENUM(NSInteger, VLCMediaState) {</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;    VLCMediaStateNothingSpecial,        </div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;    VLCMediaStateBuffering,             </div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;    VLCMediaStatePlaying,               </div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;    VLCMediaStateError,                 </div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;};</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160; </div>
<div class="line"><a name="l00086"></a><span class="lineno"><a class="line" href="protocol_v_l_c_media_delegate-p.html">   86</a></span>&#160;<span class="keyword">@protocol </span><a class="code" href="protocol_v_l_c_media_delegate-p.html">VLCMediaDelegate</a> &lt;NSObject&gt;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160; </div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;<span class="keyword">@optional</span></div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160; </div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;- (void)mediaMetaDataDidChange:(<a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *)aMedia;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160; </div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;- (void)mediaDidFinishParsing:(<a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *)aMedia;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160; </div>
<div class="line"><a name="l00112"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html">  112</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_media.html">VLCMedia</a> : NSObject</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160; </div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="comment">/* Factories */</span></div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;+ (instancetype)mediaWithURL:(NSURL *)anURL;</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160; </div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;+ (instancetype)mediaWithPath:(NSString *)aPath;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160; </div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;+ (NSString *)codecNameForFourCC:(uint32_t)fourcc trackType:(NSString *)trackType;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160; </div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;+ (instancetype)mediaAsNodeWithName:(NSString *)aName;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160; </div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;<span class="comment">/* Initializers */</span></div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;- (instancetype)initWithURL:(NSURL *)anURL;</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160; </div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;- (instancetype)initWithPath:(NSString *)aPath;</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160; </div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;- (instancetype)initWithStream:(NSInputStream *)stream;</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160; </div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;- (instancetype)initAsNodeWithName:(NSString *)aName;</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160; </div>
<div class="line"><a name="l00186"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#af4439eb3074ae064da27365b68ddbfc8">  186</a></span>&#160;<span class="keyword">typedef</span> <a class="code" href="interface_v_l_c_media.html#af4439eb3074ae064da27365b68ddbfc8">NS_ENUM</a>(NSUInteger, VLCMediaOrientation) {</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;    VLCMediaOrientationTopLeft,</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;    VLCMediaOrientationTopRight,</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;    VLCMediaOrientationBottomLeft,</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;    VLCMediaOrientationBottomRight,</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;    VLCMediaOrientationLeftTop,</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;    VLCMediaOrientationLeftBottom,</div>
<div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;    VLCMediaOrientationRightTop,</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;    VLCMediaOrientationRightBottom</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;};</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160; </div>
<div class="line"><a name="l00200"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a3ae7faaa9307383b5651794c141b3e5e">  200</a></span>&#160;<span class="keyword">typedef</span> <a class="code" href="interface_v_l_c_media.html#a3ae7faaa9307383b5651794c141b3e5e">NS_ENUM</a>(NSUInteger, VLCMediaProjection) {</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;    VLCMediaProjectionRectangular,</div>
<div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;    VLCMediaProjectionEquiRectangular,</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;    VLCMediaProjectionCubemapLayoutStandard = 0x100</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;};</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160; </div>
<div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a353de8b3f3676f9f422630ed595fdfcc">  209</a></span>&#160;<span class="keyword">typedef</span> <a class="code" href="interface_v_l_c_media.html#a353de8b3f3676f9f422630ed595fdfcc">NS_ENUM</a>(NSUInteger, VLCMediaType) {</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;    VLCMediaTypeUnknown,</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;    VLCMediaTypeFile,</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;    VLCMediaTypeDirectory,</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;    VLCMediaTypeDisc,</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;    VLCMediaTypeStream,</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;    VLCMediaTypePlaylist,</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;};</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160; </div>
<div class="line"><a name="l00222"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a8f6e78f8cc5d52384047ddaea9e01dcf">  222</a></span>&#160;<span class="keyword">@property</span> (readonly) VLCMediaType <a class="code" href="interface_v_l_c_media.html#a8f6e78f8cc5d52384047ddaea9e01dcf">mediaType</a>;</div>
<div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160; </div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;- (NSComparisonResult)compare:(<a class="code" href="interface_v_l_c_media.html">VLCMedia</a> *)media;</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160; </div>
<div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;<span class="comment">/* Properties */</span></div>
<div class="line"><a name="l00239"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a2cb849f8dceb22cebbac149921c785a5">  239</a></span>&#160;<span class="keyword">@property</span> (nonatomic, weak) id&lt;VLCMediaDelegate&gt; <a class="code" href="interface_v_l_c_media.html#a2cb849f8dceb22cebbac149921c785a5">delegate</a>;</div>
<div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160; </div>
<div class="line"><a name="l00246"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a82e93da5f18bf8584beff1b714d496d4">  246</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readwrite, strong) <a class="code" href="interface_v_l_c_time.html">VLCTime</a> * <a class="code" href="interface_v_l_c_media.html#afd47b541ffd9e93a5864ced1f127101d">length</a>;</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160; </div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;- (<a class="code" href="interface_v_l_c_time.html">VLCTime</a> *)lengthWaitUntilDate:(NSDate *)aDate;</div>
<div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160; </div>
<div class="line"><a name="l00262"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#ac32a90c64851638af38108040b37e454">  262</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly) BOOL isParsed <a class="code" href="interface_v_l_c_media.html#ac32a90c64851638af38108040b37e454">__attribute__</a>((deprecated));</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160; </div>
<div class="line"><a name="l00267"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a4ad7dacc361919932777b2bf5a141023">  267</a></span>&#160;<span class="keyword">typedef</span> <a class="code" href="interface_v_l_c_media.html#a4ad7dacc361919932777b2bf5a141023">NS_ENUM</a>(<span class="keywordtype">unsigned</span>, VLCMediaParsedStatus)</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;{</div>
<div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;    VLCMediaParsedStatusInit = 0,</div>
<div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;    VLCMediaParsedStatusSkipped,</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;    VLCMediaParsedStatusFailed,</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;    VLCMediaParsedStatusTimeout,</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;    VLCMediaParsedStatusDone</div>
<div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;};</div>
<div class="line"><a name="l00278"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#adc94b1c776ed671be57746c79e04f187">  278</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly) VLCMediaParsedStatus <a class="code" href="interface_v_l_c_media.html#adc94b1c776ed671be57746c79e04f187">parsedStatus</a>;</div>
<div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160; </div>
<div class="line"><a name="l00283"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#aef3995dbdd704cc5c8ed4fc2e383e0a6">  283</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly, strong) NSURL * <a class="code" href="interface_v_l_c_media.html#aef3995dbdd704cc5c8ed4fc2e383e0a6">url</a>;</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160; </div>
<div class="line"><a name="l00288"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a08f3d51d9b8199fd20143d178b368b2f">  288</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly, strong) <a class="code" href="interface_v_l_c_media_list.html">VLCMediaList</a> * <a class="code" href="interface_v_l_c_media.html#a08f3d51d9b8199fd20143d178b368b2f">subitems</a>;</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160; </div>
<div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;- (NSString *)metadataForKey:(NSString *)key;</div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160; </div>
<div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;- (void)setMetadata:(NSString *)data forKey:(NSString *)key;</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160; </div>
<div class="line"><a name="l00310"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a2332173d72093469abebf56f4c70ae80">  310</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) BOOL <a class="code" href="interface_v_l_c_media.html#a2332173d72093469abebf56f4c70ae80">saveMetadata</a>;</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160; </div>
<div class="line"><a name="l00315"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#ac61f729efe7481e86d26e7e92fff0dd2">  315</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly, copy) NSDictionary * <a class="code" href="interface_v_l_c_media.html#ac61f729efe7481e86d26e7e92fff0dd2">metaDictionary</a>;</div>
<div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160; </div>
<div class="line"><a name="l00320"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#af247fea93ce48e219ddef15bdaf256de">  320</a></span>&#160;<span class="keyword">@property</span> (nonatomic, readonly) VLCMediaState <a class="code" href="interface_v_l_c_media.html#af247fea93ce48e219ddef15bdaf256de">state</a>;</div>
<div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160; </div>
<div class="line"><a name="l00325"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a2018328bfcb5934f725b285026fe4e98">  325</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, getter=isMediaSizeSuitableForDevice, readonly) BOOL <a class="code" href="interface_v_l_c_media.html#a2018328bfcb5934f725b285026fe4e98">mediaSizeSuitableForDevice</a>;</div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160; </div>
<div class="line"><a name="l00335"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a0e44431952021460c5f59f600236630b">  335</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#a0e44431952021460c5f59f600236630b">VLCMediaTracksInformationCodec</a>;</div>
<div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160; </div>
<div class="line"><a name="l00341"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a523d5f9351c2fcac0d9b600773734c81">  341</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#a523d5f9351c2fcac0d9b600773734c81">VLCMediaTracksInformationId</a>;</div>
<div class="line"><a name="l00350"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#ac5ccaa4e433a8bc847e54739d69827b7">  350</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#ac5ccaa4e433a8bc847e54739d69827b7">VLCMediaTracksInformationType</a>;</div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160; </div>
<div class="line"><a name="l00356"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a076812e00bd51440c4d47da823011f86">  356</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#a076812e00bd51440c4d47da823011f86">VLCMediaTracksInformationCodecProfile</a>;</div>
<div class="line"><a name="l00361"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#ae380aafa86ebd25ad38ab630a6dc86dd">  361</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#ae380aafa86ebd25ad38ab630a6dc86dd">VLCMediaTracksInformationCodecLevel</a>;</div>
<div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160; </div>
<div class="line"><a name="l00367"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#add3bac6827f60b1cbe44544c106b39c0">  367</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#add3bac6827f60b1cbe44544c106b39c0">VLCMediaTracksInformationBitrate</a>;</div>
<div class="line"><a name="l00372"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#ac6879d1635a7c5c306bafc23cbed755a">  372</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#ac6879d1635a7c5c306bafc23cbed755a">VLCMediaTracksInformationLanguage</a>;</div>
<div class="line"><a name="l00377"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a480f68a87f30c723f9364f00620de519">  377</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#a480f68a87f30c723f9364f00620de519">VLCMediaTracksInformationDescription</a>;</div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160; </div>
<div class="line"><a name="l00383"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#aabf94e7de92ae328dba46d6c53e5d869">  383</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#aabf94e7de92ae328dba46d6c53e5d869">VLCMediaTracksInformationAudioChannelsNumber</a>;</div>
<div class="line"><a name="l00388"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a5f42247ad4cefc2cfa4a96bd95f53356">  388</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#a5f42247ad4cefc2cfa4a96bd95f53356">VLCMediaTracksInformationAudioRate</a>;</div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160; </div>
<div class="line"><a name="l00394"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a52414aa5aff9e0e929d6b3dad0461dd2">  394</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#a52414aa5aff9e0e929d6b3dad0461dd2">VLCMediaTracksInformationVideoHeight</a>;</div>
<div class="line"><a name="l00399"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a29ca0a5036cfa556f5d7098c44030123">  399</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#a29ca0a5036cfa556f5d7098c44030123">VLCMediaTracksInformationVideoWidth</a>;</div>
<div class="line"><a name="l00400"></a><span class="lineno">  400</span>&#160; </div>
<div class="line"><a name="l00405"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a32e842b07314f6b7a965fd8d5770bf8d">  405</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#a32e842b07314f6b7a965fd8d5770bf8d">VLCMediaTracksInformationVideoOrientation</a>;</div>
<div class="line"><a name="l00410"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#ac28eab6679f8761ce13ea02d61562d21">  410</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#ac28eab6679f8761ce13ea02d61562d21">VLCMediaTracksInformationVideoProjection</a>;</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160; </div>
<div class="line"><a name="l00416"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#ada9d9ba5acf71414913ecc83cb975bf6">  416</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#ada9d9ba5acf71414913ecc83cb975bf6">VLCMediaTracksInformationSourceAspectRatio</a>;</div>
<div class="line"><a name="l00421"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#aa3b1ead249368c4b73a544f07c84bcdc">  421</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#aa3b1ead249368c4b73a544f07c84bcdc">VLCMediaTracksInformationSourceAspectRatioDenominator</a>;</div>
<div class="line"><a name="l00422"></a><span class="lineno">  422</span>&#160; </div>
<div class="line"><a name="l00427"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#afd5e8623f3246506f21576ca006df47e">  427</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#afd5e8623f3246506f21576ca006df47e">VLCMediaTracksInformationFrameRate</a>;</div>
<div class="line"><a name="l00432"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a635234c93bcb43393868435ab98ad0a8">  432</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#a635234c93bcb43393868435ab98ad0a8">VLCMediaTracksInformationFrameRateDenominator</a>;</div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160; </div>
<div class="line"><a name="l00438"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#ad173cd33fb9d51175e676b62838cd980">  438</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#ad173cd33fb9d51175e676b62838cd980">VLCMediaTracksInformationTextEncoding</a>;</div>
<div class="line"><a name="l00439"></a><span class="lineno">  439</span>&#160; </div>
<div class="line"><a name="l00443"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#ac6eb47ffe2b3a79f562e0164e83416b1">  443</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#ac6eb47ffe2b3a79f562e0164e83416b1">VLCMediaTracksInformationTypeAudio</a>;</div>
<div class="line"><a name="l00447"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a2ac631a7a8c7416ac9a13b914efeb22e">  447</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#a2ac631a7a8c7416ac9a13b914efeb22e">VLCMediaTracksInformationTypeVideo</a>;</div>
<div class="line"><a name="l00451"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a3d8f7f156478e43c45dfedf7459c9939">  451</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#a3d8f7f156478e43c45dfedf7459c9939">VLCMediaTracksInformationTypeText</a>;</div>
<div class="line"><a name="l00455"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a2b91c4e456c6ce07682477d41772adc2">  455</a></span>&#160;<span class="keyword">extern</span> NSString *<span class="keyword">const</span> <a class="code" href="interface_v_l_c_media.html#a2b91c4e456c6ce07682477d41772adc2">VLCMediaTracksInformationTypeUnknown</a>;</div>
<div class="line"><a name="l00456"></a><span class="lineno">  456</span>&#160; </div>
<div class="line"><a name="l00491"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a7b098bacc67ab0ff8fa9d316bef987d6">  491</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly, copy) NSArray *<a class="code" href="interface_v_l_c_media.html#a7b098bacc67ab0ff8fa9d316bef987d6">tracksInformation</a>;</div>
<div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160; </div>
<div class="line"><a name="l00503"></a><span class="lineno">  503</span>&#160;- (void)parse <a class="code" href="interface_v_l_c_media.html#a28c23c5d427727732476f86c6d0645ee">__attribute__</a>((deprecated));</div>
<div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160; </div>
<div class="line"><a name="l00511"></a><span class="lineno">  511</span>&#160;- (void)synchronousParse <a class="code" href="interface_v_l_c_media.html#a28c23c5d427727732476f86c6d0645ee">__attribute__</a>((deprecated));</div>
<div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160; </div>
<div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;<span class="keyword">enum</span> {</div>
<div class="line"><a name="l00514"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba96d692455552df3b6332ce02b5c955a1">  514</a></span>&#160;    <a class="code" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba96d692455552df3b6332ce02b5c955a1">VLCMediaParseLocal</a>          = 0x00,     </div>
<div class="line"><a name="l00515"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cbadac6d0a10ac08b36376db311ce6a91a5">  515</a></span>&#160;    <a class="code" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cbadac6d0a10ac08b36376db311ce6a91a5">VLCMediaParseNetwork</a>        = 0x01,     </div>
<div class="line"><a name="l00516"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba802f388c99aa52b9bd26a11f55ffd4f0">  516</a></span>&#160;    <a class="code" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba802f388c99aa52b9bd26a11f55ffd4f0">VLCMediaFetchLocal</a>          = 0x02,     </div>
<div class="line"><a name="l00517"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cbaf9a01d0578ab528242b3de74d5e216dc">  517</a></span>&#160;    <a class="code" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cbaf9a01d0578ab528242b3de74d5e216dc">VLCMediaFetchNetwork</a>        = 0x04,     </div>
<div class="line"><a name="l00518"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba0184ba9260e22d0182cf418b2d73c20c">  518</a></span>&#160;    <a class="code" href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba0184ba9260e22d0182cf418b2d73c20c">VLCMediaDoInteract</a>          = 0x08,     </div>
<div class="line"><a name="l00519"></a><span class="lineno">  519</span>&#160;};</div>
<div class="line"><a name="l00524"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">  524</a></span>&#160;<span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code" href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">VLCMediaParsingOptions</a>;</div>
<div class="line"><a name="l00525"></a><span class="lineno">  525</span>&#160; </div>
<div class="line"><a name="l00536"></a><span class="lineno">  536</span>&#160;- (int)parseWithOptions:(<a class="code" href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">VLCMediaParsingOptions</a>)options;</div>
<div class="line"><a name="l00537"></a><span class="lineno">  537</span>&#160; </div>
<div class="line"><a name="l00549"></a><span class="lineno">  549</span>&#160;- (int)parseWithOptions:(<a class="code" href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">VLCMediaParsingOptions</a>)options timeout:(<span class="keywordtype">int</span>)timeoutValue;</div>
<div class="line"><a name="l00550"></a><span class="lineno">  550</span>&#160; </div>
<div class="line"><a name="l00557"></a><span class="lineno">  557</span>&#160;- (void)<a class="code" href="interface_v_l_c_media.html#a0213a3ea482353bce0d7bb59355d497a">parseStop</a>;</div>
<div class="line"><a name="l00558"></a><span class="lineno">  558</span>&#160; </div>
<div class="line"><a name="l00567"></a><span class="lineno">  567</span>&#160;- (void)addOption:(NSString *)option;</div>
<div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;- (void)addOptions:(NSDictionary*)options;</div>
<div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160; </div>
<div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;- (int)storeCookie:(NSString * _Nonnull)cookie</div>
<div class="line"><a name="l00586"></a><span class="lineno">  586</span>&#160;           forHost:(NSString * _Nonnull)host</div>
<div class="line"><a name="l00587"></a><span class="lineno">  587</span>&#160;              path:(NSString * _Nonnull)path;</div>
<div class="line"><a name="l00588"></a><span class="lineno">  588</span>&#160; </div>
<div class="line"><a name="l00596"></a><span class="lineno">  596</span>&#160;- (void)<a class="code" href="interface_v_l_c_media.html#a51bdb09726b9f4d72072e144ea7314cc">clearStoredCookies</a>;</div>
<div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160; </div>
<div class="line"><a name="l00603"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a9c2d85f9c2dba700d7b2ca18cf12049a">  603</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly, copy, nullable) NSDictionary *<a class="code" href="interface_v_l_c_media.html#a9c2d85f9c2dba700d7b2ca18cf12049a">stats</a>;</div>
<div class="line"><a name="l00604"></a><span class="lineno">  604</span>&#160; </div>
<div class="line"><a name="l00605"></a><span class="lineno">  605</span>&#160;<span class="preprocessor">#pragma mark - individual stats</span></div>
<div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160; </div>
<div class="line"><a name="l00611"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#aadc4d2257ae507913c39611e9c935665">  611</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSInteger <a class="code" href="interface_v_l_c_media.html#aadc4d2257ae507913c39611e9c935665">numberOfReadBytesOnInput</a>;</div>
<div class="line"><a name="l00616"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a2eb646a3d37eaec7de62ba174b9682f7">  616</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) <span class="keywordtype">float</span> <a class="code" href="interface_v_l_c_media.html#a2eb646a3d37eaec7de62ba174b9682f7">inputBitrate</a>;</div>
<div class="line"><a name="l00617"></a><span class="lineno">  617</span>&#160; </div>
<div class="line"><a name="l00622"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a936f14e9dbdb6355604040bb963cf1b2">  622</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSInteger <a class="code" href="interface_v_l_c_media.html#a936f14e9dbdb6355604040bb963cf1b2">numberOfReadBytesOnDemux</a>;</div>
<div class="line"><a name="l00627"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a1e060d1cb138c0e0ecffe53d985b2dd3">  627</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) <span class="keywordtype">float</span> <a class="code" href="interface_v_l_c_media.html#a1e060d1cb138c0e0ecffe53d985b2dd3">demuxBitrate</a>;</div>
<div class="line"><a name="l00628"></a><span class="lineno">  628</span>&#160; </div>
<div class="line"><a name="l00633"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a0d65e705f516777543cb6ac2df310779">  633</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSInteger <a class="code" href="interface_v_l_c_media.html#a0d65e705f516777543cb6ac2df310779">numberOfDecodedVideoBlocks</a>;</div>
<div class="line"><a name="l00638"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#ae5f6aa8f4cfd924c9f31cea1292739de">  638</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSInteger <a class="code" href="interface_v_l_c_media.html#ae5f6aa8f4cfd924c9f31cea1292739de">numberOfDecodedAudioBlocks</a>;</div>
<div class="line"><a name="l00639"></a><span class="lineno">  639</span>&#160; </div>
<div class="line"><a name="l00644"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a42f0be6a3830572833122e758ddaafb1">  644</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSInteger <a class="code" href="interface_v_l_c_media.html#a42f0be6a3830572833122e758ddaafb1">numberOfDisplayedPictures</a>;</div>
<div class="line"><a name="l00649"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#ab7456ceac9f4ac4b395bcc50064d58dd">  649</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSInteger <a class="code" href="interface_v_l_c_media.html#ab7456ceac9f4ac4b395bcc50064d58dd">numberOfLostPictures</a>;</div>
<div class="line"><a name="l00650"></a><span class="lineno">  650</span>&#160; </div>
<div class="line"><a name="l00655"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a958ceff6c2c01085c9c11963fc00e9ab">  655</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSInteger <a class="code" href="interface_v_l_c_media.html#a958ceff6c2c01085c9c11963fc00e9ab">numberOfPlayedAudioBuffers</a>;</div>
<div class="line"><a name="l00660"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a13d927d07a8bc2cebab7363317c0a932">  660</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSInteger <a class="code" href="interface_v_l_c_media.html#a13d927d07a8bc2cebab7363317c0a932">numberOfLostAudioBuffers</a>;</div>
<div class="line"><a name="l00661"></a><span class="lineno">  661</span>&#160; </div>
<div class="line"><a name="l00666"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a65060dbc9eefe3518c4aa81daba05320">  666</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSInteger <a class="code" href="interface_v_l_c_media.html#a65060dbc9eefe3518c4aa81daba05320">numberOfSentPackets</a>;</div>
<div class="line"><a name="l00671"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a4f663bcbd8cfea3c1fa23035a5e2e119">  671</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSInteger <a class="code" href="interface_v_l_c_media.html#a4f663bcbd8cfea3c1fa23035a5e2e119">numberOfSentBytes</a>;</div>
<div class="line"><a name="l00676"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#afd944ae42af805d532f4ab36d5b0fe7d">  676</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) <span class="keywordtype">float</span> <a class="code" href="interface_v_l_c_media.html#afd944ae42af805d532f4ab36d5b0fe7d">streamOutputBitrate</a>;</div>
<div class="line"><a name="l00682"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a289dc0ff117c7013b6b5363d9f35fd01">  682</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSInteger <a class="code" href="interface_v_l_c_media.html#a289dc0ff117c7013b6b5363d9f35fd01">numberOfCorruptedDataPackets</a>;</div>
<div class="line"><a name="l00688"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a148f7e15ef691ed0c6cf631eb3bc34d8">  688</a></span>&#160;<span class="keyword">@property</span> (NS_NONATOMIC_IOSONLY, readonly) NSInteger <a class="code" href="interface_v_l_c_media.html#a148f7e15ef691ed0c6cf631eb3bc34d8">numberOfDiscontinuties</a>;</div>
<div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160; </div>
<div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00691"></a><span class="lineno">  691</span>&#160; </div>
<div class="line"><a name="l00692"></a><span class="lineno"><a class="line" href="interface_v_l_c_media.html#a38e5fb8f18d50b6de684a7e56c1611fa">  692</a></span>&#160;NS_ASSUME_NONNULL_END</div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="ainterface_v_l_c_media_html_a4f663bcbd8cfea3c1fa23035a5e2e119"><div class="ttname"><a href="interface_v_l_c_media.html#a4f663bcbd8cfea3c1fa23035a5e2e119">VLCMedia::numberOfSentBytes</a></div><div class="ttdeci">NSInteger numberOfSentBytes</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:671</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_afd944ae42af805d532f4ab36d5b0fe7d"><div class="ttname"><a href="interface_v_l_c_media.html#afd944ae42af805d532f4ab36d5b0fe7d">VLCMedia::streamOutputBitrate</a></div><div class="ttdeci">float streamOutputBitrate</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:676</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a0e44431952021460c5f59f600236630b"><div class="ttname"><a href="interface_v_l_c_media.html#a0e44431952021460c5f59f600236630b">VLCMedia::VLCMediaTracksInformationCodec</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationCodec</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:335</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_ac6879d1635a7c5c306bafc23cbed755a"><div class="ttname"><a href="interface_v_l_c_media.html#ac6879d1635a7c5c306bafc23cbed755a">VLCMedia::VLCMediaTracksInformationLanguage</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationLanguage</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:372</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a2cb849f8dceb22cebbac149921c785a5"><div class="ttname"><a href="interface_v_l_c_media.html#a2cb849f8dceb22cebbac149921c785a5">VLCMedia::delegate</a></div><div class="ttdeci">id&lt; VLCMediaDelegate &gt; delegate</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:239</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a42f0be6a3830572833122e758ddaafb1"><div class="ttname"><a href="interface_v_l_c_media.html#a42f0be6a3830572833122e758ddaafb1">VLCMedia::numberOfDisplayedPictures</a></div><div class="ttdeci">NSInteger numberOfDisplayedPictures</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:644</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_ae5f6aa8f4cfd924c9f31cea1292739de"><div class="ttname"><a href="interface_v_l_c_media.html#ae5f6aa8f4cfd924c9f31cea1292739de">VLCMedia::numberOfDecodedAudioBlocks</a></div><div class="ttdeci">NSInteger numberOfDecodedAudioBlocks</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:638</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a17eb20a065d628caf152e8e4c83bc4cbadac6d0a10ac08b36376db311ce6a91a5"><div class="ttname"><a href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cbadac6d0a10ac08b36376db311ce6a91a5">VLCMedia::VLCMediaParseNetwork</a></div><div class="ttdeci">@ VLCMediaParseNetwork</div><div class="ttdoc">Parse media even if it's a network file.</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:515</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a32e842b07314f6b7a965fd8d5770bf8d"><div class="ttname"><a href="interface_v_l_c_media.html#a32e842b07314f6b7a965fd8d5770bf8d">VLCMedia::VLCMediaTracksInformationVideoOrientation</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationVideoOrientation</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:405</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_af4439eb3074ae064da27365b68ddbfc8"><div class="ttname"><a href="interface_v_l_c_media.html#af4439eb3074ae064da27365b68ddbfc8">-[VLCMedia NS_ENUM]</a></div><div class="ttdeci">typedef NS_ENUM(NSUInteger, VLCMediaOrientation)</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:186</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a958ceff6c2c01085c9c11963fc00e9ab"><div class="ttname"><a href="interface_v_l_c_media.html#a958ceff6c2c01085c9c11963fc00e9ab">VLCMedia::numberOfPlayedAudioBuffers</a></div><div class="ttdeci">NSInteger numberOfPlayedAudioBuffers</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:655</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a353de8b3f3676f9f422630ed595fdfcc"><div class="ttname"><a href="interface_v_l_c_media.html#a353de8b3f3676f9f422630ed595fdfcc">-[VLCMedia NS_ENUM]</a></div><div class="ttdeci">typedef NS_ENUM(NSUInteger, VLCMediaType)</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:209</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_aadc4d2257ae507913c39611e9c935665"><div class="ttname"><a href="interface_v_l_c_media.html#aadc4d2257ae507913c39611e9c935665">VLCMedia::numberOfReadBytesOnInput</a></div><div class="ttdeci">NSInteger numberOfReadBytesOnInput</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:611</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a1e060d1cb138c0e0ecffe53d985b2dd3"><div class="ttname"><a href="interface_v_l_c_media.html#a1e060d1cb138c0e0ecffe53d985b2dd3">VLCMedia::demuxBitrate</a></div><div class="ttdeci">float demuxBitrate</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:627</div></div>
<div class="ttc" id="ainterface_v_l_c_media_list_html"><div class="ttname"><a href="interface_v_l_c_media_list.html">VLCMediaList</a></div><div class="ttdef"><b>Definition:</b> VLCMediaList.h:68</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_ac6eb47ffe2b3a79f562e0164e83416b1"><div class="ttname"><a href="interface_v_l_c_media.html#ac6eb47ffe2b3a79f562e0164e83416b1">VLCMedia::VLCMediaTracksInformationTypeAudio</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationTypeAudio</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:443</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a480f68a87f30c723f9364f00620de519"><div class="ttname"><a href="interface_v_l_c_media.html#a480f68a87f30c723f9364f00620de519">VLCMedia::VLCMediaTracksInformationDescription</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationDescription</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:377</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a2018328bfcb5934f725b285026fe4e98"><div class="ttname"><a href="interface_v_l_c_media.html#a2018328bfcb5934f725b285026fe4e98">VLCMedia::mediaSizeSuitableForDevice</a></div><div class="ttdeci">BOOL mediaSizeSuitableForDevice</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:325</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a17eb20a065d628caf152e8e4c83bc4cba802f388c99aa52b9bd26a11f55ffd4f0"><div class="ttname"><a href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba802f388c99aa52b9bd26a11f55ffd4f0">VLCMedia::VLCMediaFetchLocal</a></div><div class="ttdeci">@ VLCMediaFetchLocal</div><div class="ttdoc">Fetch meta and covert art using local resources.</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:516</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a7b098bacc67ab0ff8fa9d316bef987d6"><div class="ttname"><a href="interface_v_l_c_media.html#a7b098bacc67ab0ff8fa9d316bef987d6">VLCMedia::tracksInformation</a></div><div class="ttdeci">NSArray * tracksInformation</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:491</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a148f7e15ef691ed0c6cf631eb3bc34d8"><div class="ttname"><a href="interface_v_l_c_media.html#a148f7e15ef691ed0c6cf631eb3bc34d8">VLCMedia::numberOfDiscontinuties</a></div><div class="ttdeci">NSInteger numberOfDiscontinuties</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:688</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html"><div class="ttname"><a href="interface_v_l_c_media.html">VLCMedia</a></div><div class="ttdef"><b>Definition:</b> VLCMedia.h:113</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_af247fea93ce48e219ddef15bdaf256de"><div class="ttname"><a href="interface_v_l_c_media.html#af247fea93ce48e219ddef15bdaf256de">VLCMedia::state</a></div><div class="ttdeci">VLCMediaState state</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:320</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a635234c93bcb43393868435ab98ad0a8"><div class="ttname"><a href="interface_v_l_c_media.html#a635234c93bcb43393868435ab98ad0a8">VLCMedia::VLCMediaTracksInformationFrameRateDenominator</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationFrameRateDenominator</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:432</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a17eb20a065d628caf152e8e4c83bc4cba0184ba9260e22d0182cf418b2d73c20c"><div class="ttname"><a href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba0184ba9260e22d0182cf418b2d73c20c">VLCMedia::VLCMediaDoInteract</a></div><div class="ttdeci">@ VLCMediaDoInteract</div><div class="ttdoc">Interact with the user when preparsing this item (and not its sub items). Set this flag in order to r...</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:518</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a4ad7dacc361919932777b2bf5a141023"><div class="ttname"><a href="interface_v_l_c_media.html#a4ad7dacc361919932777b2bf5a141023">-[VLCMedia NS_ENUM]</a></div><div class="ttdeci">typedef NS_ENUM(unsigned, VLCMediaParsedStatus)</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:267</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a08f3d51d9b8199fd20143d178b368b2f"><div class="ttname"><a href="interface_v_l_c_media.html#a08f3d51d9b8199fd20143d178b368b2f">VLCMedia::subitems</a></div><div class="ttdeci">VLCMediaList * subitems</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:288</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a076812e00bd51440c4d47da823011f86"><div class="ttname"><a href="interface_v_l_c_media.html#a076812e00bd51440c4d47da823011f86">VLCMedia::VLCMediaTracksInformationCodecProfile</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationCodecProfile</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:356</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a9c2d85f9c2dba700d7b2ca18cf12049a"><div class="ttname"><a href="interface_v_l_c_media.html#a9c2d85f9c2dba700d7b2ca18cf12049a">VLCMedia::stats</a></div><div class="ttdeci">NSDictionary * stats</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:603</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a2b91c4e456c6ce07682477d41772adc2"><div class="ttname"><a href="interface_v_l_c_media.html#a2b91c4e456c6ce07682477d41772adc2">VLCMedia::VLCMediaTracksInformationTypeUnknown</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationTypeUnknown</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:455</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_aabf94e7de92ae328dba46d6c53e5d869"><div class="ttname"><a href="interface_v_l_c_media.html#aabf94e7de92ae328dba46d6c53e5d869">VLCMedia::VLCMediaTracksInformationAudioChannelsNumber</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationAudioChannelsNumber</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:383</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_ac28eab6679f8761ce13ea02d61562d21"><div class="ttname"><a href="interface_v_l_c_media.html#ac28eab6679f8761ce13ea02d61562d21">VLCMedia::VLCMediaTracksInformationVideoProjection</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationVideoProjection</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:410</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a52414aa5aff9e0e929d6b3dad0461dd2"><div class="ttname"><a href="interface_v_l_c_media.html#a52414aa5aff9e0e929d6b3dad0461dd2">VLCMedia::VLCMediaTracksInformationVideoHeight</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationVideoHeight</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:394</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_ab7456ceac9f4ac4b395bcc50064d58dd"><div class="ttname"><a href="interface_v_l_c_media.html#ab7456ceac9f4ac4b395bcc50064d58dd">VLCMedia::numberOfLostPictures</a></div><div class="ttdeci">NSInteger numberOfLostPictures</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:649</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_ada9d9ba5acf71414913ecc83cb975bf6"><div class="ttname"><a href="interface_v_l_c_media.html#ada9d9ba5acf71414913ecc83cb975bf6">VLCMedia::VLCMediaTracksInformationSourceAspectRatio</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationSourceAspectRatio</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:416</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a13d927d07a8bc2cebab7363317c0a932"><div class="ttname"><a href="interface_v_l_c_media.html#a13d927d07a8bc2cebab7363317c0a932">VLCMedia::numberOfLostAudioBuffers</a></div><div class="ttdeci">NSInteger numberOfLostAudioBuffers</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:660</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a0213a3ea482353bce0d7bb59355d497a"><div class="ttname"><a href="interface_v_l_c_media.html#a0213a3ea482353bce0d7bb59355d497a">-[VLCMedia parseStop]</a></div><div class="ttdeci">void parseStop()</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a2eb646a3d37eaec7de62ba174b9682f7"><div class="ttname"><a href="interface_v_l_c_media.html#a2eb646a3d37eaec7de62ba174b9682f7">VLCMedia::inputBitrate</a></div><div class="ttdeci">float inputBitrate</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:616</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_aa3b1ead249368c4b73a544f07c84bcdc"><div class="ttname"><a href="interface_v_l_c_media.html#aa3b1ead249368c4b73a544f07c84bcdc">VLCMedia::VLCMediaTracksInformationSourceAspectRatioDenominator</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationSourceAspectRatioDenominator</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:421</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a3ae7faaa9307383b5651794c141b3e5e"><div class="ttname"><a href="interface_v_l_c_media.html#a3ae7faaa9307383b5651794c141b3e5e">-[VLCMedia NS_ENUM]</a></div><div class="ttdeci">typedef NS_ENUM(NSUInteger, VLCMediaProjection)</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:200</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a17eb20a065d628caf152e8e4c83bc4cba96d692455552df3b6332ce02b5c955a1"><div class="ttname"><a href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cba96d692455552df3b6332ce02b5c955a1">VLCMedia::VLCMediaParseLocal</a></div><div class="ttdeci">@ VLCMediaParseLocal</div><div class="ttdoc">Parse media if it's a local file.</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:514</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a289dc0ff117c7013b6b5363d9f35fd01"><div class="ttname"><a href="interface_v_l_c_media.html#a289dc0ff117c7013b6b5363d9f35fd01">VLCMedia::numberOfCorruptedDataPackets</a></div><div class="ttdeci">NSInteger numberOfCorruptedDataPackets</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:682</div></div>
<div class="ttc" id="aprotocol_v_l_c_media_delegate-p_html"><div class="ttname"><a href="protocol_v_l_c_media_delegate-p.html">VLCMediaDelegate-p</a></div><div class="ttdef"><b>Definition:</b> VLCMedia.h:86</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a29ca0a5036cfa556f5d7098c44030123"><div class="ttname"><a href="interface_v_l_c_media.html#a29ca0a5036cfa556f5d7098c44030123">VLCMedia::VLCMediaTracksInformationVideoWidth</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationVideoWidth</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:399</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a51bdb09726b9f4d72072e144ea7314cc"><div class="ttname"><a href="interface_v_l_c_media.html#a51bdb09726b9f4d72072e144ea7314cc">-[VLCMedia clearStoredCookies]</a></div><div class="ttdeci">void clearStoredCookies()</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_aef3995dbdd704cc5c8ed4fc2e383e0a6"><div class="ttname"><a href="interface_v_l_c_media.html#aef3995dbdd704cc5c8ed4fc2e383e0a6">VLCMedia::url</a></div><div class="ttdeci">NSURL * url</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:283</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a936f14e9dbdb6355604040bb963cf1b2"><div class="ttname"><a href="interface_v_l_c_media.html#a936f14e9dbdb6355604040bb963cf1b2">VLCMedia::numberOfReadBytesOnDemux</a></div><div class="ttdeci">NSInteger numberOfReadBytesOnDemux</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:622</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a0d65e705f516777543cb6ac2df310779"><div class="ttname"><a href="interface_v_l_c_media.html#a0d65e705f516777543cb6ac2df310779">VLCMedia::numberOfDecodedVideoBlocks</a></div><div class="ttdeci">NSInteger numberOfDecodedVideoBlocks</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:633</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_afd47b541ffd9e93a5864ced1f127101d"><div class="ttname"><a href="interface_v_l_c_media.html#afd47b541ffd9e93a5864ced1f127101d">VLCMedia::length</a></div><div class="ttdeci">VLCTime * length</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:246</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_adc94b1c776ed671be57746c79e04f187"><div class="ttname"><a href="interface_v_l_c_media.html#adc94b1c776ed671be57746c79e04f187">VLCMedia::parsedStatus</a></div><div class="ttdeci">VLCMediaParsedStatus parsedStatus</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:274</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_ad173cd33fb9d51175e676b62838cd980"><div class="ttname"><a href="interface_v_l_c_media.html#ad173cd33fb9d51175e676b62838cd980">VLCMedia::VLCMediaTracksInformationTextEncoding</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationTextEncoding</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:438</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a65060dbc9eefe3518c4aa81daba05320"><div class="ttname"><a href="interface_v_l_c_media.html#a65060dbc9eefe3518c4aa81daba05320">VLCMedia::numberOfSentPackets</a></div><div class="ttdeci">NSInteger numberOfSentPackets</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:666</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a28c23c5d427727732476f86c6d0645ee"><div class="ttname"><a href="interface_v_l_c_media.html#a28c23c5d427727732476f86c6d0645ee">-[VLCMedia __attribute__]</a></div><div class="ttdeci">(deprecated __attribute__()</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a8deaf0be0fb1bae484ce026866ff902b"><div class="ttname"><a href="interface_v_l_c_media.html#a8deaf0be0fb1bae484ce026866ff902b">VLCMedia::VLCMediaParsingOptions</a></div><div class="ttdeci">int VLCMediaParsingOptions</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:524</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_ac61f729efe7481e86d26e7e92fff0dd2"><div class="ttname"><a href="interface_v_l_c_media.html#ac61f729efe7481e86d26e7e92fff0dd2">VLCMedia::metaDictionary</a></div><div class="ttdeci">NSDictionary * metaDictionary</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:315</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a17eb20a065d628caf152e8e4c83bc4cbaf9a01d0578ab528242b3de74d5e216dc"><div class="ttname"><a href="interface_v_l_c_media.html#a17eb20a065d628caf152e8e4c83bc4cbaf9a01d0578ab528242b3de74d5e216dc">VLCMedia::VLCMediaFetchNetwork</a></div><div class="ttdeci">@ VLCMediaFetchNetwork</div><div class="ttdoc">Fetch meta and covert art using network resources.</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:517</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_ae380aafa86ebd25ad38ab630a6dc86dd"><div class="ttname"><a href="interface_v_l_c_media.html#ae380aafa86ebd25ad38ab630a6dc86dd">VLCMedia::VLCMediaTracksInformationCodecLevel</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationCodecLevel</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:361</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a2ac631a7a8c7416ac9a13b914efeb22e"><div class="ttname"><a href="interface_v_l_c_media.html#a2ac631a7a8c7416ac9a13b914efeb22e">VLCMedia::VLCMediaTracksInformationTypeVideo</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationTypeVideo</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:447</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a3d8f7f156478e43c45dfedf7459c9939"><div class="ttname"><a href="interface_v_l_c_media.html#a3d8f7f156478e43c45dfedf7459c9939">VLCMedia::VLCMediaTracksInformationTypeText</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationTypeText</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:451</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a2332173d72093469abebf56f4c70ae80"><div class="ttname"><a href="interface_v_l_c_media.html#a2332173d72093469abebf56f4c70ae80">VLCMedia::saveMetadata</a></div><div class="ttdeci">BOOL saveMetadata</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:310</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_add3bac6827f60b1cbe44544c106b39c0"><div class="ttname"><a href="interface_v_l_c_media.html#add3bac6827f60b1cbe44544c106b39c0">VLCMedia::VLCMediaTracksInformationBitrate</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationBitrate</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:367</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_afd5e8623f3246506f21576ca006df47e"><div class="ttname"><a href="interface_v_l_c_media.html#afd5e8623f3246506f21576ca006df47e">VLCMedia::VLCMediaTracksInformationFrameRate</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationFrameRate</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:427</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_ac5ccaa4e433a8bc847e54739d69827b7"><div class="ttname"><a href="interface_v_l_c_media.html#ac5ccaa4e433a8bc847e54739d69827b7">VLCMedia::VLCMediaTracksInformationType</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationType</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:350</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a8f6e78f8cc5d52384047ddaea9e01dcf"><div class="ttname"><a href="interface_v_l_c_media.html#a8f6e78f8cc5d52384047ddaea9e01dcf">VLCMedia::mediaType</a></div><div class="ttdeci">VLCMediaType mediaType</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:216</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a5f42247ad4cefc2cfa4a96bd95f53356"><div class="ttname"><a href="interface_v_l_c_media.html#a5f42247ad4cefc2cfa4a96bd95f53356">VLCMedia::VLCMediaTracksInformationAudioRate</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationAudioRate</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:388</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_ac32a90c64851638af38108040b37e454"><div class="ttname"><a href="interface_v_l_c_media.html#ac32a90c64851638af38108040b37e454">-[VLCMedia __attribute__]</a></div><div class="ttdeci">BOOL isParsed __attribute__((deprecated))</div></div>
<div class="ttc" id="ainterface_v_l_c_media_html_a523d5f9351c2fcac0d9b600773734c81"><div class="ttname"><a href="interface_v_l_c_media.html#a523d5f9351c2fcac0d9b600773734c81">VLCMedia::VLCMediaTracksInformationId</a></div><div class="ttdeci">NSString *const VLCMediaTracksInformationId</div><div class="ttdef"><b>Definition:</b> VLCMedia.h:341</div></div>
<div class="ttc" id="ainterface_v_l_c_time_html"><div class="ttname"><a href="interface_v_l_c_time.html">VLCTime</a></div><div class="ttdef"><b>Definition:</b> VLCTime.h:31</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
