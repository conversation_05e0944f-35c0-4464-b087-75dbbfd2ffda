<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Class Members</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="contents">
<div class="textblock">Here is a list of all documented class members with links to the class documentation for each member:</div>

<h3><a id="index_t"></a>- t -</h3><ul>
<li>thumbnail
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#a2cc0a6c7836be7d967858c67e51a5f50">VLCMediaThumbnailer</a>
</li>
<li>thumbnailerWithMedia:andDelegate:
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#a364d20f11c696b300c42fd5eb404c3f9">VLCMediaThumbnailer</a>
</li>
<li>thumbnailerWithMedia:delegate:andVLCLibrary:
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#a8b4b7e2a8aa3b3c51146916ef33dc043">VLCMediaThumbnailer</a>
</li>
<li>thumbnailHeight
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#ad183c5b8990465157f049c61c2e188a0">VLCMediaThumbnailer</a>
</li>
<li>thumbnailWidth
: <a class="el" href="interface_v_l_c_media_thumbnailer.html#a8c71e4fc706ae741252eecb95d33a055">VLCMediaThumbnailer</a>
</li>
<li>time
: <a class="el" href="interface_v_l_c_media_player.html#a5915273012b273885dd9570d56777ccf">VLCMediaPlayer</a>
</li>
<li>timeWithInt:
: <a class="el" href="interface_v_l_c_time.html#a936e42eed02e03b404f14bdbce13d06f">VLCTime</a>
</li>
<li>timeWithNumber:
: <a class="el" href="interface_v_l_c_time.html#af9b7c469a7cb064a3d9c2c5bcaa769bb">VLCTime</a>
</li>
<li>titleDescriptions
: <a class="el" href="interface_v_l_c_media_player.html#a937a50fb274ec99b146d999fd8c02a1b">VLCMediaPlayer</a>
</li>
<li>tracksInformation
: <a class="el" href="interface_v_l_c_media.html#a7b098bacc67ab0ff8fa9d316bef987d6">VLCMedia</a>
</li>
<li>transcode:finishedSucessfully:
: <a class="el" href="protocol_v_l_c_transcoder_delegate-p.html#ae3f6bd7dfd97c113a2d2cb2cbb52571f">&lt;VLCTranscoderDelegate&gt;</a>
</li>
<li>type
: <a class="el" href="interface_v_l_c_renderer_item.html#aaac905fef90a323b9a56d116aba7c840">VLCRendererItem</a>
</li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
