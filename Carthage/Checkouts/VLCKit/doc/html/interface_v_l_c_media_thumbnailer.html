<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: VLCMediaThumbnailer Class Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="#pub-static-methods">Class Methods</a> &#124;
<a href="#properties">Properties</a> &#124;
<a href="interface_v_l_c_media_thumbnailer-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">VLCMediaThumbnailer Class Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_media_thumbnailer_8h_source.html">VLCMediaThumbnailer.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for VLCMediaThumbnailer:</div>
<div class="dyncontent">
 <div class="center">
  <img src="interface_v_l_c_media_thumbnailer.png" alt=""/>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:a8c0bce3c2eef22eae4d8b046ce37761b"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="interface_v_l_c_media_thumbnailer.html#a8c0bce3c2eef22eae4d8b046ce37761b">fetchThumbnail</a></td></tr>
<tr class="separator:a8c0bce3c2eef22eae4d8b046ce37761b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Class Methods</h2></td></tr>
<tr class="memitem:a364d20f11c696b300c42fd5eb404c3f9"><td class="memItemLeft" align="right" valign="top">(<a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a> *)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_media_thumbnailer.html#a364d20f11c696b300c42fd5eb404c3f9">thumbnailerWithMedia:andDelegate:</a></td></tr>
<tr class="separator:a364d20f11c696b300c42fd5eb404c3f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8b4b7e2a8aa3b3c51146916ef33dc043"><td class="memItemLeft" align="right" valign="top">(<a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a> *)&#160;</td><td class="memItemRight" valign="bottom">+ <a class="el" href="interface_v_l_c_media_thumbnailer.html#a8b4b7e2a8aa3b3c51146916ef33dc043">thumbnailerWithMedia:delegate:andVLCLibrary:</a></td></tr>
<tr class="separator:a8b4b7e2a8aa3b3c51146916ef33dc043"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="properties"></a>
Properties</h2></td></tr>
<tr class="memitem:a48ad5cbc29377c223ed35eb92097d4ac"><td class="memItemLeft" align="right" valign="top">id&lt; <a class="el" href="protocol_v_l_c_media_thumbnailer_delegate-p.html">VLCMediaThumbnailerDelegate</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_thumbnailer.html#a48ad5cbc29377c223ed35eb92097d4ac">delegate</a></td></tr>
<tr class="separator:a48ad5cbc29377c223ed35eb92097d4ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aecc4d4aeea24bd960190a0d1a47a5a56"><td class="memItemLeft" align="right" valign="top"><a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_thumbnailer.html#aecc4d4aeea24bd960190a0d1a47a5a56">media</a></td></tr>
<tr class="separator:aecc4d4aeea24bd960190a0d1a47a5a56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2cc0a6c7836be7d967858c67e51a5f50"><td class="memItemLeft" align="right" valign="top">CGImageRef&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_thumbnailer.html#a2cc0a6c7836be7d967858c67e51a5f50">thumbnail</a></td></tr>
<tr class="separator:a2cc0a6c7836be7d967858c67e51a5f50"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a14051a6f88fd60fb723c6de3aa5a7321"><td class="memItemLeft" align="right" valign="top">void *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_thumbnailer.html#a14051a6f88fd60fb723c6de3aa5a7321">libVLCinstance</a></td></tr>
<tr class="separator:a14051a6f88fd60fb723c6de3aa5a7321"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad183c5b8990465157f049c61c2e188a0"><td class="memItemLeft" align="right" valign="top">CGFloat&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_thumbnailer.html#ad183c5b8990465157f049c61c2e188a0">thumbnailHeight</a></td></tr>
<tr class="separator:ad183c5b8990465157f049c61c2e188a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c71e4fc706ae741252eecb95d33a055"><td class="memItemLeft" align="right" valign="top">CGFloat&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_thumbnailer.html#a8c71e4fc706ae741252eecb95d33a055">thumbnailWidth</a></td></tr>
<tr class="separator:a8c71e4fc706ae741252eecb95d33a055"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab6c36054a654a28cc678082d0a1e02fa"><td class="memItemLeft" align="right" valign="top">float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="interface_v_l_c_media_thumbnailer.html#ab6c36054a654a28cc678082d0a1e02fa">snapshotPosition</a></td></tr>
<tr class="separator:ab6c36054a654a28cc678082d0a1e02fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>a facility allowing you to do thumbnails in an efficient manner </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="a8c0bce3c2eef22eae4d8b046ce37761b"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8c0bce3c2eef22eae4d8b046ce37761b">&#9670;&nbsp;</a></span>fetchThumbnail</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void) fetchThumbnail </td>
          <td></td>
          <td class="paramname"></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Starts the thumbnailing process </p>

</div>
</div>
<a id="a364d20f11c696b300c42fd5eb404c3f9"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a364d20f11c696b300c42fd5eb404c3f9">&#9670;&nbsp;</a></span>thumbnailerWithMedia:andDelegate:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (<a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a> *) thumbnailerWithMedia: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *)&#160;</td>
          <td class="paramname"><em>media</em></td>
        </tr>
        <tr>
          <td class="paramkey">andDelegate:</td>
          <td></td>
          <td class="paramtype">(id&lt; <a class="el" href="protocol_v_l_c_media_thumbnailer_delegate-p.html">VLCMediaThumbnailerDelegate</a> &gt;)&#160;</td>
          <td class="paramname"><em>delegate</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>initializer </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">media</td><td>the media item to thumbnail </td></tr>
    <tr><td class="paramname">delegate</td><td>the delegate implementing the required protocol </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the thumbnailer instance </dd></dl>
<dl class="section note"><dt>Note</dt><dd>This will use the default shared library instance </dd></dl>

</div>
</div>
<a id="a8b4b7e2a8aa3b3c51146916ef33dc043"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8b4b7e2a8aa3b3c51146916ef33dc043">&#9670;&nbsp;</a></span>thumbnailerWithMedia:delegate:andVLCLibrary:</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">+ (<a class="el" href="interface_v_l_c_media_thumbnailer.html">VLCMediaThumbnailer</a> *) thumbnailerWithMedia: </td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_media.html">VLCMedia</a> *)&#160;</td>
          <td class="paramname"><em>media</em></td>
        </tr>
        <tr>
          <td class="paramkey">delegate:</td>
          <td></td>
          <td class="paramtype">(id&lt; <a class="el" href="protocol_v_l_c_media_thumbnailer_delegate-p.html">VLCMediaThumbnailerDelegate</a> &gt;)&#160;</td>
          <td class="paramname"><em>delegate</em></td>
        </tr>
        <tr>
          <td class="paramkey">andVLCLibrary:</td>
          <td></td>
          <td class="paramtype">(<a class="el" href="interface_v_l_c_library.html">VLCLibrary</a> *)&#160;</td>
          <td class="paramname"><em>library</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>initializer </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">media</td><td>the media item to thumbnail </td></tr>
    <tr><td class="paramname">delegate</td><td>the delegate implementing the required protocol </td></tr>
    <tr><td class="paramname">library</td><td>a library instance, potentially configured by you in a special way </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>the thumbnailer instance </dd></dl>

</div>
</div>
<h2 class="groupheader">Property Documentation</h2>
<a id="a48ad5cbc29377c223ed35eb92097d4ac"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a48ad5cbc29377c223ed35eb92097d4ac">&#9670;&nbsp;</a></span>delegate</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (id&lt;<a class="el" href="protocol_v_l_c_media_thumbnailer_delegate-p.html">VLCMediaThumbnailerDelegate</a>&gt;) delegate</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">weak</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>delegate object associated with the thumbnailer instance implementing the required protocol </p>

</div>
</div>
<a id="a14051a6f88fd60fb723c6de3aa5a7321"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a14051a6f88fd60fb723c6de3aa5a7321">&#9670;&nbsp;</a></span>libVLCinstance</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (void*) libVLCinstance</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">atomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>the libvlc instance used for thumbnailing </p><dl class="section note"><dt>Note</dt><dd>Whatever you do, using this instance is most likely wrong </dd></dl>

</div>
</div>
<a id="aecc4d4aeea24bd960190a0d1a47a5a56"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aecc4d4aeea24bd960190a0d1a47a5a56">&#9670;&nbsp;</a></span>media</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (<a class="el" href="interface_v_l_c_media.html">VLCMedia</a>*) media</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>the media object that is being thumbnailed </p>

</div>
</div>
<a id="ab6c36054a654a28cc678082d0a1e02fa"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab6c36054a654a28cc678082d0a1e02fa">&#9670;&nbsp;</a></span>snapshotPosition</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (float) snapshotPosition</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Snapshot Position You shouldn't change this after -fetchThumbnail has been called. </p><dl class="section return"><dt>Returns</dt><dd>snapshot position. Default value 0.3 </dd></dl>

</div>
</div>
<a id="a2cc0a6c7836be7d967858c67e51a5f50"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a2cc0a6c7836be7d967858c67e51a5f50">&#9670;&nbsp;</a></span>thumbnail</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (CGImageRef) thumbnail</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>The thumbnail created for the media object </p>

</div>
</div>
<a id="ad183c5b8990465157f049c61c2e188a0"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ad183c5b8990465157f049c61c2e188a0">&#9670;&nbsp;</a></span>thumbnailHeight</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (CGFloat) thumbnailHeight</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Thumbnail Height You shouldn't change this after -fetchThumbnail has been called. </p><dl class="section return"><dt>Returns</dt><dd>thumbnail height. Default value 240. </dd></dl>

</div>
</div>
<a id="a8c71e4fc706ae741252eecb95d33a055"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a8c71e4fc706ae741252eecb95d33a055">&#9670;&nbsp;</a></span>thumbnailWidth</h2>

<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">- (CGFloat) thumbnailWidth</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">read</span><span class="mlabel">write</span><span class="mlabel">nonatomic</span><span class="mlabel">assign</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Thumbnail Width You shouldn't change this after -fetchThumbnail has been called. </p><dl class="section return"><dt>Returns</dt><dd>thumbnail height. Default value 320 </dd></dl>

</div>
</div>
<hr/>The documentation for this class was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_media_thumbnailer_8h_source.html">VLCMediaThumbnailer.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
