<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.19"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: Headers/Public/VLCLibrary.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.19 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_692516b589a32b3bfce781ca9f6d1534.html">Headers</a></li><li class="navelem"><a class="el" href="dir_28c64fbfc9a6ab228413a335a5a957f9.html">Public</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">VLCLibrary.h</div>  </div>
</div><!--header-->
<div class="contents">
<div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/*****************************************************************************</span></div>
<div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * VLCLibrary.h: VLCKit.framework VLCLibrary header</span></div>
<div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *****************************************************************************</span></div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Copyright (C) 2007 Pierre d&#39;Herbemont</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * Copyright (C) 2007-2019 VLC authors and VideoLAN</span></div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> * $Id$</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> * Authors: <AUTHORS>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *          Felix Paul Kühne &lt;fkuehne # videolan.org&gt;</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> * This program is free software; you can redistribute it and/or modify it</span></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> * under the terms of the GNU Lesser General Public License as published by</span></div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> * the Free Software Foundation; either version 2.1 of the License, or</span></div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> * (at your option) any later version.</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * This program is distributed in the hope that it will be useful,</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * but WITHOUT ANY WARRANTY; without even the implied warranty of</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * GNU Lesser General Public License for more details.</span></div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> *</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * You should have received a copy of the GNU Lesser General Public License</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * along with this program; if not, write to the Free Software Foundation,</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> *****************************************************************************/</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160; </div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#import &lt;Foundation/Foundation.h&gt;</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="preprocessor">#import &quot;VLCAudio.h&quot;</span></div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#import &quot;VLCMediaList.h&quot;</span></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#import &quot;VLCMedia.h&quot;</span></div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160; </div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="keyword">@class</span> <a class="code" href="interface_v_l_c_audio.html">VLCAudio</a>;</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160; </div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;NS_ASSUME_NONNULL_BEGIN</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160; </div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="keyword">@protocol </span><a class="code" href="protocol_v_l_c_library_log_receiver_protocol-p.html">VLCLibraryLogReceiverProtocol</a>;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160; </div>
<div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="interface_v_l_c_library.html">   46</a></span>&#160;<span class="keyword">@interface </span><a class="code" href="interface_v_l_c_library.html">VLCLibrary</a> : NSObject</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160; </div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;+ (<a class="code" href="interface_v_l_c_library.html">VLCLibrary</a> *)<a class="code" href="interface_v_l_c_library.html#a7eddfd69ce66ffc5603bc68a5777187e">sharedLibrary</a>;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160; </div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160; - (instancetype)initWithOptions:(NSArray *)options;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160; </div>
<div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="interface_v_l_c_library.html#a30523c86de4b6a2fbc86f1d6a8274b59">   65</a></span>&#160;<span class="keyword">@property</span> (readwrite, nonatomic) BOOL <a class="code" href="interface_v_l_c_library.html#a30523c86de4b6a2fbc86f1d6a8274b59">debugLogging</a>;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160; </div>
<div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="interface_v_l_c_library.html#a86c8d6b68e6852dea7045a40a9110dde">   77</a></span>&#160;<span class="keyword">@property</span> (readwrite, nonatomic) <span class="keywordtype">int</span> <a class="code" href="interface_v_l_c_library.html#a0b0d7e806f3c1c49243873f99c3608fa">debugLoggingLevel</a>;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160; </div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;- (BOOL)setDebugLoggingToFile:(NSString *)filePath;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160; </div>
<div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="interface_v_l_c_library.html#a7194d31353ddeef7273dd9d9c394841a">   94</a></span>&#160;<span class="keyword">@property</span> (readwrite, nonatomic) id&lt;VLCLibraryLogReceiverProtocol&gt; <a class="code" href="interface_v_l_c_library.html#a7194d31353ddeef7273dd9d9c394841a">debugLoggingTarget</a>;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160; </div>
<div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="interface_v_l_c_library.html#a99403afc6373f5f0ca5b89b6c64d3f32">  100</a></span>&#160;<span class="keyword">@property</span> (readonly, copy) NSString *<a class="code" href="interface_v_l_c_library.html#a99403afc6373f5f0ca5b89b6c64d3f32">version</a>;</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160; </div>
<div class="line"><a name="l00106"></a><span class="lineno"><a class="line" href="interface_v_l_c_library.html#a91485a448eb7b18c7b5c36bba4c56184">  106</a></span>&#160;<span class="keyword">@property</span> (readonly, copy) NSString *<a class="code" href="interface_v_l_c_library.html#a91485a448eb7b18c7b5c36bba4c56184">compiler</a>;</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160; </div>
<div class="line"><a name="l00112"></a><span class="lineno"><a class="line" href="interface_v_l_c_library.html#ae91040ee78413a160918871e1e9475ff">  112</a></span>&#160;<span class="keyword">@property</span> (readonly, copy) NSString *<a class="code" href="interface_v_l_c_library.html#a9201c5f4b1ee745e31e3875835914124">changeset</a>;</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160; </div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;- (void)setHumanReadableName:(NSString *)readableName withHTTPUserAgent:(NSString *)userAgent;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160; </div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;- (void)setApplicationIdentifier:(NSString *)identifier withVersion:(NSString *)version andApplicationIconName:(NSString *)icon;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160; </div>
<div class="line"><a name="l00134"></a><span class="lineno"><a class="line" href="interface_v_l_c_library.html#abb375a92ef38f3cacab3d1d501b747d3">  134</a></span>&#160;<span class="keyword">@property</span> (nonatomic, assign) <span class="keywordtype">void</span> *<a class="code" href="interface_v_l_c_library.html#abb375a92ef38f3cacab3d1d501b747d3">instance</a>;</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160; </div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160; </div>
<div class="line"><a name="l00138"></a><span class="lineno"><a class="line" href="protocol_v_l_c_library_log_receiver_protocol-p.html">  138</a></span>&#160;<span class="keyword">@protocol </span><a class="code" href="protocol_v_l_c_library_log_receiver_protocol-p.html">VLCLibraryLogReceiverProtocol</a> &lt;NSObject&gt;</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;<span class="keyword">@required</span></div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;- (void)handleMessage:(NSString *)message</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;           debugLevel:(<span class="keywordtype">int</span>)level;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;<span class="keyword">@end</span></div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160; </div>
<div class="line"><a name="l00149"></a><span class="lineno"><a class="line" href="interface_v_l_c_library.html#a7eddfd69ce66ffc5603bc68a5777187e">  149</a></span>&#160;NS_ASSUME_NONNULL_END</div>
</div><!-- fragment --></div><!-- contents -->
<div class="ttc" id="ainterface_v_l_c_library_html_abb375a92ef38f3cacab3d1d501b747d3"><div class="ttname"><a href="interface_v_l_c_library.html#abb375a92ef38f3cacab3d1d501b747d3">VLCLibrary::instance</a></div><div class="ttdeci">void * instance</div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:134</div></div>
<div class="ttc" id="ainterface_v_l_c_library_html_a7eddfd69ce66ffc5603bc68a5777187e"><div class="ttname"><a href="interface_v_l_c_library.html#a7eddfd69ce66ffc5603bc68a5777187e">+[VLCLibrary sharedLibrary]</a></div><div class="ttdeci">VLCLibrary * sharedLibrary()</div></div>
<div class="ttc" id="aprotocol_v_l_c_library_log_receiver_protocol-p_html"><div class="ttname"><a href="protocol_v_l_c_library_log_receiver_protocol-p.html">VLCLibraryLogReceiverProtocol-p</a></div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:138</div></div>
<div class="ttc" id="ainterface_v_l_c_library_html_a99403afc6373f5f0ca5b89b6c64d3f32"><div class="ttname"><a href="interface_v_l_c_library.html#a99403afc6373f5f0ca5b89b6c64d3f32">VLCLibrary::version</a></div><div class="ttdeci">NSString * version</div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:100</div></div>
<div class="ttc" id="ainterface_v_l_c_library_html_a30523c86de4b6a2fbc86f1d6a8274b59"><div class="ttname"><a href="interface_v_l_c_library.html#a30523c86de4b6a2fbc86f1d6a8274b59">VLCLibrary::debugLogging</a></div><div class="ttdeci">BOOL debugLogging</div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:65</div></div>
<div class="ttc" id="ainterface_v_l_c_library_html_a9201c5f4b1ee745e31e3875835914124"><div class="ttname"><a href="interface_v_l_c_library.html#a9201c5f4b1ee745e31e3875835914124">VLCLibrary::changeset</a></div><div class="ttdeci">NSString * changeset</div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:112</div></div>
<div class="ttc" id="ainterface_v_l_c_audio_html"><div class="ttname"><a href="interface_v_l_c_audio.html">VLCAudio</a></div><div class="ttdef"><b>Definition:</b> VLCAudio.h:37</div></div>
<div class="ttc" id="ainterface_v_l_c_library_html"><div class="ttname"><a href="interface_v_l_c_library.html">VLCLibrary</a></div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:47</div></div>
<div class="ttc" id="ainterface_v_l_c_library_html_a91485a448eb7b18c7b5c36bba4c56184"><div class="ttname"><a href="interface_v_l_c_library.html#a91485a448eb7b18c7b5c36bba4c56184">VLCLibrary::compiler</a></div><div class="ttdeci">NSString * compiler</div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:106</div></div>
<div class="ttc" id="ainterface_v_l_c_library_html_a0b0d7e806f3c1c49243873f99c3608fa"><div class="ttname"><a href="interface_v_l_c_library.html#a0b0d7e806f3c1c49243873f99c3608fa">VLCLibrary::debugLoggingLevel</a></div><div class="ttdeci">int debugLoggingLevel</div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:77</div></div>
<div class="ttc" id="ainterface_v_l_c_library_html_a7194d31353ddeef7273dd9d9c394841a"><div class="ttname"><a href="interface_v_l_c_library.html#a7194d31353ddeef7273dd9d9c394841a">VLCLibrary::debugLoggingTarget</a></div><div class="ttdeci">id&lt; VLCLibraryLogReceiverProtocol &gt; debugLoggingTarget</div><div class="ttdef"><b>Definition:</b> VLCLibrary.h:94</div></div>
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by&#160;<a href="http://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.8.19
</small></address>
</body>
</html>
