<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.14"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>VLCKit: &lt;VLCCustomDialogRendererProtocol &gt; Protocol Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">VLCKit
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.14 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
var searchBox = new SearchBox("searchBox", "search",false,'Search');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:cf05388f2679ee054f2beb29a391d25f4e673ac3&amp;dn=gpl-2.0.txt GPL-v2 */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */</script>
<div id="main-nav"></div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Instance Methods</a> &#124;
<a href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">&lt;VLCCustomDialogRendererProtocol &gt; Protocol Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#import &lt;<a class="el" href="_v_l_c_dialog_provider_8h_source.html">VLCDialogProvider.h</a>&gt;</code></p>
<div class="dynheader">
Inheritance diagram for &lt;VLCCustomDialogRendererProtocol &gt;:</div>
<div class="dyncontent">
 <div class="center">
  <img src="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.png" usemap="#_3CVLCCustomDialogRendererProtocol_20_3E_map" alt=""/>
  <map id="_3CVLCCustomDialogRendererProtocol_20_3E_map" name="_3CVLCCustomDialogRendererProtocol_20_3E_map">
</map>
 </div></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Instance Methods</h2></td></tr>
<tr class="memitem:ab4308c597c3b339bb50085e3c5750075"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html#ab4308c597c3b339bb50085e3c5750075">showErrorWithTitle:message:</a></td></tr>
<tr class="separator:ab4308c597c3b339bb50085e3c5750075"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af369ee55629e2172323081fb4e85dba4"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html#af369ee55629e2172323081fb4e85dba4">showLoginWithTitle:message:defaultUsername:askingForStorage:withReference:</a></td></tr>
<tr class="separator:af369ee55629e2172323081fb4e85dba4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf89ec0239b9d1533e007c7f4af38dfc"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html#aaf89ec0239b9d1533e007c7f4af38dfc">showQuestionWithTitle:message:type:cancelString:action1String:action2String:withReference:</a></td></tr>
<tr class="separator:aaf89ec0239b9d1533e007c7f4af38dfc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a008624d653baaff6b763bc40852ce065"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html#a008624d653baaff6b763bc40852ce065">showProgressWithTitle:message:isIndeterminate:position:cancelString:withReference:</a></td></tr>
<tr class="separator:a008624d653baaff6b763bc40852ce065"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7b5d3cd75a42166e5c5eb3e9e08229b2"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html#a7b5d3cd75a42166e5c5eb3e9e08229b2">updateProgressWithReference:message:postion:</a></td></tr>
<tr class="separator:a7b5d3cd75a42166e5c5eb3e9e08229b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a288ed9f961ae4bb468db61e35b631da2"><td class="memItemLeft" align="right" valign="top">(void)&#160;</td><td class="memItemRight" valign="bottom">- <a class="el" href="protocol_v_l_c_custom_dialog_renderer_protocol_01-p.html#a288ed9f961ae4bb468db61e35b631da2">cancelDialogWithReference:</a></td></tr>
<tr class="separator:a288ed9f961ae4bb468db61e35b631da2"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p>the protocol to use if you decide to run a custom dialog appearance </p>
</div><h2 class="groupheader">Method Documentation</h2>
<a id="a288ed9f961ae4bb468db61e35b631da2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a288ed9f961ae4bb468db61e35b631da2">&#9670;&nbsp;</a></span>cancelDialogWithReference:()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void VLCCustomDialogRendererProtocol) cancelDialogWithReference: </td>
          <td></td>
          <td class="paramtype">(NSValue *_Nonnull)&#160;</td>
          <td class="paramname"><em>reference</em></td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>VLC decided to destroy a dialog </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">reference</td><td>to the dialog to destroy </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="ab4308c597c3b339bb50085e3c5750075"></a>
<h2 class="memtitle"><span class="permalink"><a href="#ab4308c597c3b339bb50085e3c5750075">&#9670;&nbsp;</a></span>showErrorWithTitle:message:()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void VLCCustomDialogRendererProtocol) showErrorWithTitle: </td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>error</em></td>
        </tr>
        <tr>
          <td class="paramkey">message:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>message</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>called when VLC wants to show an error </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">error</td><td>the dialog title </td></tr>
    <tr><td class="paramname">message</td><td>the error message </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="af369ee55629e2172323081fb4e85dba4"></a>
<h2 class="memtitle"><span class="permalink"><a href="#af369ee55629e2172323081fb4e85dba4">&#9670;&nbsp;</a></span>showLoginWithTitle:message:defaultUsername:askingForStorage:withReference:()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void VLCCustomDialogRendererProtocol) showLoginWithTitle: </td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>title</em></td>
        </tr>
        <tr>
          <td class="paramkey">message:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>message</em></td>
        </tr>
        <tr>
          <td class="paramkey">defaultUsername:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nullable)&#160;</td>
          <td class="paramname"><em>username</em></td>
        </tr>
        <tr>
          <td class="paramkey">askingForStorage:</td>
          <td></td>
          <td class="paramtype">(BOOL)&#160;</td>
          <td class="paramname"><em>askingForStorage</em></td>
        </tr>
        <tr>
          <td class="paramkey">withReference:</td>
          <td></td>
          <td class="paramtype">(NSValue *_Nonnull)&#160;</td>
          <td class="paramname"><em>reference</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>called when user logs in to something If VLC includes a keychain module for your platform, a user can store stuff </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">title</td><td>login dialog title </td></tr>
    <tr><td class="paramname">message</td><td>an explaining message </td></tr>
    <tr><td class="paramname">username</td><td>a default username within context </td></tr>
    <tr><td class="paramname">askingForStorage</td><td>indicator whether storing is even a possibility </td></tr>
    <tr><td class="paramname">reference</td><td>you need to send the results to </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a008624d653baaff6b763bc40852ce065"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a008624d653baaff6b763bc40852ce065">&#9670;&nbsp;</a></span>showProgressWithTitle:message:isIndeterminate:position:cancelString:withReference:()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void VLCCustomDialogRendererProtocol) showProgressWithTitle: </td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>title</em></td>
        </tr>
        <tr>
          <td class="paramkey">message:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>message</em></td>
        </tr>
        <tr>
          <td class="paramkey">isIndeterminate:</td>
          <td></td>
          <td class="paramtype">(BOOL)&#160;</td>
          <td class="paramname"><em>isIndeterminate</em></td>
        </tr>
        <tr>
          <td class="paramkey">position:</td>
          <td></td>
          <td class="paramtype">(float)&#160;</td>
          <td class="paramname"><em>position</em></td>
        </tr>
        <tr>
          <td class="paramkey">cancelString:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nullable)&#160;</td>
          <td class="paramname"><em>cancelString</em></td>
        </tr>
        <tr>
          <td class="paramkey">withReference:</td>
          <td></td>
          <td class="paramtype">(NSValue *_Nonnull)&#160;</td>
          <td class="paramname"><em>reference</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>called when VLC wants to show some progress </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">title</td><td>the dialog title </td></tr>
    <tr><td class="paramname">message</td><td>an explaining message </td></tr>
    <tr><td class="paramname">isIndeterminate</td><td>indicator whether progress indeterminate </td></tr>
    <tr><td class="paramname">position</td><td>initial progress position </td></tr>
    <tr><td class="paramname">cancelString</td><td>optional string for cancel button if operation is cancellable </td></tr>
    <tr><td class="paramname">reference</td><td>VLC will include in updates </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="aaf89ec0239b9d1533e007c7f4af38dfc"></a>
<h2 class="memtitle"><span class="permalink"><a href="#aaf89ec0239b9d1533e007c7f4af38dfc">&#9670;&nbsp;</a></span>showQuestionWithTitle:message:type:cancelString:action1String:action2String:withReference:()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void VLCCustomDialogRendererProtocol) showQuestionWithTitle: </td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>title</em></td>
        </tr>
        <tr>
          <td class="paramkey">message:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nonnull)&#160;</td>
          <td class="paramname"><em>message</em></td>
        </tr>
        <tr>
          <td class="paramkey">type:</td>
          <td></td>
          <td class="paramtype">(VLCDialogQuestionType)&#160;</td>
          <td class="paramname"><em>questionType</em></td>
        </tr>
        <tr>
          <td class="paramkey">cancelString:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nullable)&#160;</td>
          <td class="paramname"><em>cancelString</em></td>
        </tr>
        <tr>
          <td class="paramkey">action1String:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nullable)&#160;</td>
          <td class="paramname"><em>action1String</em></td>
        </tr>
        <tr>
          <td class="paramkey">action2String:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nullable)&#160;</td>
          <td class="paramname"><em>action2String</em></td>
        </tr>
        <tr>
          <td class="paramkey">withReference:</td>
          <td></td>
          <td class="paramtype">(NSValue *_Nonnull)&#160;</td>
          <td class="paramname"><em>reference</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>called when VLC needs the user to decide something </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">title</td><td>the dialog title </td></tr>
    <tr><td class="paramname">message</td><td>an explaining message text </td></tr>
    <tr><td class="paramname">questionType</td><td>a question type </td></tr>
    <tr><td class="paramname">cancelString</td><td>cancel button text </td></tr>
    <tr><td class="paramname">action1String</td><td>action 1 text </td></tr>
    <tr><td class="paramname">action2String</td><td>action 2 text </td></tr>
    <tr><td class="paramname">reference</td><td>you need to send the action to </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a id="a7b5d3cd75a42166e5c5eb3e9e08229b2"></a>
<h2 class="memtitle"><span class="permalink"><a href="#a7b5d3cd75a42166e5c5eb3e9e08229b2">&#9670;&nbsp;</a></span>updateProgressWithReference:message:postion:()</h2>

<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">- (void VLCCustomDialogRendererProtocol) updateProgressWithReference: </td>
          <td></td>
          <td class="paramtype">(NSValue *_Nonnull)&#160;</td>
          <td class="paramname"><em>reference</em></td>
        </tr>
        <tr>
          <td class="paramkey">message:</td>
          <td></td>
          <td class="paramtype">(NSString *_Nullable)&#160;</td>
          <td class="paramname"><em>message</em></td>
        </tr>
        <tr>
          <td class="paramkey">postion:</td>
          <td></td>
          <td class="paramtype">(float)&#160;</td>
          <td class="paramname"><em>position</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td></td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>called when VLC wants to update an existing progress dialog </p><dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">reference</td><td>to the existing progress dialog </td></tr>
    <tr><td class="paramname">message</td><td>updated message </td></tr>
    <tr><td class="paramname">position</td><td>current position </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<hr/>The documentation for this protocol was generated from the following file:<ul>
<li>Headers/Public/<a class="el" href="_v_l_c_dialog_provider_8h_source.html">VLCDialogProvider.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.14
</small></address>
</body>
</html>
