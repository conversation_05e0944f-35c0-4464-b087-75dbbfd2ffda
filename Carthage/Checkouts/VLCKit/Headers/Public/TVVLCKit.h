/*****************************************************************************
 * VLCKit: TVVLCKit
 *****************************************************************************
 * Copyright (C) 2010-2023 <PERSON> and VideoLAN
 *
 * Authors: <AUTHORS>
 *          <PERSON> <fkuehne # videolan.org
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation; either version 2.1 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston MA 02110-1301, USA.
 *****************************************************************************/

#import <TVVLCKit/VLCAudio.h>
#import <TVVLCKit/VLCLibrary.h>
#import <TVVLCKit/VLCMedia.h>
#import <TVVLCKit/VLCMediaDiscoverer.h>
#import <TVVLCKit/VLCMediaList.h>
#import <TVVLCKit/VLCMediaPlayer.h>
#import <TVVLCKit/VLCAudioEqualizer.h>
#import <TVVLCKit/VLCMediaListPlayer.h>
#import <TVVLCKit/VLCMediaThumbnailer.h>
#import <TVVLCKit/VLCMediaMetaData.h>
#import <TVVLCKit/VLCTime.h>
#import <TVVLCKit/VLCDialogProvider.h>
#import <TVVLCKit/VLCFilter.h>
#import <TVVLCKit/VLCAdjustFilter.h>
#import <TVVLCKit/VLCLogging.h>
#import <TVVLCKit/VLCConsoleLogger.h>
#import <TVVLCKit/VLCFileLogger.h>
#import <TVVLCKit/VLCLogMessageFormatter.h>
#import <TVVLCKit/VLCEventsConfiguration.h>

@class VLCMedia;
@class VLCMediaLibrary;
@class VLCMediaList;
@class VLCTime;
@class VLCVideoView;
@class VLCAudio;
@class VLCMediaThumbnailer;
@class VLCMediaListPlayer;
@class VLCMediaPlayer;
@class VLCAudioEqualizer;
@class VLCAudioEqualizerBand;
@class VLCAudioEqualizerPreset;
@class VLCMediaMetaData;
@class VLCFilterParameter;
@class VLCAdjustFilter;
@class VLCConsoleLogger;
@class VLCFileLogger;
@class VLCLogMessageFormatter;
