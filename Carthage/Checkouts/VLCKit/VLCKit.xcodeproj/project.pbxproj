// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		3CF2BE86297F022700BBE2E3 /* VLCAudioEqualizer.h in Headers */ = {isa = PBXBuildFile; fileRef = 3CF2BE85297F022700BBE2E3 /* VLCAudioEqualizer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		3CF2BE88297F026C00BBE2E3 /* VLCAudioEqualizer.m in Sources */ = {isa = PBXBuildFile; fileRef = 3CF2BE87297F026C00BBE2E3 /* VLCAudioEqualizer.m */; };
		4102567221B56E6E00253ECF /* VLCTranscoder.m in Sources */ = {isa = PBXBuildFile; fileRef = 4102566F21B56C7500253ECF /* VLCTranscoder.m */; };
		41657C232059D49200AB915B /* VLCRendererDiscoverer.h in Headers */ = {isa = PBXBuildFile; fileRef = 41657C212059D49200AB915B /* VLCRendererDiscoverer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		41657C242059D49200AB915B /* VLCRendererItem.h in Headers */ = {isa = PBXBuildFile; fileRef = 41657C222059D49200AB915B /* VLCRendererItem.h */; settings = {ATTRIBUTES = (Public, ); }; };
		41657C322059D63D00AB915B /* VLCHelperCode.h in Headers */ = {isa = PBXBuildFile; fileRef = 41657C312059D63D00AB915B /* VLCHelperCode.h */; settings = {ATTRIBUTES = (Private, ); }; };
		41657C3D2059D99E00AB915B /* VLCCustomDialogProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 41657C3A2059D99E00AB915B /* VLCCustomDialogProvider.h */; settings = {ATTRIBUTES = (Private, ); }; };
		41657C3E2059D99E00AB915B /* VLCEmbeddedDialogProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 41657C3B2059D99E00AB915B /* VLCEmbeddedDialogProvider.h */; settings = {ATTRIBUTES = (Private, ); }; };
		41657C3F2059D99E00AB915B /* VLCiOSLegacyDialogProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 41657C3C2059D99E00AB915B /* VLCiOSLegacyDialogProvider.h */; settings = {ATTRIBUTES = (Private, ); }; };
		41657C422059DE7B00AB915B /* VLCRendererDiscoverer.m in Sources */ = {isa = PBXBuildFile; fileRef = 41657C402059DE7B00AB915B /* VLCRendererDiscoverer.m */; };
		41657C432059DE7B00AB915B /* VLCRendererItem.m in Sources */ = {isa = PBXBuildFile; fileRef = 41657C412059DE7B00AB915B /* VLCRendererItem.m */; };
		41BD71FA21B9666000795629 /* VLCTranscoder.h in Headers */ = {isa = PBXBuildFile; fileRef = 4102566E21B56C7500253ECF /* VLCTranscoder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C13FFB729BF8D0800C4C68E /* VLCEventsHandler.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C13FFB629BF8D0800C4C68E /* VLCEventsHandler.h */; settings = {ATTRIBUTES = (Private, ); }; };
		6C13FFB929BF8D2D00C4C68E /* VLCEventsHandler.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C13FFB829BF8D2D00C4C68E /* VLCEventsHandler.m */; };
		6C633565281843A000AF8CCF /* VLCConsoleLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C633562281843A000AF8CCF /* VLCConsoleLogger.m */; };
		6C633566281843A000AF8CCF /* VLCFileLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C633563281843A000AF8CCF /* VLCFileLogger.m */; };
		6C633567281843A000AF8CCF /* VLCLogMessageFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C633564281843A000AF8CCF /* VLCLogMessageFormatter.m */; };
		6C63356B281843D900AF8CCF /* VLCFileLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C633568281843D900AF8CCF /* VLCFileLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C63356C281843D900AF8CCF /* VLCLogMessageFormatter.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C633569281843D900AF8CCF /* VLCLogMessageFormatter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C63356D281843D900AF8CCF /* VLCConsoleLogger.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C63356A281843D900AF8CCF /* VLCConsoleLogger.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C63356F281843FC00AF8CCF /* VLCLogging.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C63356E281843FC00AF8CCF /* VLCLogging.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C6ABBC827C63501008FDB2D /* VLCFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C6ABBC627C63501008FDB2D /* VLCFilter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C6ABBC927C63501008FDB2D /* VLCAdjustFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C6ABBC727C63501008FDB2D /* VLCAdjustFilter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		6C6ABBCC27C63522008FDB2D /* VLCAdjustFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C6ABBCA27C63521008FDB2D /* VLCAdjustFilter.m */; };
		6C6ABBCD27C63522008FDB2D /* VLCFilter.m in Sources */ = {isa = PBXBuildFile; fileRef = 6C6ABBCB27C63522008FDB2D /* VLCFilter.m */; };
		6C6ABBCF27C6355B008FDB2D /* VLCMediaPlayer+Internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C6ABBCE27C6355B008FDB2D /* VLCMediaPlayer+Internal.h */; settings = {ATTRIBUTES = (Private, ); }; };
		6C6ABBD427C6645A008FDB2D /* VLCFilter+Internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 6C6ABBD327C6645A008FDB2D /* VLCFilter+Internal.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7D1908E9274FE44C00A8A947 /* libvlc_picture.h in Copy Files */ = {isa = PBXBuildFile; fileRef = 7D1908E6274FE43200A8A947 /* libvlc_picture.h */; };
		7D1908EA274FE45100A8A947 /* libvlc_version.h in Copy Files */ = {isa = PBXBuildFile; fileRef = 7D1908E3274FE42200A8A947 /* libvlc_version.h */; };
		7D1908EB274FE45800A8A947 /* libvlc_dialog.h in Copy Files */ = {isa = PBXBuildFile; fileRef = 7D1908E5274FE43200A8A947 /* libvlc_dialog.h */; };
		7DA7F2F428D87F0F00366EEB /* VLCMediaMetaData.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DA7F2F328D87F0F00366EEB /* VLCMediaMetaData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DA7F2F628D87F1800366EEB /* VLCMediaMetaData.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DA7F2F528D87F1800366EEB /* VLCMediaMetaData.m */; };
		7DAD5C592A83604D009419DC /* VLCEventsConfiguration.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DAD5C582A83604D009419DC /* VLCEventsConfiguration.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DAD5C5B2A836058009419DC /* VLCEventsConfiguration.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DAD5C5A2A836058009419DC /* VLCEventsConfiguration.m */; };
		7DC28D4B2019406B002D05C4 /* StaticLibVLC.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DC28D4A2019406B002D05C4 /* StaticLibVLC.m */; };
		7DC28D4F20194117002D05C4 /* VLCKit.h in Headers */ = {isa = PBXBuildFile; fileRef = EF78BD120CAEEEE700354E6E /* VLCKit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D5020194117002D05C4 /* VLCMedia.h in Headers */ = {isa = PBXBuildFile; fileRef = EF78BD130CAEEEE700354E6E /* VLCMedia.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D5220194117002D05C4 /* VLCMediaLibrary.h in Headers */ = {isa = PBXBuildFile; fileRef = EF78BD150CAEEEE700354E6E /* VLCMediaLibrary.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D5320194117002D05C4 /* VLCMediaList.h in Headers */ = {isa = PBXBuildFile; fileRef = EF78BD160CAEEEE700354E6E /* VLCMediaList.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D5420194117002D05C4 /* VLCTime.h in Headers */ = {isa = PBXBuildFile; fileRef = EF78BD190CAEEEE700354E6E /* VLCTime.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D5520194117002D05C4 /* VLCVideoView.h in Headers */ = {isa = PBXBuildFile; fileRef = EF78BD1A0CAEEEE700354E6E /* VLCVideoView.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D5620194117002D05C4 /* VLCMediaPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = EF8BB8CE0CAFA8D80038A613 /* VLCMediaPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D5720194117002D05C4 /* VLCAudio.h in Headers */ = {isa = PBXBuildFile; fileRef = EF73118E0CB5797B009473B4 /* VLCAudio.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D5820194117002D05C4 /* VLCLibVLCBridging.h in Headers */ = {isa = PBXBuildFile; fileRef = EFD551DC0CC6DD720074CEE1 /* VLCLibVLCBridging.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DC28D5B20194117002D05C4 /* VLCMediaDiscoverer.h in Headers */ = {isa = PBXBuildFile; fileRef = 637D5ABC0CF6F2650073EA45 /* VLCMediaDiscoverer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D5D20194117002D05C4 /* VLCVideoLayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 6341FCAE0D2C0929002A97B7 /* VLCVideoLayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D5E20194117002D05C4 /* VLCLibrary.h in Headers */ = {isa = PBXBuildFile; fileRef = 637CFB930D2D280800A041B6 /* VLCLibrary.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D5F20194117002D05C4 /* VLCVideoCommon.h in Headers */ = {isa = PBXBuildFile; fileRef = A7A0CEA20D2EF13000F2C039 /* VLCVideoCommon.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DC28D6020194117002D05C4 /* VLCDialogProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = 7D34F5611C90A96C008A39F0 /* VLCDialogProvider.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D6120194117002D05C4 /* VLCStreamSession.h in Headers */ = {isa = PBXBuildFile; fileRef = 632A0E830D3835C400AFC99B /* VLCStreamSession.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D6220194117002D05C4 /* VLCStreamOutput.h in Headers */ = {isa = PBXBuildFile; fileRef = 632A0EC10D38392E00AFC99B /* VLCStreamOutput.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D6320194117002D05C4 /* VLCMediaThumbnailer.h in Headers */ = {isa = PBXBuildFile; fileRef = 63D88D92124E9BF600F65FA0 /* VLCMediaThumbnailer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D6420194117002D05C4 /* VLCMediaListPlayer.h in Headers */ = {isa = PBXBuildFile; fileRef = 63014B7D1042E64A00534090 /* VLCMediaListPlayer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		7DC28D6720194117002D05C4 /* VLCLibrary.m in Sources */ = {isa = PBXBuildFile; fileRef = EF78BD3D0CAEEFF600354E6E /* VLCLibrary.m */; };
		7DC28D6820194117002D05C4 /* VLCMedia.m in Sources */ = {isa = PBXBuildFile; fileRef = EF78BD3E0CAEEFF600354E6E /* VLCMedia.m */; };
		7DC28D6920194117002D05C4 /* VLCMediaLibrary.m in Sources */ = {isa = PBXBuildFile; fileRef = EF78BD400CAEEFF600354E6E /* VLCMediaLibrary.m */; };
		7DC28D6A20194117002D05C4 /* VLCMediaList.m in Sources */ = {isa = PBXBuildFile; fileRef = EF78BD410CAEEFF600354E6E /* VLCMediaList.m */; };
		7DC28D6B20194117002D05C4 /* VLCTime.m in Sources */ = {isa = PBXBuildFile; fileRef = EF78BD440CAEEFF600354E6E /* VLCTime.m */; };
		7DC28D6C20194117002D05C4 /* VLCVideoView.m in Sources */ = {isa = PBXBuildFile; fileRef = EF78BD450CAEEFF600354E6E /* VLCVideoView.m */; };
		7DC28D6D20194117002D05C4 /* VLCCustomDialogProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D34F5641C90A98B008A39F0 /* VLCCustomDialogProvider.m */; };
		7DC28D6E20194117002D05C4 /* VLCMediaPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = EF8BB8CF0CAFA8D80038A613 /* VLCMediaPlayer.m */; };
		7DC28D6F20194117002D05C4 /* VLCAudio.m in Sources */ = {isa = PBXBuildFile; fileRef = EF73118F0CB5797B009473B4 /* VLCAudio.m */; };
		7DC28D7020194117002D05C4 /* VLCDialogProvider.m in Sources */ = {isa = PBXBuildFile; fileRef = 7D34F5651C90A98B008A39F0 /* VLCDialogProvider.m */; };
		7DC28D7120194117002D05C4 /* VLCMediaDiscoverer.m in Sources */ = {isa = PBXBuildFile; fileRef = 637D5ADB0CF6F2720073EA45 /* VLCMediaDiscoverer.m */; };
		7DC28D7220194117002D05C4 /* VLCVideoLayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 6341FCB00D2C0936002A97B7 /* VLCVideoLayer.m */; };
		7DC28D7320194117002D05C4 /* VLCVideoCommon.m in Sources */ = {isa = PBXBuildFile; fileRef = A7A0CEA30D2EF13000F2C039 /* VLCVideoCommon.m */; };
		7DC28D7420194117002D05C4 /* VLCStreamSession.m in Sources */ = {isa = PBXBuildFile; fileRef = 632A0E840D3835C400AFC99B /* VLCStreamSession.m */; };
		7DC28D7520194117002D05C4 /* VLCStreamOutput.m in Sources */ = {isa = PBXBuildFile; fileRef = 632A0EC20D38392E00AFC99B /* VLCStreamOutput.m */; };
		7DC28D7620194117002D05C4 /* VLCMediaListPlayer.m in Sources */ = {isa = PBXBuildFile; fileRef = 63014A781042ACE100534090 /* VLCMediaListPlayer.m */; };
		7DC28D7720194117002D05C4 /* VLCMediaThumbnailer.m in Sources */ = {isa = PBXBuildFile; fileRef = 636E974C11E8DCF1002FE8A9 /* VLCMediaThumbnailer.m */; };
		7DC28D7820194117002D05C4 /* VLCHelperCode.m in Sources */ = {isa = PBXBuildFile; fileRef = 7DF7A2041D197E8E0001FF69 /* VLCHelperCode.m */; };
		7DC28D7A20194117002D05C4 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1058C7B1FEA5585E11CA2CBB /* Cocoa.framework */; };
		7DC28D7B20194117002D05C4 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6384FD070D0DBA20005EB1F7 /* QuartzCore.framework */; };
		7DC28D8120194134002D05C4 /* libStaticLibVLC.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D4120194043002D05C4 /* libStaticLibVLC.a */; };
		7DC28D83201941A4002D05C4 /* libiconv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D82201941A4002D05C4 /* libiconv.tbd */; };
		7DC28D87201942A9002D05C4 /* vlc-plugins-MacOSX.h in Headers */ = {isa = PBXBuildFile; fileRef = 7DC28D84201942A9002D05C4 /* vlc-plugins-MacOSX.h */; settings = {ATTRIBUTES = (Private, ); }; };
		7DC28D89201942CD002D05C4 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D88201942CD002D05C4 /* libc++.tbd */; };
		7DC28D8B201942D2002D05C4 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D8A201942D2002D05C4 /* CoreAudio.framework */; };
		7DC28D8D201942D7002D05C4 /* VideoToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D8C201942D7002D05C4 /* VideoToolbox.framework */; };
		7DC28D8F201942E3002D05C4 /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D8E201942E3002D05C4 /* CoreGraphics.framework */; };
		7DC28D91201942E8002D05C4 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D90201942E8002D05C4 /* IOKit.framework */; };
		7DC28D93201942EE002D05C4 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D92201942EE002D05C4 /* CoreMedia.framework */; };
		7DC28D9520194301002D05C4 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D9420194301002D05C4 /* Security.framework */; };
		7DC28D9720194305002D05C4 /* OpenGL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D9620194305002D05C4 /* OpenGL.framework */; };
		7DC28D9920194315002D05C4 /* libbz2.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D9820194315002D05C4 /* libbz2.tbd */; };
		7DC28D9B2019431B002D05C4 /* AudioUnit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D9A2019431A002D05C4 /* AudioUnit.framework */; };
		7DC28D9D20194324002D05C4 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7DC28D9C20194324002D05C4 /* AudioToolbox.framework */; };
		7DEBDACE203C561D000A7D2F /* deprecated.h in Copy Files */ = {isa = PBXBuildFile; fileRef = CCEC5B73114D9BE800D34AAB /* deprecated.h */; };
		7DEBDACF203C561D000A7D2F /* libvlc_events.h in Copy Files */ = {isa = PBXBuildFile; fileRef = CCEC5B74114D9BE800D34AAB /* libvlc_events.h */; };
		7DEBDAD0203C561D000A7D2F /* libvlc_media_discoverer.h in Copy Files */ = {isa = PBXBuildFile; fileRef = CCEC5B75114D9BE800D34AAB /* libvlc_media_discoverer.h */; };
		7DEBDAD1203C561D000A7D2F /* libvlc_media_library.h in Copy Files */ = {isa = PBXBuildFile; fileRef = CCEC5B76114D9BE800D34AAB /* libvlc_media_library.h */; };
		7DEBDAD2203C561D000A7D2F /* libvlc_media_list_player.h in Copy Files */ = {isa = PBXBuildFile; fileRef = CCEC5B77114D9BE800D34AAB /* libvlc_media_list_player.h */; };
		7DEBDAD3203C561D000A7D2F /* libvlc_media_list.h in Copy Files */ = {isa = PBXBuildFile; fileRef = CCEC5B78114D9BE800D34AAB /* libvlc_media_list.h */; };
		7DEBDAD4203C561D000A7D2F /* libvlc_media_player.h in Copy Files */ = {isa = PBXBuildFile; fileRef = CCEC5B79114D9BE800D34AAB /* libvlc_media_player.h */; };
		7DEBDAD5203C561D000A7D2F /* libvlc_media.h in Copy Files */ = {isa = PBXBuildFile; fileRef = CCEC5B7A114D9BE800D34AAB /* libvlc_media.h */; };
		7DEBDAD6203C561D000A7D2F /* libvlc_renderer_discoverer.h in Copy Files */ = {isa = PBXBuildFile; fileRef = CCEC5B7B114D9BE800D34AAB /* libvlc_renderer_discoverer.h */; };
		7DEBDAD7203C561D000A7D2F /* libvlc_vlm.h in Copy Files */ = {isa = PBXBuildFile; fileRef = CCEC5B7C114D9BE800D34AAB /* libvlc_vlm.h */; };
		7DEBDAD8203C561D000A7D2F /* libvlc.h in Copy Files */ = {isa = PBXBuildFile; fileRef = CCEC5B7D114D9BE800D34AAB /* libvlc.h */; };
		7DEBDAD9203C561D000A7D2F /* vlc.h in Copy Files */ = {isa = PBXBuildFile; fileRef = CCEC5B7E114D9BE800D34AAB /* vlc.h */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		7DACDDB62028A5A5002E2FE9 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0867D690FE84028FC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7DC28D4020194043002D05C4;
			remoteInfo = StaticLibVLC;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		7DEBDACD203C5607000A7D2F /* Copy Files */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = ../Headers/vlc;
			dstSubfolderSpec = 7;
			files = (
				7D1908EB274FE45800A8A947 /* libvlc_dialog.h in Copy Files */,
				7D1908EA274FE45100A8A947 /* libvlc_version.h in Copy Files */,
				7D1908E9274FE44C00A8A947 /* libvlc_picture.h in Copy Files */,
				7DEBDACE203C561D000A7D2F /* deprecated.h in Copy Files */,
				7DEBDACF203C561D000A7D2F /* libvlc_events.h in Copy Files */,
				7DEBDAD0203C561D000A7D2F /* libvlc_media_discoverer.h in Copy Files */,
				7DEBDAD1203C561D000A7D2F /* libvlc_media_library.h in Copy Files */,
				7DEBDAD2203C561D000A7D2F /* libvlc_media_list_player.h in Copy Files */,
				7DEBDAD3203C561D000A7D2F /* libvlc_media_list.h in Copy Files */,
				7DEBDAD4203C561D000A7D2F /* libvlc_media_player.h in Copy Files */,
				7DEBDAD5203C561D000A7D2F /* libvlc_media.h in Copy Files */,
				7DEBDAD6203C561D000A7D2F /* libvlc_renderer_discoverer.h in Copy Files */,
				7DEBDAD7203C561D000A7D2F /* libvlc_vlm.h in Copy Files */,
				7DEBDAD8203C561D000A7D2F /* libvlc.h in Copy Files */,
				7DEBDAD9203C561D000A7D2F /* vlc.h in Copy Files */,
			);
			name = "Copy Files";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0867D69BFE84028FC02AAC07 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = /System/Library/Frameworks/Foundation.framework; sourceTree = "<absolute>"; };
		0867D6A5FE840307C02AAC07 /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = /System/Library/Frameworks/AppKit.framework; sourceTree = "<absolute>"; };
		1058C7B1FEA5585E11CA2CBB /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = /System/Library/Frameworks/Cocoa.framework; sourceTree = "<absolute>"; };
		32DBCF5E0370ADEE00C91783 /* Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Prefix.pch; path = Headers/PCH/Prefix.pch; sourceTree = "<group>"; };
		3CF2BE85297F022700BBE2E3 /* VLCAudioEqualizer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCAudioEqualizer.h; path = Public/VLCAudioEqualizer.h; sourceTree = "<group>"; };
		3CF2BE87297F026C00BBE2E3 /* VLCAudioEqualizer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCAudioEqualizer.m; sourceTree = "<group>"; };
		4102566E21B56C7500253ECF /* VLCTranscoder.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = VLCTranscoder.h; path = Public/VLCTranscoder.h; sourceTree = "<group>"; };
		4102566F21B56C7500253ECF /* VLCTranscoder.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VLCTranscoder.m; sourceTree = "<group>"; };
		41657C212059D49200AB915B /* VLCRendererDiscoverer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCRendererDiscoverer.h; path = Public/VLCRendererDiscoverer.h; sourceTree = "<group>"; };
		41657C222059D49200AB915B /* VLCRendererItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCRendererItem.h; path = Public/VLCRendererItem.h; sourceTree = "<group>"; };
		41657C312059D63D00AB915B /* VLCHelperCode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCHelperCode.h; path = Internal/VLCHelperCode.h; sourceTree = "<group>"; };
		41657C3A2059D99E00AB915B /* VLCCustomDialogProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCCustomDialogProvider.h; path = Internal/VLCCustomDialogProvider.h; sourceTree = "<group>"; };
		41657C3B2059D99E00AB915B /* VLCEmbeddedDialogProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCEmbeddedDialogProvider.h; path = Internal/VLCEmbeddedDialogProvider.h; sourceTree = "<group>"; };
		41657C3C2059D99E00AB915B /* VLCiOSLegacyDialogProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCiOSLegacyDialogProvider.h; path = Internal/VLCiOSLegacyDialogProvider.h; sourceTree = "<group>"; };
		41657C402059DE7B00AB915B /* VLCRendererDiscoverer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCRendererDiscoverer.m; sourceTree = "<group>"; };
		41657C412059DE7B00AB915B /* VLCRendererItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCRendererItem.m; sourceTree = "<group>"; };
		63014A781042ACE100534090 /* VLCMediaListPlayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCMediaListPlayer.m; sourceTree = "<group>"; };
		63014B7D1042E64A00534090 /* VLCMediaListPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaListPlayer.h; path = Public/VLCMediaListPlayer.h; sourceTree = "<group>"; };
		63030CC70CCA652C0088ECD1 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 30; lastKnownFileType = text.plist.xml; name = Info.plist; path = Resources/VLCKit/Info.plist; sourceTree = "<group>"; };
		632A0E830D3835C400AFC99B /* VLCStreamSession.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCStreamSession.h; path = Public/VLCStreamSession.h; sourceTree = "<group>"; };
		632A0E840D3835C400AFC99B /* VLCStreamSession.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCStreamSession.m; sourceTree = "<group>"; };
		632A0EC10D38392E00AFC99B /* VLCStreamOutput.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCStreamOutput.h; path = Public/VLCStreamOutput.h; sourceTree = "<group>"; };
		632A0EC20D38392E00AFC99B /* VLCStreamOutput.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCStreamOutput.m; sourceTree = "<group>"; };
		6341FCAE0D2C0929002A97B7 /* VLCVideoLayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCVideoLayer.h; path = Public/VLCVideoLayer.h; sourceTree = "<group>"; };
		6341FCB00D2C0936002A97B7 /* VLCVideoLayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCVideoLayer.m; sourceTree = "<group>"; };
		636E974C11E8DCF1002FE8A9 /* VLCMediaThumbnailer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCMediaThumbnailer.m; sourceTree = "<group>"; };
		637CFB930D2D280800A041B6 /* VLCLibrary.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCLibrary.h; path = Public/VLCLibrary.h; sourceTree = "<group>"; };
		637D5ABC0CF6F2650073EA45 /* VLCMediaDiscoverer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaDiscoverer.h; path = Public/VLCMediaDiscoverer.h; sourceTree = "<group>"; };
		637D5ADB0CF6F2720073EA45 /* VLCMediaDiscoverer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCMediaDiscoverer.m; sourceTree = "<group>"; };
		6384FD070D0DBA20005EB1F7 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = /System/Library/Frameworks/QuartzCore.framework; sourceTree = "<absolute>"; };
		63D88D92124E9BF600F65FA0 /* VLCMediaThumbnailer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaThumbnailer.h; path = Public/VLCMediaThumbnailer.h; sourceTree = "<group>"; };
		6C13FFB629BF8D0800C4C68E /* VLCEventsHandler.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCEventsHandler.h; path = Internal/VLCEventsHandler.h; sourceTree = "<group>"; };
		6C13FFB829BF8D2D00C4C68E /* VLCEventsHandler.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCEventsHandler.m; sourceTree = "<group>"; };
		6C633562281843A000AF8CCF /* VLCConsoleLogger.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCConsoleLogger.m; sourceTree = "<group>"; };
		6C633563281843A000AF8CCF /* VLCFileLogger.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCFileLogger.m; sourceTree = "<group>"; };
		6C633564281843A000AF8CCF /* VLCLogMessageFormatter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCLogMessageFormatter.m; sourceTree = "<group>"; };
		6C633568281843D900AF8CCF /* VLCFileLogger.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCFileLogger.h; path = Public/VLCFileLogger.h; sourceTree = "<group>"; };
		6C633569281843D900AF8CCF /* VLCLogMessageFormatter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCLogMessageFormatter.h; path = Public/VLCLogMessageFormatter.h; sourceTree = "<group>"; };
		6C63356A281843D900AF8CCF /* VLCConsoleLogger.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCConsoleLogger.h; path = Public/VLCConsoleLogger.h; sourceTree = "<group>"; };
		6C63356E281843FC00AF8CCF /* VLCLogging.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCLogging.h; path = Public/VLCLogging.h; sourceTree = "<group>"; };
		6C6ABBC627C63501008FDB2D /* VLCFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCFilter.h; path = Public/VLCFilter.h; sourceTree = "<group>"; };
		6C6ABBC727C63501008FDB2D /* VLCAdjustFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCAdjustFilter.h; path = Public/VLCAdjustFilter.h; sourceTree = "<group>"; };
		6C6ABBCA27C63521008FDB2D /* VLCAdjustFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCAdjustFilter.m; sourceTree = "<group>"; };
		6C6ABBCB27C63522008FDB2D /* VLCFilter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCFilter.m; sourceTree = "<group>"; };
		6C6ABBCE27C6355B008FDB2D /* VLCMediaPlayer+Internal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "VLCMediaPlayer+Internal.h"; path = "Internal/VLCMediaPlayer+Internal.h"; sourceTree = "<group>"; };
		6C6ABBD327C6645A008FDB2D /* VLCFilter+Internal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "VLCFilter+Internal.h"; path = "Internal/VLCFilter+Internal.h"; sourceTree = "<group>"; };
		7D1908E3274FE42200A8A947 /* libvlc_version.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = libvlc_version.h; sourceTree = "<group>"; };
		7D1908E5274FE43200A8A947 /* libvlc_dialog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_dialog.h; path = libvlc/vlc/include/vlc/libvlc_dialog.h; sourceTree = SOURCE_ROOT; };
		7D1908E6274FE43200A8A947 /* libvlc_picture.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_picture.h; path = libvlc/vlc/include/vlc/libvlc_picture.h; sourceTree = SOURCE_ROOT; };
		7D34F5611C90A96C008A39F0 /* VLCDialogProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCDialogProvider.h; path = Public/VLCDialogProvider.h; sourceTree = "<group>"; };
		7D34F5641C90A98B008A39F0 /* VLCCustomDialogProvider.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCCustomDialogProvider.m; sourceTree = "<group>"; };
		7D34F5651C90A98B008A39F0 /* VLCDialogProvider.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCDialogProvider.m; sourceTree = "<group>"; };
		7DA7F2F328D87F0F00366EEB /* VLCMediaMetaData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaMetaData.h; path = Public/VLCMediaMetaData.h; sourceTree = "<group>"; };
		7DA7F2F528D87F1800366EEB /* VLCMediaMetaData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCMediaMetaData.m; sourceTree = "<group>"; };
		7DAD5C582A83604D009419DC /* VLCEventsConfiguration.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCEventsConfiguration.h; path = Public/VLCEventsConfiguration.h; sourceTree = "<group>"; };
		7DAD5C5A2A836058009419DC /* VLCEventsConfiguration.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCEventsConfiguration.m; sourceTree = "<group>"; };
		7DC28D3C201938D9002D05C4 /* vlc-plugins-MacOSX.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = "vlc-plugins-MacOSX.xcconfig"; path = "Resources/MobileVLCKit/vlc-plugins-MacOSX.xcconfig"; sourceTree = "<group>"; };
		7DC28D4120194043002D05C4 /* libStaticLibVLC.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libStaticLibVLC.a; sourceTree = BUILT_PRODUCTS_DIR; };
		7DC28D4A2019406B002D05C4 /* StaticLibVLC.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = StaticLibVLC.m; path = Sources/StaticLibVLC.m; sourceTree = SOURCE_ROOT; };
		7DC28D7F20194117002D05C4 /* VLCKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = VLCKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7DC28D82201941A4002D05C4 /* libiconv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libiconv.tbd; path = usr/lib/libiconv.tbd; sourceTree = SDKROOT; };
		7DC28D84201942A9002D05C4 /* vlc-plugins-MacOSX.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "vlc-plugins-MacOSX.h"; path = "Resources/MobileVLCKit/vlc-plugins-MacOSX.h"; sourceTree = SOURCE_ROOT; };
		7DC28D88201942CD002D05C4 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		7DC28D8A201942D2002D05C4 /* CoreAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudio.framework; path = System/Library/Frameworks/CoreAudio.framework; sourceTree = SDKROOT; };
		7DC28D8C201942D7002D05C4 /* VideoToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = VideoToolbox.framework; path = System/Library/Frameworks/VideoToolbox.framework; sourceTree = SDKROOT; };
		7DC28D8E201942E3002D05C4 /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		7DC28D90201942E8002D05C4 /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = System/Library/Frameworks/IOKit.framework; sourceTree = SDKROOT; };
		7DC28D92201942EE002D05C4 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		7DC28D9420194301002D05C4 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		7DC28D9620194305002D05C4 /* OpenGL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGL.framework; path = System/Library/Frameworks/OpenGL.framework; sourceTree = SDKROOT; };
		7DC28D9820194315002D05C4 /* libbz2.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libbz2.tbd; path = usr/lib/libbz2.tbd; sourceTree = SDKROOT; };
		7DC28D9A2019431A002D05C4 /* AudioUnit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioUnit.framework; path = System/Library/Frameworks/AudioUnit.framework; sourceTree = SDKROOT; };
		7DC28D9C20194324002D05C4 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		7DF7A2041D197E8E0001FF69 /* VLCHelperCode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCHelperCode.m; sourceTree = "<group>"; };
		A7A0CEA20D2EF13000F2C039 /* VLCVideoCommon.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCVideoCommon.h; path = Internal/VLCVideoCommon.h; sourceTree = "<group>"; };
		A7A0CEA30D2EF13000F2C039 /* VLCVideoCommon.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCVideoCommon.m; sourceTree = "<group>"; };
		CCEC5B73114D9BE800D34AAB /* deprecated.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = deprecated.h; path = libvlc/vlc/include/vlc/deprecated.h; sourceTree = SOURCE_ROOT; };
		CCEC5B74114D9BE800D34AAB /* libvlc_events.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_events.h; path = libvlc/vlc/include/vlc/libvlc_events.h; sourceTree = SOURCE_ROOT; };
		CCEC5B75114D9BE800D34AAB /* libvlc_media_discoverer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_media_discoverer.h; path = libvlc/vlc/include/vlc/libvlc_media_discoverer.h; sourceTree = SOURCE_ROOT; };
		CCEC5B76114D9BE800D34AAB /* libvlc_media_library.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_media_library.h; path = libvlc/vlc/include/vlc/libvlc_media_library.h; sourceTree = SOURCE_ROOT; };
		CCEC5B77114D9BE800D34AAB /* libvlc_media_list_player.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_media_list_player.h; path = libvlc/vlc/include/vlc/libvlc_media_list_player.h; sourceTree = SOURCE_ROOT; };
		CCEC5B78114D9BE800D34AAB /* libvlc_media_list.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_media_list.h; path = libvlc/vlc/include/vlc/libvlc_media_list.h; sourceTree = SOURCE_ROOT; };
		CCEC5B79114D9BE800D34AAB /* libvlc_media_player.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_media_player.h; path = libvlc/vlc/include/vlc/libvlc_media_player.h; sourceTree = SOURCE_ROOT; };
		CCEC5B7A114D9BE800D34AAB /* libvlc_media.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_media.h; path = libvlc/vlc/include/vlc/libvlc_media.h; sourceTree = SOURCE_ROOT; };
		CCEC5B7B114D9BE800D34AAB /* libvlc_renderer_discoverer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_renderer_discoverer.h; path = libvlc/vlc/include/vlc/libvlc_renderer_discoverer.h; sourceTree = SOURCE_ROOT; };
		CCEC5B7C114D9BE800D34AAB /* libvlc_vlm.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc_vlm.h; path = libvlc/vlc/include/vlc/libvlc_vlm.h; sourceTree = SOURCE_ROOT; };
		CCEC5B7D114D9BE800D34AAB /* libvlc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = libvlc.h; path = libvlc/vlc/include/vlc/libvlc.h; sourceTree = SOURCE_ROOT; };
		CCEC5B7E114D9BE800D34AAB /* vlc.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = vlc.h; path = libvlc/vlc/include/vlc/vlc.h; sourceTree = SOURCE_ROOT; };
		EF73118E0CB5797B009473B4 /* VLCAudio.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCAudio.h; path = Public/VLCAudio.h; sourceTree = "<group>"; };
		EF73118F0CB5797B009473B4 /* VLCAudio.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCAudio.m; sourceTree = "<group>"; };
		EF78BD120CAEEEE700354E6E /* VLCKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCKit.h; path = Public/VLCKit.h; sourceTree = "<group>"; };
		EF78BD130CAEEEE700354E6E /* VLCMedia.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMedia.h; path = Public/VLCMedia.h; sourceTree = "<group>"; };
		EF78BD150CAEEEE700354E6E /* VLCMediaLibrary.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaLibrary.h; path = Public/VLCMediaLibrary.h; sourceTree = "<group>"; };
		EF78BD160CAEEEE700354E6E /* VLCMediaList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaList.h; path = Public/VLCMediaList.h; sourceTree = "<group>"; };
		EF78BD190CAEEEE700354E6E /* VLCTime.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCTime.h; path = Public/VLCTime.h; sourceTree = "<group>"; };
		EF78BD1A0CAEEEE700354E6E /* VLCVideoView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCVideoView.h; path = Public/VLCVideoView.h; sourceTree = "<group>"; };
		EF78BD3D0CAEEFF600354E6E /* VLCLibrary.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCLibrary.m; sourceTree = "<group>"; };
		EF78BD3E0CAEEFF600354E6E /* VLCMedia.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCMedia.m; sourceTree = "<group>"; };
		EF78BD400CAEEFF600354E6E /* VLCMediaLibrary.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCMediaLibrary.m; sourceTree = "<group>"; };
		EF78BD410CAEEFF600354E6E /* VLCMediaList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCMediaList.m; sourceTree = "<group>"; };
		EF78BD440CAEEFF600354E6E /* VLCTime.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCTime.m; sourceTree = "<group>"; };
		EF78BD450CAEEFF600354E6E /* VLCVideoView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCVideoView.m; sourceTree = "<group>"; };
		EF8BB8CE0CAFA8D80038A613 /* VLCMediaPlayer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCMediaPlayer.h; path = Public/VLCMediaPlayer.h; sourceTree = "<group>"; };
		EF8BB8CF0CAFA8D80038A613 /* VLCMediaPlayer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = VLCMediaPlayer.m; sourceTree = "<group>"; };
		EFD551DC0CC6DD720074CEE1 /* VLCLibVLCBridging.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = VLCLibVLCBridging.h; path = Internal/VLCLibVLCBridging.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7DC28D3E20194043002D05C4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7DC28D7920194117002D05C4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7DC28D9D20194324002D05C4 /* AudioToolbox.framework in Frameworks */,
				7DC28D9B2019431B002D05C4 /* AudioUnit.framework in Frameworks */,
				7DC28D9920194315002D05C4 /* libbz2.tbd in Frameworks */,
				7DC28D9720194305002D05C4 /* OpenGL.framework in Frameworks */,
				7DC28D9520194301002D05C4 /* Security.framework in Frameworks */,
				7DC28D93201942EE002D05C4 /* CoreMedia.framework in Frameworks */,
				7DC28D91201942E8002D05C4 /* IOKit.framework in Frameworks */,
				7DC28D8F201942E3002D05C4 /* CoreGraphics.framework in Frameworks */,
				7DC28D8D201942D7002D05C4 /* VideoToolbox.framework in Frameworks */,
				7DC28D8B201942D2002D05C4 /* CoreAudio.framework in Frameworks */,
				7DC28D89201942CD002D05C4 /* libc++.tbd in Frameworks */,
				7DC28D83201941A4002D05C4 /* libiconv.tbd in Frameworks */,
				7DC28D8120194134002D05C4 /* libStaticLibVLC.a in Frameworks */,
				7DC28D7A20194117002D05C4 /* Cocoa.framework in Frameworks */,
				7DC28D7B20194117002D05C4 /* QuartzCore.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		034768DFFF38A50411DB9C8B /* Products */ = {
			isa = PBXGroup;
			children = (
				7DC28D4120194043002D05C4 /* libStaticLibVLC.a */,
				7DC28D7F20194117002D05C4 /* VLCKit.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0867D691FE84028FC02AAC07 /* VLC */ = {
			isa = PBXGroup;
			children = (
				EF78BD3B0CAEEFD000354E6E /* Headers */,
				08FB77AEFE84172EC02AAC07 /* Sources */,
				32C88DFF0371C24200C91783 /* Other Sources */,
				089C1665FE841158C02AAC07 /* Resources */,
				0867D69AFE84028FC02AAC07 /* External Frameworks and Libraries */,
				7DC28D4220194043002D05C4 /* StaticLibVLC */,
				034768DFFF38A50411DB9C8B /* Products */,
				7DF2DC2F1D6314FB00CDA90D /* Frameworks */,
			);
			name = VLC;
			sourceTree = "<group>";
		};
		0867D69AFE84028FC02AAC07 /* External Frameworks and Libraries */ = {
			isa = PBXGroup;
			children = (
				1058C7B0FEA5585E11CA2CBB /* Linked Frameworks */,
				1058C7B2FEA5585E11CA2CBB /* Other Frameworks */,
			);
			name = "External Frameworks and Libraries";
			sourceTree = "<group>";
		};
		089C1665FE841158C02AAC07 /* Resources */ = {
			isa = PBXGroup;
			children = (
				63030CC70CCA652C0088ECD1 /* Info.plist */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		08FB77AEFE84172EC02AAC07 /* Sources */ = {
			isa = PBXGroup;
			children = (
				6C13FFB829BF8D2D00C4C68E /* VLCEventsHandler.m */,
				7DAD5C5A2A836058009419DC /* VLCEventsConfiguration.m */,
				41657C402059DE7B00AB915B /* VLCRendererDiscoverer.m */,
				41657C412059DE7B00AB915B /* VLCRendererItem.m */,
				6C6ABBCA27C63521008FDB2D /* VLCAdjustFilter.m */,
				6C633562281843A000AF8CCF /* VLCConsoleLogger.m */,
				6C633563281843A000AF8CCF /* VLCFileLogger.m */,
				6C6ABBCB27C63522008FDB2D /* VLCFilter.m */,
				EF78BD3D0CAEEFF600354E6E /* VLCLibrary.m */,
				6C633564281843A000AF8CCF /* VLCLogMessageFormatter.m */,
				EF78BD3E0CAEEFF600354E6E /* VLCMedia.m */,
				637D5ADB0CF6F2720073EA45 /* VLCMediaDiscoverer.m */,
				EF78BD410CAEEFF600354E6E /* VLCMediaList.m */,
				63014A781042ACE100534090 /* VLCMediaListPlayer.m */,
				EF8BB8CF0CAFA8D80038A613 /* VLCMediaPlayer.m */,
				3CF2BE87297F026C00BBE2E3 /* VLCAudioEqualizer.m */,
				636E974C11E8DCF1002FE8A9 /* VLCMediaThumbnailer.m */,
				EF78BD400CAEEFF600354E6E /* VLCMediaLibrary.m */,
				7DA7F2F528D87F1800366EEB /* VLCMediaMetaData.m */,
				A7A0CEA30D2EF13000F2C039 /* VLCVideoCommon.m */,
				6341FCB00D2C0936002A97B7 /* VLCVideoLayer.m */,
				EF78BD450CAEEFF600354E6E /* VLCVideoView.m */,
				EF78BD440CAEEFF600354E6E /* VLCTime.m */,
				EF73118F0CB5797B009473B4 /* VLCAudio.m */,
				4102566F21B56C7500253ECF /* VLCTranscoder.m */,
				7DF7A2041D197E8E0001FF69 /* VLCHelperCode.m */,
				7D34F56C1C90A990008A39F0 /* Dialogs */,
				632A0F7B0D38F78500AFC99B /* Stream */,
			);
			path = Sources;
			sourceTree = "<group>";
		};
		1058C7B0FEA5585E11CA2CBB /* Linked Frameworks */ = {
			isa = PBXGroup;
			children = (
				6384FD070D0DBA20005EB1F7 /* QuartzCore.framework */,
				1058C7B1FEA5585E11CA2CBB /* Cocoa.framework */,
			);
			name = "Linked Frameworks";
			sourceTree = "<group>";
		};
		1058C7B2FEA5585E11CA2CBB /* Other Frameworks */ = {
			isa = PBXGroup;
			children = (
				0867D6A5FE840307C02AAC07 /* AppKit.framework */,
				0867D69BFE84028FC02AAC07 /* Foundation.framework */,
			);
			name = "Other Frameworks";
			sourceTree = "<group>";
		};
		32C88DFF0371C24200C91783 /* Other Sources */ = {
			isa = PBXGroup;
			children = (
				32DBCF5E0370ADEE00C91783 /* Prefix.pch */,
				7DC28D3C201938D9002D05C4 /* vlc-plugins-MacOSX.xcconfig */,
			);
			name = "Other Sources";
			sourceTree = "<group>";
		};
		632A0F7B0D38F78500AFC99B /* Stream */ = {
			isa = PBXGroup;
			children = (
				632A0E840D3835C400AFC99B /* VLCStreamSession.m */,
				632A0EC20D38392E00AFC99B /* VLCStreamOutput.m */,
			);
			name = Stream;
			sourceTree = "<group>";
		};
		632A0F7C0D38F79200AFC99B /* Stream */ = {
			isa = PBXGroup;
			children = (
				632A0E830D3835C400AFC99B /* VLCStreamSession.h */,
				632A0EC10D38392E00AFC99B /* VLCStreamOutput.h */,
			);
			name = Stream;
			sourceTree = "<group>";
		};
		637CFB960D2D281900A041B6 /* Internal */ = {
			isa = PBXGroup;
			children = (
				6C13FFB629BF8D0800C4C68E /* VLCEventsHandler.h */,
				6C6ABBD327C6645A008FDB2D /* VLCFilter+Internal.h */,
				6C6ABBCE27C6355B008FDB2D /* VLCMediaPlayer+Internal.h */,
				7DC28D84201942A9002D05C4 /* vlc-plugins-MacOSX.h */,
				EFD551DC0CC6DD720074CEE1 /* VLCLibVLCBridging.h */,
				A7A0CEA20D2EF13000F2C039 /* VLCVideoCommon.h */,
				41657C3A2059D99E00AB915B /* VLCCustomDialogProvider.h */,
				41657C3B2059D99E00AB915B /* VLCEmbeddedDialogProvider.h */,
				41657C3C2059D99E00AB915B /* VLCiOSLegacyDialogProvider.h */,
				41657C312059D63D00AB915B /* VLCHelperCode.h */,
			);
			name = Internal;
			sourceTree = "<group>";
		};
		7D34F56C1C90A990008A39F0 /* Dialogs */ = {
			isa = PBXGroup;
			children = (
				7D34F5641C90A98B008A39F0 /* VLCCustomDialogProvider.m */,
				7D34F5651C90A98B008A39F0 /* VLCDialogProvider.m */,
			);
			name = Dialogs;
			sourceTree = "<group>";
		};
		7DC28D4220194043002D05C4 /* StaticLibVLC */ = {
			isa = PBXGroup;
			children = (
				7DC28D4A2019406B002D05C4 /* StaticLibVLC.m */,
			);
			path = StaticLibVLC;
			sourceTree = "<group>";
		};
		7DF2DC2F1D6314FB00CDA90D /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				7DC28D9C20194324002D05C4 /* AudioToolbox.framework */,
				7DC28D9A2019431A002D05C4 /* AudioUnit.framework */,
				7DC28D9820194315002D05C4 /* libbz2.tbd */,
				7DC28D9620194305002D05C4 /* OpenGL.framework */,
				7DC28D9420194301002D05C4 /* Security.framework */,
				7DC28D92201942EE002D05C4 /* CoreMedia.framework */,
				7DC28D90201942E8002D05C4 /* IOKit.framework */,
				7DC28D8E201942E3002D05C4 /* CoreGraphics.framework */,
				7DC28D8C201942D7002D05C4 /* VideoToolbox.framework */,
				7DC28D8A201942D2002D05C4 /* CoreAudio.framework */,
				7DC28D88201942CD002D05C4 /* libc++.tbd */,
				7DC28D82201941A4002D05C4 /* libiconv.tbd */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		CCEC5B6C114D9BD300D34AAB /* libvlc */ = {
			isa = PBXGroup;
			children = (
				CCEC5B73114D9BE800D34AAB /* deprecated.h */,
				CCEC5B74114D9BE800D34AAB /* libvlc_events.h */,
				CCEC5B75114D9BE800D34AAB /* libvlc_media_discoverer.h */,
				CCEC5B76114D9BE800D34AAB /* libvlc_media_library.h */,
				CCEC5B77114D9BE800D34AAB /* libvlc_media_list_player.h */,
				CCEC5B78114D9BE800D34AAB /* libvlc_media_list.h */,
				CCEC5B79114D9BE800D34AAB /* libvlc_media_player.h */,
				CCEC5B7A114D9BE800D34AAB /* libvlc_media.h */,
				CCEC5B7B114D9BE800D34AAB /* libvlc_renderer_discoverer.h */,
				CCEC5B7C114D9BE800D34AAB /* libvlc_vlm.h */,
				7D1908E3274FE42200A8A947 /* libvlc_version.h */,
				CCEC5B7D114D9BE800D34AAB /* libvlc.h */,
				7D1908E5274FE43200A8A947 /* libvlc_dialog.h */,
				7D1908E6274FE43200A8A947 /* libvlc_picture.h */,
				CCEC5B7E114D9BE800D34AAB /* vlc.h */,
			);
			name = libvlc;
			sourceTree = "<group>";
		};
		EF6BC9110DA1932F00DD37EF /* Public */ = {
			isa = PBXGroup;
			children = (
				632A0F7C0D38F79200AFC99B /* Stream */,
				6C6ABBC727C63501008FDB2D /* VLCAdjustFilter.h */,
				EF73118E0CB5797B009473B4 /* VLCAudio.h */,
				7D34F5611C90A96C008A39F0 /* VLCDialogProvider.h */,
				6C6ABBC627C63501008FDB2D /* VLCFilter.h */,
				6C63356A281843D900AF8CCF /* VLCConsoleLogger.h */,
				6C633568281843D900AF8CCF /* VLCFileLogger.h */,
				EF78BD120CAEEEE700354E6E /* VLCKit.h */,
				637CFB930D2D280800A041B6 /* VLCLibrary.h */,
				7DAD5C582A83604D009419DC /* VLCEventsConfiguration.h */,
				6C63356E281843FC00AF8CCF /* VLCLogging.h */,
				6C633569281843D900AF8CCF /* VLCLogMessageFormatter.h */,
				EF78BD130CAEEEE700354E6E /* VLCMedia.h */,
				637D5ABC0CF6F2650073EA45 /* VLCMediaDiscoverer.h */,
				EF78BD150CAEEEE700354E6E /* VLCMediaLibrary.h */,
				EF78BD160CAEEEE700354E6E /* VLCMediaList.h */,
				63014B7D1042E64A00534090 /* VLCMediaListPlayer.h */,
				EF8BB8CE0CAFA8D80038A613 /* VLCMediaPlayer.h */,
				3CF2BE85297F022700BBE2E3 /* VLCAudioEqualizer.h */,
				63D88D92124E9BF600F65FA0 /* VLCMediaThumbnailer.h */,
				7DA7F2F328D87F0F00366EEB /* VLCMediaMetaData.h */,
				41657C212059D49200AB915B /* VLCRendererDiscoverer.h */,
				41657C222059D49200AB915B /* VLCRendererItem.h */,
				EF78BD190CAEEEE700354E6E /* VLCTime.h */,
				6341FCAE0D2C0929002A97B7 /* VLCVideoLayer.h */,
				EF78BD1A0CAEEEE700354E6E /* VLCVideoView.h */,
				4102566E21B56C7500253ECF /* VLCTranscoder.h */,
			);
			name = Public;
			sourceTree = "<group>";
		};
		EF78BD3B0CAEEFD000354E6E /* Headers */ = {
			isa = PBXGroup;
			children = (
				CCEC5B6C114D9BD300D34AAB /* libvlc */,
				637CFB960D2D281900A041B6 /* Internal */,
				EF6BC9110DA1932F00DD37EF /* Public */,
			);
			path = Headers;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		7DC28D3F20194043002D05C4 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7DC28D4D20194117002D05C4 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7DC28D4F20194117002D05C4 /* VLCKit.h in Headers */,
				7DC28D5020194117002D05C4 /* VLCMedia.h in Headers */,
				7DC28D5220194117002D05C4 /* VLCMediaLibrary.h in Headers */,
				7DC28D5320194117002D05C4 /* VLCMediaList.h in Headers */,
				7DC28D5420194117002D05C4 /* VLCTime.h in Headers */,
				7DC28D5520194117002D05C4 /* VLCVideoView.h in Headers */,
				7DC28D5620194117002D05C4 /* VLCMediaPlayer.h in Headers */,
				3CF2BE86297F022700BBE2E3 /* VLCAudioEqualizer.h in Headers */,
				41BD71FA21B9666000795629 /* VLCTranscoder.h in Headers */,
				7DC28D5720194117002D05C4 /* VLCAudio.h in Headers */,
				41657C232059D49200AB915B /* VLCRendererDiscoverer.h in Headers */,
				7DC28D5B20194117002D05C4 /* VLCMediaDiscoverer.h in Headers */,
				7DC28D5D20194117002D05C4 /* VLCVideoLayer.h in Headers */,
				6C6ABBC827C63501008FDB2D /* VLCFilter.h in Headers */,
				6C6ABBC927C63501008FDB2D /* VLCAdjustFilter.h in Headers */,
				6C63356B281843D900AF8CCF /* VLCFileLogger.h in Headers */,
				7DC28D5E20194117002D05C4 /* VLCLibrary.h in Headers */,
				7DC28D6020194117002D05C4 /* VLCDialogProvider.h in Headers */,
				7DAD5C592A83604D009419DC /* VLCEventsConfiguration.h in Headers */,
				7DA7F2F428D87F0F00366EEB /* VLCMediaMetaData.h in Headers */,
				41657C242059D49200AB915B /* VLCRendererItem.h in Headers */,
				41657C322059D63D00AB915B /* VLCHelperCode.h in Headers */,
				6C6ABBD427C6645A008FDB2D /* VLCFilter+Internal.h in Headers */,
				7DC28D5820194117002D05C4 /* VLCLibVLCBridging.h in Headers */,
				6C6ABBCF27C6355B008FDB2D /* VLCMediaPlayer+Internal.h in Headers */,
				41657C3D2059D99E00AB915B /* VLCCustomDialogProvider.h in Headers */,
				7DC28D5F20194117002D05C4 /* VLCVideoCommon.h in Headers */,
				41657C3E2059D99E00AB915B /* VLCEmbeddedDialogProvider.h in Headers */,
				7DC28D87201942A9002D05C4 /* vlc-plugins-MacOSX.h in Headers */,
				41657C3F2059D99E00AB915B /* VLCiOSLegacyDialogProvider.h in Headers */,
				6C13FFB729BF8D0800C4C68E /* VLCEventsHandler.h in Headers */,
				7DC28D6120194117002D05C4 /* VLCStreamSession.h in Headers */,
				7DC28D6220194117002D05C4 /* VLCStreamOutput.h in Headers */,
				6C63356C281843D900AF8CCF /* VLCLogMessageFormatter.h in Headers */,
				7DC28D6320194117002D05C4 /* VLCMediaThumbnailer.h in Headers */,
				7DC28D6420194117002D05C4 /* VLCMediaListPlayer.h in Headers */,
				6C63356F281843FC00AF8CCF /* VLCLogging.h in Headers */,
				6C63356D281843D900AF8CCF /* VLCConsoleLogger.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		7DC28D4020194043002D05C4 /* StaticLibVLC */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7DC28D4720194043002D05C4 /* Build configuration list for PBXNativeTarget "StaticLibVLC" */;
			buildPhases = (
				7DC28D3D20194043002D05C4 /* Sources */,
				7DC28D3E20194043002D05C4 /* Frameworks */,
				7DC28D3F20194043002D05C4 /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = StaticLibVLC;
			productName = StaticLibVLC;
			productReference = 7DC28D4120194043002D05C4 /* libStaticLibVLC.a */;
			productType = "com.apple.product-type.library.static";
		};
		7DC28D4C20194117002D05C4 /* VLCKit */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7DC28D7C20194117002D05C4 /* Build configuration list for PBXNativeTarget "VLCKit" */;
			buildPhases = (
				7DC28D4D20194117002D05C4 /* Headers */,
				7DC28D6520194117002D05C4 /* Sources */,
				7DC28D7920194117002D05C4 /* Frameworks */,
				7DEBDACD203C5607000A7D2F /* Copy Files */,
			);
			buildRules = (
			);
			comments = "-lvlc -lvlc-control -dylib_file @loader_path/../lib/vlc_libintl.dylib:$(TARGET_BUILD_DIR)/$(PROJECT_NAME).framework/lib/vlc_libintl.dylib -dylib_file @loader_path/../lib/libvlc.dylib:$(TARGET_BUILD_DIR)/$(PROJECT_NAME).framework/lib/libvlc.dylib";
			dependencies = (
				7DACDDB72028A5A5002E2FE9 /* PBXTargetDependency */,
			);
			name = VLCKit;
			productInstallPath = "$(HOME)/Library/Frameworks";
			productName = VLC;
			productReference = 7DC28D7F20194117002D05C4 /* VLCKit.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0867D690FE84028FC02AAC07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0800;
				TargetAttributes = {
					7DC28D4020194043002D05C4 = {
						CreatedOnToolsVersion = 9.2;
						DevelopmentTeam = 75GAHG3SZQ;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 1DEB91B108733DA50010E9CD /* Build configuration list for PBXProject "VLCKit" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				English,
				Japanese,
				French,
				German,
			);
			mainGroup = 0867D691FE84028FC02AAC07 /* VLC */;
			productRefGroup = 034768DFFF38A50411DB9C8B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7DC28D4020194043002D05C4 /* StaticLibVLC */,
				7DC28D4C20194117002D05C4 /* VLCKit */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		7DC28D3D20194043002D05C4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7DC28D4B2019406B002D05C4 /* StaticLibVLC.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7DC28D6520194117002D05C4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7DC28D6720194117002D05C4 /* VLCLibrary.m in Sources */,
				7DA7F2F628D87F1800366EEB /* VLCMediaMetaData.m in Sources */,
				7DC28D6820194117002D05C4 /* VLCMedia.m in Sources */,
				7DC28D6920194117002D05C4 /* VLCMediaLibrary.m in Sources */,
				3CF2BE88297F026C00BBE2E3 /* VLCAudioEqualizer.m in Sources */,
				7DC28D6A20194117002D05C4 /* VLCMediaList.m in Sources */,
				7DC28D6B20194117002D05C4 /* VLCTime.m in Sources */,
				6C633567281843A000AF8CCF /* VLCLogMessageFormatter.m in Sources */,
				4102567221B56E6E00253ECF /* VLCTranscoder.m in Sources */,
				7DC28D6C20194117002D05C4 /* VLCVideoView.m in Sources */,
				7DC28D6D20194117002D05C4 /* VLCCustomDialogProvider.m in Sources */,
				41657C422059DE7B00AB915B /* VLCRendererDiscoverer.m in Sources */,
				7DC28D6E20194117002D05C4 /* VLCMediaPlayer.m in Sources */,
				6C633566281843A000AF8CCF /* VLCFileLogger.m in Sources */,
				6C6ABBCC27C63522008FDB2D /* VLCAdjustFilter.m in Sources */,
				7DC28D6F20194117002D05C4 /* VLCAudio.m in Sources */,
				7DC28D7020194117002D05C4 /* VLCDialogProvider.m in Sources */,
				7DC28D7120194117002D05C4 /* VLCMediaDiscoverer.m in Sources */,
				7DC28D7220194117002D05C4 /* VLCVideoLayer.m in Sources */,
				7DC28D7320194117002D05C4 /* VLCVideoCommon.m in Sources */,
				6C6ABBCD27C63522008FDB2D /* VLCFilter.m in Sources */,
				6C13FFB929BF8D2D00C4C68E /* VLCEventsHandler.m in Sources */,
				7DC28D7420194117002D05C4 /* VLCStreamSession.m in Sources */,
				7DC28D7520194117002D05C4 /* VLCStreamOutput.m in Sources */,
				7DC28D7620194117002D05C4 /* VLCMediaListPlayer.m in Sources */,
				7DAD5C5B2A836058009419DC /* VLCEventsConfiguration.m in Sources */,
				41657C432059DE7B00AB915B /* VLCRendererItem.m in Sources */,
				7DC28D7720194117002D05C4 /* VLCMediaThumbnailer.m in Sources */,
				7DC28D7820194117002D05C4 /* VLCHelperCode.m in Sources */,
				6C633565281843A000AF8CCF /* VLCConsoleLogger.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		7DACDDB72028A5A5002E2FE9 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7DC28D4020194043002D05C4 /* StaticLibVLC */;
			targetProxy = 7DACDDB62028A5A5002E2FE9 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1DEB91B208733DA50010E9CD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_VERSION = "";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				VLC_BUILD_DIR = "$(SYMROOT)/$(CONFIGURATION)/vlc_build_dir";
				VLC_SRC_DIR = "$(SRCROOT)/vlc-unstable";
			};
			name = Debug;
		};
		1DEB91B308733DA50010E9CD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ARCHS = "$(ARCHS_STANDARD)";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_VERSION = "";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				ONLY_ACTIVE_ARCH = NO;
				SDKROOT = macosx;
				VLC_BUILD_DIR = "$(SYMROOT)/$(CONFIGURATION)/vlc_build_dir";
				VLC_SRC_DIR = "$(SRCROOT)/vlc-unstable";
			};
			name = Release;
		};
		7DC28D4820194043002D05C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7DC28D3C201938D9002D05C4 /* vlc-plugins-MacOSX.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Mac Developer";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 75GAHG3SZQ;
				EXECUTABLE_PREFIX = lib;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				MTL_ENABLE_DEBUG_INFO = YES;
				OTHER_LIBTOOLFLAGS = "$(VLC_PLUGINS_DEVICE_LDFLAGS)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
			};
			name = Debug;
		};
		7DC28D4920194043002D05C4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7DC28D3C201938D9002D05C4 /* vlc-plugins-MacOSX.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CODE_SIGN_IDENTITY = "Mac Developer";
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 75GAHG3SZQ;
				ENABLE_NS_ASSERTIONS = NO;
				EXECUTABLE_PREFIX = lib;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_LIBTOOLFLAGS = "$(VLC_PLUGINS_DEVICE_LDFLAGS)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
			};
			name = Release;
		};
		7DC28D7D20194117002D05C4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7DC28D3C201938D9002D05C4 /* vlc-plugins-MacOSX.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_OBJC_IMPLICIT_ATOMIC_PROPERTIES = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@loader_path/../Frameworks";
				FRAMEWORK_VERSION = A;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Headers/PCH/Prefix.pch;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/libvlc/vlc/include",
					"Headers/**",
				);
				INFOPLIST_FILE = "$(SRCROOT)/Resources/VLCKit/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = "$(CONFIGURATION_BUILD_DIR)/$(PRODUCT_NAME).$(WRAPPER_EXTENSION)/lib";
				MODULEMAP_FILE = Resources/VLCKit/module.modulemap;
				PRIVATE_HEADERS_FOLDER_PATH = "$(CONTENTS_FOLDER_PATH)/PrivateHeaders";
				PRODUCT_BUNDLE_IDENTIFIER = org.videolan.vlckitframework;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SCAN_ALL_SOURCE_FILES_FOR_INCLUDES = YES;
				SDKROOT = macosx;
				WRAPPER_EXTENSION = framework;
			};
			name = Debug;
		};
		7DC28D7E20194117002D05C4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7DC28D3C201938D9002D05C4 /* vlc-plugins-MacOSX.xcconfig */;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ARCHS = "$(ARCHS_STANDARD)";
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_OBJC_IMPLICIT_ATOMIC_PROPERTIES = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@loader_path/../Frameworks";
				ENABLE_NS_ASSERTIONS = NO;
				FRAMEWORK_VERSION = A;
				GCC_C_LANGUAGE_STANDARD = c11;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Headers/PCH/Prefix.pch;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)/libvlc/vlc/include",
					"Headers/**",
				);
				INFOPLIST_FILE = "$(SRCROOT)/Resources/VLCKit/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				LIBRARY_SEARCH_PATHS = "$(CONFIGURATION_BUILD_DIR)/$(PRODUCT_NAME).$(WRAPPER_EXTENSION)/lib";
				MODULEMAP_FILE = Resources/VLCKit/module.modulemap;
				PRIVATE_HEADERS_FOLDER_PATH = "$(CONTENTS_FOLDER_PATH)/PrivateHeaders";
				PRODUCT_BUNDLE_IDENTIFIER = org.videolan.vlckitframework;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SCAN_ALL_SOURCE_FILES_FOR_INCLUDES = YES;
				SDKROOT = macosx;
				WRAPPER_EXTENSION = framework;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1DEB91B108733DA50010E9CD /* Build configuration list for PBXProject "VLCKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1DEB91B208733DA50010E9CD /* Debug */,
				1DEB91B308733DA50010E9CD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7DC28D4720194043002D05C4 /* Build configuration list for PBXNativeTarget "StaticLibVLC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7DC28D4820194043002D05C4 /* Debug */,
				7DC28D4920194043002D05C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7DC28D7C20194117002D05C4 /* Build configuration list for PBXNativeTarget "VLCKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7DC28D7D20194117002D05C4 /* Debug */,
				7DC28D7E20194117002D05C4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0867D690FE84028FC02AAC07 /* Project object */;
}
