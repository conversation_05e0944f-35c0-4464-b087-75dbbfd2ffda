# 播放器常見問題快速修復

## 可能的問題和解決方案

### 問題 1: 播放器視窗開啟但是黑屏
**症狀**: 視窗開啟但是沒有視頻顯示，或者顯示黑屏

**可能原因**:
- AVPlayer 未正確初始化
- 媒體檔案無法載入
- VideoPlayer 組件未正確渲染

**修復方案**:
```swift
// 在 PlayerWindowView 中確保 VideoPlayer 正確設置
if MediaInfoView.isVideoFile(url: url) {
    VideoPlayer(player: player)
        .ignoresSafeArea()
        .onAppear {
            // 延遲播放，確保視窗完全載入
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                player.play()
            }
        }
}
```

### 問題 2: 無法載入媒體檔案
**症狀**: 選擇檔案後無法播放，出現錯誤訊息

**可能原因**:
- 檔案格式不支援
- 檔案路徑權限問題
- AVURLAsset 載入失敗

**修復方案**:
```swift
// 改進 loadMedia 函數中的錯誤處理
func loadMedia(from url: URL, completion: @escaping (Bool) -> Void) {
    // 確保在主線程
    DispatchQueue.main.async {
        self.isLoading = true
        self.errorMessage = nil
        
        // 檢查檔案存在
        guard FileManager.default.fileExists(atPath: url.path) else {
            self.errorMessage = "檔案不存在: \(url.lastPathComponent)"
            self.isLoading = false
            completion(false)
            return
        }
        
        // 檢查檔案可讀取
        guard FileManager.default.isReadableFile(atPath: url.path) else {
            self.errorMessage = "檔案無法讀取: \(url.lastPathComponent)"
            self.isLoading = false
            completion(false)
            return
        }
        
        // 使用 AVURLAsset 載入
        let asset = AVURLAsset(url: url)
        
        // 異步載入資產資訊
        Task {
            do {
                let isPlayable = try await asset.load(.isPlayable)
                let isReadable = try await asset.load(.isReadable)
                
                await MainActor.run {
                    if !isPlayable {
                        self.errorMessage = "檔案格式不支援播放"
                        self.isLoading = false
                        completion(false)
                        return
                    }
                    
                    if !isReadable {
                        self.errorMessage = "檔案無法讀取"
                        self.isLoading = false
                        completion(false)
                        return
                    }
                    
                    // 設置播放器
                    self.setupPlayerWithAsset(asset) { success in
                        completion(success)
                    }
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "載入媒體時發生錯誤: \(error.localizedDescription)"
                    self.isLoading = false
                    completion(false)
                }
            }
        }
    }
}
```

### 問題 3: 控制按鈕無法使用
**症狀**: 播放/暫停、音量等控制按鈕點擊無效

**可能原因**:
- PlayerManager 的函數未正確綁定
- UI 更新問題

**修復方案**:
```swift
// 確保控制按鈕正確綁定
Button(action: {
    playerWindow.playerManager.togglePlayPause()
}) {
    Image(systemName: playerWindow.playerManager.isPlaying ? "pause.fill" : "play.fill")
}

// 在 PlayerManager 中改進 togglePlayPause
func togglePlayPause() {
    guard let player = player else { return }
    
    DispatchQueue.main.async {
        if self.isPlaying {
            player.pause()
        } else {
            player.play()
        }
        self.userInteracted() // 重置控制項顯示計時器
    }
}
```

### 問題 4: 視窗無法關閉
**症狀**: 點擊關閉按鈕視窗不會關閉

**修復方案**:
```swift
// 確保關閉按鈕正確實現
Button(action: {
    // 使用安全關閉方法
    WindowManager.shared.safeCloseWindow(withId: playerWindow.id)
}) {
    Image(systemName: "xmark.circle.fill")
        .font(.system(size: 20))
        .foregroundColor(.white)
}
.buttonStyle(PlainButtonStyle())
```

### 問題 5: 音頻檔案無法播放
**症狀**: 視頻檔案可以播放但音頻檔案不行

**修復方案**:
```swift
// 在音頻播放界面添加播放控制
VStack {
    // 音頻視覺化界面
    Image(systemName: "music.note")
        .font(.system(size: 80))
        .foregroundColor(.white.opacity(0.8))
    
    Text(url.lastPathComponent)
        .font(.title2)
        .foregroundColor(.white)
        .padding(.top, 30)
    
    // 確保音頻也會自動播放
    Text(playerWindow.playerManager.isPlaying ? "播放中..." : "暫停")
        .font(.caption)
        .foregroundColor(.white.opacity(0.6))
}
.onAppear {
    // 音頻檔案也需要觸發播放
    if let player = playerWindow.playerManager.player {
        player.play()
    }
}
```

## 完整檢查清單

### 1. 檢查 AVPlayer 初始化
```swift
// 在 PlayerManager 中
private func setupPlayerWithAsset(_ asset: AVURLAsset, completion: @escaping (Bool) -> Void) {
    let playerItem = AVPlayerItem(asset: asset)
    
    // 清理舊的觀察者
    removeObservers()
    
    if let existingPlayer = self.player {
        existingPlayer.replaceCurrentItem(with: playerItem)
    } else {
        self.player = AVPlayer(playerItem: playerItem)
        self.player?.automaticallyWaitsToMinimizeStalling = true
    }
    
    // 設置初始狀態
    self.player?.rate = 0 // 先暫停
    self.player?.volume = self.volume
    self.player?.isMuted = self.isMuted
    
    // 設置觀察者
    setupObservers(for: playerItem)
    
    // 等待準備完成
    playerItem.addObserver(self, forKeyPath: "status", options: [.new], context: nil)
}
```

### 2. 檢查檔案格式支援
```swift
static let supportedTypes: [UTType] = [
    .movie,           // 通用視頻
    .video,           // 通用視頻
    .mpeg4Movie,      // MP4
    .quickTimeMovie,  // MOV
    .avi,             // AVI
    .audio,           // 通用音頻
    .mp3,             // MP3
    .wav,             // WAV
    .m4a,             // M4A
    .aac,             // AAC
    .flac             // FLAC
]
```

### 3. 檢查權限設置
```swift
// 在 loadMedia 中
let didStartAccessing = url.startAccessingSecurityScopedResource()
defer {
    if didStartAccessing {
        url.stopAccessingSecurityScopedResource()
    }
}
```

## 使用診斷工具
我已經創建了 `PlayerDiagnostics.swift` 檔案，您可以：

1. 將 `DiagnosticPlayerWindowView` 集成到播放器中
2. 在出現問題時點擊"診斷"按鈕
3. 查看詳細的診斷資訊來定位問題

## 如果問題持續存在
請告訴我具體的錯誤訊息或症狀，我可以提供更針對性的解決方案。