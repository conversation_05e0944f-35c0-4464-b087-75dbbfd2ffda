# 🎮 自動播放調試

## 🔧 最新修復

我剛剛發現了自動播放失敗的可能原因：

### 問題分析
1. **狀態觀察者衝突** - `statusObserver` 會根據播放器的實際狀態覆蓋我們手動設置的 `isPlaying`
2. **播放器狀態** - 播放器可能因為某種原因沒有真正開始播放

### 修復內容
```swift
// 之前：簡單設置狀態
self?.isPlaying = (status == .playing)

// 現在：詳細追蹤 + 自動重試
let wasPlaying = self?.isPlaying ?? false
let nowPlaying = (status == .playing)
self?.isPlaying = nowPlaying

print("🎮 播放狀態變化: \(wasPlaying) → \(nowPlaying) (status: \(status.rawValue))")

// 如果播放器意外停止，嘗試重新播放
if !nowPlaying && status == .paused {
    print("⚠️ 播放器意外暫停，嘗試重新播放")
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
        player.play()
    }
}
```

### 額外調試
```swift
// 確保音量設置正確
print("🔊 當前音量: \(newPlayer.volume), 靜音: \(newPlayer.isMuted)")

// 強制設置播放速率
newPlayer.rate = 1.0

print("⚡ 播放命令已發送 (\(fileExtension.uppercased())) - rate: \(newPlayer.rate)")
```

## 🧪 測試步驟

### 1. 重新運行
```
▶️ Cmd+R 重新啟動應用
```

### 2. 觀察控制台
點擊第二個檔案時，應該看到：
```
🎵 UI點擊：準備播放索引 1, 檔案: filename.mp4
🚀 快速載入: filename.mp4
🔄 播放器狀態已重置
🔄 播放器已切換到: filename.mp4
🔊 當前音量: 0.8, 靜音: false
⚡ 播放命令已發送 (MP4) - rate: 1.0
🎮 播放狀態變化: false → true (status: 2)  ← 正常情況
```

### 3. 異常情況檢測
如果看到：
```
🎮 播放狀態變化: true → false (status: 0)
⚠️ 播放器意外暫停，嘗試重新播放
```
表示播放器被意外暫停，系統會自動重試。

## 🎯 可能的原因

### 1. 音頻會話問題
- macOS 可能阻止自動播放
- 需要用戶交互才允許播放

### 2. 檔案格式問題
- 某些 MP4 編碼可能有問題
- 需要額外的解碼時間

### 3. 系統權限問題
- macOS 音頻權限設置
- 檔案訪問權限

## 📊 狀態碼參考

AVPlayer.TimeControlStatus 值：
- `0` = `.paused` (暫停)
- `1` = `.waitingToPlayAtSpecifiedRate` (等待中)
- `2` = `.playing` (播放中)

## 🔍 下一步調試

如果問題仍然存在：

### 1. 檢查音頻會話
```swift
import AVFoundation

// 檢查音頻會話狀態
let audioSession = AVAudioSession.sharedInstance()
print("音頻會話類別: \(audioSession.category)")
```

### 2. 檢查播放器準備狀態
```swift
// 檢查播放項目狀態
if let item = player.currentItem {
    print("播放項目狀態: \(item.status.rawValue)")
    print("播放項目錯誤: \(item.error?.localizedDescription ?? "無")")
}
```

### 3. 手動播放測試
如果自動播放失敗，手動點擊播放按鈕是否能正常工作？

---

**調試時間**: 2025-01-08  
**問題**: 自動播放失敗，需要手動點擊播放  
**策略**: 狀態追蹤 + 自動重試機制  
**狀態**: 🔍 深度調試中