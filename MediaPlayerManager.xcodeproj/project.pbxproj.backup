// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		88BC92F854C37A1EFFB440DD /* Pods_MediaPlayerManagerTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A7AB85AE7DBBF90E0C765FE8 /* Pods_MediaPlayerManagerTests.framework */; };
		CFE68C7009877A957DF61245 /* Pods_MediaPlayerManager.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0F3CFADBB1100A8EDA81832C /* Pods_MediaPlayerManager.framework */; };
		EA6BF736DD7340D6063A9B3D /* Pods_MediaPlayerManager_MediaPlayerManagerUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 76B4B22EF6C0D6DBBA5DAB26 /* Pods_MediaPlayerManager_MediaPlayerManagerUITests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		1EC9E0902DA8D30A0053B25D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1EC9E0792DA8D3090053B25D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EC9E0802DA8D3090053B25D;
			remoteInfo = MediaPlayerManager;
		};
		1EC9E09A2DA8D30A0053B25D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1EC9E0792DA8D3090053B25D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EC9E0802DA8D3090053B25D;
			remoteInfo = MediaPlayerManager;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		09FA01DDEDCD5FCD1D70DDF8 /* Pods-MediaPlayerManager.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MediaPlayerManager.debug.xcconfig"; path = "Target Support Files/Pods-MediaPlayerManager/Pods-MediaPlayerManager.debug.xcconfig"; sourceTree = "<group>"; };
		0F3CFADBB1100A8EDA81832C /* Pods_MediaPlayerManager.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_MediaPlayerManager.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1EC9E0812DA8D3090053B25D /* MediaPlayerManager.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MediaPlayerManager.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1EC9E08F2DA8D30A0053B25D /* MediaPlayerManagerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MediaPlayerManagerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1EC9E0992DA8D30A0053B25D /* MediaPlayerManagerUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MediaPlayerManagerUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1EF77C5A2DACB175001434CD /* MediaPlayerManager.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = MediaPlayerManager.xctestplan; sourceTree = "<group>"; };
		45078616B34A788C312846B5 /* Pods-MediaPlayerManager.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MediaPlayerManager.release.xcconfig"; path = "Target Support Files/Pods-MediaPlayerManager/Pods-MediaPlayerManager.release.xcconfig"; sourceTree = "<group>"; };
		4BC05EBBE89E0E965E62DEFC /* Pods-MediaPlayerManager-MediaPlayerManagerUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MediaPlayerManager-MediaPlayerManagerUITests.release.xcconfig"; path = "Target Support Files/Pods-MediaPlayerManager-MediaPlayerManagerUITests/Pods-MediaPlayerManager-MediaPlayerManagerUITests.release.xcconfig"; sourceTree = "<group>"; };
		68A0C7B383FC278EE7E18197 /* Pods-MediaPlayerManagerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MediaPlayerManagerTests.release.xcconfig"; path = "Target Support Files/Pods-MediaPlayerManagerTests/Pods-MediaPlayerManagerTests.release.xcconfig"; sourceTree = "<group>"; };
		76B4B22EF6C0D6DBBA5DAB26 /* Pods_MediaPlayerManager_MediaPlayerManagerUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_MediaPlayerManager_MediaPlayerManagerUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		A7AB85AE7DBBF90E0C765FE8 /* Pods_MediaPlayerManagerTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_MediaPlayerManagerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D995F8434532ED71C7ABD9AA /* Pods-MediaPlayerManagerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MediaPlayerManagerTests.debug.xcconfig"; path = "Target Support Files/Pods-MediaPlayerManagerTests/Pods-MediaPlayerManagerTests.debug.xcconfig"; sourceTree = "<group>"; };
		E90A422A9E393304F2397646 /* Pods-MediaPlayerManager-MediaPlayerManagerUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MediaPlayerManager-MediaPlayerManagerUITests.debug.xcconfig"; path = "Target Support Files/Pods-MediaPlayerManager-MediaPlayerManagerUITests/Pods-MediaPlayerManager-MediaPlayerManagerUITests.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		1EC9E0832DA8D3090053B25D /* MediaPlayerManager */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MediaPlayerManager;
			sourceTree = "<group>";
		};
		1EC9E0922DA8D30A0053B25D /* MediaPlayerManagerTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MediaPlayerManagerTests;
			sourceTree = "<group>";
		};
		1EC9E09C2DA8D30A0053B25D /* MediaPlayerManagerUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MediaPlayerManagerUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		1EC9E07E2DA8D3090053B25D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CFE68C7009877A957DF61245 /* Pods_MediaPlayerManager.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1EC9E08C2DA8D30A0053B25D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				88BC92F854C37A1EFFB440DD /* Pods_MediaPlayerManagerTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1EC9E0962DA8D30A0053B25D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				EA6BF736DD7340D6063A9B3D /* Pods_MediaPlayerManager_MediaPlayerManagerUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1EC9E0782DA8D3090053B25D = {
			isa = PBXGroup;
			children = (
				1EF77C5A2DACB175001434CD /* MediaPlayerManager.xctestplan */,
				1EC9E0832DA8D3090053B25D /* MediaPlayerManager */,
				1EC9E0922DA8D30A0053B25D /* MediaPlayerManagerTests */,
				1EC9E09C2DA8D30A0053B25D /* MediaPlayerManagerUITests */,
				1EC9E0822DA8D3090053B25D /* Products */,
				95E8C3578E81E792E9EA6D69 /* Pods */,
				74C7D007C72B17C8FC2C5BF8 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		1EC9E0822DA8D3090053B25D /* Products */ = {
			isa = PBXGroup;
			children = (
				1EC9E0812DA8D3090053B25D /* MediaPlayerManager.app */,
				1EC9E08F2DA8D30A0053B25D /* MediaPlayerManagerTests.xctest */,
				1EC9E0992DA8D30A0053B25D /* MediaPlayerManagerUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		74C7D007C72B17C8FC2C5BF8 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				0F3CFADBB1100A8EDA81832C /* Pods_MediaPlayerManager.framework */,
				76B4B22EF6C0D6DBBA5DAB26 /* Pods_MediaPlayerManager_MediaPlayerManagerUITests.framework */,
				A7AB85AE7DBBF90E0C765FE8 /* Pods_MediaPlayerManagerTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		95E8C3578E81E792E9EA6D69 /* Pods */ = {
			isa = PBXGroup;
			children = (
				09FA01DDEDCD5FCD1D70DDF8 /* Pods-MediaPlayerManager.debug.xcconfig */,
				45078616B34A788C312846B5 /* Pods-MediaPlayerManager.release.xcconfig */,
				E90A422A9E393304F2397646 /* Pods-MediaPlayerManager-MediaPlayerManagerUITests.debug.xcconfig */,
				4BC05EBBE89E0E965E62DEFC /* Pods-MediaPlayerManager-MediaPlayerManagerUITests.release.xcconfig */,
				D995F8434532ED71C7ABD9AA /* Pods-MediaPlayerManagerTests.debug.xcconfig */,
				68A0C7B383FC278EE7E18197 /* Pods-MediaPlayerManagerTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1EC9E0802DA8D3090053B25D /* MediaPlayerManager */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1EC9E0A32DA8D30A0053B25D /* Build configuration list for PBXNativeTarget "MediaPlayerManager" */;
			buildPhases = (
				F7236FE84E48247A78BE2C10 /* [CP] Check Pods Manifest.lock */,
				1EC9E07D2DA8D3090053B25D /* Sources */,
				1EC9E07E2DA8D3090053B25D /* Frameworks */,
				1EC9E07F2DA8D3090053B25D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				1EC9E0832DA8D3090053B25D /* MediaPlayerManager */,
			);
			name = MediaPlayerManager;
			productName = MediaPlayerManager;
			productReference = 1EC9E0812DA8D3090053B25D /* MediaPlayerManager.app */;
			productType = "com.apple.product-type.application";
		};
		1EC9E08E2DA8D30A0053B25D /* MediaPlayerManagerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1EC9E0A62DA8D30A0053B25D /* Build configuration list for PBXNativeTarget "MediaPlayerManagerTests" */;
			buildPhases = (
				1A1533E64D12EBA8DBE9C300 /* [CP] Check Pods Manifest.lock */,
				1EC9E08B2DA8D30A0053B25D /* Sources */,
				1EC9E08C2DA8D30A0053B25D /* Frameworks */,
				1EC9E08D2DA8D30A0053B25D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1EC9E0912DA8D30A0053B25D /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1EC9E0922DA8D30A0053B25D /* MediaPlayerManagerTests */,
			);
			name = MediaPlayerManagerTests;
			productName = MediaPlayerManagerTests;
			productReference = 1EC9E08F2DA8D30A0053B25D /* MediaPlayerManagerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		1EC9E0982DA8D30A0053B25D /* MediaPlayerManagerUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1EC9E0A92DA8D30A0053B25D /* Build configuration list for PBXNativeTarget "MediaPlayerManagerUITests" */;
			buildPhases = (
				69CF9CDB908C0CC8261A21C3 /* [CP] Check Pods Manifest.lock */,
				1EC9E0952DA8D30A0053B25D /* Sources */,
				1EC9E0962DA8D30A0053B25D /* Frameworks */,
				1EC9E0972DA8D30A0053B25D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1EC9E09B2DA8D30A0053B25D /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1EC9E09C2DA8D30A0053B25D /* MediaPlayerManagerUITests */,
			);
			name = MediaPlayerManagerUITests;
			productName = MediaPlayerManagerUITests;
			productReference = 1EC9E0992DA8D30A0053B25D /* MediaPlayerManagerUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1EC9E0792DA8D3090053B25D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					1EC9E0802DA8D3090053B25D = {
						CreatedOnToolsVersion = 16.3;
					};
					1EC9E08E2DA8D30A0053B25D = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 1EC9E0802DA8D3090053B25D;
					};
					1EC9E0982DA8D30A0053B25D = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 1EC9E0802DA8D3090053B25D;
					};
				};
			};
			buildConfigurationList = 1EC9E07C2DA8D3090053B25D /* Build configuration list for PBXProject "MediaPlayerManager" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1EC9E0782DA8D3090053B25D;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				1EF77BFD2DA9325F001434CD /* XCRemoteSwiftPackageReference "VLCKit" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 1EC9E0822DA8D3090053B25D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1EC9E0802DA8D3090053B25D /* MediaPlayerManager */,
				1EC9E08E2DA8D30A0053B25D /* MediaPlayerManagerTests */,
				1EC9E0982DA8D30A0053B25D /* MediaPlayerManagerUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1EC9E07F2DA8D3090053B25D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1EC9E08D2DA8D30A0053B25D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1EC9E0972DA8D30A0053B25D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1A1533E64D12EBA8DBE9C300 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MediaPlayerManagerTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		69CF9CDB908C0CC8261A21C3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MediaPlayerManager-MediaPlayerManagerUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		F7236FE84E48247A78BE2C10 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MediaPlayerManager-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1EC9E07D2DA8D3090053B25D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1EC9E08B2DA8D30A0053B25D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1EC9E0952DA8D30A0053B25D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1EC9E0912DA8D30A0053B25D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1EC9E0802DA8D3090053B25D /* MediaPlayerManager */;
			targetProxy = 1EC9E0902DA8D30A0053B25D /* PBXContainerItemProxy */;
		};
		1EC9E09B2DA8D30A0053B25D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1EC9E0802DA8D3090053B25D /* MediaPlayerManager */;
			targetProxy = 1EC9E09A2DA8D30A0053B25D /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1EC9E0A12DA8D30A0053B25D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = JCQTNU94J5;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1EC9E0A22DA8D30A0053B25D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = JCQTNU94J5;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		1EC9E0A42DA8D30A0053B25D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 09FA01DDEDCD5FCD1D70DDF8 /* Pods-MediaPlayerManager.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MediaPlayerManager/MediaPlayerManager.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "-";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JCQTNU94J5;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.video";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kasa.MediaPlayerManager;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		1EC9E0A52DA8D30A0053B25D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 45078616B34A788C312846B5 /* Pods-MediaPlayerManager.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MediaPlayerManager/MediaPlayerManager.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JCQTNU94J5;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.video";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kasa.MediaPlayerManager;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		1EC9E0A72DA8D30A0053B25D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D995F8434532ED71C7ABD9AA /* Pods-MediaPlayerManagerTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JCQTNU94J5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kasa.MediaPlayerManagerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MediaPlayerManager.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MediaPlayerManager";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		1EC9E0A82DA8D30A0053B25D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 68A0C7B383FC278EE7E18197 /* Pods-MediaPlayerManagerTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JCQTNU94J5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kasa.MediaPlayerManagerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MediaPlayerManager.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MediaPlayerManager";
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
		1EC9E0AA2DA8D30A0053B25D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = E90A422A9E393304F2397646 /* Pods-MediaPlayerManager-MediaPlayerManagerUITests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JCQTNU94J5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kasa.MediaPlayerManagerUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = MediaPlayerManager;
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Debug;
		};
		1EC9E0AB2DA8D30A0053B25D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4BC05EBBE89E0E965E62DEFC /* Pods-MediaPlayerManager-MediaPlayerManagerUITests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JCQTNU94J5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.kasa.MediaPlayerManagerUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = MediaPlayerManager;
				XROS_DEPLOYMENT_TARGET = 2.4;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1EC9E07C2DA8D3090053B25D /* Build configuration list for PBXProject "MediaPlayerManager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1EC9E0A12DA8D30A0053B25D /* Debug */,
				1EC9E0A22DA8D30A0053B25D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1EC9E0A32DA8D30A0053B25D /* Build configuration list for PBXNativeTarget "MediaPlayerManager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1EC9E0A42DA8D30A0053B25D /* Debug */,
				1EC9E0A52DA8D30A0053B25D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1EC9E0A62DA8D30A0053B25D /* Build configuration list for PBXNativeTarget "MediaPlayerManagerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1EC9E0A72DA8D30A0053B25D /* Debug */,
				1EC9E0A82DA8D30A0053B25D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1EC9E0A92DA8D30A0053B25D /* Build configuration list for PBXNativeTarget "MediaPlayerManagerUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1EC9E0AA2DA8D30A0053B25D /* Debug */,
				1EC9E0AB2DA8D30A0053B25D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		1EF77BFD2DA9325F001434CD /* XCRemoteSwiftPackageReference "VLCKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://code.videolan.org/videolan/VLCKit.git";
			requirement = {
				kind = exactVersion;
				version = 3.6.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */
	};
	rootObject = 1EC9E0792DA8D3090053B25D /* Project object */;
}
