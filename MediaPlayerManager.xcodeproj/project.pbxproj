// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		1EC9E0902DA8D30A0053B25D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1EC9E0792DA8D3090053B25D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EC9E0802DA8D3090053B25D;
			remoteInfo = MediaPlayerManager;
		};
		1EC9E09A2DA8D30A0053B25D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1EC9E0792DA8D3090053B25D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1EC9E0802DA8D3090053B25D;
			remoteInfo = MediaPlayerManager;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1EC9E0812DA8D3090053B25D /* MediaPlayerManager.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MediaPlayerManager.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1EC9E08F2DA8D30A0053B25D /* MediaPlayerManagerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MediaPlayerManagerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1EC9E0992DA8D30A0053B25D /* MediaPlayerManagerUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = MediaPlayerManagerUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		1EF77C5A2DACB175001434CD /* MediaPlayerManager.xctestplan */ = {isa = PBXFileReference; lastKnownFileType = text; path = MediaPlayerManager.xctestplan; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		1EC9E0832DA8D3090053B25D /* MediaPlayerManager */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MediaPlayerManager;
			sourceTree = "<group>";
		};
		1EC9E0922DA8D30A0053B25D /* MediaPlayerManagerTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MediaPlayerManagerTests;
			sourceTree = "<group>";
		};
		1EC9E09C2DA8D30A0053B25D /* MediaPlayerManagerUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MediaPlayerManagerUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		1EC9E07E2DA8D3090053B25D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1EC9E08C2DA8D30A0053B25D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1EC9E0962DA8D30A0053B25D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1EC9E0782DA8D3090053B25D = {
			isa = PBXGroup;
			children = (
				1EF77C5A2DACB175001434CD /* MediaPlayerManager.xctestplan */,
				1EC9E0832DA8D3090053B25D /* MediaPlayerManager */,
				1EC9E0922DA8D30A0053B25D /* MediaPlayerManagerTests */,
				1EC9E09C2DA8D30A0053B25D /* MediaPlayerManagerUITests */,
				1EC9E0822DA8D3090053B25D /* Products */,
			);
			sourceTree = "<group>";
		};
		1EC9E0822DA8D3090053B25D /* Products */ = {
			isa = PBXGroup;
			children = (
				1EC9E0812DA8D3090053B25D /* MediaPlayerManager.app */,
				1EC9E08F2DA8D30A0053B25D /* MediaPlayerManagerTests.xctest */,
				1EC9E0992DA8D30A0053B25D /* MediaPlayerManagerUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1EC9E0802DA8D3090053B25D /* MediaPlayerManager */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1EC9E0A32DA8D30A0053B25D /* Build configuration list for PBXNativeTarget "MediaPlayerManager" */;
			buildPhases = (
				1EC9E07D2DA8D3090053B25D /* Sources */,
				1EC9E07E2DA8D3090053B25D /* Frameworks */,
				1EC9E07F2DA8D3090053B25D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				1EC9E0832DA8D3090053B25D /* MediaPlayerManager */,
			);
			name = MediaPlayerManager;
			productName = MediaPlayerManager;
			productReference = 1EC9E0812DA8D3090053B25D /* MediaPlayerManager.app */;
			productType = "com.apple.product-type.application";
		};
		1EC9E08E2DA8D30A0053B25D /* MediaPlayerManagerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1EC9E0A62DA8D30A0053B25D /* Build configuration list for PBXNativeTarget "MediaPlayerManagerTests" */;
			buildPhases = (
				1EC9E08B2DA8D30A0053B25D /* Sources */,
				1EC9E08C2DA8D30A0053B25D /* Frameworks */,
				1EC9E08D2DA8D30A0053B25D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1EC9E0912DA8D30A0053B25D /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1EC9E0922DA8D30A0053B25D /* MediaPlayerManagerTests */,
			);
			name = MediaPlayerManagerTests;
			productName = MediaPlayerManagerTests;
			productReference = 1EC9E08F2DA8D30A0053B25D /* MediaPlayerManagerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		1EC9E0982DA8D30A0053B25D /* MediaPlayerManagerUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1EC9E0A92DA8D30A0053B25D /* Build configuration list for PBXNativeTarget "MediaPlayerManagerUITests" */;
			buildPhases = (
				1EC9E0952DA8D30A0053B25D /* Sources */,
				1EC9E0962DA8D30A0053B25D /* Frameworks */,
				1EC9E0972DA8D30A0053B25D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				1EC9E09B2DA8D30A0053B25D /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				1EC9E09C2DA8D30A0053B25D /* MediaPlayerManagerUITests */,
			);
			name = MediaPlayerManagerUITests;
			productName = MediaPlayerManagerUITests;
			productReference = 1EC9E0992DA8D30A0053B25D /* MediaPlayerManagerUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1EC9E0792DA8D3090053B25D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					1EC9E0802DA8D3090053B25D = {
						CreatedOnToolsVersion = 16.0;
					};
					1EC9E08E2DA8D30A0053B25D = {
						CreatedOnToolsVersion = 16.0;
						TestTargetID = 1EC9E0802DA8D3090053B25D;
					};
					1EC9E0982DA8D30A0053B25D = {
						CreatedOnToolsVersion = 16.0;
						TestTargetID = 1EC9E0802DA8D3090053B25D;
					};
				};
			};
			buildConfigurationList = 1EC9E07C2DA8D3090053B25D /* Build configuration list for PBXProject "MediaPlayerManager" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1EC9E0782DA8D3090053B25D;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 1EC9E0822DA8D3090053B25D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1EC9E0802DA8D3090053B25D /* MediaPlayerManager */,
				1EC9E08E2DA8D30A0053B25D /* MediaPlayerManagerTests */,
				1EC9E0982DA8D30A0053B25D /* MediaPlayerManagerUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1EC9E07F2DA8D3090053B25D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1EC9E08D2DA8D30A0053B25D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1EC9E0972DA8D30A0053B25D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1EC9E07D2DA8D3090053B25D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1EC9E08B2DA8D30A0053B25D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1EC9E0952DA8D30A0053B25D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1EC9E0912DA8D30A0053B25D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1EC9E0802DA8D3090053B25D /* MediaPlayerManager */;
			targetProxy = 1EC9E0902DA8D30A0053B25D /* PBXContainerItemProxy */;
		};
		1EC9E09B2DA8D30A0053B25D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1EC9E0802DA8D3090053B25D /* MediaPlayerManager */;
			targetProxy = 1EC9E09A2DA8D30A0053B25D /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1EC9E07A2DA8D3090053B25D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1EC9E07B2DA8D3090053B25D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		1EC9E0A42DA8D30A0053B25D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				"ASSETCATALOG_COMPILER_APPICON_NAME[sdk=macosx*]" = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CODE_SIGN_ENTITLEMENTS = MediaPlayerManager/MediaPlayerManager.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"MediaPlayerManager/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.MediaPlayerManager;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		1EC9E0A52DA8D30A0053B25D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				"ASSETCATALOG_COMPILER_APPICON_NAME[sdk=macosx*]" = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CODE_SIGN_ENTITLEMENTS = MediaPlayerManager/MediaPlayerManager.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "-";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"MediaPlayerManager/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.MediaPlayerManager;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		1EC9E0A72DA8D30A0053B25D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.MediaPlayerManagerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MediaPlayerManager.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MediaPlayerManager";
			};
			name = Debug;
		};
		1EC9E0A82DA8D30A0053B25D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.MediaPlayerManagerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/MediaPlayerManager.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/MediaPlayerManager";
			};
			name = Release;
		};
		1EC9E0AA2DA8D30A0053B25D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.MediaPlayerManagerUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = MediaPlayerManager;
			};
			name = Debug;
		};
		1EC9E0AB2DA8D30A0053B25D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.MediaPlayerManagerUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = MediaPlayerManager;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1EC9E07C2DA8D3090053B25D /* Build configuration list for PBXProject "MediaPlayerManager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1EC9E07A2DA8D3090053B25D /* Debug */,
				1EC9E07B2DA8D3090053B25D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1EC9E0A32DA8D30A0053B25D /* Build configuration list for PBXNativeTarget "MediaPlayerManager" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1EC9E0A42DA8D30A0053B25D /* Debug */,
				1EC9E0A52DA8D30A0053B25D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1EC9E0A62DA8D30A0053B25D /* Build configuration list for PBXNativeTarget "MediaPlayerManagerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1EC9E0A72DA8D30A0053B25D /* Debug */,
				1EC9E0A82DA8D30A0053B25D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1EC9E0A92DA8D30A0053B25D /* Build configuration list for PBXNativeTarget "MediaPlayerManagerUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1EC9E0AA2DA8D30A0053B25D /* Debug */,
				1EC9E0AB2DA8D30A0053B25D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1EC9E0792DA8D3090053B25D /* Project object */;
}
