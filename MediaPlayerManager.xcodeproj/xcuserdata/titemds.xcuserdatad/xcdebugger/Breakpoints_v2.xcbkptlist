<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "CDE32008-B83C-4E96-9378-2083071245D7"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "744C8D6C-95E1-4F49-9737-214D0AB7D00A"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MediaPlayerManager/ContentView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "305"
            endingLineNumber = "305"
            landmarkName = "body"
            landmarkType = "24">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
