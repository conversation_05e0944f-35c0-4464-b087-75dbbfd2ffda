#!/usr/bin/swift

import Foundation
import AVFoundation

// Test script to validate the problematic file
let testFilePath = "/Volumes/SP PHD U3/其他/Jable/早野詩/ssis-221/ssis-221.mp4"
let testURL = URL(fileURLWithPath: testFilePath)

print("🔍 Testing file validation for problematic file:")
print("📁 Path: \(testFilePath)")
print("📁 URL: \(testURL.absoluteString)")

// Check basic file existence
let fileExists = FileManager.default.fileExists(atPath: testFilePath)
print("📁 File exists: \(fileExists)")

if fileExists {
    // Check file attributes
    do {
        let attributes = try FileManager.default.attributesOfItem(atPath: testFilePath)
        let fileSize = attributes[.size] as? Int64 ?? 0
        print("📊 File size: \(fileSize) bytes (\(fileSize / 1024 / 1024) MB)")
        
        let isReadable = FileManager.default.isReadableFile(atPath: testFilePath)
        print("👀 Is readable: \(isReadable)")
        
        // Check file extension
        let fileExtension = testURL.pathExtension.lowercased()
        print("📝 File extension: .\(fileExtension)")
        
        // Check for problematic characters in path
        let problematicChars = CharacterSet(charactersIn: "\"<>|*?")
        let hasProblematicChars = testURL.path.rangeOfCharacter(from: problematicChars) != nil
        print("⚠️  Has problematic characters: \(hasProblematicChars)")
        
        // Test with AVAsset
        print("\n🎬 Testing with AVAsset...")
        let asset = AVURLAsset(url: testURL)
        
        // Test asset properties synchronously (older method)
        let tracks = asset.tracks
        print("🎵 Track count: \(tracks.count)")
        
        for (index, track) in tracks.enumerated() {
            print("🎵 Track \(index): \(track.mediaType.rawValue)")
        }
        
        print("⏱️  Duration: \(CMTimeGetSeconds(asset.duration)) seconds")
        print("✅ Playable: \(asset.isPlayable)")
        
    } catch {
        print("❌ Error reading file attributes: \(error)")
    }
    
} else {
    print("❌ File does not exist at the specified path")
    print("💡 Please check if the external drive is mounted and the path is correct")
}