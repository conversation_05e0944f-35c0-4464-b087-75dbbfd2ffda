# 增強版重試系統報告

## 問題診斷

從截圖可以看到「播放失敗，已重試 2 次」，表示：
1. ✅ 重試機制有在工作
2. ❌ 但重試 2 次後仍失敗
3. 🔍 需要更深度的清理和診斷

## 增強改進

### 1. 更強健的重試機制
```swift
// 重試參數增強
private var maxRetries = 3          // 增加到 3 次
private var retryDelay: TimeInterval = 1.0  // 遞增延遲

// 智能延遲策略
retryDelay = min(retryDelay * 1.5, 3.0)  // 1s → 1.5s → 2.25s → 3s
```

### 2. 系統級深度清理
```swift
private func performSystemLevelCleanup() {
    // 1. 強制釋放所有 AVFoundation 資源
    player?.pause()
    player?.rate = 0
    player?.replaceCurrentItem(with: nil)
    player = nil
    
    // 2. 清理所有觀察者和通知
    removeObservers()
    NotificationCenter.default.removeObserver(self)
    
    // 3. 清理所有快取 (包括系統快取)
    URLCache.shared.removeAllCachedResponses()
    preloadedAssets.removeAll()
    PlayerManager.processedAssets.removeAll()
    
    // 4. 重置預熱狀態 (強制重新預熱)
    PlayerManager.preWarmedFormats.removeAll()
    
    // 5. 強制垃圾回收和系統清理
    Thread.sleep(forTimeInterval: 0.1)
}
```

### 3. 增強調試資訊
```swift
print("📊 檔案資訊: \(asset.url?.lastPathComponent ?? "未知")")
print("📊 檔案大小: \(asset.url?.fileSizeString ?? "未知")")
print("📊 PlayerItem 初始狀態: \(playerItem.status.rawValue)")
print("📊 錯誤碼: \(error?.code ?? -1)")
print("📊 錯誤域: \(nsError.domain)")
```

### 4. 更長的準備時間
- 超時時間從 5 秒增加到 7 秒
- 給 AVFoundation 更多時間來準備複雜的媒體檔案

## 診斷策略

### A. 重試期間的清理流程
1. **完全資源釋放**: 播放器、觀察者、通知
2. **系統快取清理**: URLCache、預載入快取、處理快取
3. **預熱狀態重置**: 強制重新預熱格式支援
4. **垃圾回收**: 觸發記憶體整理
5. **延遲等待**: 給系統時間完成清理

### B. 詳細錯誤追蹤
- PlayerItem 狀態變化追蹤
- 錯誤碼和錯誤域記錄
- 檔案存在性和大小檢查
- 時長和軌道資訊驗證

## 測試建議

### 第一次測試
1. 運行程式
2. 播放第一個檔案 (應該成功)
3. 播放第二個檔案
4. 查看控制台輸出，注意：
   - 📊 檔案資訊和大小
   - 🔍 PlayerItem 狀態變化
   - ❌ 具體錯誤訊息
   - 🧹 清理過程

### 預期改善
- **更多重試機會**: 3 次重試 vs 2 次
- **更徹底的清理**: 系統級清理 vs 基本清理  
- **更好的診斷**: 詳細錯誤資訊 vs 基本錯誤
- **更智能的延遲**: 遞增延遲 vs 固定延遲

## 下一步策略

如果這個版本仍然失敗，我們會看到更詳細的錯誤信息，然後可以：
1. 針對特定錯誤類型設計解決方案
2. 實施格式特定的處理策略
3. 添加更激進的清理方法
4. 考慮使用不同的 AVPlayer 初始化策略

讓我們看看這次的調試輸出會告訴我們什麼！